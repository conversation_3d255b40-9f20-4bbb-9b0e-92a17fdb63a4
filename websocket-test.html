<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        #messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            background-color: white;
        }
        .error { border-left-color: #dc3545; }
        .success { border-left-color: #28a745; }
    </style>
</head>
<body>
    <h1>WebSocket连接测试</h1>
    
    <div class="container">
        <h3>连接状态</h3>
        <div id="status" class="status disconnected">未连接</div>
        
        <div>
            <label for="wsUrl">WebSocket URL:</label>
            <input type="text" id="wsUrl" value="ws://localhost:8080/api/websocket/debug" style="width: 400px;">
        </div>
        
        <div style="margin: 10px 0;">
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            <button onclick="clearMessages()">清空消息</button>
        </div>
    </div>
    
    <div class="container">
        <h3>发送消息</h3>
        <div>
            <label for="sessionId">Session ID:</label>
            <input type="text" id="sessionId" value="test_session_123" style="width: 200px;">
        </div>
        <div style="margin: 10px 0;">
            <button onclick="sendBindMessage()">发送绑定消息</button>
            <button onclick="sendTestMessage()">发送测试消息</button>
        </div>
    </div>
    
    <div class="container">
        <h3>消息日志</h3>
        <div id="messages"></div>
    </div>

    <script>
        let websocket = null;
        let isConnecting = false;

        function addMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
        }

        function updateButtons(connected) {
            document.getElementById('connectBtn').disabled = connected || isConnecting;
            document.getElementById('disconnectBtn').disabled = !connected;
        }

        function connect() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                addMessage('WebSocket已经连接', 'error');
                return;
            }

            const url = document.getElementById('wsUrl').value;
            if (!url) {
                addMessage('请输入WebSocket URL', 'error');
                return;
            }

            isConnecting = true;
            updateStatus('连接中...', 'connecting');
            updateButtons(false);
            addMessage(`尝试连接到: ${url}`, 'info');

            try {
                websocket = new WebSocket(url);

                websocket.onopen = function(event) {
                    isConnecting = false;
                    updateStatus('已连接', 'connected');
                    updateButtons(true);
                    addMessage('WebSocket连接成功', 'success');
                };

                websocket.onmessage = function(event) {
                    addMessage(`收到消息: ${event.data}`, 'success');
                };

                websocket.onclose = function(event) {
                    isConnecting = false;
                    updateStatus('连接已关闭', 'disconnected');
                    updateButtons(false);
                    addMessage(`连接关闭: 代码=${event.code}, 原因=${event.reason}`, 'error');
                };

                websocket.onerror = function(event) {
                    isConnecting = false;
                    updateStatus('连接错误', 'disconnected');
                    updateButtons(false);
                    addMessage('WebSocket连接错误', 'error');
                };

            } catch (error) {
                isConnecting = false;
                updateStatus('连接失败', 'disconnected');
                updateButtons(false);
                addMessage(`连接异常: ${error.message}`, 'error');
            }
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
        }

        function sendBindMessage() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket未连接', 'error');
                return;
            }

            const sessionId = document.getElementById('sessionId').value;
            const message = {
                sessionId: sessionId,
                type: 'BIND',
                timestamp: Date.now()
            };

            websocket.send(JSON.stringify(message));
            addMessage(`发送绑定消息: ${JSON.stringify(message)}`, 'info');
        }

        function sendTestMessage() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket未连接', 'error');
                return;
            }

            const message = {
                type: 'TEST',
                data: 'Hello WebSocket!',
                timestamp: Date.now()
            };

            websocket.send(JSON.stringify(message));
            addMessage(`发送测试消息: ${JSON.stringify(message)}`, 'info');
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 页面加载时初始化
        window.onload = function() {
            updateStatus('未连接', 'disconnected');
            updateButtons(false);
            addMessage('页面加载完成，可以开始测试WebSocket连接', 'info');
        };

        // 页面关闭时清理连接
        window.onbeforeunload = function() {
            if (websocket) {
                websocket.close();
            }
        };
    </script>
</body>
</html>
