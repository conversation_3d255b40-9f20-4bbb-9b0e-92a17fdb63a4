<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
<!--    <parent>-->
<!--        <artifactId>risk-insight</artifactId>-->
<!--        <groupId>com.yupaopao.risk.insight</groupId>-->
<!--        <version>1.0.0-SNAPSHOT</version>-->
<!--    </parent>-->
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yupaopao.risk.insight</groupId>
    <artifactId>insight-common_${flink.version}</artifactId>
    <version>2.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <flink.version>1.13.3</flink.version>
        <scala.binary.version>2.11</scala.binary.version>
        <commons-lang3.version>3.6</commons-lang3.version>
        <lombok.version>1.18.10</lombok.version>
        <redis.clients.version>3.2.0</redis.clients.version>
        <fastjson.version>1.2.83</fastjson.version>
        <ali-hbase.client.version>2.0.6</ali-hbase.client.version>
        <json-flattener.version>0.8.0</json-flattener.version>
        <groovy-all.version>2.4.16</groovy-all.version>
        <gremlin-driver.version>3.4.6</gremlin-driver.version>
        <okhttp.version>3.8.1</okhttp.version>
        <config-client.version>0.10.21</config-client.version>
        <commons-codec.version>1.11</commons-codec.version>
        <ru.clickhouse-jdbc.version>0.2</ru.clickhouse-jdbc.version>
        <com.clikhouse-http-client.version>0.4.5</com.clikhouse-http-client.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
            <scope>provided</scope>
        </dependency>
        <!-- lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- redis client  -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${redis.clients.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
            <version>${fastjson.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>config-client</artifactId>
            <version>${config-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.cat</groupId>
                    <artifactId>cat-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yupaopao.moat</groupId>
                    <artifactId>moat-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.logstash.logback</groupId>
                    <artifactId>logstash-logback-encoder</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <version>${flink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>20.0</version>
        </dependency>
        <!-- ali hbase client  -->
        <dependency>
            <groupId>com.aliyun.hbase</groupId>
            <artifactId>alihbase-client</artifactId>
            <version>${ali-hbase.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-cli</groupId>
                    <artifactId>commons-cli</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>
        <!-- flatter json  -->
        <dependency>
            <groupId>com.github.wnameless.json</groupId>
            <artifactId>json-flattener</artifactId>
            <version>${json-flattener.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy-all.version}</version>
            <scope>provided</scope>
        </dependency>
        <!--  common flink connector  -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-cep_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.tinkerpop</groupId>
            <artifactId>gremlin-driver</artifactId>
            <version>${gremlin-driver.version}</version>
            <exclusions>
                <!-- commons-collections 在insight-flink-common包已经包含，避免冲突需要排除 -->
                <exclusion>
                    <groupId>commons-configuration</groupId>
                    <artifactId>commons-configuration</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-json</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.roaringbitmap</groupId>
            <artifactId>RoaringBitmap</artifactId>
            <version>0.8.13</version>
        </dependency>

        <!--  kafka 上云 -->
        <dependency>
            <groupId>com.yupaopao.metacenter</groupId>
            <artifactId>metacenter-client</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.cat</groupId>
                    <artifactId>cat-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.inject</groupId>
                    <artifactId>guice</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-io</groupId>
                    <artifactId>commons-io</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons-codec.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.clickhouse</groupId>
            <!-- or clickhouse-grpc-client if you prefer gRPC -->
            <artifactId>clickhouse-http-client</artifactId>
            <version>${com.clikhouse-http-client.version}</version>
        </dependency>

        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>${ru.clickhouse-jdbc.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpmime</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>


    </dependencies>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-clean-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-install-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>yupaopao-release</id>
            <url>
                http://maven.yupaopao.com/nexus/content/repositories/releases/
            </url>
        </repository>
        <snapshotRepository>
            <id>yupaopao-snapshots</id>
            <url>
                http://maven.yupaopao.com/nexus/content/repositories/snapshots/
            </url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>add-dependencies-for-IDEA</id>

            <activation>
                <property>
                    <name>idea.version</name>
                </property>
            </activation>

            <dependencies>
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-java</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-streaming-java_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-table-api-java-bridge_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-streaming-scala_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-table-common</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-table-planner_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>com.dianping.cat</groupId>
                    <artifactId>cat-client-flink</artifactId>
                    <version>1.0.1</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                    <version>1.2.3</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                    <version>1.2.3</version>
                    <scope>compile</scope>
                </dependency>

                <!-- Add the log4j -> sfl4j (-> logback) bridge into the classpath
                 Hadoop is logging to log4j! -->
                <dependency>
                    <groupId>org.slf4j</groupId>
                    <artifactId>log4j-over-slf4j</artifactId>
                    <version>1.7.15</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>net.logstash.logback</groupId>
                    <artifactId>logstash-logback-encoder</artifactId>
                    <version>4.11</version>
                    <scope>compile</scope>
                </dependency>


                <dependency>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                    <version>${fastjson.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-all</artifactId>
                    <version>${groovy-all.version}</version>
                    <scope>compile</scope>
                </dependency>
                <!--  common flink connector  -->
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-cep_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
            </dependencies>
        </profile>
    </profiles>


</project>
