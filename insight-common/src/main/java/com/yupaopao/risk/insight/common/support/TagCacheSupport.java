package com.yupaopao.risk.insight.common.support;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.common.property.PropertiesFactory;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.thread.InsightFlinkThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by Avalon on 2020/5/8 12:08
 */
@Slf4j
public class TagCacheSupport {

    private static JedisPool jedisPool;

    private volatile static Map<String/** code */, TagInfoBO> tagInfoCodeMap = new HashMap<>();
    private volatile static Map<Integer/** type */, List<TagInfoBO>> tagInfoTypeMap = new HashMap<>();

    private static final String REDIS_KEY_TAG_LIST = "risk_insight:tags";

    private static ScheduledExecutorService scheduler = null;

    static {
        startSchedulerCache();
    }

    private static void startSchedulerCache() {
        createPool();
        refreshLocalTagCache();
        startScheduler();
    }

    private static void startScheduler() {
        if (scheduler == null || scheduler.isShutdown()) {
            scheduler = Executors.newSingleThreadScheduledExecutor(new InsightFlinkThreadFactory("tag-cfg-cache"));
            scheduler.scheduleAtFixedRate(() -> refreshLocalTagCache(), 0, 2, TimeUnit.MINUTES);
        }
    }

    private static void createPool() {
        if (jedisPool != null && !jedisPool.isClosed()) {
            return;
        }
        synchronized (TagCacheSupport.class) {
            if (jedisPool == null || jedisPool.isClosed()) {
                RedisProperties redisProperties = (RedisProperties) PropertiesFactory.loadProperty(PropertyType.REDIS);
                jedisPool = RedisPoolCreator.createJedisPool(redisProperties);
            }
        }
    }

    private static void refreshLocalTagCache() {
        createPool();
        try (Jedis resource = jedisPool.getResource();) {
            log.info("start refresh tag cache...");
            Map<String/** code */, TagInfoBO> tmpCodeMap = new HashMap<>();
            Map<Integer/** type */, List<TagInfoBO>> tmpTypeMap = new HashMap<>();
            Map<String, String> tmpTagMap = resource.hgetAll(REDIS_KEY_TAG_LIST);
            if (tmpTagMap != null) {
                tmpTagMap.forEach((k, v) -> {
                    TagInfoBO tag = JSONObject.parseObject((String) JSONObject.parse(v), TagInfoBO.class);
                    try{
                        JSONObject.parseObject(tag.getDataContent());
                    }catch (Exception e) {
                        log.error("parse data content error", e);
                    }
                    tmpCodeMap.put(k, tag);
                    if (tag != null && tag.getType() != null) {
                        List<TagInfoBO> tagList = tmpTypeMap.get(tag.getType());
                        if (tagList == null) {
                            tagList = new ArrayList<>();
                            tmpTypeMap.put(tag.getType(), tagList);
                        }
                        tagList.add(tag);
                    }
                });
            }
            tagInfoCodeMap = tmpCodeMap;
            tagInfoTypeMap = tmpTypeMap;
        } catch (Exception e) {
            log.error("refresh local tag cache occurs error: " + TagCacheSupport.class.getClassLoader(), e);
        }

    }

    public static TagInfoBO getTagInfoByCode(String code) {
        checkAutoRefreshConfig();
        return tagInfoCodeMap.get(code);
    }

    public static List<TagInfoBO> getTagInfoByType(Integer type) {
        return tagInfoTypeMap.get(type);
    }

    public static List<TagInfoBO> getTagInfoByTypeCode(String typeCode) {
        return tagInfoCodeMap.values().stream()
                .filter(p -> StringUtils.isNotEmpty(p.getTypeCode()) && p.getTypeCode().equals(typeCode))
                .collect(Collectors.toList());
    }

    public static Map<String, String> getTagTypeColumnMap(String groupKey) {
        Map<String, String> columnMap = new HashMap<>();

        if (tagInfoCodeMap != null) {
            tagInfoCodeMap.forEach((k, v) -> {
                if (v != null && v.getDataContent() != null) {
                    String dataContent = v.getDataContent();
                    JSONObject dataContentJson = JSONObject.parseObject(dataContent);
                    if (null != dataContentJson && groupKey.equals(dataContentJson.getOrDefault("groupKey", ""))) {
                        columnMap.put(v.getCode(), v.getValueType());
                    }
                }
            });
        }
        return columnMap;
    }

    public static Map<String, TagInfoBO> getAllTagInfo() {
        return tagInfoCodeMap;
    }


    public static void cleanResource(ClassLoader closeClassLoader) {
        try {
            //flink  不同job classloadder 加载不同可能出现资源重复,通过系统类加载器加载的可以不close
            ClassLoader tagClassLoader = TagCacheSupport.class.getClassLoader();
            log.info("close tag cache resource... classloader: {} , from: {}", tagClassLoader, closeClassLoader);
            if (tagClassLoader != null && closeClassLoader == tagClassLoader) {
//                log.info("close tag cache resource... classloader: {}", tagClassLoader.getClass().getName());
                if (scheduler != null) {
                    scheduler.shutdownNow();
                }
                if (jedisPool != null) {
                    jedisPool.close();
                }
            }
        } catch (Exception e) {
            log.error("close tagCache resource error: ", e);
        }
    }

    private static void checkAutoRefreshConfig() {
        if (scheduler != null && !scheduler.isShutdown() && !jedisPool.isClosed()) {
            return;
        }
        synchronized (TagCacheSupport.class) {
            if (jedisPool.isClosed()) {
                log.info("start jedis pools");
                createPool();
            }
            if (scheduler == null || scheduler.isShutdown()) {
                log.info("start tag cache scheduler");
                startScheduler();
            }
        }
    }

}
