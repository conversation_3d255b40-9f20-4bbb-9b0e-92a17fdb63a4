package com.yupaopao.risk.insight.common.beans.hbase;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-15 20:43
 *
 ***/
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class HBaseCellValue implements Serializable {
    private String columnName;
    private Object columnValue;
    private Long timestamp;

    public String toString(){
        return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
    }
}
