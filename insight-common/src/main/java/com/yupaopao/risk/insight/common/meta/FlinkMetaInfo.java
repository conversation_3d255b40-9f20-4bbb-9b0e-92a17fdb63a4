package com.yupaopao.risk.insight.common.meta;

import lombok.Getter;
import lombok.Setter;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.types.DataType;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.apache.flink.table.api.DataTypes.*;
import static org.apache.flink.table.api.DataTypes.TIME;

@Getter
@Setter
public class FlinkMetaInfo implements Serializable {
    private String[] fieldNames;
    private DataType[] fieldTypes;

    public FlinkMetaInfo(String[] names, DataType[] types) {
        if (names.length != types.length) {
            throw new IllegalArgumentException("the fieldName and fieldType not match");
        }
        fieldNames = names;
        fieldTypes = types;
    }

    public DataType getFieldDataType(int i) {
        return fieldTypes[i];
    }

    public TableSchema toTableSchema() {
        TableSchema.Builder builder = TableSchema.builder();
        Field[] fieldList = new Field[fieldNames.length];
        for (int i = 0; i < fieldNames.length; i++) {
            fieldList[i]=DataTypes.FIELD(fieldNames[i], fieldTypes[i]);
//            builder.field(fieldNames[i], fieldTypes[i]);
        }
        builder.field("info",DataTypes.ROW(fieldList))
                .field("rowkey",STRING());
        return builder.build();
    }

    public static FlinkMetaInfo fromTableSchema(TableSchema tableSchema) {
        return new FlinkMetaInfo(tableSchema.getFieldNames(), tableSchema.getFieldDataTypes());
    }
}
