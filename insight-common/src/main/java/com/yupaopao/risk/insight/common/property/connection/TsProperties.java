package com.yupaopao.risk.insight.common.property.connection;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;


/***
 * 阿里云表格存储相关配置(tableStore)
 *
 */
@Getter
@Setter
public class TsProperties  extends BaseProperties {

    public final static String TEMP_RESULT_TABLE_NAME = "risk_analysis_result";


    //table store 相关连接参数
    private String endPoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String instanceName;
    //table store表名称
    //private String tableName;

    public BaseProperties fromMap(Map<String, String> propMap) {
        TsProperties ts = new TsProperties();
        return ts.withAccessKeyId(propMap.get("spring.tablestore.accessKeyId"))
                .withAccessKeySecret(propMap.get("spring.tablestore.accessKeySecret"))
                .withEndPoint(propMap.get("spring.tablestore.endPoint"))
                .withInstanceName(propMap.get("spring.tablestore.instanceName"));
    }

    public TsProperties withEndPoint(String endPoint) {
        this.endPoint = endPoint;
        return this;
    }

    public TsProperties withAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
        return this;
    }

    public TsProperties withAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
        return this;
    }

    public TsProperties withInstanceName(String instanceName) {
        this.instanceName = instanceName;
        return this;
    }

//    public TsProperties withTableName(String tableName) {
//        this.tableName = tableName;
//        return this;
//    }

    public boolean checkEmpty() {
//        if (StringUtils.isEmpty(tableName)) {
//            throw new IllegalArgumentException("table store tableName is empty");
//        }
        if (StringUtils.isEmpty(endPoint)) {
            throw new IllegalArgumentException("table store endPoint is empty");
        }
        if (StringUtils.isEmpty(accessKeyId)) {
            throw new IllegalArgumentException("table store accessKeyId is empty");
        }
        if (StringUtils.isEmpty(accessKeySecret)) {
            throw new IllegalArgumentException("table store accessKeySecret is empty");
        }
        if (StringUtils.isEmpty(instanceName)) {
            throw new IllegalArgumentException("table store instanceName is empty");
        }

        return true;
    }
}
