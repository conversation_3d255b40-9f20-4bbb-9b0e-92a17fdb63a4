package com.yupaopao.risk.insight.common.property.connection;

import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;


/**
 * Created by Avalon on 2020/1/9 11:07
 */
@Getter
@Setter
@Slf4j
public class ClickHouseProperties extends BaseProperties {

    private String host;
    private String username;
    private String password;
    private int batchSize = 5000;
    private int numWriters = 10;
    private int queueMaxCapacity = 10000;
    private int maxRetries = 3;
    private int timeout = 5000;
    private boolean canFlush = true;

    //connection related
    private int socketTimeout = 2 * 60 * 1000;
    private int connectionTimeout = 3000;
    private int dataTransferTimeout = 2 * 60 * 1000;
    private int syncIntervalInMillis = 60 * 60 * 1000;

    private int keepAliveTimeout = 30 * 1000;

    private String defaultHost;

    private int healthCheckInterval = 60 * 1000;

    private String otherOptions;// eg &failover=2;

    private Boolean insertDistributedSync; //true: sync false: async

    private String ipHosts;

    @Override
    public BaseProperties fromMap(Map<String, String> propMap) {
        ClickHouseProperties clickHouseProperties = new ClickHouseProperties();

        clickHouseProperties.setHost(propMap.get("host"));
        clickHouseProperties.setUsername(propMap.get("username"));
        clickHouseProperties.setPassword(propMap.get("password"));
        clickHouseProperties.setDefaultHost(propMap.get("defaultHost"));
        try {
            if (propMap.get("otherOptions") != null) {
                clickHouseProperties.setOtherOptions(propMap.get("otherOptions"));
            }
            if (propMap.get("canFlush") != null) {
                clickHouseProperties.setCanFlush(Boolean.valueOf(propMap.get("canFlush")));
            }
            if (propMap.get("batchSize") != null) {
                clickHouseProperties.setBatchSize(Integer.valueOf(propMap.get("batchSize")));
            }
            if (propMap.get("numWriters") != null) {
                clickHouseProperties.setNumWriters(Integer.valueOf(propMap.get("numWriters")));
            }
            if (propMap.get("queueMaxCapacity") != null) {
                clickHouseProperties.setQueueMaxCapacity(Integer.valueOf(propMap.get("queueMaxCapacity")));
            }
            if (propMap.get("maxRetries") != null) {
                clickHouseProperties.setMaxRetries(Integer.valueOf(propMap.get("maxRetries")));
            }
            if (propMap.get("timeout") != null) {
                clickHouseProperties.setTimeout(Integer.valueOf(propMap.get("timeout")));
            }
            if (propMap.get("socketTimeout") != null) {
                clickHouseProperties.setSocketTimeout(Integer.valueOf(propMap.get("socketTimeout")));
            }
            if (propMap.get("connectionTimeout") != null) {
                clickHouseProperties.setConnectionTimeout(Integer.valueOf(propMap.get("connectionTimeout")));
            }
            if (propMap.get("dataTransferTimeout") != null) {
                clickHouseProperties.setDataTransferTimeout(Integer.valueOf(propMap.get("dataTransferTimeout")));
            }
            if (propMap.get("flushIntervalInMillis") != null) {
                clickHouseProperties.setFlushIntervalInMillis(Integer.valueOf(propMap.get("flushIntervalInMillis")));
            }
            if (propMap.get("syncIntervalInMillis") != null) {
                clickHouseProperties.setSyncIntervalInMillis(Integer.valueOf(propMap.get("syncIntervalInMillis")));
            }
            if (propMap.get("healthCheckInterval") != null) {
                clickHouseProperties.setHealthCheckInterval(Integer.valueOf(propMap.get("healthCheckInterval")));
            }

            if (propMap.get("insertDistributedSync") != null && Arrays.asList("true","false").contains(propMap.get("insertDistributedSync"))) {
                clickHouseProperties.setInsertDistributedSync(Boolean.valueOf(propMap.get("insertDistributedSync")));
            }
            if (propMap.get("ipHosts") != null) {
                clickHouseProperties.setIpHosts(propMap.get("ipHosts"));
            }

        } catch (Exception e) {
            log.warn("init ckProperties error", e);
        }
        return clickHouseProperties;
    }

}
