package com.yupaopao.risk.insight.common.support;

import com.alibaba.fastjson.JSONObject;
import com.github.wnameless.json.flattener.FlattenMode;
import com.github.wnameless.json.flattener.JsonFlattener;
import com.github.wnameless.json.flattener.KeyTransformer;
import com.github.wnameless.json.unflattener.JsonUnflattener;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JsonFlatterUtils {
    /***
     * json转为flatter形式的map
     * @param json
     * @return
     */
    public static Map<String, Object> toMap(String json) {
        return JsonFlattener.flattenAsMap(json);
    }

    public static Map<String, Object> toMap(String json, FlattenMode flattenMode) {

        return new JsonFlattener(json).withFlattenMode(flattenMode).withSeparator('_').flattenAsMap();
    }

    public static String toString(String json, FlattenMode flattenMode) {

        return new JsonFlattener(json).withFlattenMode(flattenMode).withSeparator('_').flatten();
    }

    public static Map<String, Object> toMap(String json, FlattenMode flattenMode, KeyTransformer kt) {

        return new JsonFlattener(json).withFlattenMode(flattenMode).withKeyTransformer(kt).withSeparator('_').flattenAsMap();
    }

    public static Map<String, Object> toMap(String json, FlattenMode flattenMode, char replace) {

        return new JsonFlattener(json).withFlattenMode(flattenMode).withSeparator(replace).flattenAsMap();
    }

    public static String toJson(String jsonData, FlattenMode flattenMode) {
        return new JsonUnflattener(jsonData).withFlattenMode(flattenMode).withSeparator('_').unflatten();
    }

    public static Map<String, Object> toCkMap(String value) {
        Map<String, Object> objectMap = JsonFlatterUtils.toMap(value, FlattenMode.KEEP_ARRAYS);

        Map<String, Object> flattenMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
            Object mapValue = entry.getValue();
            if (null == mapValue) {
                continue;
            }
            //ck类型处理，boolean转为int , list 转为string
            if (mapValue instanceof List) {
                if (((List<?>) mapValue).isEmpty()) {
                    entry.setValue(null);
                    continue;
                }
                entry.setValue(JSONObject.toJSONString(mapValue));
            } else if (mapValue instanceof Boolean) {
                Boolean bool = (Boolean) entry.getValue();
                entry.setValue(bool ? 1 : 0);
            }
            //对flatten后的json含有_的特殊类型字段处理
            if (entry.getKey().contains("[\\\"") && entry.getKey().contains("\\\"]")) {
                String newKey = entry.getKey().replace("[\\\"", "_").replace("\\\"]", "");
                flattenMap.put(newKey, entry.getValue());
            } else {
                flattenMap.put(entry.getKey(), entry.getValue());
            }
        }
        return flattenMap;
    }
}
