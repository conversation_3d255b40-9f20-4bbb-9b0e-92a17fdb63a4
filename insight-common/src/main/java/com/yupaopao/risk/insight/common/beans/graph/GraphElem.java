package com.yupaopao.risk.insight.common.beans.graph;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-30 14:37
 *
 ***/

@Getter
@Setter
public abstract class GraphElem implements Serializable {
    private String id;
    private String label;
    private Map<String, Object> properties;

    public abstract String getAddDsl();

    public abstract String getUpdateDsl();

    public abstract String getBatchExistDsl();

    public abstract Map<String,Object> getParameters();

    public String getPropertyDsl() {
       return getPropertyDsl(properties);
    }

    public String getPropertyDsl(Map<String,Object> props){
        String dsl = "";
        if (properties == null || properties.isEmpty()) {
            return dsl;
        }
        for (String key : properties.keySet()) {
            dsl += ".property('" + key + "'," + key + ")";
        }
        return dsl;
    }



    public String toString(){
        return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
    }
}
