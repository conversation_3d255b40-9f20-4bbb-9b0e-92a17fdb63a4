package com.yupaopao.risk.insight.common.property;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Getter
@Setter
public class AccumulateProperties implements Serializable {
    private static final long serialVersionUID = -8765855170619816048L;
    private static Logger LOG = LoggerFactory.getLogger(AccumulateProperties.class);

    private String whiteIp;

    private static AccumulateProperties properties;

    private AccumulateProperties() {
        Config config = ConfigService.getAppConfig();

        String whiteIpList = "gateway.ip_white_list";
        this.whiteIp = config.getProperty(whiteIpList, "");

        config.addChangeListener(configChangeEvent -> {
            if (configChangeEvent.isChanged(whiteIpList)) {
                LOG.info("更新网关白名单 list：{}", whiteIpList);
                getInstance().setWhiteIp(config.getProperty(whiteIpList, ""));
            }
        });
    }

    public static AccumulateProperties getInstance() {
        if (properties == null) {
            synchronized (AccumulateProperties.class) {
                if (properties == null) {
                    properties = new AccumulateProperties();
                }
            }
        }
        return properties;
    }
}
