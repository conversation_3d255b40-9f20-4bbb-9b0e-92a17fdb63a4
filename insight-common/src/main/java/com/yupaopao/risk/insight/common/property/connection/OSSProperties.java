package com.yupaopao.risk.insight.common.property.connection;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/****
 * zengxiangcai
 * 2021/3/10 7:53 下午
 ***/

@Getter
@Setter
public class OSSProperties  extends BaseProperties {
    private String accessKeyId;
    private String accessKeySecret;
    private String endpoint;
    private String bucket;
    public BaseProperties fromMap(Map<String, String> propMap) {
        OSSProperties ossProperties = new OSSProperties();
        ossProperties.setAccessKeyId(propMap.get("accessKeyId"));
        ossProperties.setAccessKeySecret(propMap.get("accessKeySecret"));
        ossProperties.setEndpoint(propMap.get("endpoint"));
        ossProperties.setBucket(propMap.get("bucketName"));
        return ossProperties;
    }
}
