package com.yupaopao.risk.insight.common.property.enums;

import lombok.Getter;

/***
 * namespace 字段对应apollo namespace
 */
@Getter
public enum PropertyType {
    TABLE_STORE("middleware.ots.risk", ""),
    KAFKA("middleware.kafka", ""),  //核心业务集群
    KAFKA_BIGDATA("middleware.kafka-bigdata", ""), //大数据集群
    KAFKA_RISK("middleware.kafka.risk", ""),  //风控专用集群
    KAFKA_RISK_ALI("middleware.kafka.risk", "ali-risk"),  //风控集群上云
    KAFKA_TOOLS("middleware.kafka.tools-operation", ""),  //工具集群
    DB("middleware.db.risk.properties", ""),
    DB_INSIGHT("middleware.db.insight-parent", ""),
    REDIS("middleware.redis.risk-engine", ""),
    HBASE("middleware.hbase.risk", ""),
    ES("elasticsearch", ""),
    ES_FLINK_AUDIT("flink.es.audit", ""),
    CLICK_HOUSE("clickhouse", ""),
    MASERATI_KAFKA("middleware.kafka-maserati", "ali-maserati"),
    GDB("middleware.gdb.risk", ""),
    OSS("middleware.riskoss", ""),
    SLS("sls", ""),
    KAFKA_SLS("middleware.kafka-sls", "ali-middleware"),
    CLICK_HOUSE_NEW("clickhouse-new", ""),
    KAFKA_HUG("middleware.kafka.hug", ""),
    DB_RISK_HUG("middleware.db.hug-risk", ""),
    REDIS_RISK_HUG("middleware.redis.hug-risk-engine", ""),

    CLICK_HOUSE_HUG("hug-risk-ck", ""),

    DB_METABASE("risk-metabase", ""),

    CLICKHOUSE_RISK("middleware.risk_ck",""),  //最新合并后的ck

    ;

    private PropertyType(String name, String aliNamespace){
        this.namespace = name;
        this.aliNamespace = aliNamespace;
    }
    private String namespace; // 其他apollo配置
    private String aliNamespace; // 使用阿里云kafka时使用
}
