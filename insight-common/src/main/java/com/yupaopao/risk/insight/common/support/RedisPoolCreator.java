package com.yupaopao.risk.insight.common.support;

import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-24 16:47
 *
 ***/
public class RedisPoolCreator {

    public static JedisPool createJedisPool(RedisProperties redisProperties){
        JedisPoolConfig poolConfig = new JedisPoolConfig ();
        poolConfig.setMaxIdle(redisProperties.getMaxIdle());
        poolConfig.setMaxTotal(redisProperties.getMaxActive());
        poolConfig.setMinIdle(redisProperties.getMinIdle());

        return  new JedisPool(poolConfig,
                redisProperties.getHost(),
                redisProperties.getPort(),
                2000,   //connect timeout ms
                redisProperties.getPassword(),
                redisProperties.getDatabase());
    }


}
