package com.yupaopao.risk.insight.common.algorithm;

import com.yupaopao.risk.insight.common.algorithm.episode.AlgoTKE;
import com.yupaopao.risk.insight.common.algorithm.episode.Episode;
import com.yupaopao.risk.insight.common.algorithm.prefixspan.AlgoPrefixSpan;
import com.yupaopao.risk.insight.common.algorithm.prefixspan.SequentialPattern;
import com.yupaopao.risk.insight.common.algorithm.prefixspan.SequentialPatterns;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-09-15 16:37
 * spmf算法执行入口
 ***/
public class AlgorithmRunner {

    private static final Logger LOG = Logger.getLogger(AlgorithmRunner.class.getName());

    public static final String ITEMSET_SEPARATOR = ";";

    public static final String ITEM_SEPARATOR = ",";

    /***
     * 序列中的频繁模式
     * @param originalSequenceList 字符串序列格式为itemset以;分开，itemset里面的item以,分开
     * @param minSupportPercent 最小支持度占比
     * @param maxPatternLength 最大模式长度
     * @return
     * @throws Exception
     */
    public static List<Map<String, Object>> runPrefixSpan(List<String> originalSequenceList,
                                                          double minSupportPercent,
                                                          int maxPatternLength) throws Exception {

        /***
         * 符合算法格式的序列,  序列中的项转为数字，分隔符;用-1标识分隔符逗号(,)用空格标识, 结尾是-2
         * 比如 login,im-message;login,login对应于 0 1 -1 0 0 -2
         *
         */
        List<String> seqList = new ArrayList<>();

        Map<String, Integer> eventCodeMap = new HashMap<>();

        Map<Integer, String> eventCodeNameMap = new HashMap<>();

        //对每一个序列
        for (String originalSequence : originalSequenceList) {

            List<String> allItemSets = new ArrayList<>();

            //序列项集
            String[] itemSets = originalSequence.split(ITEMSET_SEPARATOR);
            //for itemSets
            for (String itemSet : itemSets) {

                List<Integer> intCurrentItemSets = new ArrayList<>(); //itemSet转为int
                //项
                String[] items = itemSet.split(ITEM_SEPARATOR);
                //for each itemSet
                for (String item : items) {
                    if (!eventCodeMap.containsKey(item)) {
                        int currentId = eventCodeMap.size() + 1;//start from 1
                        eventCodeMap.put(item, currentId);
                        eventCodeNameMap.put(currentId, item);
                    }
                    intCurrentItemSets.add(eventCodeMap.get(item));
                }
                allItemSets.add(StringUtils.join(intCurrentItemSets, " "));
            }  //end all itemSets


            String finalSeq = StringUtils.join(allItemSets, " -1 ") + " -2";
            seqList.add(finalSeq);
        }

        Integer totalCount = seqList.size();

        Integer minSupport = (int) (totalCount * minSupportPercent);

//        LOG.info("process with minsupport: , seqSize: " + minSupport + "," + totalCount);

        if (minSupport <= 1) {
            minSupport = 2;
        }

        AlgoPrefixSpan algo = new AlgoPrefixSpan();
        algo.setMaximumPatternLength(maxPatternLength);

        SequentialPatterns patterns = algo.runAlgorithm(seqList, minSupport);

        LOG.info("process with minsupport: , seqSize: , patternSize:  " + minSupport + "," + totalCount + "," + patterns.levels.size());

        List<Map<String, Object>> resultList = new ArrayList<>();

        //patterns中levels的下标标识模式中items的数量，倒序返回,后续可以考虑对有大于1的序列的不返回等于1的
        Integer totalLevel = patterns.levels.size();
        for (int i = totalLevel - 1; i >= 0; i--) {
            List<SequentialPattern> level = patterns.levels.get(i);
            Integer currentLevelSeqCount = 0;
            for (SequentialPattern pattern : level) {
                List<String> tempResultList = pattern.getItemsets().stream()
                        .map(elem -> eventCodeNameMap.get(elem.getItems().get(0))) //简化处理，考虑每个itemset只有一个item
                        .collect(Collectors.toList());

                Map<String, Object> row = new HashMap<>();
                row.put("itemSet", StringUtils.join(tempResultList, " ").trim());
                row.put("supportCount", pattern.getAbsoluteSupport());
                row.put("itemCount", tempResultList.size());
//                if (totalLevel > 2 && resultList.size() > 0 && tempResultList.size() == 1) {
//                    //levels的下标标识的是某一类的itemCount，如果只有itemCount=1的则保留，否则不保留
//                    continue;
//                }
                //防止范围的模式过多，每一个不超过5k
                if (currentLevelSeqCount++ > 5000) {
                    break;
                }
                resultList.add(row);
            }
        }
        return resultList;
    }

    /***
     * 序列种挖掘topk情景模式
     *
     * @param sequenceList ,比如第一天是im-message,第二天使login->im-message, 对应于两个原素，(im-message) (login im-message)
     * @param window
     * @param k
     */
    public static List<Map<String, Object>> runTKE(List<String> sequenceList, int window, int k) throws Exception {
        Map<String, Integer> eventCodeMap = new HashMap<>();
        Map<Integer, String> eventCodeNameMap = new HashMap<>();

        List<String> intInputList = new ArrayList<>(sequenceList.size());
        for (String seq : sequenceList) {
            String[] items = seq.split(" ");
            List<Integer> tempItemList = new ArrayList<>(items.length);
            for (String item : items) {
                if (!eventCodeMap.containsKey(item)) {
                    int currentId = eventCodeMap.size() + 1;
                    eventCodeMap.put(item, currentId);
                    eventCodeNameMap.put(currentId, item);
                }
                tempItemList.add(eventCodeMap.get(item));
            }
            intInputList.add(StringUtils.join(tempItemList, " "));
        }
        AlgoTKE algo = new AlgoTKE();
        algo.setUseDynamicSearch(true);
        PriorityQueue<Episode> kEpisodes = algo.runAlgorithm(intInputList, k, window);

        if (kEpisodes.size() <= 0) {
            return new ArrayList<>();
        }
        //按顺序返回：

        Object[] patterns = kEpisodes.toArray();
        Arrays.sort(patterns);

        List<Map<String, Object>> resultList = new ArrayList<>();
        // for each pattern
        for (Object patternObj : patterns) {
            Episode pattern = (Episode) patternObj;
//            System.err.println(pattern.toString());

            int episodeLength = pattern.getEvents().size();
            List<String> episodeList = new ArrayList<>(episodeLength);
            for (int i = 0; i < episodeLength; i++) {
                //forItemSet
                int[] itemSet = pattern.getEvents().get(i);
                List<String> itemSetList = new ArrayList<>(itemSet.length);
                for (int item : itemSet) {
                    itemSetList.add(eventCodeNameMap.get(item));
                }
                if (itemSetList.size() > 1) {
                    episodeList.add("<" + StringUtils.join(itemSetList, " ") + ">");
                } else {
                    episodeList.add(itemSetList.get(0));
                }
            }
            Map<String, Object> row = new HashMap<>();
            row.put("itemSet", StringUtils.join(episodeList, " "));
            row.put("supportCount", pattern.getSupport());
            row.put("itemCount", episodeLength);
            resultList.add(row);
        }

        return resultList;

    }


}
