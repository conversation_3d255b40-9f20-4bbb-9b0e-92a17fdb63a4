package com.yupaopao.risk.insight.common.support;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.beans.graph.EdgeInfo;
import com.yupaopao.risk.insight.common.beans.graph.GdbDSLParam;
import com.yupaopao.risk.insight.common.beans.graph.GraphElem;
import com.yupaopao.risk.insight.common.property.connection.GDBProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tinkerpop.gremlin.driver.*;
import org.apache.tinkerpop.gremlin.driver.ser.Serializers;
import org.apache.tinkerpop.gremlin.structure.Property;
import org.apache.tinkerpop.gremlin.structure.util.detached.DetachedElement;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-27 16:48
 *
 ***/

@Slf4j
public class GDBUtil {


    public static AtomicLong deletedCount = new AtomicLong(0);

    public static boolean addGraphElements(Client client, List<? extends GraphElem> graphElems) {
        if (CollectionUtils.isEmpty(graphElems)) {
            return false;
        }
        //保持唯一性，先get ，存在然后判断是否要改属性，不存在insert
        Map<String, GraphElem> newElementMap = graphElems.stream().collect(Collectors.toMap(GraphElem::getId,
                Function.identity()));
        String batchExistDsl = graphElems.get(0).getBatchExistDsl();

        List<DetachedElement> existElementList = new ArrayList<>();
        try {
            //检查是否存在
            Map<String, Object> parameter = new HashMap<>();
            parameter.put("ids", newElementMap.keySet());
            List<Result> results = sendRequest(client,
                    new GdbDSLParam().withDsl(batchExistDsl).withParameter(parameter));
            results.forEach(elem -> {
                if (elem.getObject() instanceof DetachedElement) {
                    DetachedElement resultElem = (DetachedElement) elem.getObject();
                    existElementList.add(resultElem);
                } else {
                    log.warn("the result is not detachedVertex: {}", elem.getObject().getClass().getName());
                }
            });
        } catch (Exception e) {
            log.warn("query exists vertex error, ids=" + newElementMap.keySet(),e);
        }
        List<GraphElem> updateList = new ArrayList<>();
        for (DetachedElement existElem : existElementList) {
            String id = existElem.id().toString();
            GraphElem newElem = newElementMap.get(id);
            newElementMap.remove(id);
            if (newElem.getProperties() == null || newElem.getProperties().isEmpty()) {
                continue;
            }
            processEdgeTimeProperties(newElem, existElem);

            for (Map.Entry<String, Object> entry : newElem.getProperties().entrySet()) {
                if (entry.getValue() == null) {
                    continue;
                }
                Property p = existElem.property(entry.getKey());
                Object existPropValue = p.isPresent() ? existElem.property(entry.getKey()).value() : null;

                if (!entry.getValue().equals(existPropValue)) {
                    //不同需要更新
                    updateList.add(newElem);
                    break;
                }
            }
        }


        for (Map.Entry<String, GraphElem> entry : newElementMap.entrySet()) {
            sendRequestIgnoreExist(client,
                    new GdbDSLParam().withDsl(entry.getValue().getAddDsl()).withParameter(entry.getValue().getParameters()));
        }

        for (GraphElem updateE : updateList) {
            sendRequestIgnoreExist(client,
                    new GdbDSLParam().withDsl(updateE.getUpdateDsl()).withParameter(updateE.getParameters()));
        }
        return true;
    }


    public static boolean insertGraphElements(Client client, List<? extends GraphElem> graphElems) {
        if(CollectionUtils.isEmpty(graphElems)){
            return true;
        }
        for (GraphElem insertE : graphElems) {
            sendRequestIgnoreExist(client,
                    new GdbDSLParam().withDsl(insertE.getAddDsl()).withParameter(insertE.getParameters()));
        }
        return true;
    }

    private static void processEdgeTimeProperties(GraphElem newElem, DetachedElement existElem) {

        removeNotUpdateTimeProp(newElem, existElem, EdgeInfo.EDGE_PROP_UPDATE_TIME);
        removeNotUpdateTimeProp(newElem, existElem, EdgeInfo.EDGE_PROP_CREATE_TIME);
    }

    private static void removeNotUpdateTimeProp(GraphElem newElem, DetachedElement existElem, String key) {
        Object newValue = newElem.getProperties().get(key);
        Property p = existElem.property(key);
        Object existValue = p.isPresent() ? p.value() : null;

        if (newValue == null || existValue == null) {
            return;
        }
        // update : old = null || old < new
        if(EdgeInfo.EDGE_PROP_UPDATE_TIME.equals(key) && Long.parseLong(newValue.toString()) <= Long.parseLong(existValue.toString())){
            newElem.getProperties().remove(key);
        }
        //create: old = null || old>new
        if(EdgeInfo.EDGE_PROP_CREATE_TIME.equals(key) && Long.parseLong(newValue.toString()) >= Long.parseLong(existValue.toString())){
            newElem.getProperties().remove(key);
        }
    }


    private static List<Result> sendRequestIgnoreExist(Client client, GdbDSLParam gdbDsl) {
        try {
            return sendRequest(client, gdbDsl);
        } catch (Exception e) {
            if (e != null && e.getMessage().contains("GraphDB id exists")) {
                log.debug("execute gdb script id exists: dsl= " + gdbDsl);
            } else {
                log.warn("execute gdb script error: dsl= " + gdbDsl, e);
            }
            return new ArrayList<>(0);
        }
    }

    /***
     *  send with java jdk
     * @param gdbDsl
     */
    public static List<Result> sendRequest(Client client, GdbDSLParam gdbDsl) throws Exception {

        if (StringUtils.isEmpty(gdbDsl.getDsl())) {
            throw new IllegalArgumentException("gremlinDsl cannot be empty");
        }
        //bindings
        org.apache.tinkerpop.gremlin.driver.RequestOptions.Builder options = RequestOptions.build();
        if (gdbDsl.getParameter() != null && !gdbDsl.getParameter().isEmpty()) {
            gdbDsl.getParameter().forEach(options::addParameter);
        }
        //timeout
        options.timeout(gdbDsl.getTimeout() != null && gdbDsl.getTimeout() > 0 ? gdbDsl.getTimeout() : 4000);
        ResultSet res = client.submit(gdbDsl.getDsl(), options.create());
        List<Result> resultList = res.all().join();
        return resultList;

    }


    public static Client initClient(Cluster cluster) {
        return cluster.connect().init();
    }


    public static Cluster getCluster(GDBProperties gdbProperties) {
        Cluster.Builder builder = createBuilder(gdbProperties);
        return builder.create();
    }

    private static Cluster.Builder createBuilder(GDBProperties gdbProperties) {
        Cluster.Builder builder =
                Cluster.build()
                        .addContactPoint(gdbProperties.getHost())
                        .port(Integer.parseInt(gdbProperties.getPort()))
                        .path("/gremlin")
                .workerPoolSize(8);
        builder.credentials(gdbProperties.getUserName(), gdbProperties.getPassword());
        builder.maxContentLength(1024*1024*10);

        try {
            MessageSerializer messageSerializer = Serializers.GRAPHSON_V3D0.simpleInstance();
            Map<String, Object> cfg = new HashMap<>();
            cfg.put("serializeResultToString", true);
            messageSerializer.configure(cfg, null);
            builder.serializer(messageSerializer);
            return builder;
        } catch (Exception e) {
            throw new IllegalStateException("Could not establish serializer - " + e.getMessage());
        }
    }



    /***
     *  httpClient request
     * @param gdbDsl
     */
    public static String sendRequest(OkHttpClient client, GdbDSLParam gdbDsl) throws Exception {
        try {
            if (StringUtils.isEmpty(gdbDsl.getDsl())) {
                throw new IllegalArgumentException("gremlinDsl cannot be empty");
            }

            String credential = Credentials.basic(gdbProperties.getUserName(), gdbProperties.getPassword());
            Request request = new Request.Builder()
                    .url("http://" + gdbProperties.getHost() + ":" + gdbProperties.getPort() + "/gremlin")
                    .header("Authorization", credential)
                    .post(RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(gdbDsl)))
                    .build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseStr = response.body().string();
                return responseStr;
            } else {
                throw new IOException("http request for gdb failed");
            }

        } catch (Exception e) {
            log.error("execute gdb script error: dsl= " + gdbDsl, e);
            throw e;
        }
    }

    private static GDBProperties gdbProperties = GDBProperties.getProperties(PropertyType.GDB);


}
