package com.yupaopao.risk.insight.common.constants;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-05 19:47
 *
 ***/
public class TsConstants {

    //table
    public final static String TABLE_RISK_ANALYSIS_RESULT = "risk_analysis_result";
    public final static String TABLE_RISK_HIT_LOG = "risk_hit_log";
    public final static String TABLE_RISK_LOGON_DETAIL = "risk_logon_detail";
    public final static String TABLE_RISK_USER_DEFINE_DATA = "risk_user_define_data";
    public final static String TABLE_RISK_USER = "risk_user";

    public final static String TABLE_RISK_CEP_RESULT = "risk_cep_result";

    public final static String TABLE_TYPE_SYSTEM="SYSTEM";
    public final static String TABLE_TYPE_TEMP="TEMP";

    //risk_analysis_result pk
    public final static String RISK_ANALYSIS_RESULT_PK_JOB_ID = "pkJobId";
    public final static String RISK_ANALYSIS_RESULT_PK_UUID = "pkUuid";

    //结果数据写入时间
    public final static String RISK_ANALYSIS_RESULT_INSERT_TIME = "resultInsertTime";



    //risk_hit_log pk
    public static final String RISK_HIT_LOG_DATE_PARTITION = "pkDatePartition";
    public static final String RISK_HIT_LOG_TRACE_ID = "traceId";


    //risk_user_define_data pk
    public static final String RISK_USER_DEFINE_DATA_TABLE_ID = "pkTableId";
    public static final String RISK_USER_DEFINE_DATA_UUID = "pkUuid";

    //risk_hit_log每天的总分桶数
    public static final int RISK_HIT_LOG_TOTAL_BUCKET_PER_DAY = 1024;


    //大表默认的时间区段查询字段：
    public static final String TABLE_QUERY_PERIOD_COLUMN = "createdAt";

    public static final String TABLE_RISK_PORTRAIT_ETL = "risk_portrait_etl";




    //ts表主键存储在apollo,没找到存储相关常用表
    public static final Map<String,String> tablePkMap = new ConcurrentHashMap<>();
    static{
        tablePkMap.put(TABLE_RISK_HIT_LOG,"pkDatePartition#STRING,traceId#STRING");
        tablePkMap.put(TABLE_RISK_ANALYSIS_RESULT,"pkJobId#STRING,pkUuid#LONG");
        tablePkMap.put(TABLE_RISK_USER_DEFINE_DATA,"pkTableId#LONG,pkUuid#STRING");
    }





    public static boolean isAnalysisTable(String tableName){
       return  TABLE_RISK_ANALYSIS_RESULT.equalsIgnoreCase(tableName);
    }


}
