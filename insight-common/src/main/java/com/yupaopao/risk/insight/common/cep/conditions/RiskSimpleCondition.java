package com.yupaopao.risk.insight.common.cep.conditions;

import com.yupaopao.risk.insight.common.cep.beans.RiskEvent;
import com.yupaopao.risk.insight.common.groovy.GroovyCommonExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;

import java.util.Map;


/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-14 11:39
 *
 ***/

@Slf4j
public class RiskSimpleCondition extends SimpleCondition<RiskEvent> {

    private String condition;

    public RiskSimpleCondition(String condition) {
        this.condition = condition;
    }

    @Override
    public boolean filter(RiskEvent value) throws Exception {
        try {
            if (StringUtils.isEmpty(condition)) {
                return false;
            }
            Map<String, Object> data = value.getData();
            Object result = GroovyCommonExecutor.getResult(condition,condition,data);
            return (boolean) result;
        } catch (groovy.lang.MissingPropertyException e1) {
            return false;
        } catch (Exception e) {
            log.warn("groovy execute error, param= " + value.getData() + ", condition: " + condition, e);
            throw e;
        } finally {

        }
    }

}
