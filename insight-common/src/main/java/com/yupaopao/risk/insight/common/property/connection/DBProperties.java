package com.yupaopao.risk.insight.common.property.connection;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Getter
@Setter
public class DBProperties extends BaseProperties {
    private String url;
    private String usename;
    private String password;
    private String driver;

    private String validationQuery;

    @Override
    public BaseProperties fromMap(Map<String, String> propMap) {
        DBProperties db = new DBProperties();
        db.setUrl(propMap.get("spring.datasource.url"));
        String userName = propMap.get("spring.datasource.userName");
        // apollo中配置名称中db.risk 和db_insight有大小区分
        if(StringUtils.isEmpty(userName)){
            userName = propMap.get("spring.datasource.username");
        }
        db.setUsename(userName);
        db.setPassword(propMap.get("spring.datasource.password"));
        db.setDriver(propMap.get("spring.datasource.driverClassName"));
        db.setValidationQuery(propMap.get("spring.datasource.validationQuery"));
        return db;
    }
}
