package com.yupaopao.risk.insight.common.meta;

import com.yupaopao.risk.insight.common.enums.InsightFlinkDataType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.table.types.DataType;
import org.apache.flink.table.types.utils.TypeConversions;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * job 定制需要的参数
 * job名称，sql信息
 * job 原表信息，job输出表信息【都暂时考虑基于tablestore】
 * 链接原可以通过apollo配置
 */
@Getter
@Setter
public class JobParams implements Serializable {

    //本次启动job名称
    private String jobName = "flink-sql";

    private String jobType;
    //本次执行的sql
    private String sql;

    private String owner; //job发起人

    private String randomJobId; //因sql提交的jobId和执行的jobId不一样，因此在提交前生成一个randomUuid当做和jobId的映射

    //每个表查询的时间范围(没有配置的全表查)
    private List<TableQueryDatePeriod> datePeriodList;

    private int bucketPerSplit = 8;

    //每个flink表列信息,方便转换ts数据和flink转换
    private List<FlinkTableInfo> tableList;


    private String remarks;

    private Integer workspaceTaskId;//工作台管理的sql Id

    private String resultTableName;

    // 源表名
    private String sourceTableName;

    private Map<String, Map<String, String>> propertiesMap = new HashMap<>();

    private Map<String, String> typeMap = new HashMap<>();

    public Map<String, Object> toMap() {
        Map<String, Object> params = new HashMap<>();
        params.put("jobName", jobName);
        params.put("jobType", jobType);
        params.put("sql", sql);
        params.put("tableList", tableList);
        params.put("owner", owner);
        params.put("randomJobId", randomJobId);
        params.put("datePeriodList", datePeriodList);
        params.put("bucketPerSplit",bucketPerSplit);
        params.put("resultTableName", resultTableName);
        params.put("sourceTableName", sourceTableName);
        params.put("propertiesMap", propertiesMap);
        params.put("typeMap", typeMap);
        return params;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TableQueryDatePeriod implements Serializable{
        private String tableName; //sql中限制时间的表名称
        private String beginDate; //sql中表限制的开始时间yyyyMMdd (include)
        private String endDate; //sql中表限制的结束时间yyyyMMdd (exclude)

        @Override
        public String toString(){
            return ReflectionToStringBuilder.toString(this, ToStringStyle.SIMPLE_STYLE);
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FlinkTableInfo implements Serializable{
        private String tableName;
        private String strColumns; //列信息，格式为：列1#类型1,列2#类型2,列3#类型3 ....
        private String tableId;//用户自定义表有id限制
        private String tableType; //表类型

        public RowTypeInfo toRowType() {
            String[] tableColumns = strColumns.split(",");
            String[] fieldNames = new String[tableColumns.length];
            DataType[] types = new DataType[tableColumns.length];
            for (int i = 0; i < tableColumns.length; i++) {
                String nameType[] = tableColumns[i].split("#");
                fieldNames[i] =nameType[0];
                DataType type = InsightFlinkDataType.getDataTypeFromString(nameType[1].toUpperCase());
                if (type == null) {
                    throw new IllegalArgumentException("not support type: " + nameType[1] + ", current supported " +
                            "types: " + InsightFlinkDataType.getSupportSqlDataType());
                }
                types[i] = type;
            }
            RowTypeInfo rowType = new RowTypeInfo(TypeConversions.fromDataTypeToLegacyInfo(types), fieldNames);
            return rowType;
        }


        @Override
        public String toString(){
            return ReflectionToStringBuilder.toString(this, ToStringStyle.JSON_STYLE);
        }
     }


}
