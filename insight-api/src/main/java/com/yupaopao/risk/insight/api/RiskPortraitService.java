package com.yupaopao.risk.insight.api;

import com.yupaopao.risk.insight.bean.CustomTag;
import com.yupaopao.risk.insight.bean.PortraitResult;
import com.yupaopao.risk.insight.bean.PortraitTag;
import com.yupaopao.risk.insight.bean.TagCleanBean;

import java.util.List;

public interface RiskPortraitService {


    /**
     * 批量发送打标标签
     * @param customList
     * @return
     */
    PortraitResult batchMarkTags(List<CustomTag> customList);

    /**
     * 发送自定义打标标签
     * @param customTag
     * @return
     */
    PortraitResult sendCustomTag(CustomTag customTag);

    /**
     * 打标
     * @param portraitTag
     * @return
     */
    PortraitResult mark(PortraitTag portraitTag);


    /**
     * 取消打标
     * @param portraitTag
     * @return
     */
    PortraitResult unMark(PortraitTag portraitTag);

    /**
     * 按入参清洗标签数据
     * @param tagCleanBean bean
     * @return 返回结果
     */
    PortraitResult cleanPortrait(TagCleanBean tagCleanBean);


    PortraitResult writeTagToHbase(List<CustomTag> customList);




}
