package com.yupaopao.risk.insight.web.config;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;

/**
 * Created by Avalon on 2019/12/14 14:58
 */
@Configuration
@EnableApolloConfig("resource")
public class ResourceConfig {

    @Value("${cdn.host}")
    private String host;

    @Value("${rs.key}")
    private String rs;

    private static final String RS_KEY = "rs.key";

    private List<String> jsList;
    private List<String> styleList;

    @PostConstruct
    public void init() {
        refreshResource(rs);
    }

    @ApolloConfigChangeListener("resource")
    public void onChanged(ConfigChangeEvent event) {
        if (event.isChanged(RS_KEY)) {
            refreshResource(event.getChange(RS_KEY).getNewValue());
        }
    }

    public List<String> getJsScript() {
        return jsList;
    }

    public List<String> getStyleScript() {
        return styleList;
    }

    private void refreshResource(String rs) {
        List<String> elements = JSONObject.parseArray(rs, String.class);
        if (CollectionUtils.isEmpty(elements)) {
            return;
        }
        List<String> scriptList = Lists.newArrayList();
        List<String> cssList = Lists.newArrayList();
        for (String element : elements) {
            if (element.endsWith("js")) {
                scriptList.add(getUrl(element));
            } else if (element.endsWith("css")) {
                cssList.add(getUrl(element));
            }
        }
        if (!CollectionUtils.isEmpty(scriptList)) {
            jsList = Collections.unmodifiableList(scriptList);
        }
        if (!CollectionUtils.isEmpty(cssList)) {
            styleList = Collections.unmodifiableList(cssList);
        }
    }

    private String getUrl(String path) {
        return host.concat("/").concat(path);
    }

}
