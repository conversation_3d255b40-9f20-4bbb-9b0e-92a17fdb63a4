package com.yupaopao.risk.insight.web.controller;

import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.beans.DebugResultReq;
import com.yupaopao.risk.insight.repository.model.AlgorithmConfig;
import com.yupaopao.risk.insight.repository.model.UserInfo;
import com.yupaopao.risk.insight.service.AlgorithmCongService;
import com.yupaopao.risk.insight.service.beans.PageResult;
import com.yupaopao.risk.insight.web.support.annotation.CurrentUser;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/****
 * zengxiangcai
 * 2022/5/12 14:07
 ***/

@Api(tags = "算法挖掘分析配置")
@RestController
public class AlgorithmConfigController {

    @Autowired
    private AlgorithmCongService algorithmCongService;

    @GetMapping("/algorithm/config-list")
    public Response<PageResult<AlgorithmConfig>> getAlgorithmConfigList(@RequestParam("type") String type) {

        return Response.success(algorithmCongService.getAlgorithmConfigByType(type));

    }

    @PostMapping("/algorithm/config")
    public Response<Boolean> addList(@RequestBody AlgorithmConfig params,
                                     @CurrentUser UserInfo userInfo) {
        if (params != null && params.getId() != null) {
            return Response.success(algorithmCongService.editConfig(addUserParam(userInfo, params)));
        } else {
            return Response.success(algorithmCongService.addConfig(addUserParam(userInfo, params)));
        }

    }

    @PostMapping("/algorithm/disabled-config")
    public Response<Boolean> removeConfig(@RequestParam("id") Integer id) {
        return Response.success(algorithmCongService.disableConfig(id));
    }

    @PostMapping("/algorithm/runJob")
    public Response<Boolean> debugJob(@RequestParam("id") Integer id) {
        return Response.success(algorithmCongService.debugJob(id));
    }

    @PostMapping("/algorithm/isolation/param-columns")
    public Response<Boolean> addIsolationColumns(@RequestParam("columns") String columns) {
        return Response.success(algorithmCongService.addIsolationColumns(columns));
    }

    @PostMapping("/algorithm/debug-result")
    public Response<PageResult<Map<String,String>>> debugResult(@RequestBody DebugResultReq req) {
        return Response.success(algorithmCongService.debugResult(req));
    }

    private AlgorithmConfig addUserParam(UserInfo userInfo, AlgorithmConfig request) {
        if (userInfo != null && StringUtils.isNotEmpty(userInfo.getUsername())) {
            request.setCreateBy(userInfo.getUsername());
            request.setUpdateBy(userInfo.getUsername());
        } else {
            request.setCreateBy("admin");
            request.setUpdateBy("admin");
        }
        return request;
    }

}
