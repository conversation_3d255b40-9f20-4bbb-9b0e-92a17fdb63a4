package com.yupaopao.risk.insight.web.config.cas;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.jasig.cas.client.authentication.AuthenticationFilter;
import org.jasig.cas.client.session.SingleSignOutFilter;
import org.jasig.cas.client.session.SingleSignOutHttpSessionListener;
import org.jasig.cas.client.util.AssertionThreadLocalFilter;
import org.jasig.cas.client.util.HttpServletRequestWrapperFilter;
import org.jasig.cas.client.validation.Cas20ProxyReceivingTicketValidationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.web.authentication.logout.LogoutFilter;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;

@Configuration
public class CasConfig {

    @Autowired
    SpringCasAutoconfig autoconfig;

    private static boolean casEnabled = true;

    public CasConfig() {
    }

    @Bean
    public SpringCasAutoconfig getSpringCasAutoconfig() {
        return new SpringCasAutoconfig();
    }

    /**
     * 用于实现单点登出功能
     */
    @Bean
    public ServletListenerRegistrationBean<SingleSignOutHttpSessionListener> singleSignOutHttpSessionListener() {
        ServletListenerRegistrationBean<SingleSignOutHttpSessionListener> listener = new ServletListenerRegistrationBean<>();
        listener.setEnabled(casEnabled);
        listener.setListener(new SingleSignOutHttpSessionListener());
        listener.setOrder(1);
        return listener;
    }

    /**
     * 该过滤器用于实现单点登出功能，单点退出配置，一定要放在其他filter之前
     */
    @Bean
    public FilterRegistrationBean logOutFilter() {
        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
        LogoutFilter logoutFilter = new LogoutFilter(autoconfig.getCasServerUrlPrefix() + "/logout?service=" + autoconfig.getServerName(), new SecurityContextLogoutHandler());
        filterRegistration.setFilter(logoutFilter);
        filterRegistration.setEnabled(casEnabled);
        if (autoconfig.getSignOutFilters().size() > 0)
            filterRegistration.setUrlPatterns(autoconfig.getSignOutFilters());
        else
            filterRegistration.addUrlPatterns("/logout");
        filterRegistration.addInitParameter("casServerUrlPrefix", autoconfig.getCasServerUrlPrefix());
        filterRegistration.addInitParameter("serverName", autoconfig.getServerName());
        filterRegistration.setOrder(2);
        return filterRegistration;
    }


    /**
     * logout广播，单本机器收到server的logout请求时，像其他机器发起广播
     */
    @Bean
    public FilterRegistrationBean casSingleLogoutClusterFilter() {
        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
        filterRegistration.setFilter(new CasSingleLogoutClusterFilter());
        filterRegistration.setEnabled(casEnabled);
        // 本地机器的地址
        //filterRegistration.addInitParameter("clientHostName",autoconfig.getClientHostName());
        // 集群中其他机器地址
        filterRegistration.addInitParameter("peersUrls", autoconfig.getPeersUrls());
        filterRegistration.addUrlPatterns("/*");
        filterRegistration.setOrder(3);
        return filterRegistration;
    }

    /**
     * 该过滤器用于实现单点登出功能，单点退出配置，一定要放在其他filter之前
     */
    @Bean
    public FilterRegistrationBean singleSignOutFilter() {
        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
        filterRegistration.setFilter(new SingleSignOutFilter());
        filterRegistration.setEnabled(casEnabled);
        /*if(autoconfig.getSignOutFilters().size()>0)
            filterRegistration.setUrlPatterns(autoconfig.getSignOutFilters());
        else*/
        filterRegistration.addUrlPatterns("/*");
        filterRegistration.addInitParameter("casServerUrlPrefix", autoconfig.getCasServerUrlPrefix());
        filterRegistration.addInitParameter("serverName", autoconfig.getServerName());
        // 放行业务WebSocket握手路径（使用正则策略，按URI匹配）
        filterRegistration.addInitParameter("ignoreUrlPatternType", "org.jasig.cas.client.authentication.RegexUrlPatternMatcherStrategy");
        // 放行固定WS端点及其子路径（严格按URI匹配，不含协议主机）
        filterRegistration.addInitParameter("ignorePattern", "^/api/websocket(/.*)?$");
        filterRegistration.setOrder(4);
        return filterRegistration;
    }

    /**
     * 该过滤器负责用户的认证工作
     */
    @Bean
    public FilterRegistrationBean authenticationFilter() {
        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
        filterRegistration.setFilter(new AuthenticationFilter());
        filterRegistration.setEnabled(casEnabled);
        if (autoconfig.getAuthFilters().size() > 0)
            filterRegistration.setUrlPatterns(autoconfig.getAuthFilters());
        else {
            // 排除WebSocket路径，只对非WebSocket路径应用CAS认证
            filterRegistration.addUrlPatterns("/*");
            filterRegistration.addInitParameter("excludePattern", "^/api/websocket(/.*)?$");
        }
        //casServerLoginUrl:cas服务的登陆url
        filterRegistration.addInitParameter("casServerLoginUrl", autoconfig.getCasServerLoginUrl());
        //本项目登录ip+port
        filterRegistration.addInitParameter("serverName", autoconfig.getServerName());
        filterRegistration.addInitParameter("useSession", autoconfig.isUseSession() ? "true" : "false");
        filterRegistration.addInitParameter("redirectAfterValidation", autoconfig.isRedirectAfterValidation() ? "true" : "false");
        // 放行业务WebSocket握手路径（使用正则策略，按URI匹配）
        filterRegistration.addInitParameter("ignoreUrlPatternType", "org.jasig.cas.client.authentication.RegexUrlPatternMatcherStrategy");
        // 放行固定WS端点及其子路径（严格按URI匹配，不含协议主机）
        filterRegistration.addInitParameter("ignorePattern", "^/api/websocket(/.*)?$");
//        filterRegistration.addInitParameter("authenticationRedirectStrategyClass", AjaxAuthenticationRedirectStrategy.class.getCanonicalName());
        filterRegistration.setOrder(5);
        return filterRegistration;
    }

    /**
     * 该过滤器负责对Ticket的校验工作
     */
    @Bean
    public FilterRegistrationBean cas20ProxyReceivingTicketValidationFilter() {
        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
        Cas20ProxyReceivingTicketValidationFilter cas20ProxyReceivingTicketValidationFilter = new Cas20ProxyReceivingTicketValidationFilter();
        cas20ProxyReceivingTicketValidationFilter.setServerName(autoconfig.getServerName());
        filterRegistration.setFilter(cas20ProxyReceivingTicketValidationFilter);
        filterRegistration.setEnabled(casEnabled);
        if (autoconfig.getValidateFilters().size() > 0)
            filterRegistration.setUrlPatterns(autoconfig.getValidateFilters());
        else
            filterRegistration.addUrlPatterns("/*");
        filterRegistration.addInitParameter("casServerUrlPrefix", autoconfig.getCasServerUrlPrefix());
        filterRegistration.addInitParameter("serverName", autoconfig.getServerName());
        // 放行业务WebSocket握手路径（使用正则策略，按URI匹配）
        filterRegistration.addInitParameter("ignoreUrlPatternType", "org.jasig.cas.client.authentication.RegexUrlPatternMatcherStrategy");
        // 放行固定WS端点及其子路径（严格按URI匹配，不含协议主机）
        filterRegistration.addInitParameter("ignorePattern", "^/api/websocket(/.*)?$");
        filterRegistration.setOrder(6);
        return filterRegistration;
    }


    /**
     * 该过滤器对HttpServletRequest请求包装， 可通过HttpServletRequest的getRemoteUser()方法获得登录用户的登录名
     */
    @Bean
    public FilterRegistrationBean httpServletRequestWrapperFilter() {
        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
        filterRegistration.setFilter(new HttpServletRequestWrapperFilter());
        filterRegistration.setEnabled(true);
        if (autoconfig.getRequestWrapperFilters().size() > 0)
            filterRegistration.setUrlPatterns(autoconfig.getRequestWrapperFilters());
        else
            filterRegistration.addUrlPatterns("/login");
        filterRegistration.setOrder(7);
        return filterRegistration;
    }

    /**
     * WebSocket/CAS 调试日志过滤器，定位谁拦截了握手
     */
    @Bean
    public FilterRegistrationBean casDebugLoggingFilter() {
        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
        filterRegistration.setFilter(new CasDebugLoggingFilter());
        filterRegistration.setEnabled(true);
        filterRegistration.addUrlPatterns("/*");
        // 放在 CAS 认证/校验过滤器之前，便于看到前后的状态
        filterRegistration.setOrder(2);
        return filterRegistration;
    }

    /**
     * 该过滤器使得可以通过org.jasig.cas.client.util.AssertionHolder来获取用户的登录名。
     * 比如AssertionHolder.getAssertion().getPrincipal().getName()。
     * 这个类把Assertion信息放在ThreadLocal变量中，这样应用程序不在web层也能够获取到当前登录信息
     */
    @Bean
    public FilterRegistrationBean assertionThreadLocalFilter() {
        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
        filterRegistration.setFilter(new AssertionThreadLocalFilter());
        filterRegistration.setEnabled(true);
        if (autoconfig.getAssertionFilters().size() > 0) {
            filterRegistration.setUrlPatterns(autoconfig.getAssertionFilters());
        } else {
            filterRegistration.addUrlPatterns("/*");
        }
        // 放行业务WebSocket握手路径（使用正则策略，按URI匹配）
        filterRegistration.addInitParameter("ignoreUrlPatternType", "org.jasig.cas.client.authentication.RegexUrlPatternMatcherStrategy");
        // 放行固定WS端点及其子路径（严格按URI匹配，不含协议主机）
        filterRegistration.addInitParameter("ignorePattern", "^/api/websocket(/.*)?$");
        filterRegistration.setOrder(8);
        return filterRegistration;
    }

    @Bean
    public FilterRegistrationBean initUserFilter() {
        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
        filterRegistration.setFilter(new CasUserFilter());
        filterRegistration.setEnabled(true);
        filterRegistration.addUrlPatterns("/*");
        filterRegistration.setOrder(9);
        return filterRegistration;
    }
//    @Bean
//    public FilterRegistrationBean initMenuFilter() {
//        FilterRegistrationBean filterRegistration = new FilterRegistrationBean();
//        filterRegistration.setFilter(new MenuFilter());
//        Config config = ConfigService.getAppConfig();
//        String permissionFilter = config.getProperty("insight.menu.permission.filter", "/*");
//        filterRegistration.addUrlPatterns(permissionFilter.split(","));
//        filterRegistration.setOrder(9);
//        return filterRegistration;
//    }

}
