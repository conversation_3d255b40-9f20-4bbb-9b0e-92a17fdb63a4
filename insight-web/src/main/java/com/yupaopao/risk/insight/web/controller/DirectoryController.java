package com.yupaopao.risk.insight.web.controller;

import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.repository.model.Directory;
import com.yupaopao.risk.insight.service.DirectoryService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.yupaopao.risk.insight.enums.ErrorMessage.*;

/**
 * Created by Avalon on 2020/2/22 20:06
 */
@Slf4j
@RestController
@RequestMapping(value = "/dir")
public class DirectoryController {

    @Resource
    private DirectoryService directoryService;

    /***
     * 树状目录结构列表
     */
    @ApiOperation(value = "获取目录结构")
    @GetMapping("/fetch")
    public Response<List<Directory>> fetchDirConstruct(@RequestParam("type") String type) {
        try {
            return Response.success(directoryService.fetchDirTree(type));
        } catch (Exception e) {
            log.error(DIR_FETCH_ERROR.getMsg(), e);
            return Response.fail(DIR_FETCH_ERROR.toBizCode());
        }
    }

    /***
     * 获取节点
     */
    @ApiOperation(value = "获取节点")
    @GetMapping("/{id}")
    public Response<Directory> fetchDirConstruct(@PathVariable("id") Integer id) {
        try {
            return Response.success(directoryService.getNode(id));
        } catch (Exception e) {
            log.error(DIR_FETCH_ERROR.getMsg(), e);
            return Response.fail(DIR_FETCH_ERROR.toBizCode());
        }
    }


    /***
     * 新增节点
     */
    @ApiOperation(value = "新增节点")
    @PostMapping("/add")
    public Response<Boolean> addDirNode(@RequestBody Directory directory) {
        try {
            return Response.success(directoryService.addNode(directory));
        } catch (Exception e) {
            log.error(DIR_ADD_NODE_ERROR.getMsg(), e);
            return Response.fail(DIR_ADD_NODE_ERROR.toBizCode());
        }
    }

    /***
     * 修改节点
     */
    @ApiOperation(value = "修改节点")
    @PostMapping("/modify")
    public Response<Boolean> modifyDirNode(@RequestBody Directory directory) {
        try {
            return Response.success(directoryService.modifyNode(directory));
        } catch (Exception e) {
            log.error(DIR_MODIFY_NODE_ERROR.getMsg(), e);
            return Response.fail(DIR_MODIFY_NODE_ERROR.toBizCode());
        }
    }


    /***
     * 移除节点
     */
    @ApiOperation(value = "移除节点")
    @PostMapping("/remove")
    public Response<Boolean> removeDirNode(@RequestParam("id") Integer id) {
        try {
            return Response.success(directoryService.removeNode(id));
        } catch (Exception e) {
            log.error(DIR_REMOVE_NODE_ERROR.getMsg(), e);
            return Response.fail(DIR_REMOVE_NODE_ERROR.toBizCode());
        }
    }

    /***
     * 关联资源
     */
    @ApiOperation(value = "关联资源")
    @GetMapping("/link")
    public Response<Boolean> linkDirNode(@RequestParam("id") Integer id, @RequestParam("linkId") Integer linkId) {
        try {
            return Response.success(directoryService.linkObj(id, linkId));
        } catch (Exception e) {
            log.error(NODE_LINK_ERROR.getMsg(), e);
            return Response.fail(NODE_LINK_ERROR.toBizCode());
        }
    }
}
