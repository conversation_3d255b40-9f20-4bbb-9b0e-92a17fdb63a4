package com.yupaopao.risk.insight.web.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * WebSocket配置类
 * 使用Java标准WebSocket (@ServerEndpoint)
 *
 * <AUTHOR>
 * @date 2019-09-06
 */
@Configuration
public class WebSocketConfig {

    /**
     * 注入ServerEndpointExporter，用于扫描和注册@ServerEndpoint注解的WebSocket端点
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
}
