package com.yupaopao.risk.insight.web.controller;


import com.alibaba.fastjson.JSONObject;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.data.bean.RequestData;
import com.yupaopao.risk.insight.service.PortraitTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Api(tags = "测试接口")
@RequestMapping("/test")
@RestController
public class TestController {

    @Autowired
    PortraitTagService portraitTagService;


    @ApiOperation(value = "测试接口", notes = "query risk data")
    @PostMapping("/data")
    public Response testData(@RequestBody String body){
        String resp = null;
        try {
            Map bodyMap = JSONObject.parseObject(body, Map.class);
            Object conditionParam = bodyMap.get("conditionParam");
            RequestData requestData = new RequestData();
            requestData.withSystemCode(bodyMap.get("systemCode")+"")
                    .withApproachCode(bodyMap.get("approachCode")+"");
            requestData.getConditionParam().putAll(JSONObject.parseObject(conditionParam+"", Map.class));
            resp = portraitTagService.questData(requestData);
        } catch (Exception e) {
            resp="查询失败:"+e.getMessage();
        }
        return Response.success(resp);
    }

    @Value("${service.list}")
    private String serviceName;

    @ApiOperation(value = "测试接口", notes = "query risk data")
    @GetMapping("/get/config")
    public Response getConfig(){
        Map map = JSONObject.parseObject(serviceName, Map.class);
        return Response.success(map);
    }


}
