package com.yupaopao.risk.insight.web.controller;

import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.InsightException;
import com.yupaopao.risk.insight.dashboard.beans.QueryRequest;
import com.yupaopao.risk.insight.enums.ErrorMessage;
import com.yupaopao.risk.insight.repository.model.ChartInfo;
import com.yupaopao.risk.insight.repository.model.UserInfo;
import com.yupaopao.risk.insight.service.ChartService;
import com.yupaopao.risk.insight.service.ElasticSearchService;
import com.yupaopao.risk.insight.service.beans.ChartData;
import com.yupaopao.risk.insight.web.support.annotation.CurrentUser;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

import static com.yupaopao.risk.insight.enums.ErrorMessage.PARAM_ILLEGAL;

/**
 * Created by Avalon on 2020/2/22 20:06
 */
@Slf4j
@RestController
@RequestMapping(value = "/chart")
public class ChartController {

    @Resource
    private ChartService chartService;

    @Resource
    private ElasticSearchService elasticSearchService;

    /***
     * 获取图表信息
     */
    @ApiOperation(value = "获取图表信息")
    @GetMapping("/{id}")
    public Response<ChartInfo> getChartInfo(@PathVariable("id") Integer tableId) {
        try {
            return Response.success(chartService.getChartInfo(tableId));
        } catch (Exception e) {
            log.error(ErrorMessage.TABLE_NOT_EXIST_ERROR.getMsg(), e);
            return Response.fail(ErrorMessage.TABLE_NOT_EXIST_ERROR.toBizCode());
        }
    }


    /***
     * 保存图表信息
     */
    @ApiOperation(value = "保存图表信息")
    @PostMapping("/save")
    public Response<ChartInfo> saveChartInfo(@RequestBody ChartInfo chartInfo, @CurrentUser UserInfo userInfo) {
        if (chartInfo == null) {
            return Response.fail(PARAM_ILLEGAL.getCode(), "param illegal");
        }
        try {
            chartInfo.setCreatedBy(userInfo != null ? userInfo.getUsername() : "admin");
            return Response.success(chartService.save(chartInfo));
        } catch (Exception e) {
            log.error(ErrorMessage.TABLE_NOT_EXIST_ERROR.getMsg(), e);
            return Response.fail(ErrorMessage.TABLE_NOT_EXIST_ERROR.toBizCode());
        }
    }

    /***
     * 获取结果表字段
     */
    @ApiOperation(value = "获取结果表字段")
    @GetMapping("/fields")
    public Response<List<String>> fetchFields(@RequestParam("tableName") String tableName) {
        try {
            return Response.success(elasticSearchService.fetchFields(tableName));
        } catch (Exception e) {
            log.error(ErrorMessage.TABLE_NOT_EXIST_ERROR.getMsg(), e);
            return Response.fail(ErrorMessage.TABLE_NOT_EXIST_ERROR.toBizCode());
        }
    }

    /***
     * 获取图表数据
     */
    @ApiOperation(value = "获取图表数据")
    @PostMapping("/fetchData")
    public Response<ChartData> fetchData(@RequestBody QueryRequest request) {
        try {
            return Response.success(chartService.fetchData(request));
        } catch (InsightException e) {
            log.error(e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());
        }
    }



}
