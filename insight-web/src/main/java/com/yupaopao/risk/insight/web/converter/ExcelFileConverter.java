package com.yupaopao.risk.insight.web.converter;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.beans.CKImportParam;
import com.yupaopao.risk.insight.beans.DBTableInfo;
import com.yupaopao.risk.insight.beans.DBTableInfo.TableColumn;
import com.yupaopao.risk.insight.common.enums.InsightFlinkDataType;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import ru.yandex.clickhouse.domain.ClickHouseDataType;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-27 15:51
 *
 ***/

@Slf4j
@Component
public class ExcelFileConverter {

    public CKImportParam getDataFromCsv(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        boolean isxss = false;
        if (fileName.toLowerCase().endsWith("xlsx")) {
            isxss = true;
        } else if (fileName.toLowerCase().endsWith("xls")) {
            isxss = false;
        } else {
            throw new IllegalArgumentException("不支持的文件类型,暂支持xlsx、xls");
        }
        try (Workbook wb = isxss ? new XSSFWorkbook(file.getInputStream()) : new HSSFWorkbook(file.getInputStream())) {

            Sheet dataSheet = wb.getSheetAt(0);
            int totalRows = dataSheet.getLastRowNum();
            if (totalRows < 1) {
                throw new IllegalArgumentException("file rows is empty");
            }
            List<TableColumn> columns = new ArrayList<>();
            //列信息columnName#columnType
            Row firstRow = dataSheet.getRow(0);
            for (int i = 0; i < firstRow.getLastCellNum(); i++) {
                Cell cell = firstRow.getCell(i, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
                if (cell == null) {
                    throw new IllegalArgumentException("table column cannot be null, line 1 column " + (i + 1));
                }
                String columnInfo = cell.getStringCellValue();
                String columnName = columnInfo.split("#")[0];
                String columnType = columnInfo.split("#")[1];
                if ("Boolean".equalsIgnoreCase(columnType)) {
                    throw new IllegalArgumentException("boolean类型不支持，请使用" + InsightFlinkDataType.TINYINT + "类型替换");
                }
                TableColumn column = new TableColumn(columnName, columnType);
                columns.add(column);
            }

            //行数据
            List<String> rows = new ArrayList<>();
            for (int i = 1; i <= totalRows; i++) {
                Row row = dataSheet.getRow(i);
                if (row == null) {
                    continue;
                }
                String jsonData = formatRowToJson(row, columns);
                if (StringUtils.isEmpty(jsonData)) {
                    continue;
                }
                rows.add(jsonData);
            }

            DBTableInfo tableInfo = new DBTableInfo();
            tableInfo.setTableColumns(columns);
            return new CKImportParam(tableInfo, rows);
        } catch (IllegalArgumentException e1) {
            throw e1;
        } catch (Exception e) {
            log.error("read file error", e);
            throw new IllegalArgumentException("parse csv error");
        }
    }

    private String convertFlinkTypeToCk(String type) {
        String ckType = InsightFlinkDataType.getCkType(type);
        if (StringUtils.isEmpty(ckType)) {
            throw new IllegalArgumentException("类型不支持,当前支持的类型是：" + InsightFlinkDataType.getSupportSqlDataType());
        }
        return ckType;
    }


    private String formatRowToJson(Row row, List<TableColumn> columns) {
        Map<String, Object> rowData = new HashMap<>();
        for (int i = 0; i < columns.size(); i++) {
            Cell cell = row.getCell(i, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
            if (cell == null) {
                continue;
            }
            Object columnValue = null;
            ClickHouseDataType ckDataType = ClickHouseDataType.valueOf(convertFlinkTypeToCk(columns.get(i).getType()));
            switch (cell.getCellType()) {
                case STRING:
                    columnValue = transStringToCkData(cell.getStringCellValue(), ckDataType);
                    break;
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        columnValue = transDateToCkData(cell.getDateCellValue(), ckDataType);
//                        SimpleDateFormat simpleFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                       value = simpleFormat.format(tempValue);

                    } else {
                        columnValue = transNumericToCkData(cell.getNumericCellValue(), ckDataType);
                    }
                    break;
                default:
                    throw new IllegalArgumentException("仅支持string、 numeric 类型单元格数据");
            }
            rowData.put(columns.get(i).getColumnName(), columnValue);
        }
        if (rowData.isEmpty()) {
            return null;
        }
        return JSON.toJSONString(rowData);
    }

    private Object transStringToCkData(String value, ClickHouseDataType dataType) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        switch (dataType) {
            case UInt64:
                return new BigInteger(value);
            case UInt32:
            case Int64:
                return Long.valueOf(value);
            case UInt16:
            case UInt8:
            case Int32:
            case Int16:
            case Int8:
                return Integer.valueOf(value);
            case Date:
                Date date = InsightDateUtils.getDateFromString(value, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
                if (date == null) {
                    throw new IllegalArgumentException("日期格式不对,正确格式为：" + InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
                }
                return date;
            case DateTime:
                Date dateTime = InsightDateUtils.getDateFromString(value, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
                if (dateTime == null) {
                    throw new IllegalArgumentException("日期格式不对,正确格式为：" + InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
                }
                return dateTime;
            case Float32:
                return Float.valueOf(value);
            case Float64:
                return Double.valueOf(value);
            case Decimal32:
            case Decimal64:
            case Decimal128:
            case Decimal:
                return new BigDecimal(value);
            case String:
            case FixedString:
                return value.trim();
            default:
                throw new IllegalArgumentException("not support data Types: " + dataType);
        }
    }

    private Object transNumericToCkData(Double value, ClickHouseDataType dataType) {
        if (value == null) {
            return null;
        }
        switch (dataType) {
            case UInt64:
                return new BigInteger(String.valueOf(value.longValue()));
            case UInt32:
            case Int64:
                return value.longValue();
            case UInt16:
            case UInt8:
            case Int32:
            case Int16:
            case Int8:
                return value.intValue();
            case Date:
            case DateTime:
                Date date = new Date(value.longValue());
                return date;
            case Float32:
                return value.floatValue();
            case Float64:
                return value;
            case Decimal32:
            case Decimal64:
            case Decimal128:
            case Decimal:
                return new BigDecimal(value);
            case String:
            case FixedString:
                return value.toString();
            default:
                throw new IllegalArgumentException("not support data Types: " + dataType);
        }
    }

    private Object transDateToCkData(Date value, ClickHouseDataType dataType) {
        if (value == null) {
            return null;
        }
        switch (dataType) {
            case UInt64:
                return new BigInteger(String.valueOf(value.getTime()));
            case UInt32:
            case Int64:
                return value.getTime();
            case Date:
            case DateTime:
                return value;
            case String:
            case FixedString:
                return InsightDateUtils.getDateStr(value, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
            default:
                throw new IllegalArgumentException("not support data Types: " + dataType);
        }
    }

}
