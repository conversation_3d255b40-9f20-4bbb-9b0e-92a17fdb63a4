package com.yupaopao.risk.insight.web.controller;

import com.yupaopao.platform.common.dto.Code;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.service.PortraitTagService;
import com.yupaopao.risk.insight.service.beans.PortraitTagQueryVO;
import com.yupaopao.risk.insight.service.beans.QueryTag;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Api(tags = "画像查询接口")
@RequestMapping("/portrait")
@RestController
@Slf4j
public class PortraitQueryController {


    @Autowired
    PortraitTagService portraitTagService;
    private static final String SUCCESS_CODE = "000";
    private static final String PORTRAIT_SERVICE = "portraitQueryService";

    @ApiOperation(value = "查询天象设备标签原始数据", notes = "query portrait data")
    @PostMapping("/query")
    public Response queryPortraitTag(@RequestBody PortraitTagQueryVO queryVO) {
        try {
            if (StringUtils.isEmpty(queryVO.getValue())) {
                return Response.fail("1001", "请输入查询ID");
            }
            Map<String, List<QueryTag>> queryTags = portraitTagService.queryTag(queryVO);
            return Response.success(queryTags);
        } catch (Exception e) {
            log.error("query tag error ", e);
            return Response.fail(Code.ERROR_PARAM.getCode(), "query tag error");
        }
    }



}
