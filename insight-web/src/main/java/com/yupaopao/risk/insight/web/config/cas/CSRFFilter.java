package com.yupaopao.risk.insight.web.config.cas;

import com.alibaba.fastjson.JSONArray;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class CSRFFilter extends HandlerInterceptorAdapter {

    private List<String> csrfCheckList;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        boolean ignore = false;
        Config config = ConfigService.getAppConfig();
        if (CollectionUtils.isEmpty(csrfCheckList)){
            String csrfCheckUrl = config.getProperty("csrf.check.url", "['http://localhost','https://insight.yupaopao.com/','https://insight-visual.yupaopao.com/']");
            csrfCheckList = JSONArray.parseArray(csrfCheckUrl,String.class);
        }
        String servletPath = ((HttpServletRequest) request).getServletPath();
        String passUrl = config.getProperty("authority.pass.url", "/csrf,/v2/api-docs");
        ignore = Arrays.asList(passUrl.split(",")).contains(servletPath);
        if (ignore) {
            return true;
        }
        if(servletPath!=null && servletPath.contains("swagger")){
            return true;
        }
        String referer = request.getHeader("Referer");
        log.info("start check:{},{}",csrfCheckList.size(), referer);
        for (int i = 0; i < csrfCheckList.size(); i++) {
            String url = csrfCheckList.get(i);
            log.info("referer:{},{}", url, referer.startsWith(url));
            if (referer.startsWith(url)) {
                return true;
            }
        }
        return false;
    }
}
