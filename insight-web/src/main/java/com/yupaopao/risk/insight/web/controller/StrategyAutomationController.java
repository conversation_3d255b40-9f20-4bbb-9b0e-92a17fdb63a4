package com.yupaopao.risk.insight.web.controller;

import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.repository.model.BaseEntity;
import com.yupaopao.risk.insight.repository.model.StrategyAutoTaskResult;
import com.yupaopao.risk.insight.repository.model.StrategyAutomationTask;
import com.yupaopao.risk.insight.repository.model.UserInfo;
import com.yupaopao.risk.insight.service.StrategyAutomationService;
import com.yupaopao.risk.insight.service.beans.*;
import com.yupaopao.risk.insight.service.beans.DefaultSampleRequestParam;
import com.yupaopao.risk.insight.util.OSSUtils;
import com.yupaopao.risk.insight.util.XlsxUtils;
import com.yupaopao.risk.insight.web.converter.SampleFileConverter;
import com.yupaopao.risk.insight.web.support.annotation.CurrentUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.exceptions.IllegalArgumentIOException;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;

import static com.yupaopao.risk.insight.enums.ErrorMessage.*;

/****
 * zengxiangcai
 * 2023/10/24 17:52
 ***/

@Slf4j
@RestController
@RequestMapping("/strategy-automation")
public class StrategyAutomationController {

    @Autowired
    private StrategyAutomationService strategyAutomationTaskService;

    @Autowired
    private SampleFileConverter sampleFileConverter;

    /***
     * 下载样本模版
     * @param response
     * @param fileExtend
     * @throws IOException
     */
    @GetMapping("/sample/template-download")
    public void downloadSampleTemplate(HttpServletResponse response,
                                       @RequestParam(value = "fileExtend", defaultValue = "xlsx") String fileExtend) throws IOException {
        response.setContentType("multipart/form-data");
        // 下载文件的默认名称
        response.setHeader("Content-Disposition",
                "attachment;filename=" + "sample-template" + System.currentTimeMillis() + "." + fileExtend);
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        List<List<String>> data = new ArrayList<>();
        List<String> row1 = new ArrayList<>();
        row1.add("20205100636586***");
        row1.add("1");
//        row1.add("1.0");
        data.add(row1);

        List<String> row2 = new ArrayList<>();
        row2.add("20205100636599***");
        row2.add("0");
//        row1.add("1.0");
        data.add(row2);

        Workbook workbook = XlsxUtils.getWorkBook(Arrays.asList("id", "label"), data);
        workbook.write(response.getOutputStream());
    }

    @GetMapping("/download-result-image")
    public Response<StrategyAnalysisDetailResponse> downloadAnalysisResultImg(@RequestParam(value = "url") String url) throws IOException {
        if (StringUtils.isEmpty(url)) {
            throw new IllegalArgumentIOException("url is empty");
        }
        try {
            InputStream fs = OSSUtils.downloadAnalysisResult(url);

            StrategyAnalysisDetailResponse response = new StrategyAnalysisDetailResponse();

            String img = Base64.getEncoder().encodeToString(IOUtils.toByteArray(fs));
            response.setBase64Img(img);
            fs.close();
            return Response.success(response);
        } catch (IOException e) {
            log.error("download image error ,url=" + url, e);
            throw e;
        }


    }


    @PostMapping("/task-list")
    Response<PageResult<StrategyAutomationTask>> getStrategyAnalysisTaskList(@RequestBody StrategyAutomationTask queryTask,
                                                                             @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                             @RequestParam(value = "currentPage", required = false) Integer currentPage) {
        if (currentPage == null) {
            currentPage = 0;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        return Response.success(strategyAutomationTaskService.getStrategyTaskList(queryTask, currentPage, pageSize));

    }

    @PostMapping("/task/save")
    public Response<Boolean> saveAnalysisTask(@RequestBody StrategyAutomationTask saveParam,
                                              @CurrentUser UserInfo userInfo) {
        try {
            setOperator(userInfo, saveParam);
            return Response.success(strategyAutomationTaskService.saveStrategyTask(saveParam));
        } catch (RuntimeException ee) {
            return Response.fail(STRATEGY_AUTOMATION_TASK_SAVE_ERROR.getCode(), ee.getMessage());
        } catch (Exception e) {
            return Response.fail(STRATEGY_AUTOMATION_TASK_SAVE_ERROR.toBizCode());
        }
    }


    @PostMapping(value = "/sample/setting", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response<Boolean> saveSample(MultipartFile file,
                                        StrategyAutomationSampleParam params,
                                        @CurrentUser UserInfo userInfo) {
        try {
            params.setDataList(sampleFileConverter.getDataFromExcel(file));
            params.setOperator(StringUtils.isNotEmpty(userInfo.getUsername()) ? userInfo.getUsername() : "admin");
            strategyAutomationTaskService.saveSample(params);
            return Response.success(true);
        } catch (RuntimeException re) {
            return Response.fail(STRATEGY_AUTOMATION_SAMPLE_SAVE_ERROR.getCode(), re.getMessage());
        } catch (Exception e) {
            return Response.fail(STRATEGY_AUTOMATION_SAMPLE_SAVE_ERROR.toBizCode());
        }
    }

    /****
     * 样本调整，主要是补充黑样本
     * @param params
     * @param userInfo
     * @return
     */
    @PostMapping(value = "/sample/tuning", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Response<Boolean> tuneSample(StrategyAutomationSampleTunningParam params,
                                        @CurrentUser UserInfo userInfo) {
        try {

            params.setOperator(StringUtils.isNotEmpty(userInfo.getUsername()) ? userInfo.getUsername() : "admin");
            strategyAutomationTaskService.tuneSample(params);
            return Response.success(true);
        } catch (RuntimeException re) {
            return Response.fail(STRATEGY_AUTOMATION_SAMPLE_TUNE_ERROR.getCode(), re.getMessage());
        } catch (Exception e) {
            return Response.fail(STRATEGY_AUTOMATION_SAMPLE_TUNE_ERROR.toBizCode());
        }
    }

    @PostMapping("/analysis-start")
    public Response<Boolean> saveAnalysisTask(@RequestBody StrategyAutomationAnalysisParam analysisParam,
                                              @CurrentUser UserInfo userInfo) {
        try {
            if (userInfo != null && StringUtils.isNotEmpty(userInfo.getUsername())) {
                analysisParam.setOperator(userInfo.getUsername());
            } else {
                analysisParam.setOperator("admin");
            }
            boolean startResult = strategyAutomationTaskService.startStrategyTask(analysisParam);
            return Response.success(startResult);
        } catch (RuntimeException ee) {
            return Response.fail(STRATEGY_AUTOMATION_ANALYSIS_ERROR.getCode(), ee.getMessage());
        } catch (Exception e) {
            return Response.fail(STRATEGY_AUTOMATION_ANALYSIS_ERROR.toBizCode());
        }
    }


    @PostMapping("/analysis-results")
    Response<PageResult<StrategyAutoTaskResult>> getStrategyAnalysisTaskList(@RequestBody StrategyAutoTaskResult analysisResultQuery,
                                                                             @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                             @RequestParam(value = "currentPage", required = false) Integer currentPage) {
        if (currentPage == null) {
            currentPage = 0;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        return Response.success(strategyAutomationTaskService.getAnalysisResults(analysisResultQuery, currentPage, pageSize));

    }

    /***
     * 分析默认白样本配置
     * @param requestParam

     * @return
     */
    @PostMapping("/default-sample-cfg")
    Response<Boolean> configureDefaultSample(@RequestBody DefaultSampleRequestParam requestParam) {
        try {
            return Response.success(strategyAutomationTaskService.configureDefaultSample(requestParam));
        } catch (Exception e) {
            return Response.fail(STRATEGY_AUTOMATION_DEFAULT_WHITE_SAMPLE_CONFIGURE_ERROR.toBizCode());
        }
    }


    @PostMapping("/default-sample-cfg-list")
    Response<PageResult<Map<String, String>>> getDefaultSampleConfigList(@RequestBody DefaultSampleRequestParam requestParam,
                                                                         @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                         @RequestParam(value = "currentPage", required = false) Integer currentPage) {
        if (currentPage == null) {
            currentPage = 0;
        }
        if (pageSize == null) {
            pageSize = 10;
        }

        return Response.success(strategyAutomationTaskService.getDefaultSampleConfigList(requestParam, currentPage,
                pageSize));
    }

    private void setOperator(UserInfo operator, BaseEntity baseParam) {
        if (operator != null && StringUtils.isNotEmpty(operator.getUsername())) {
            baseParam.setUpdateBy(operator.getUsername());
        } else {
            baseParam.setUpdateBy("admin");
        }
        baseParam.setUpdateTime(new Date());
        if (baseParam.getId() == null) {
            baseParam.setCreateTime(new Date());
            baseParam.setCreateBy(baseParam.getUpdateBy());
        }
    }
}
