package com.yupaopao.risk.insight.web.controller;

import com.alibaba.fastjson.JSON;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.InsightException;
import com.yupaopao.risk.insight.repository.model.JobInfo;
import com.yupaopao.risk.insight.repository.model.UserInfo;
import com.yupaopao.risk.insight.repository.model.WorkspaceTaskInfo;
import com.yupaopao.risk.insight.repository.model.WorkspaceTaskLog;
import com.yupaopao.risk.insight.service.WorkspaceService;
import com.yupaopao.risk.insight.service.beans.PageResult;
import com.yupaopao.risk.insight.service.beans.WorkspaceLogParam;
import com.yupaopao.risk.insight.service.beans.WorkspaceNode;
import com.yupaopao.risk.insight.service.beans.WorkspaceRequestParam;
import com.yupaopao.risk.insight.web.support.annotation.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

import static com.yupaopao.risk.insight.enums.ErrorMessage.*;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-24 15:55
 * 用户工作台
 ***/


@Api(tags = "用户工作台接口")
@Slf4j
@RestController
@RequestMapping(value = "/workspace")
public class WorkspaceController {

    @Autowired
    private WorkspaceService workspaceService;

    /***
     * 工作台树状目录结构列表
     */
    @ApiOperation(value = "获取工作台目录结构", notes = "all node in workspace")
    @GetMapping("/detail")
    public Response<List<WorkspaceNode>> getWorkspaceList() {
        try {
            return Response.success(workspaceService.getRootNode());
        } catch (RuntimeException e1) {
            return Response.fail(WORKSPACE_GET_DETAIL_ERROR.getCode(), e1.getMessage());
        } catch (Exception e) {
            log.error("get workspace detail error", e);
            return Response.fail(WORKSPACE_GET_DETAIL_ERROR.toBizCode());
        }
    }

    /**
     * 新建job作业文件(叶子节点)
     *
     * @param param
     */
    @ApiOperation(value = "增加文件夹、作业", notes = "add node (directory or job file)[name,leafNode cannot be empty]")
    @PostMapping(value = "/node/add")
    public Response<Integer> createFile(@RequestBody WorkspaceRequestParam param, @CurrentUser UserInfo userInfo) {
        try {
            if (StringUtils.isEmpty(param.getName())) {
                return Response.fail(PARAM_ILLEGAL.getCode(), "name cannot be empty");
            }
            param.setCreateBy(userInfo != null ? userInfo.getUsername() : "admin");
            return Response.success(workspaceService.addNode(param));
        } catch (RuntimeException e1) {
            return Response.fail(WORKSPACE_CREATE_NODE_ERROR.getCode(), e1.getMessage());
        } catch (Exception e) {
            log.error("create node error, param=" + JSON.toJSONString(param), e);
            return Response.fail(WORKSPACE_CREATE_NODE_ERROR.toBizCode());
        }
    }

    @ApiOperation(value = "修改文件夹、作业名称", notes = "change the name of node[id,name cannot be empty]")
    @PostMapping(value = "/node/change")
    public Response<Boolean> changeName(@RequestBody WorkspaceRequestParam param, @CurrentUser UserInfo userInfo) {
        if (StringUtils.isEmpty(param.getName()) || param.getId() == null) {
            Response.fail(PARAM_ILLEGAL.toBizCode());
        }
        try {
            log.info("change node with param: {} ,user: {}", JSON.toJSONString(param), userInfo.getName());
            return Response.success(workspaceService.changeNodeName(param.getId(), param.getName()));
        } catch (Exception e) {
            log.error("change node name error, param=" + JSON.toJSONString(param), e);
            return Response.fail(WORKSPACE_CHANGE_NAME_ERROR.toBizCode());
        }
    }

    @ApiOperation(value = "删除文件夹、作业", notes = "delete directory or file[id cannot be empty]")
    @PostMapping(value = "/node/delete")
    public Response<Boolean> deleteDirectoryOrFile(Integer id, @CurrentUser UserInfo userInfo) {
        log.info("username: {} start to delete node: {}", userInfo.getUsername(), id);
        if (id == null) {
            Response.fail(PARAM_ILLEGAL.toBizCode());
        }
        try {
            return Response.success(workspaceService.deleteNode(id));
        } catch (Exception e) {
            log.error("delete node error, id=" + id, e);
            return Response.fail(WORKSPACE_DELETE_NODE_ERROR.toBizCode());
        }
    }


    @ApiOperation(value = "保存作业", notes = "save sql file content[id,sql cannot be empty]")
    @PostMapping(value = "/sql/save")
    public Response<Boolean> saveSql(@RequestBody WorkspaceRequestParam param, @CurrentUser UserInfo userInfo) {
        if (StringUtils.isEmpty(param.getSql()) || param.getId() == null) {
            Response.fail(PARAM_ILLEGAL.toBizCode());
        }
        try {
            param.setCreateBy(userInfo != null ? userInfo.getUsername() : "admin");
            boolean result = workspaceService.saveTWTaskInfo(param, userInfo.getUsername());
            return Response.success(result);
        } catch (Exception e) {
            log.error("save sql error param=" + JSON.toJSONString(param), e);
            return Response.fail(WORKSPACE_SAVE_SQL_ERROR.toBizCode());
        }

    }

    @ApiOperation(value = "启动作业", notes = "start job from workspace[id,sql cannot be empty]")
    @PostMapping(value = "/job/start")
    public Response<Object> startJob(@RequestBody WorkspaceRequestParam param, @CurrentUser UserInfo userInfo) {
        if (userInfo != null) {
            param.setCreateBy(userInfo.getUsername());
        }
        //首次执行才save
        Optional<WorkspaceTaskInfo> existsSaveSql = workspaceService.getLatestTask(param.getId()) ;
        if(!existsSaveSql.isPresent()){
            Response<Boolean> saveSQLResult = saveSql(param, userInfo);
            if (!saveSQLResult.isSuccess()) {
                return Response.fail(saveSQLResult.getCode(), saveSQLResult.getMsg());
            }
        }
        try {
            Object o = workspaceService.startJobFromWorkspace(param);
            return Response.success(o);
        } catch (InsightException e) {
            return Response.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("save sql error param=" + JSON.toJSONString(param), e);
            return Response.fail(WORKSPACE_START_JOB_ERROR.toBizCode());
        }
    }

    @ApiOperation(value = "获取作业文件当前sql", notes = "get latest sql for the related sql file")
    @PostMapping(value = "/sql/fetch")
    public Response<String> getSQLInfoByFileId(@RequestParam("fileId") Integer fileId) {
        try {
            return Response.success(workspaceService.getSQLInfoByFileId(fileId));
        } catch (Exception e) {
            log.error("get sql error fileId=" + fileId, e);
            return Response.fail(WORKSPACE_GET_FILE_SQL_ERROR.toBizCode());
        }
    }

    @ApiOperation(value = "获取当前作业文件历史sql", notes = "get history sql for the related sql file")
    @PostMapping(value = "/query/workspace/log")
    public Response<List<WorkspaceTaskLog>> queryWorkspaceLog(@RequestBody WorkspaceLogParam param, @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                                                    @RequestParam(value = "currentPage", required = false) Integer currentPage) {
        try {
            return Response.success(workspaceService.queryWorkspaceTaskLog(param, pageSize, currentPage));
        } catch (Exception e) {
            log.error("get history sql error:{}", param , e);
            return Response.fail(WORKSPACE_GET_HISTORY_SQL_ERROR.toBizCode());
        }
    }



}
