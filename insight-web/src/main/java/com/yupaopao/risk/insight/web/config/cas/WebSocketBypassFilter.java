package com.yupaopao.risk.insight.web.config.cas;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * WebSocket路径绕过过滤器
 * 用于绕过CAS认证，直接放行WebSocket请求
 */
public class WebSocketBypassFilter implements Filter {

    private static final Logger log = LoggerFactory.getLogger(WebSocketBypassFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("WebSocketBypassFilter initialized");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) 
            throws IOException, ServletException {
        
        if (!(request instanceof HttpServletRequest) || !(response instanceof HttpServletResponse)) {
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String uri = httpRequest.getRequestURI();
        String upgrade = httpRequest.getHeader("Upgrade");
        boolean isWebSocketPath = uri != null && uri.startsWith("/api/websocket");
        boolean isWebSocketUpgrade = "websocket".equalsIgnoreCase(upgrade);
        
        if (isWebSocketPath || isWebSocketUpgrade) {
            log.info("绕过CAS认证，直接放行WebSocket请求: {} {}", httpRequest.getMethod(), uri);
            
            // 设置CORS头，允许WebSocket连接
            httpResponse.setHeader("Access-Control-Allow-Origin", "*");
            httpResponse.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            httpResponse.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization, Upgrade, Connection, Sec-WebSocket-Key, Sec-WebSocket-Version, Sec-WebSocket-Protocol");
            
            // 直接跳过后续的CAS过滤器，继续处理请求
            chain.doFilter(request, response);
            return;
        }
        
        // 非WebSocket请求，继续正常的过滤器链
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        log.info("WebSocketBypassFilter destroyed");
    }
}
