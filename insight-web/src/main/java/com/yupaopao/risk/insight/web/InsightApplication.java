package com.yupaopao.risk.insight.web;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.yupaopao.framework.spring.boot.aries.annotation.EnableAriesConfiguration;
import com.yupaopao.framework.spring.boot.datasource.annotation.EnableDataSourceConfiguration;
import com.yupaopao.framework.spring.boot.kafka.annotation.EnableKafkaConfiguration;
import com.yupaopao.framework.spring.boot.redis.annotation.EnableRedisConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

@Slf4j
@SpringBootApplication(scanBasePackages = {"com.yupaopao.risk.insight", "com.yupaopao.operation.common.sdk"})
@EnableDataSourceConfiguration(value = {"middleware.db-v2.insight-parent", "middleware.db-v2.risk"})
@EnableApolloConfig({"application", "middleware.actuator2x-dubbo", "cas-config", "elasticsearch", "flink.es.audit", "middleware.riskoss"})
@EnableScheduling
@EnableWebSocket
@EnableRedisConfiguration({"middleware.redis.risk-engine","middleware.redis.risk","middleware.redis.risk-live"})
@EnableAriesConfiguration("d9udvoaoyjs")
@EnableKafkaConfiguration(producerTopics = {"RISK-TAG-NOTIFY", "risk_factor_operate", "RISK-INSIGHT-CEP-MATCH-RECORD", "RISK-INSIGHT-CUSTOM-TAG"})
public class InsightApplication {

    public static void main(String[] args) {
        ApplicationContext ctx = SpringApplication.run(InsightApplication.class);
        String profiles[] = ctx.getEnvironment().getActiveProfiles();
        String activeProfile = "";
        if (profiles != null && activeProfile.length() > 0) {
            activeProfile = profiles[0];
        }

        log.info("InsightApplication 启动成功, activeProfile = " + activeProfile);
    }
}
