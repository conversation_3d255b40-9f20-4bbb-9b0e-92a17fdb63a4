package com.yupaopao.risk.insight.web.support;

import com.yupaopao.risk.insight.service.beans.ExportData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-09-18 14:59
 * execl导出
 ***/
public class ExportUtil {

    public static void exportExcel(HttpServletResponse response, ExportData exportData) throws Exception {
        SXSSFWorkbook wb = new SXSSFWorkbook(100);
        Sheet sh = wb.createSheet();
        OutputStream outputStream = response.getOutputStream();
        if (CollectionUtils.isEmpty(exportData.getDataList()) || CollectionUtils.isEmpty(exportData.getHeaders())) {
            Row row = sh.createRow(0);
            row.createCell(0).setCellValue("无数据");
            wb.write(outputStream);
            outputStream.close();
            wb.dispose();
            return;
        }
        Row colRow = sh.createRow(0);
        int headerIndex = 0;
        for (String column : exportData.getHeaders()) {
            Cell cell = colRow.createCell(headerIndex++);
            cell.setCellValue(column);
        }
        int totalRows = exportData.getDataList().size();
        for (int rowNum = 0; rowNum < totalRows; rowNum++) {
            Row row = sh.createRow(rowNum + 1);
            int index = 0;
            for (String columnValue : exportData.getDataList().get(rowNum)) {
                Cell cell = row.createCell(index++);

                cell.setCellValue(StringUtils.isEmpty(columnValue) ? "" : columnValue);
            }

        }
        wb.write(outputStream);
        outputStream.close();
        wb.dispose();
    }
}
