package com.yupaopao.risk.insight.web.controller;

import com.yupaopao.platform.common.dto.Code;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.repository.model.TaskInfo;
import com.yupaopao.risk.insight.repository.model.VersionManager;
import com.yupaopao.risk.insight.service.VersionService;
import com.yupaopao.risk.insight.service.beans.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc VersionController
 * @date 2022/1/4
 */
@Api(tags="历史版本")
@Slf4j
@RestController
public class VersionController {
    @Autowired
    private VersionService versionService;

    @ApiOperation(value = "获取历史版本列表", notes = "获取历史版本列表")
    @PostMapping("/version/info")
    public Response<PageResult<VersionManager>> getVersionInfoList(@RequestBody(required = false) VersionManager versionManager,
        @RequestParam(value = "pageSize", required = false) Integer pageSize,
        @RequestParam(value = "currentPage", required = false) Integer currentPage) {
        if (pageSize == null || pageSize <= 0) {
            pageSize = 10;
        }
        if (currentPage == null || currentPage < 0) {
            currentPage = 0;
        }
        try {
            return Response.success(versionService.getVersionInfoList(versionManager, pageSize, currentPage));
        } catch (Exception e) {
            return Response.fail(Code.ERROR_PARAM.getCode(), e.getCause().getMessage());
        }
    }
}
