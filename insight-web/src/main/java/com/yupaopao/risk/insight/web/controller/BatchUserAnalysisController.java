package com.yupaopao.risk.insight.web.controller;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.platform.user.api.entity.UserInfoDTO;
import com.yupaopao.platform.user.auth.api.entity.response.UserAuthDataMaskingDTO;
import com.yupaopao.risk.insight.beans.BatchAnalysisDataCollect;
import com.yupaopao.risk.insight.beans.DeviceRiskResult;
import com.yupaopao.risk.insight.beans.PieVO;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.repository.risk.bean.GrayList;
import com.yupaopao.risk.insight.service.*;
import com.yupaopao.risk.insight.util.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = "用户分析")
@RequestMapping("/user")
@RestController
public class BatchUserAnalysisController {

    @Autowired
    UserDetailService userDetailService;

    @Autowired
    BatchBaseUserInfoAnalysisService batchBaseUserInfoAnalysisService;

    @Autowired
    private DeviceRiskAnalysisService deviceRiskAnalysisService;


    @Autowired
    SceneAnalysisService sceneAnalysisService;

    @Autowired
    private RiskOverviewService riskOverviewService;

    @Value("${risk.dispatcher.url}")
    private String riskUrl;

    @Value("${batch.analysis.export}")
    private String userExportConf;

    @Value("${apps}")
    private String appsList;

    @GetMapping("/apps/config")
    public Response getAppsConfig() {
        return Response.success(JSONObject.parseObject(appsList, List.class));
    }


    @GetMapping("/get/export/config")
    public Response getExportConfig() {
        List list = JSONObject.parseObject(userExportConf, List.class);
        return Response.success(list);
    }


    @ApiOperation(value = "批量抢单分析接口", notes = "batch realTime order analysis")
    @PostMapping("/batch/realTime/order/analysis")
    public Response<Object> realTimeOrderAnalysis(@RequestBody String request) {
        return Response.success(getResponse(request, "realTime"));
    }

    @ApiOperation(value = "批量定向单分析接口", notes = "batch realTime order analysis")
    @PostMapping("/batch/assign/analysis")
    public Response<Object> assignAnalysis(@RequestBody String request) {
        return Response.success(getResponse(request, "assign"));
    }

    @ApiOperation(value = "批量画像分析接口", notes = "batch realTime order analysis")
    @PostMapping("/batch/portrait/analysis")
    public Response<Object> portraitAnalysis(@RequestBody String request) {
        return Response.success(getResponse(request, "portrait"));
    }

    @ApiOperation(value = "批量用户风险概览", notes = "batch user risk overview")
    @PostMapping("/batch/risk-overview")
    public Response<Object> riskOverview(@RequestBody String request) {
        return Response.success(getResponse(request, "riskOverview"));
    }

    @ApiOperation(value = "用户风险概览-聚集明细", notes = "batch user risk overview")
    @PostMapping("/batch/risk-overview/cluster-details")
    public Response<Object> getUserRiskOverviewClusterDetail(@RequestBody String request) {
        return Response.success(riskOverviewService.getRiskOverviewClusterDetail(request));
    }

    @ApiOperation(value = "用户风险概览-同步标签任务标签映射关系", notes = "batch user risk overview")
    @GetMapping("/batch/risk-overview/synchroTrap-mapping")
    public Response<Map<String, String>> getSynchroTrapTagsMapping() {
        return Response.success(riskOverviewService.getSynchroTrapTagMapping());
    }

    @ApiOperation(value = "用户风险概览-同步标签任务标签映射关系", notes = "batch user risk overview")
    @PostMapping("/batch/risk-overview/synchrotrap-detail")
    public Response<List<Map<String, String>>> getSynchroTrapDetails(@RequestBody String request) {
        return Response.success(riskOverviewService.getSynchroTrapDetails(request));
    }


    private Map<String, Object> getResponse(String request, String type) {
        try {
            Map<String, Object> map = JSONObject.parseObject(request, Map.class);
            List uidList = (List) map.get("uidList");
            String queryEndTime = map.get("queryEndTime")!=null? map.get("queryEndTime").toString():
                    InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
            Map mapResult;
            if ("assign".equals(type)) {
                mapResult = sceneAnalysisService.batchAssignOrder(uidList, map.get("time") + "", queryEndTime);
            } else if ("realTime".equals(type)) {
                mapResult = sceneAnalysisService.batchRealTimeOrderAccept(uidList, map.get("time") + "", queryEndTime);
            } else if ("riskOverview".equals(type)) {
                mapResult = sceneAnalysisService.batchUserRiskOverview(uidList, map.get("time").toString(), queryEndTime);
            } else {
                mapResult = sceneAnalysisService.batchPortraitAnalysis(uidList, map.get("time") + "", Boolean.valueOf(map.get("sensitiveData") + ""));
            }
            Map<String, Object> result = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(mapResult)) {
                result.put("hasData", true);
                result.put("data", mapResult);
            } else {
                result.put("hasData", false);
            }
            return result;
        } catch (Exception e) {
            log.error("batch analysis error", e);
            throw e;
        }
    }

    @ApiOperation(value = "查询用户基本信息", notes = "batch query userInfo")
    @PostMapping("/batch/query")
    public Response<Object> batchQueryUserInfo(@RequestBody String request) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("riskUrl", riskUrl);
        BatchAnalysisDataCollect batchAnalysisDataCollect = new BatchAnalysisDataCollect();
        try {
            List<UserInfoDTO> userInfoList = new ArrayList();
            QueryUID queryUID = parseBatchId(request, batchAnalysisDataCollect);
            Map<String, List> userInfo = userDetailService.getUserInfo(queryUID.getShowNoList(), queryUID.getUidList(), queryUID.getMobileList(), queryUID.getAppCode());
            userInfoList.addAll(userInfo.get("userInfoList"));
            setUidList(userInfoList, batchAnalysisDataCollect);
            batchAnalysisDataCollect.getUnRegisteredList().addAll(userInfo.get("unRegisterList"));
            List uidList = batchAnalysisDataCollect.getUidList();
            //大神
            batchAnalysisDataCollect.setBatchBiggie(userDetailService.getBatchBiggie(uidList));
            //冻结信息
            batchAnalysisDataCollect.setAccountFreezeInfo(userDetailService.getAccountFreezeInfo(uidList));
            //比心鱼饵 等级
//            batchAnalysisDataCollect.setAllExpsByUid(userDetailService.getAllExpsByUid(uidList));
            batchAnalysisDataCollect.setAllSceneMemberships(userDetailService.getSceneMembership(uidList));
            //获取用户手机号
            batchAnalysisDataCollect.setMobiles(userDetailService.getMobileInfoByUserId(uidList));
            batchAnalysisDataCollect.setMobilePortrait(batchBaseUserInfoAnalysisService.mobilePortrait(batchAnalysisDataCollect.getMobiles()));
            //黑名单
            batchAnalysisDataCollect.setBatchBlackGray(userDetailService.getBatchBlackGray(batchAnalysisDataCollect.getMobiles()));
            //实名信息
            batchAnalysisDataCollect.setAuthInfo(userDetailService.queryAuthBatch(queryUID.getAppCode(), uidList));

            batchAnalysisDataCollect.setDeviceRiskResult(deviceRiskAnalysisService.analysis(uidList,
                    batchAnalysisDataCollect.getQueryEndTime()));

            batchAnalysisDataCollect.setLiveRoom(userDetailService.batchQueryYuerLive(uidList));

            result.put("userCount", userInfoList.size());
            result.put("invalidUserCount", batchAnalysisDataCollect.getUnRegisteredList().size());

            result.put("detail", buildDetail(sortUserInfo(batchAnalysisDataCollect, userInfo), batchAnalysisDataCollect));
        } catch (Exception e) {
            log.error("系统错误:", e);
        }
        setResult(result);
        return Response.success(result);
    }


    private List<Object> sortUserInfo(BatchAnalysisDataCollect batchAnalysisDataCollect, Map<String, List> userInfo) {

        List resultList = new ArrayList();
        List sourceUidList = batchAnalysisDataCollect.getSourceUid();
        for (Object o : sourceUidList) {
            resultList.add(getUserInfo(o, userInfo, batchAnalysisDataCollect));
        }
        return resultList;
    }

    private Object getUserInfo(Object id, Map<String, List> userInfo, BatchAnalysisDataCollect batchAnalysisDataCollect) {
        List userInfoList = userInfo.get("userInfoList");
        List unRegisterList = userInfo.get("unRegisterList");
        if (!CollectionUtils.isEmpty(unRegisterList) && unRegisterList.contains(id)) {
            return id;
        }
        UserInfoDTO userInfoDTO = getUserInfoDTO(id, userInfoList);
        if (Objects.nonNull(userInfoDTO)) return userInfoDTO;
        Long uid = getUidByMobile(id, batchAnalysisDataCollect);
        if (Objects.nonNull(uid)) {
            return getUserInfoDTO(uid.toString(), userInfoList);
        }
        return id;
    }

    private UserInfoDTO getUserInfoDTO(Object id, List userInfoList) {
        for (Object o : userInfoList) {
            UserInfoDTO userInfoDTO = (UserInfoDTO) o;
            if (id.equals(userInfoDTO.getShowNo() + "") || id.equals(userInfoDTO.getUid() + "")) {
                return userInfoDTO;
            }
        }
        return null;
    }


    private Long getUidByMobile(Object mobileNo, BatchAnalysisDataCollect batchAnalysisDataCollect) {
        List<UserAuthDataMaskingDTO> mobiles = batchAnalysisDataCollect.getMobiles();
        if (CollectionUtils.isEmpty(mobiles)) return null;
        for (UserAuthDataMaskingDTO mobile : mobiles) {
            if (mobile.getMobile().equals(mobileNo)) {
                return mobile.getUid();
            }
        }
        return null;
    }


    @Data
    class QueryUID {
        private List uidList = new ArrayList();
        private List showNoList = new ArrayList();
        private List mobileList = new ArrayList();
        private Integer appCode = 10;
    }

    // 创建 Pattern 对象
    Pattern reg = Pattern.compile("^[0-9]*$");
    Pattern regShowNo = Pattern.compile("^[1-9]\\d{0,8}$");

    private QueryUID parseBatchId(String request, BatchAnalysisDataCollect batchAnalysisDataCollect) {
        JSONObject jsonObject = JSONObject.parseObject(request);
        String[] uidSplit = jsonObject.getString("request").split("\n");
        String queryTime = jsonObject.getString("time");
        String queryType = jsonObject.getString("type");
        Integer appCode = jsonObject.containsKey("appCode") ? jsonObject.getInteger("appCode") : 10;
        String sensitiveData = jsonObject.getString("sensitiveData");
        batchAnalysisDataCollect.setTime(queryTime);
        batchAnalysisDataCollect.setSensitiveData(sensitiveData);
        batchAnalysisDataCollect.setRiskType(jsonObject.getString("riskType"));
        String queryEndTime = jsonObject.containsKey("queryEndTime") ? jsonObject.getString("queryEndTime") :
                InsightDateUtils.getDateStr(new Date(), InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        batchAnalysisDataCollect.setQueryEndTime(queryEndTime);

        if (Objects.isNull(uidSplit) || uidSplit.length <= 0) {
            return null;
        }
        QueryUID queryUID = new QueryUID();
        queryUID.setAppCode(appCode);
        // 现在创建 matcher 对象
        List sourceList = new ArrayList();
        for (String id : uidSplit) {
            if (null == id || StringUtils.isEmpty(id.trim())) {
                continue;
            }
            id = id.trim();
            if (id.contains("\t")) {
                String[] split = id.split("\t");
                id = split[0];
            }
            switch (queryType) {
                case "ALL":
                    Integer len = id.length();
                    if (regShowNo.matcher(id).matches()) {
                        queryUID.getShowNoList().add(id);
                    } else if ((len < 11 && len > 6) || (len > 11 && len < 16) || (len == 11 && reg.matcher(id).matches() && id.startsWith("1"))) {
                        queryUID.getMobileList().add(id);
                    } else if (len > 15 && reg.matcher(id).matches()) {
                        queryUID.getUidList().add(id);
                    }
                    break;
                case "UID":
                    queryUID.getUidList().add(id);
                    break;
                case "MOBILENO":
                    queryUID.getMobileList().add(id);
                    break;
                case "SHOWNO":
                    queryUID.getShowNoList().add(id);
                    break;
            }

            sourceList.add(id);
        }
        batchAnalysisDataCollect.setSourceUid(sourceList);
        return queryUID;
    }


    @ApiOperation(value = "批量用户分析接口", notes = "batch analysis")
    @PostMapping("/batch/analysis")
    public Response<Object> batchAnalysis(@RequestBody String request) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("riskUrl", riskUrl);
        BatchAnalysisDataCollect batchAnalysisDataCollect = new BatchAnalysisDataCollect();
        try {
            List<UserInfoDTO> userInfoList = new ArrayList();
            QueryUID queryUID = parseBatchId(request, batchAnalysisDataCollect);
            Map<String, List> userInfo = userDetailService.getUserInfo(queryUID.getShowNoList(), queryUID.getUidList(), queryUID.getMobileList(), queryUID.getAppCode());
            userInfoList.addAll(userInfo.get("userInfoList"));
            setUidList(userInfoList, batchAnalysisDataCollect);
            batchAnalysisDataCollect.getUnRegisteredList().addAll(userInfo.get("unRegisterList"));
            if (!CollectionUtils.isEmpty(userInfoList)) {
                setUidList(userInfoList, batchAnalysisDataCollect);
                Map<String, Object> analysisMap = batchBaseUserInfoAnalysisService.baseAnalysis(batchAnalysisDataCollect, queryUID.getAppCode());
                result.putAll(analysisMap);
            }
            result.put("userCount", userInfoList.size());
            result.put("invalidUserCount", batchAnalysisDataCollect.getUnRegisteredList().size());
            result.put("detail", buildDetail(sortUserInfo(batchAnalysisDataCollect, userInfo), batchAnalysisDataCollect));
            result.put("blackGrayCount", batchAnalysisDataCollect.getBlackListCount());
        } catch (Exception e) {
            log.error("系统错误:", e);
        }
        setResult(result);
        return Response.success(result);
    }

    public List<Object> buildDetail(List<Object> userInfoList, BatchAnalysisDataCollect batchAnalysisDataCollect) {
        List<Object> detailList = new ArrayList<>();
        for (Object userInfoDTO : userInfoList) {
            if (userInfoDTO instanceof UserInfoDTO) {
                UserInfoDTO user = (UserInfoDTO) userInfoDTO;
                Long uid = user.getUid();
                Map<String, Object> record = Maps.newHashMap();
                Map<String, Object> userInfo = buildUserInfo(batchAnalysisDataCollect, user);
                record.put("userInfo", userInfo);
                Map<String, Object> commonInfo = buildCommonInfo(batchAnalysisDataCollect, uid, userInfo.get("mobileNo") + "");
                record.put("commonInfo", commonInfo);

                if (Objects.nonNull(batchAnalysisDataCollect.getRegisterAnalysisResult())) {
                    Map<String, Object> registerResult = batchAnalysisDataCollect.getRegisterAnalysisResult().getLastRegisterRiskList().get(userInfo.get("mobileNo"));
                    removeMobile(batchAnalysisDataCollect, registerResult);
                    record.put("lastRegisterInfo", registerResult);
                }
                setActiveInfo(batchAnalysisDataCollect.getBatchActiveTop(), uid, record);
                setComplainInfo(batchAnalysisDataCollect.getComplainReasonTop(), uid, record);
                detailList.add(record);
            } else {
                Map<String, Object> record = Maps.newHashMap();
                Map<String, Object> userInfo = Maps.newHashMap();
                userInfo.put("uid", userInfoDTO);
                userInfo.put("remark", "该用户不存在");
                record.put("userInfo", userInfo);
                detailList.add(record);
            }
        }
        return detailList;
    }

    public void removeMobile(BatchAnalysisDataCollect batchAnalysisDataCollect, Map<String, Object> registerResult) {
        if (StringUtils.isEmpty(batchAnalysisDataCollect.getSensitiveData()) || !Boolean.valueOf(batchAnalysisDataCollect.getSensitiveData())) {
            if (!MapUtils.isEmpty(registerResult)) {
                registerResult.remove("mobileNo");
            }
        }
    }


    public void setUidList(List<UserInfoDTO> userInfoList, BatchAnalysisDataCollect batchAnalysisDataCollect) {
        List uidList = new ArrayList();
        for (UserInfoDTO userInfoDTO : userInfoList) {
            uidList.add(userInfoDTO.getUid());
        }
        batchAnalysisDataCollect.setUidList(uidList);
    }


    private void setResult(Map<String, Object> result) {
        if (CollectionUtils.isEmpty(result)) {
            result.put("hasData", false);
            result.put("msg", "没有找到用户");
        } else {
            result.put("hasData", true);
        }
    }

    private void setComplainInfo(Map<String, Set<PieVO>> complainReasonTop, Long uid, Map<String, Object> record) {
        if (!CollectionUtils.isEmpty(complainReasonTop)) {
            Set<PieVO> pieVOS = complainReasonTop.get(uid + "");
            List<PieVO> list = new ArrayList<>();
            if (!CollectionUtils.isEmpty(pieVOS)) {
                getTopComplainInfo(pieVOS, list);
                record.put("complainInfoTop", list);
            }
        }
    }

    private void getTopComplainInfo(Set<PieVO> pieVOS, List<PieVO> list) {
        Integer count = 0;
        if (pieVOS.size() > 5) {
            for (PieVO pieVO : pieVOS) {
                if (count < 5) {
                    list.add(pieVO);
                    count++;
                } else {
                    break;
                }
            }
        } else {
            list.addAll(pieVOS);
        }
    }

    private void setActiveInfo(Map<String, List<PieVO>> batchActiveTop, Long uid, Map<String, Object> record) {
        if (!CollectionUtils.isEmpty(batchActiveTop)) {
            List<PieVO> pieVOS = batchActiveTop.get(uid + "");
            if (!CollectionUtils.isEmpty(pieVOS)) {
                record.put("activeInfoTop", batchActiveTop.get(uid + "").size() > 5
                        ? batchActiveTop.get(uid + "").subList(0, 5) : batchActiveTop.get(uid + ""));
            }
        }
    }

    private Map<String, Object> buildCommonInfo(BatchAnalysisDataCollect batchAnalysisDataCollect, Long uid, String mobileNo) {
        Map<String, Object> commonInfo = Maps.newHashMap();
        if (Objects.nonNull(batchAnalysisDataCollect.getLoginAnalysisResult())) {
            Map<Long, Map<String, Object>> lastLoginRiskList = batchAnalysisDataCollect.getLoginAnalysisResult().getLastLoginRiskList();
            Map<String, Object> loginMap = lastLoginRiskList.get(uid);
            removeMobile(batchAnalysisDataCollect, loginMap);
            commonInfo.put("lastLoginRisk", loginMap);
        }
        return commonInfo;
    }

    private Map<String, Object> buildUserInfo(BatchAnalysisDataCollect batchAnalysisDataCollect, UserInfoDTO userInfoDTO) {
        Map<String, Object> userInfo = Maps.newHashMap();
        DeviceRiskResult deviceRiskResult = batchAnalysisDataCollect.getDeviceRiskResult();
        Long uid = userInfoDTO.getUid();
//        Map<Long, List<JSONObject>> allExpsByUid = batchAnalysisDataCollect.getAllExpsByUid();
        Map<Long, List<Map<String, Object>>> allSceneMembersByUid = batchAnalysisDataCollect.getAllSceneMemberships();
        List<Map<String, Object>> userExperienceDTOS = allSceneMembersByUid.get(uid);
        userInfo.put("experienceList", userExperienceDTOS);
        userInfo.put("freezeInfo", batchAnalysisDataCollect.getAccountFreezeInfo().get(uid));
        if (Objects.nonNull(deviceRiskResult) && !CollectionUtils.isEmpty(deviceRiskResult.getCommonDeviceMap())) {
            Object devicePortrait = deviceRiskResult.getCommonDeviceMap().get(uid + "");
            userInfo.put("devicePortrait", Objects.isNull(devicePortrait) || devicePortrait.equals("0") ? null : devicePortrait);
        }
        userInfo.put("uid", uid + "");
        userInfo.put("userInfoDTO", userInfoDTO);
        Map<String, Object> biggieMap = batchAnalysisDataCollect.getBatchBiggie().get(uid);
        if (!CollectionUtils.isEmpty(biggieMap)) {
            userInfo.put("biggie", biggieMap.get("flag"));
            userInfo.put("biggieIcon", biggieMap.get("icon"));
        }
        if (Objects.nonNull(deviceRiskResult) && !CollectionUtils.isEmpty(deviceRiskResult.getCommonIPMap())) {
            userInfo.put("ip", deviceRiskResult.getCommonIPMap().get(uid + ""));
            userInfo.put("city", deviceRiskResult.getCommonCityMap().get(uid + ""));
        }
        String mobileNo = getMobileNo(batchAnalysisDataCollect.getMobiles(), uid);
        if (!StringUtils.isEmpty(batchAnalysisDataCollect.getSensitiveData()) && Boolean.valueOf(batchAnalysisDataCollect.getSensitiveData())) {
            userInfo.put("mobileNo", mobileNo);
        }
        Map<String, Object> mobilePortrait = batchAnalysisDataCollect.getMobilePortrait();
        String mobile = getMobile(batchAnalysisDataCollect.getMobiles(), uid);
        if (!CollectionUtils.isEmpty(mobilePortrait) && !StringUtils.isEmpty(mobile)) {
            userInfo.put("mobilePortrait", mobilePortrait.get(mobile));
        }

        List<JSONObject> blackList = isBlackList(batchAnalysisDataCollect.getBatchBlackGray(), uid, mobileNo);
        if (blackList.size() > 0) {
            batchAnalysisDataCollect.setBlackListCount(batchAnalysisDataCollect.getBlackListCount() + 1);
        }
        userInfo.put("blackList", blackList);
        //TODO 鱼饵主播
        userInfo.put("yuerAnchor", batchAnalysisDataCollect.getLiveRoom().containsKey(uid) ? true : false);
        userInfo.put("authInfo", getAuthInfo(batchAnalysisDataCollect, uid));
        userInfo.put("createAt", DateUtil.formatDate(userInfoDTO.getCreateTime(), DateUtil.YYYY_MM_DD_HH_MM_SS));
        log.info("build userInfo:{}", userInfo);
        return userInfo;
    }

    private Map<String, Object> getAuthInfo(BatchAnalysisDataCollect batchAnalysisDataCollect, Long uid) {
        List<Map<String, Object>> authInfo = batchAnalysisDataCollect.getAuthInfo();
        if (CollectionUtils.isEmpty(authInfo)) {
            return null;
        }
        for (Map<String, Object> map : authInfo) {
            if (uid.equals(map.get("uid"))) {
                return map;
            }
        }
        return null;
    }

    private String getMobileNo(List<UserAuthDataMaskingDTO> mobiles, Long uid) {
        for (UserAuthDataMaskingDTO mobile : mobiles) {
            if (uid.equals(mobile.getUid())) {
                return mobile.getNationCode() + " " + mobile.getMobile();
            }
        }
        return "";
    }

    private String getMobile(List<UserAuthDataMaskingDTO> mobiles, Long uid) {
        for (UserAuthDataMaskingDTO mobile : mobiles) {
            if (uid.equals(mobile.getUid())) {
                return mobile.getNationCode() + mobile.getMobile();
            }
        }
        return "";
    }

    private List<JSONObject> isBlackList(List<GrayList> batchBlackGray, Long uid, String mobileNo) {
        List<JSONObject> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(batchBlackGray)) {
            return list;
        }
        for (GrayList grayList : batchBlackGray) {
            if (mobileNo.contains(grayList.getUid()) || grayList.getUid().equals(uid + "")) {
                JSONObject newGrayList = JSONObject.parseObject(JSONObject.toJSONString(grayList));
                newGrayList.put("flag", "true");
                newGrayList.put("expireTime", coverDate(grayList.getExpireTime()));
                newGrayList.put("startTime", coverDate(grayList.getStartTime()));
                list.add(newGrayList);
            }
        }
        return list;
    }

    private String coverDate(Date date) {
        if (Objects.isNull(date)) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String newDate = sdf.format(date);
        return newDate;
    }

}
