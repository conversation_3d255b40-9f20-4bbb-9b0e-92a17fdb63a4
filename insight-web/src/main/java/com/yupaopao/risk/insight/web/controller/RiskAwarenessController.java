package com.yupaopao.risk.insight.web.controller;

import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.repository.model.SuspiciousInfo;
import com.yupaopao.risk.insight.repository.model.UserInfo;
import com.yupaopao.risk.insight.service.RiskAwarenessService;
import com.yupaopao.risk.insight.service.beans.PageResult;
import com.yupaopao.risk.insight.web.support.annotation.CurrentUser;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/****
 * zengxiangcai
 * 2022/4/12 4:45 PM
 ***/

@Api(tags = "风险态势感知")
@RestController
public class RiskAwarenessController {

    @Autowired
    private RiskAwarenessService riskAwarenessService;

    @GetMapping("/risk-awareness/suspicious-list")
    public Response<PageResult<SuspiciousInfo>> getAwarenessSuspiciousList() {
        return Response.success(riskAwarenessService.getSuspiciousList());

    }

    @PostMapping("/risk-awareness/list")
    public Response<Boolean> addList(@RequestBody SuspiciousInfo params,
                                     @CurrentUser UserInfo userInfo) {
        if (params != null && params.getId() != null) {
            return Response.success(riskAwarenessService.editSuspiciousList(addUserParam(userInfo, params)));
        } else {
            return Response.success(riskAwarenessService.addSuspiciousList(addUserParam(userInfo, params)));
        }

    }


    private SuspiciousInfo addUserParam(UserInfo userInfo, SuspiciousInfo request) {
        if (userInfo != null && StringUtils.isNotEmpty(userInfo.getUsername())) {
            request.setCreateBy(userInfo.getUsername());
            request.setUpdateBy(userInfo.getUsername());
        } else {
            request.setCreateBy("admin");
            request.setUpdateBy("admin");
        }
        return request;
    }


}
