package com.yupaopao.risk.insight.web.config.cas;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统一打印 WebSocket/CAS 相关请求的关键信息，用于定位谁拦截了握手
 */
public class CasDebugLoggingFilter implements Filter {

    private static final Logger log = LoggerFactory.getLogger(CasDebugLoggingFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // no-op
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        if (!(request instanceof HttpServletRequest) || !(response instanceof HttpServletResponse)) {
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest req = (HttpServletRequest) request;
        StatusCaptureResponseWrapper resp = new StatusCaptureResponseWrapper((HttpServletResponse) response);

        String uri = req.getRequestURI();
        String upgrade = req.getHeader("Upgrade");
        String connection = req.getHeader("Connection");
        String secKey = req.getHeader("Sec-WebSocket-Key");
        String method = req.getMethod();

        boolean isWsPath = uri != null && uri.startsWith("/api/websocket");
        boolean isWsHandshake = upgrade != null && "websocket".equalsIgnoreCase(upgrade);
        boolean isSockJs = uri != null && uri.startsWith("/sockjs-node");
        long start = System.currentTimeMillis();

        if (isWsPath || isWsHandshake || isSockJs) {
            log.info("[CAS-WS] >>> {} {} | Upgrade={} Connection={} Sec-WebSocket-Key={} RemoteIP={}",
                    method, uri, upgrade, connection, secKey, req.getRemoteAddr());
        }

        try {
            chain.doFilter(req, resp);
        } finally {
            if (isWsPath || isWsHandshake || isSockJs) {
                long cost = System.currentTimeMillis() - start;
                String location = resp.getHeaderFirst("Location");
                log.info("[CAS-WS] <<< {} {} | status={} location={} cost={}ms", method, uri, resp.getStatus(), location, cost);
            }
        }
    }

    @Override
    public void destroy() {
        // no-op
    }

    /**
     * 包装响应以捕获状态码和关键头
     */
    static class StatusCaptureResponseWrapper extends HttpServletResponseWrapper {
        private int httpStatus = 200;
        private final Map<String, List<String>> headers = new HashMap<String, List<String>>();

        StatusCaptureResponseWrapper(HttpServletResponse response) {
            super(response);
        }

        @Override
        public void setStatus(int sc) {
            this.httpStatus = sc;
            super.setStatus(sc);
        }

        @Override
        public void sendError(int sc) throws IOException {
            this.httpStatus = sc;
            super.sendError(sc);
        }

        @Override
        public void sendError(int sc, String msg) throws IOException {
            this.httpStatus = sc;
            super.sendError(sc, msg);
        }

        @Override
        public void sendRedirect(String location) throws IOException {
            this.httpStatus = HttpServletResponse.SC_FOUND;
            setHeader("Location", location);
            super.sendRedirect(location);
        }

        @Override
        public void setHeader(String name, String value) {
            List<String> list = new ArrayList<String>();
            list.add(value);
            headers.put(name, list);
            super.setHeader(name, value);
        }

        @Override
        public void addHeader(String name, String value) {
            List<String> list = headers.get(name);
            if (list == null) {
                list = new ArrayList<String>();
                headers.put(name, list);
            }
            list.add(value);
            super.addHeader(name, value);
        }

        public int getStatus() {
            return httpStatus;
        }

        String getHeaderFirst(String name) {
            List<String> list = headers.get(name);
            return (list != null && !list.isEmpty()) ? list.get(0) : null;
        }
    }
}


