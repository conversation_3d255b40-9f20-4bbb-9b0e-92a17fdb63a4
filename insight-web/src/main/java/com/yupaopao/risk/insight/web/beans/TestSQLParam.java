package com.yupaopao.risk.insight.web.beans;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-06 16:23
 *
 ***/

@Getter
@Setter
public class TestSQLParam {
    @ApiModelProperty("测试sql")
    private String sql;

    @ApiModelProperty("测试数据文件对应的表名，文件列表顺序一致，如果没有改参数直接用文件名作为表名")
    private List<String> tables; //表名列表

    @ApiModelProperty("测试数据文件")
    private List<MultipartFile> files;
}
