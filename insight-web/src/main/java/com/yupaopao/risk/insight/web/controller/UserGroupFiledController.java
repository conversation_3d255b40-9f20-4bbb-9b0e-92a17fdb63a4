package com.yupaopao.risk.insight.web.controller;

import com.yupaopao.platform.common.dto.Code;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.repository.model.TUserGroupFieldConf;
import com.yupaopao.risk.insight.repository.model.UserInfo;
import com.yupaopao.risk.insight.service.UserGroupConfService;
import com.yupaopao.risk.insight.web.support.annotation.CurrentUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Api(tags = "字段配置接口")
@Slf4j
@RestController
public class UserGroupFiledController {


    @Autowired
    private UserGroupConfService userGroupConfService;


    @ApiOperation(value = "新增/更新字段", notes = "新增/更新字段")
    @PostMapping("/gui/conf/field/upsert")
    public Response<Boolean> upsertConfField(@RequestBody TUserGroupFieldConf conf, @CurrentUser UserInfo userInfo) {
        if (conf == null) {
            return Response.fail(Code.ERROR_PARAM);
        }
        boolean result;
        conf.setCreateBy(userInfo == null ? "admin" : userInfo.getUsername());
        try {
            if (Objects.isNull(conf.getId())){
                result = userGroupConfService.addField(conf);
            }else {
                result = userGroupConfService.updateField(conf);
            }
            return Response.success(result);
        } catch (Exception e) {
            return Response.fail(Code.ERROR_PARAM.getCode(), e.getCause().getMessage());
        }
    }

    @ApiOperation(value = "查询支持字段", notes = "查询支持字段")
    @PostMapping("/gui/conf/field/query")
    public Response queryConfField(@RequestBody TUserGroupFieldConf conf , @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                         @RequestParam(value = "currentPage", required = false) Integer currentPage) {
        if (conf == null) {
            return Response.fail(Code.ERROR_PARAM);
        }
        try {
            return Response.success(userGroupConfService.getFieldList(conf,pageSize, currentPage));
        } catch (Exception e) {
            return Response.fail(Code.ERROR_PARAM.getCode(), e.getCause().getMessage());
        }
    }


}
