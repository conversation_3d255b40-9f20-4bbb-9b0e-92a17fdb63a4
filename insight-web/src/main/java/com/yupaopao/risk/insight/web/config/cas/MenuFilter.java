package com.yupaopao.risk.insight.web.config.cas;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.yupaopao.platform.common.dto.Response;
import org.springframework.context.ApplicationContext;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;

public class MenuFilter implements Filter {

    private UserDetailsService userDetailsService;
    private String siteId;
    private List<String> cacheList = new ArrayList<>();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        ApplicationContext context = WebApplicationContextUtils
                .getWebApplicationContext(filterConfig.getServletContext());
        Config config = ConfigService.getAppConfig();
        siteId = config.getProperty("authority.siteId", "");
        String passUrl = config.getProperty("authority.pass.url", "/csrf,/v2/api-docs");
        cacheList = Arrays.asList(passUrl.split(","));
        userDetailsService = context.getBean(UserDetailsService.class);


    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {


//        permissionService.getPermissionsByUserIdAndSiteId(userName, siteId)


        String servletPath = ((HttpServletRequest) request).getServletPath();
        if ("/".equals(servletPath) || servletPath.contains("swagger") || cacheList.contains(servletPath)){
            chain.doFilter(request, response);
        }else {
            String userName = ((HttpServletRequest)request).getUserPrincipal().getName();
            UserDetails userDetails = userDetailsService.loadUserByUsername(userName);
            Collection<? extends GrantedAuthority> authorities = userDetails.getAuthorities();
            Iterator<? extends GrantedAuthority> iterator = authorities.iterator();
            while (iterator.hasNext()) {
                GrantedAuthority next = iterator.next();
                if (next.getAuthority().endsWith("/*")) {
                    if (servletPath.startsWith(next.getAuthority().substring(0,next.getAuthority().lastIndexOf("/*")))) {
                        chain.doFilter(request, response);
                        return;
                    }
                }else {
                    if (next.getAuthority().equals(servletPath)) {
                        chain.doFilter(request, response);
                        return;
                    }
                }

            }
            HttpServletResponse res = (HttpServletResponse) response;
            res.setStatus(HttpServletResponse.SC_FORBIDDEN);
            res.setCharacterEncoding("UTF-8");
            PrintWriter out = res.getWriter();
            out.write(JSON.toJSONString(Response.fail("99","您没有权限,请联系管理员")));
            out.flush();
            out.close();
        }


    }

    @Override
    public void destroy() {

    }
}
