<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.yupaopao.risk.insight</groupId>
        <artifactId>risk-insight</artifactId>
        <version>1.0.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yupaopao.risk.insight</groupId>
    <artifactId>insight-web</artifactId>
    <version>1.0.4</version>
    <packaging>jar</packaging>
    <name>insight-web</name>

    <properties>
        <cas.client.version>3.5.0</cas.client.version>
        <commons-csv.version>1.7</commons-csv.version>
        <apache.poi.version>5.2.3</apache.poi.version>
        <apache.poi-ooxml-schemas.version>4.1.2</apache.poi-ooxml-schemas.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yupaopao.risk.insight</groupId>
            <artifactId>insight-service</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao</groupId>
            <artifactId>authority-api</artifactId>
            <version>1.5.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>cat-web-monitor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>jboss</groupId>
                    <artifactId>jboss-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!-- swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <!-- swagger-ui -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-web</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jasig.cas.client</groupId>
            <artifactId>cas-client-core</artifactId>
            <version>${cas.client.version}</version>
        </dependency>

        <!--  csv -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>${commons-csv.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${apache.poi.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.apache.poi</groupId>-->
<!--            <artifactId>poi-ooxml-schemas</artifactId>-->
<!--            <version>${apache.poi-ooxml-schemas.version}</version>-->
<!--        </dependency>-->
        <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${apache.poi.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>user-api</artifactId>
            <version>cube-1.4.0</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>risk-insight</finalName>
        <plugins>
            <plugin>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.0.8.RELEASE</version>
                <configuration>
                    <mainClass>com.yupaopao.risk.insight.web.InsightApplication</mainClass>
                    <outputDirectory>${outputDirectory}</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>
