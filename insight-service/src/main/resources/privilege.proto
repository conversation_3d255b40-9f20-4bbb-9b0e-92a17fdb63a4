syntax = "proto3";

package protos;
option go_package = "api/grpc/protos;protos";
option java_package = "protos";


service Privilege{
  	rpc GetUserPrivilegeAll(GetUserPrivilegeAllParam) returns (GetUserPrivilegeAllReply) {}
    rpc GetUserPrivilegeTree(GetUserPrivilegeTreeParam) returns (GetUserPrivilegeTreeReply) {}
}

message GetUserPrivilegeAllParam{
  	string domain = 1; //域控
  	string siteKey = 2; // 站点唯一键
}
message GetUserPrivilegeAllReply{
  message Api{
    string api_key = 1; // 接口唯一键
    string name = 2;  //接口名称
    string url = 3; //地址
    string method = 4; //方法
  }
    message Privilege{
        string privilege_key = 1; //权限唯一健
        string name = 2;  //名称
        string parent_key = 3; //父健
        string category = 4; //权限类
        string shape = 5; //权限型
        string point = 6; //权限点
        string type = 7; //权限类型
        string icon = 8; //图标
        int32 sort_value = 9; //权重
        repeated Api api = 10; 
  }
  repeated Privilege privilege = 1;
}
message GetUserPrivilegeTreeParam{
  string domain = 1; //域控
  string siteKey = 2; // 站点唯一键
}

message GetUserPrivilegeTreeReply{
message Api{
  string api_key = 1; // 接口唯一键
  string name = 2;  //接口名称
  string url = 3; //地址
  string method = 4; //方法
}
  message Privilege{
    string privilege_key = 1; //权限唯一健
    string name = 2;  //名称
    string parent_key = 3; //父健
    string category = 4; //权限类
    string shape = 5; //权限型
    string point = 6; //权限点
    string type = 7; //权限类型
    string icon = 8; //图标
    int32 sort_value = 9; //权重
    repeated Api api = 10;
    repeated Privilege children = 11;

}
repeated Privilege privilege = 1;
}
