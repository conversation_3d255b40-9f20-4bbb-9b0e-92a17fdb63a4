<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="config.properties"/>

    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>

        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="tk.mybatis.mapper.common.Mapper"/>
        </plugin>

        <jdbcConnection driverClass="${jdbc.driverClass}"
                        connectionURL="${jdbc.url}"
                        userId="${jdbc.username}"
                        password="${jdbc.password}">
        </jdbcConnection>

        <!-- model类 package 及路径 -->
        <javaModelGenerator targetPackage="com.yupaopao.risk.insight.repository.model" targetProject="src/main/java"/>

        <!-- xml文件路径 -->
        <sqlMapGenerator targetPackage="com/yupaopao/risk/insight/service/mapper" targetProject="src/main/resources"/>

        <!-- mapper接口 package 及路径 -->
        <javaClientGenerator targetPackage="com.yupaopao.risk.insight.repository.mapper" targetProject="src/main/java"
                             type="XMLMAPPER"/>

        <table tableName="t_user_group_conf" domainObjectName="TUserGroupVO">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>



    </context>
</generatorConfiguration>