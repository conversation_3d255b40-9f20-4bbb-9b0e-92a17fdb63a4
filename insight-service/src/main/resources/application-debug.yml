# 调试功能配置
debug:
  # 是否启用调试功能
  enabled: true
  
  # 最大并发调试会话数
  max-concurrent-sessions: 5
  
  # 数据采样配置
  data-sampling:
    max-input-records: 1000              # 最大输入记录数
    sample-size: 10                      # 数据样本大小
    enable-data-collection: true         # 是否启用数据收集
  
  # WebSocket配置
  websocket:
    heartbeat-interval: 30               # 心跳间隔（秒）
    max-connections-per-session: 5       # 每个会话最大连接数
    message-buffer-size: 1024            # 消息缓冲区大小
  
  # 性能监控配置
  monitoring:
    enable-metrics: true                 # 启用性能指标收集
    metrics-interval: 5                  # 指标收集间隔（秒）

# Spring Boot配置
spring:
  profiles:
    include: debug
  
  # WebSocket配置
  websocket:
    enabled: true

# 日志配置
logging:
  level:
    com.yupaopao.risk.insight.service.debug: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
