package com.yupaopao.risk.insight.flink.job.sql;

import com.yupaopao.risk.insight.flink.connector.ts.sink.TsBatchTableSink;
import com.yupaopao.risk.insight.flink.connector.ts.source.TsBatchTableSource;
import com.yupaopao.risk.insight.flink.constants.ParamConstants;
import com.yupaopao.risk.insight.flink.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.flink.meta.JobParams;
import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.flink.property.PropertiesFactory;
import com.yupaopao.risk.insight.common.property.connection.TsProperties;
import com.yupaopao.risk.insight.flink.property.enums.PropertyType;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.api.java.BatchTableEnvironment;

public class SqlMainJob {

    /***
     *  批处理查询table store 入口
     * @param args
     * @throws Exception
     */
    public static void main(String[] args) throws Exception {

        //1、getEnvironment
        final ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();

        //2、getTableEnvironment
        BatchTableEnvironment tableEnv = BatchTableEnvironment.create(env);

        //job params
        JobParams jobParams = new JobParams();
        jobParams.setJobName("${jobName}");
        jobParams.setSql("${sql}");
        jobParams.setSqlTables("${sqlTables}");
        jobParams.setOwner("${owner}");
        jobParams.setRandomJobId("${randomJobId}");
##        jobParams.setJobName("test-02");
##        jobParams.setSql("select a.riskaction_eventcode,count(*)" +
##                " from risk_hit_log a group by a.riskaction_eventcode");
##        jobParams.setSqlTables("risk_hit_log");
##        jobParams.setOwner("zengxiangcai");




        // 3、register source
        registerTableSource(tableEnv,jobParams);

        // 4、 sql process
        Table resultTable = tableEnv.sqlQuery(jobParams.getSql());

        //5、register sink
        String sinkName = registerTableSink(tableEnv,resultTable.getSchema());


        //6、write result table into sink
        resultTable.insertInto(sinkName);

        //7、 start execute job
        //set global parameter
        Configuration configuration = new Configuration();
        configuration.setString(ParamConstants.CURRENT_JOB_OWNER,jobParams.getOwner());
        configuration.setString(ParamConstants.CURRENT_JOB_ID,jobParams.getRandomJobId());
        env.getConfig().setGlobalJobParameters(configuration);
        tableEnv.execute(jobParams.getJobName());
    }


    /***
     * 注册源表信息：
     * 涉及1)数据源的连接信息，2)数据据源的字段怎么和flink table字段映射(flink中的类型)
     * @param tableEnv
     */
    private static void registerTableSource(BatchTableEnvironment tableEnv,JobParams jobParams){

        //flink 表数据类型从外界输入
        String tsTableName = jobParams.getSqlTables(); //外界输入

        //从apollo 获取ts表的主键和其他属性列信息

        TsProperties tsProperties = (TsProperties) PropertiesFactory.loadProperty(PropertyType.TABLE_STORE);

        TsTableInfo tsInTable = new TsTableInfo().withTableName(tsTableName)
                .withTsProperties(tsProperties)
                .withPrimaryKeyType(ApolloProperties.getTsPrimaryKeyTypes(tsTableName))
                .withColumnNameType(ApolloProperties.getTsColumnNameTypes(tsTableName));

        TsBatchTableSource tsTableSource = new TsBatchTableSource(tsInTable);
        // register table source
        tableEnv.registerTableSource(tsTableName, tsTableSource);
        System.err.println("source registered");
    }

    /***
     * 注册结果表信息：
     * 涉及：1) 目标数据源的连接信息
     * @param tableEnv
     * @param tableSchema sql查询后产生的数据的tableSchema
     * @return sinkName
     */
    private static String registerTableSink(BatchTableEnvironment tableEnv, TableSchema tableSchema){
        //目前结果表约定为table中的 risk_analysis_result,后续有可要在定制
        String outTsTableName =  TsProperties.TEMP_RESULT_TABLE_NAME;
        System.err.println(tableSchema);

        TsProperties tsProperties = (TsProperties) PropertiesFactory.loadProperty(PropertyType.TABLE_STORE);


        TsTableInfo tsInTable = new TsTableInfo().withTableName(outTsTableName)
                .withTsProperties(tsProperties)
                .withPrimaryKeyType("pkJobId#STRING,pkUuid#STRING")
                ;

        // 6、define table sink
        TsBatchTableSink tsOutSink = new TsBatchTableSink(tsInTable, FlinkMetaInfo.fromTableSchema(tableSchema));
        // 7、register outTable
        tableEnv.registerTableSink(outTsTableName,tsOutSink);
        return outTsTableName;
    }



    /***
     * 结果回写
     * @param resultTable
     * @param sinkName
     */
    private void writeBackResult(Table resultTable, String sinkName){
        resultTable.insertInto(sinkName);
    }



}
