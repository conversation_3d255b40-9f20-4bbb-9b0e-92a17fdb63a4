package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc TaskInfo
 * @date 2020/9/24
 */
@Getter
@Setter
@Table(name ="t_task_history")
public class TaskHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "task_id")
    private Integer taskId;

    @Column(name = "task_status")
    private String taskStatus;

    @Column(name = "name")
    private String name;

    @Column(name = "flink_job_id")
    private String flinkJobId;

    @Column(name = "duration")
    private Long duration = 0L;

    @Column(name = "accumulators")
    private String accumulators = "";

    @Column(name = "is_cron")
    private int isCron;

    @Column(name = "is_debug")
    private int isDebug;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}
