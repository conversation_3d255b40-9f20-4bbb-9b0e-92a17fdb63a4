package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/****
 * zengxiangcai
 * 2022/4/12 6:36 PM
 ***/

@Getter
@Setter
@Table(name ="t_risk_awareness_suspicious_list")
public class SuspiciousInfo {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;

  @Column(name="list_name")
  private String listName;

  @Column(name="method_name")
  private String methodName;

  @Column(name="risk_types")
  private String riskTypes;

  @Column(name="features")
  private String features;

  @Column(name="remarks")
  private String remarks;

  @Column(name="analysis_method")
  private String analysisMethod;

  @Column(name="display_url")
  private String displayUrl;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "update_time")
  private Date updateTime;

  @Column(name = "create_by")
  private String createBy;


  @Column(name = "update_by")
  private String updateBy;

  @Column(name = "feature_charts_cfg")
  private String featureChartsCfg;

  @Column(name = "related_strategy")
  private String relatedStrategy;

}
