package com.yupaopao.risk.insight.service.analysis;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-08-20 15:17
 *
 ***/
public class ConstantsForBehavior {
   public static final String ASSIGN_ORDER = "ASSIGN-ORDER";
   public static final String FROZEN_USER = "FROZEN-USER";
   public static final String REALTIME_ORDER = "REALTIME-ORDER";
   public static final String USER_FOLLOW = "USER-FOLLOW";

   public static final Map<String, String> DISPLAY_NAME_MAPPING = new HashMap<>();

   static {
      DISPLAY_NAME_MAPPING.put(ASSIGN_ORDER, "刷单");
      DISPLAY_NAME_MAPPING.put(REALTIME_ORDER, "抢单");
      DISPLAY_NAME_MAPPING.put(USER_FOLLOW, "关注");
   }

   public static final List<String> communityNeedBehaviors = Arrays.asList(ASSIGN_ORDER, REALTIME_ORDER, USER_FOLLOW);
}
