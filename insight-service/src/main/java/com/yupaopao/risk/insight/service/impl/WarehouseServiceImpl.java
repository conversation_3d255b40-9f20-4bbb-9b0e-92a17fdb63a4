package com.yupaopao.risk.insight.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.insight.repository.mapper.WarehouseDetailMapper;
import com.yupaopao.risk.insight.repository.mapper.WarehouseMapper;
import com.yupaopao.risk.insight.repository.model.TableInfo;
import com.yupaopao.risk.insight.repository.model.WareHouseInfo;
import com.yupaopao.risk.insight.repository.model.WarehouseDetail;
import com.yupaopao.risk.insight.service.WarehouseService;
import com.yupaopao.risk.insight.service.beans.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WarehouseServiceImpl implements WarehouseService {

    @Autowired
    private WarehouseMapper warehouseMapper;

    @Autowired
    private WarehouseDetailMapper warehouseDetailMapper;

    @Override
    public PageResult<WareHouseInfo> getWarehouses(WareHouseInfo wareHouseInfo, Integer pageSize, Integer pageNumber) {
        String orderBy = " create_time desc ";
        PageHelper.startPage(pageNumber, pageSize, orderBy);

        Example search = new Example(TableInfo.class);
        Example.Criteria criteria = search.createCriteria();
        if (wareHouseInfo.getId() != null) {
            criteria.andEqualTo("id", wareHouseInfo.getId());
        }
        if (StringUtils.isNotBlank(wareHouseInfo.getName())) {
            criteria.andCondition("upper(name) like '" + "%" + wareHouseInfo.getName().toUpperCase() + "%'");
        }
        if (StringUtils.isNotBlank(wareHouseInfo.getType())) {
            criteria.andEqualTo("type", wareHouseInfo.getType());
        }
        List<WareHouseInfo> list = warehouseMapper.selectByExample(search);
        PageInfo page = new PageInfo<>(list);
        PageResult<WareHouseInfo> pageResult = new PageResult<>();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setDataList(list);
        return pageResult;
    }

    @Override
    public PageResult<WarehouseDetail> getWarehouseDetails(WarehouseDetail warehouseDetail, Integer pageSize, Integer pageNumber) {
        String orderBy = " create_time desc ";
        PageHelper.startPage(pageNumber, pageSize, orderBy);

        Example search = new Example(WarehouseDetail.class);
        Example.Criteria criteria = search.createCriteria();
        if (StringUtils.isNotBlank(warehouseDetail.getName())) {
            criteria.andCondition("upper(name) like '" + "%" + warehouseDetail.getName().toUpperCase() + "%'");
//            criteria.andLike("name", "%"+warehouseDetail.getName()+"%");
        }
        if (warehouseDetail.getWarehouseId() != null) {
            criteria.andEqualTo("warehouseId", warehouseDetail.getWarehouseId());
        }
        if (warehouseDetail.getId() != null) {
            criteria.andEqualTo("id", warehouseDetail.getId());
        }
        List<WarehouseDetail> list = warehouseDetailMapper.selectByExample(search);
        PageInfo page = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setDataList(list);
        return pageResult;
    }

    @Override
    public List<WareHouseInfo> getWarehouses() {
        return warehouseMapper.selectAll();
    }


    @Override
    public WareHouseInfo upsertWarehouse(WareHouseInfo wareHouseInfo) {
        if (wareHouseInfo.getId() == null) {
            warehouseMapper.insertSelective(wareHouseInfo);
        } else {
            warehouseMapper.updateByPrimaryKeySelective(wareHouseInfo);
        }
        return wareHouseInfo;
    }

    @Override
    public WarehouseDetail upsertWarehouseDetail(WarehouseDetail warehouseDetail) {
        if (warehouseDetail.getId() == null) {
            warehouseDetailMapper.insertSelective(warehouseDetail);
        } else {
            warehouseDetailMapper.updateByPrimaryKeySelective(warehouseDetail);
        }
        return warehouseDetail;
    }

    @Override
    public boolean upsertWarehouseDetails(List<WarehouseDetail> warehouseDetails) {
        Set<Integer> idList = new HashSet<>();
        warehouseDetails.forEach(p -> idList.add(p.getWarehouseId()));
        if (idList.size() != 1) {
            return false;
        }
        Example search = new Example(WarehouseDetail.class);
        Example.Criteria criteria = search.createCriteria();
        criteria.andEqualTo("warehouseId", idList.toArray()[0]);

        List<WarehouseDetail> result = warehouseDetailMapper.selectByExample(search);

        List<String> keyList = new ArrayList<>();
        result.forEach(p->keyList.add(p.getName()));
        for (WarehouseDetail warehouseDetail : warehouseDetails) {
            if (keyList.contains(warehouseDetail.getName())) {
                if (warehouseDetail.getId() == null) {
                    throw new RuntimeException("配置异常，存在同名key");
                }
                warehouseDetailMapper.updateByPrimaryKeySelective(warehouseDetail);
            } else {
                warehouseDetailMapper.insertSelective(warehouseDetail);
            }
        }
        return true;
    }

    @Override public boolean warehouseConnectTest(Integer warehouseId) {
        WareHouseInfo wareHouseInfo = warehouseMapper.selectByPrimaryKey(warehouseId);

        Example detailSearch = new Example(WarehouseDetail.class);
        Example.Criteria criteria = detailSearch.createCriteria();
        criteria.andEqualTo("warehouseId", warehouseId);
        List<WarehouseDetail> warehouseDetails = warehouseDetailMapper.selectByExample(detailSearch);

        if (warehouseDetails.size() == 0) {
            throw new IllegalArgumentException("can not find warehouse detail, please check warehouse config");
        }

        Map<String, String> collect = warehouseDetails.stream().collect(Collectors.toMap(WarehouseDetail::getName, WarehouseDetail::getValue));

        return checkConnection(wareHouseInfo.getType(), collect);
    }


    @Override
    public boolean deleteWarehouse(Integer warehouseId) {
        warehouseMapper.deleteByPrimaryKey(warehouseId);
        Example search = new Example(WarehouseDetail.class);
        Example.Criteria criteria = search.createCriteria();
        criteria.andEqualTo("warehouseId", warehouseId);
        warehouseDetailMapper.deleteByExample(search);
        return true;
    }

    @Override
    public boolean deleteWarehouseDetail(Integer detailId) {
        warehouseDetailMapper.deleteByPrimaryKey(detailId);
        return true;
    }

    private boolean checkConnection(String type, Map<String, String> confMap) {
        switch (type.toLowerCase()) {
            case "kafka":
            case "kafka_s":
                return checkKafkaConnection(confMap);
            case "hbase":
                return checkHbaseConnection(confMap);
            case "elasticsearch":
                return checkEsConnection(confMap);
            case "jdbc":
                return checkMysqlConnection(confMap, "jdbc:mysql://");
            case "clickhouse":
                return checkMysqlConnection(confMap, "jdbc:clickhouse://");
            case "dubbo":
                return checkDubboConnection(confMap);
            case "redis":
                return checkRedisConnection(confMap);
            default:
                throw new IllegalArgumentException("not support for this type: " + type);
        }
    }

    /**
     * kafka 连接测试
     * @param confMap 配置信息
     * @return
     */
    private static boolean checkKafkaConnection(Map<String, String> confMap) {
        String servers = confMap.get("servers");
        String groupId = confMap.get("groupId");
        String topic = confMap.get("topic");

        if (StringUtils.isEmpty(servers)) {
            throw new IllegalArgumentException("存在配置为空");
        } else {
            Socket socket = new Socket();
            String[] split = servers.split(":");
            try {
                socket.connect(new InetSocketAddress(split[0].trim(), Integer.parseInt(split[1].trim())), 10000);
                return socket.isConnected();
            } catch (IOException e) {
                throw new IllegalArgumentException("kafka 连接失败: " + e.getMessage());
            } finally {
                try {
                    socket.close();
                } catch (IOException e) {
                    log.error("关闭连接失败：", e);
                }
            }
        }
    }

    /**
     * hbase 连接测试
     * @param confMap 配置信息
     * @return
     */
    private static boolean checkHbaseConnection(Map<String, String> confMap) {
        String endpoint = confMap.get("endpoint");
        String username = confMap.get("username");
        String password = confMap.get("password");
        if (StringUtils.isEmpty(endpoint) || StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            throw new IllegalArgumentException("存在配置为空");
        } else {
            Socket socket = new Socket();
            String[] split = endpoint.split(":");
            try {
                socket.connect(new InetSocketAddress(split[0].trim(), Integer.parseInt(split[1].trim())), 10000);
                return socket.isConnected();
            } catch (IOException e) {
                throw new IllegalArgumentException("hbase 连接失败: " + e.getMessage());
            } finally {
                try {
                    socket.close();
                } catch (IOException e) {
                    log.error("关闭连接失败：", e);
                }
            }
        }
    }

    /**
     * es 连接测试
     * @param confMap 配置信息
     * @return
     */
    private static boolean checkEsConnection(Map<String, String> confMap) {
        String host = confMap.get("address");

        if (StringUtils.isEmpty(host)) {
            throw new IllegalArgumentException("存在配置为空");
        } else {
            String[] split = host.split(":");
            int port;
            if (split.length == 1) {
                port = 80;
            } else if (split.length != 2) {
                return false;
            } else {
                port = Integer.parseInt(split[1]);
            }
            return telnet(split[0], port);
        }
    }

    /**
     * mysql 连接测试
     * @param confMap 配置信息
     * @return
     */
    private static boolean checkMysqlConnection(Map<String, String> confMap, String pre) {
        String host = pre + confMap.get("host");
        String username = confMap.get("username");
        String password = confMap.get("password");
        String driver = "com.mysql.jdbc.Driver";
        if (StringUtils.isEmpty(host) || StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            throw new IllegalArgumentException("存在配置为空");
        } else {
            Connection connection = null;
            try {
                Class.forName(driver).newInstance();
                connection = DriverManager.getConnection(host, username, password);
                return true;
            } catch (Exception e) {
                throw new IllegalArgumentException("mysql 连接失败: " + e.getMessage());
            } finally {
                if (null != connection) {
                    try {
                        connection.close();
                    } catch (SQLException e) {
                        log.error("关闭mysql 连接失败：", e);;
                    }
                }
            }
        }
    }

    private static boolean checkDubboConnection(Map<String, String> confMap) {
        String host = confMap.get("address");
        String version = confMap.get("version");
        if (StringUtils.isEmpty(host) || StringUtils.isEmpty(version)) {
            throw new IllegalArgumentException("存在配置为空");
        } else {
            String[] split = host.split("//");
            if (split.length != 2) {
                return false;
            } else {
                String[] hostSplit = split[1].split(":");
                int port = 2181;
                if (hostSplit.length == 2) {
                    port = Integer.parseInt(hostSplit[1]);
                }
                return telnet(hostSplit[0], port);
            }
        }
    }

    private static boolean checkRedisConnection(Map<String, String> confMap) {
        String host = confMap.get("host");
        String port = confMap.get("port");
        if (StringUtils.isEmpty(host) || StringUtils.isEmpty(port)) {
            throw new IllegalArgumentException("存在配置为空");
        } else {
            return telnet(host, Integer.parseInt(port));
        }
    }

    private static boolean telnet(String hostname, int port){
        Socket socket = new Socket();
        boolean isConnected = false;
        try {
            socket.connect(new InetSocketAddress(hostname, port), 1000);
            isConnected = socket.isConnected();
        } catch (IOException e) {
            return false;
        }finally{
            try {
                socket.close();
            } catch (IOException e) {
                log.info("关闭 {} 端口失败", hostname);
            }
        }
        return isConnected;
    }
}
