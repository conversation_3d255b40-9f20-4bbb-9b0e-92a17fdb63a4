package com.yupaopao.risk.insight.job;

import com.yupaopao.aries.client.JobExecutionContext;
import com.yupaopao.aries.client.JobListener;
import com.yupaopao.framework.spring.boot.aries.annotation.AriesCronJobListener;
import com.yupaopao.risk.insight.repository.mapper.TaskHistoryMapper;
import com.yupaopao.risk.insight.repository.mapper.TaskInfoMapper;
import com.yupaopao.risk.insight.repository.model.TaskHistory;
import com.yupaopao.risk.insight.repository.model.TaskInfo;
import com.yupaopao.risk.insight.util.DingTalkUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@AriesCronJobListener
public class JobStatusCheckService implements JobListener {

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private TaskHistoryMapper taskHistoryMapper;

    @Value("${task.dingtalk.webhook}")
    private String webhook;

    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd mm:HH:ss");

    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        Example search = new Example(TaskInfo.class);
        Example.Criteria criteria = search.createCriteria();
        criteria.andEqualTo("taskStatus", 1);

        Example historySearch = new Example(TaskHistory.class);
        historySearch.setOrderByClause("create_time desc limit 1");

        List<TaskInfo> taskInfos = taskInfoMapper.selectByExample(search);

        taskInfos.forEach(p -> {
            Example.Criteria hisCriteria = historySearch.createCriteria();
            hisCriteria.andEqualTo("taskId", p.getId());
            List<TaskHistory> taskHistories = taskHistoryMapper.selectByExample(historySearch);
            if (taskHistories.size() > 0) {
                TaskHistory taskHistory = taskHistories.get(0);
                Date createTime = taskHistory.getCreateTime();
                if (System.currentTimeMillis() - createTime.getTime() > 24 * 60 * 60 * 1000) {
                    sendNotifyMsg(p, taskHistory, "任务运行异常，请检查Aries");
                }
            } else {
                if (System.currentTimeMillis() - p.getCreateTime().getTime() > 24 * 60 * 60 * 1000) {
                    sendNotifyMsg(p, null, "任务没有运行记录，请检查Aries");
                }
            }
        });
    }

    private void sendNotifyMsg(TaskInfo taskInfo, TaskHistory taskHistory, String message) {
        StringBuilder msg = new StringBuilder();
        msg.append("### 异常任务通知")
            .append("\n")
            .append("+ 任务名: ")
            .append(taskInfo.getName())
            .append("+ cron: ")
            .append(taskInfo.getCron())
            .append("\n")
            .append("+ 任务状态: ")
            .append(message)
            .append("\n")
        ;
        if (taskHistory != null) {
            msg.append("+ 上次运行时间: ")
                .append(simpleDateFormat.format(taskHistory.getCreateTime()))
                .append("\n")
            ;
        }
        String[] split = webhook.split(",");
        Arrays.stream(split).forEach(p-> DingTalkUtil.sendMarkdownMsg(p, msg.toString(), "任务通知"));
    }
}
