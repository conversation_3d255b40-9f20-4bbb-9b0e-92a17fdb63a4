package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-24 16:22
 * 工作台结构
 ***/

@Getter
@Setter
@Table(name = "t_workspace")
public class WorkspaceInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "parent_id")
    private Integer parentId;

    @Column(name = "name")
    private String name;

    @Column(name = "level")
    private Integer level;


    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "valid")
    private Integer valid; // 1: true, 0: false

    @Column(name = "leaf_node")
    private Integer leafNode;

    @Column(name = "type")
    private String type;
}
