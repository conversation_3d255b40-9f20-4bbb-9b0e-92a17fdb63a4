package com.yupaopao.risk.insight.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.repository.mapper.CommunityCombineLogMapper;
import com.yupaopao.risk.insight.repository.mapper.CommunityDetailMapper;
import com.yupaopao.risk.insight.repository.mapper.CommunityMapper;
import com.yupaopao.risk.insight.repository.model.CommunityCombineLog;
import com.yupaopao.risk.insight.repository.model.CommunityInfo;
import com.yupaopao.risk.insight.service.ClickHouseService;
import com.yupaopao.risk.insight.service.CommunityService;
import com.yupaopao.risk.insight.service.analysis.ConstantsForBehavior;
import com.yupaopao.risk.insight.service.beans.CommunityDispResponse;
import com.yupaopao.risk.insight.service.beans.CommunityQueryParam;
import com.yupaopao.risk.insight.service.beans.PageResult;
import com.yupaopao.risk.insight.util.ClickHouseUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ru.yandex.clickhouse.ClickHouseUtil;
import ru.yandex.clickhouse.response.ClickHouseResponse;

import java.util.*;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-31 16:18
 *
 ***/

@Slf4j
@Service
public class CommunityServiceImpl implements CommunityService {

    @Autowired
    private CommunityMapper communityMapper;

    @Autowired
    private CommunityDetailMapper communityDetailMapper;

    @Autowired
    private CommunityCombineLogMapper communityCombineLogMapper;

    @Autowired
    private ClickHouseService clickHouseService;

    @Override
    public List<String> getVertexCommunity(String vertex) {
        String sql = "select connectedLabel from community_daily where runDay = '%s' and vertexId ='%s'";
        List<Map<String,String>> response = clickHouseService.executeQuery(String.format(sql,
               InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd), vertex));
        return response.stream().map(elem->elem.get("connectedLabel")).collect(Collectors.toList());
    }


    @Override
    public PageResult<CommunityDispResponse> getCommunities(CommunityQueryParam params) {

        if (StringUtils.isEmpty(params.getCommunityLabel())) {
            //方便sql判断
            params.setCommunityLabel(null);
        }
        if (StringUtils.isEmpty(params.getName())) {
            //方便sql判断
            params.setName(null);
        }
        //分页查询
        PageHelper.startPage(params.getCurrentPage(), params.getPageSize());
        List<CommunityInfo> resultList = new ArrayList<>();
        if (StringUtils.isNotEmpty(params.getVertexId())) {
            //get from ck
            Map<String, Object> sqlParam = new HashMap<>();
            sqlParam.put("runDay", InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd));
            sqlParam.put("vertexId", params.getVertexId());
            String sql = "select connectedLabel from community_daily where runDay = '${runDay}' and vertexId = '${vertexId}' ";
            if (StringUtils.isNotEmpty(params.getCommunityLabel())) {
                sql += " and connectedLabel = '${connectedLabel}'";
                sqlParam.put("connectedLabel", params.getCommunityLabel());
            }
            StringSubstitutor sub = new StringSubstitutor(sqlParam);
            List<Map<String, String>> vertexLabelList = clickHouseService.executeQuery(sub.replace(sql));
            String vertexLabel = null;
            if (CollectionUtils.isNotEmpty(vertexLabelList)) {
                vertexLabel = vertexLabelList.get(0).get("connectedLabel");
                params.setCommunityLabel(vertexLabel);
                resultList = communityMapper.getCommunities(params);
            } else {
                resultList = new ArrayList<>();
            }

        } else {
            resultList = communityMapper.getCommunities(params);
        }

        PageInfo page = new PageInfo<>(resultList);
        PageResult<CommunityDispResponse> pageResult = new PageResult<>();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());

        Map<String, String> runDayMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(resultList)) {
            String joinLabels = "'" +
                    StringUtils.join(resultList.stream().map(elem -> elem.getCommunityLabel()).collect(Collectors.toList()),
                            "','") + "'";
            //社区经过合并后标签可能会改变，查询标签最晚发现的原始标记时间，方便和社区明细映射查询节点详情
            String sqlCreateTime = "select originalLabel,max(runDay) runDay from community_detail_daily where " +
                    "originalLabel in (%s) group by originalLabel";
            List<Map<String, String>> runDayMapList = clickHouseService.executeQuery(String.format(sqlCreateTime,
                    joinLabels));
            runDayMapList.stream().forEach(elem -> {
                runDayMap.put(elem.get("originalLabel"), elem.get("runDay"));
            });
        }


        List<CommunityDispResponse> responseList = resultList.stream().map(elem -> {
            CommunityDispResponse respElem = new CommunityDispResponse();
            respElem.setId(elem.getId());
            respElem.setName(elem.getName());
            respElem.setCommunityLabel(elem.getCommunityLabel());
            respElem.setVertexCount(elem.getVertexCount());
//            respElem.setRunDay(elem.getRunDay());
            respElem.setTags(elem.getTags());
            respElem.setRemark(elem.getRemark());
            String runDay = runDayMap.get(elem.getCommunityLabel());
            if (StringUtils.isNotBlank(runDay)) {
                respElem.setRunDay(runDay);
            }
            return respElem;
        }).collect(Collectors.toList());

        if (page.getTotal() > 0) {
            //设置机器打标

            List<String> labels = responseList.stream().map(elem -> elem.getCommunityLabel()).collect(Collectors.toList());
            //fromCk
            String labelJoins = "'" + StringUtils.join(labels, "','") + "'";

            String sql = "select connectedLabel,type,count(1) typeCount from community_behavior_detail t " +
                    "where t.connectedLabel in " +
                    "(" +
                    labelJoins +
                    ")\n and type!='FROZEN-USER' " +
                    "group by connectedLabel,type";
            List<Map<String, String>> ckResponse = clickHouseService.executeQuery(sql);
            Map<String, List<CommunityTag>> tagMap = ckResponse.stream().map(elem -> {
                String cLabel = elem.get("connectedLabel");
                String type = elem.get("type");
                return new CommunityTag(cLabel, type);
            }).collect(Collectors.groupingBy(CommunityTag::getCLabel));

            responseList.stream().forEach(elem -> {
                if (tagMap.containsKey(elem.getCommunityLabel())) {
                    String machineTags =
                            tagMap.get(elem.getCommunityLabel()).stream()
                                    .map(tagElem -> ConstantsForBehavior.DISPLAY_NAME_MAPPING.get(tagElem.getType()))
                                    .collect(Collectors.joining(","));
                    elem.setMachineTags(machineTags);
                }
            });
        }
        pageResult.setDataList(responseList);
        return pageResult;
    }

    @Override
    public List<CommunityCombineLog> getCommunityByOriginalLabels(List<String> originalLabels, String runDay) {

//
//        Example search = new Example(CommunityCombineLog.class);
//        Example.Criteria criteria = search.createCriteria();
//        criteria.andIn("originalLabel", originalLabels);
//        criteria.andEqualTo("runDay", runDay);
//
//        search.selectProperties("communityLabel", "originalLabel");
//        search.setDistinct(true);
//        List<CommunityCombineLog> communities = communityCombineLogMapper.selectByExample(search);
//        return communities;
        //从ck查询
        String joinLabels = "'" + StringUtils.join(originalLabels, "','") + "'";
        List<Map<String, String>> list = clickHouseService.executeQuery(String.format("select distinct connectedLabel," +
                "originalLabel from " +
                "community_detail_daily where originalLabel in (%s) and runDay = '%s'", joinLabels, runDay));
        return list.stream().map(elem -> {
            CommunityCombineLog log = new CommunityCombineLog();
            log.setCommunityLabel(elem.get("connectedLabel"));
            log.setOriginalLabel(elem.get("originalLabel"));
            return log;
        }).collect(Collectors.toList());

    }

    @Override
    public Boolean editCommunity(CommunityInfo params) {
        return communityMapper.editCommunity(params) > 0;
    }

    @Override
    public CommunityInfo analyze(CommunityInfo communityInfo) {
        return null;
    }

    @Override
    public List<Map<String, String>> getCommunitiesByRunDay(String runDay) {
        return clickHouseService.executeQuery(String.format("select distinct connectedLabel  " +
                "from community_detail_daily where runDay = '%s'",runDay));
    }

    @Override
    public List<Map<String, String>> getCommunityUserVertexes() {

        String runDay = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);

        return clickHouseService.executeQuery(String.format("select distinct vertexId," +
                "connectedLabel  from community_daily where runDay='%s'" +
                " and length(vertexId)<62", runDay));

    }

    @Override
    public boolean insertFrozenUser(List<String> frozenJsonList) {
        try {
            clickHouseService.executeUpdate(frozenJsonList, "community_behavior_detail");
        } catch (Exception e) {
            log.error("insert FrozenUser error: ", e);
            return false;
        }
        return true;
    }

    @Override
    public List<String> getAllManualTags() {
        List<String> allTags = communityMapper.getAllTags();
        if (CollectionUtils.isEmpty(allTags)) {
            return new ArrayList<>();
        }
        List<String> resultList = new ArrayList<>();
        allTags.stream().filter(elem -> StringUtils.isNotEmpty(elem)).forEach(elem -> resultList.addAll(Arrays.asList(elem.split(","))));
        return resultList.stream().distinct().collect(Collectors.toList());
    }


    @Data
    @AllArgsConstructor
    public static class CommunityTag {
        private String cLabel;
        private String type;
    }
}
