package com.yupaopao.risk.insight.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.insight.repository.mapper.TUserGroupFieldConfMapper;
import com.yupaopao.risk.insight.repository.model.TUserGroupFieldConf;
import com.yupaopao.risk.insight.service.UserGroupConfService;
import com.yupaopao.risk.insight.service.beans.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserGroupConfServiceImpl implements UserGroupConfService {

    @Autowired
    private TUserGroupFieldConfMapper tUserGroupFieldConfMapper;

    @Override
    public boolean addField(TUserGroupFieldConf conf) {
        tUserGroupFieldConfMapper.insert(conf);
        return tUserGroupFieldConfMapper.insert(conf)>0;
    }

    @Override
    public boolean updateField(TUserGroupFieldConf conf) {
        return tUserGroupFieldConfMapper.updateByPrimaryKeySelective(conf)>0;
    }

    @Override
    public boolean deleteField(TUserGroupFieldConf conf) {
        return tUserGroupFieldConfMapper.delete(conf)>0;
    }

    @Override
    public List<TUserGroupFieldConf> queryField(TUserGroupFieldConf conf) {
        return tUserGroupFieldConfMapper.select(conf);
    }

    @Override
    public PageResult<TUserGroupFieldConf> getFieldList(TUserGroupFieldConf conf, Integer pageSize, Integer pageNumber) {
        PageHelper.startPage(pageNumber, pageSize);
        List<TUserGroupFieldConf> select = tUserGroupFieldConfMapper.select(conf);
        PageInfo page = new PageInfo<>(select);
        PageResult<TUserGroupFieldConf> pageResult = new PageResult<>();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setDataList(select);
        return pageResult;
    }
}
