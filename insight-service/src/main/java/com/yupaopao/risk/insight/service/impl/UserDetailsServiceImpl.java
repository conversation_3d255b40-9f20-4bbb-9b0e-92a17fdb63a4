package com.yupaopao.risk.insight.service.impl;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yupaopao.risk.insight.dto.PrivilegeDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 获取用户权限
 * Created by marco on 2017/2/24.
 */
@Service("userDetailsService")
public class UserDetailsServiceImpl implements UserDetailsService {

    private final static String FORMAT_3 = "ROLE_%s_%s_%s";
    private final static String FORMAT_2 = "ROLE_%s_%s";
    private final static String FORMAT_1 = "ROLE_%s";
    private Logger LOGGER = LoggerFactory.getLogger(UserDetailsServiceImpl.class);
    @Value("${authority.url}")
    private String url;

    @Value("${authority.siteId}")
    private String siteId;

    /**
     * Locates the user based on the username. In the actual implementation, the search
     * may possibly be case sensitive, or case insensitive depending on how the
     * implementation instance is configured. In this case, the <code>UserDetails</code>
     * object that comes back may have a username that is of a different case than what
     * was actually requested..
     *
     * @param username the username identifying the user whose data is required.
     * @return a fully populated user record (never <code>null</code>)
     * @throws UsernameNotFoundException if the user could not be found or the user has no
     *                                   GrantedAuthority
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        List<GrantedAuthority> authorities = new ArrayList();

        List<PrivilegeDTO> privileges = getPrivileges(username);
        privileges.forEach(privilegeDTO -> {
            GrantedAuthority grantedAuthority = new SimpleGrantedAuthority(privilegeDTO.getCategory());
            if (grantedAuthority != null) {
                authorities.add(grantedAuthority);
            }
        });
        return new User(username, "111111", authorities);
    }

    /**
     * 权限转换
     *
     * @param privilege 权限
     * @return 转换结果
     */
    private GrantedAuthority convertAuthority(PrivilegeDTO privilege) {
        String role = "";
        if ("role".equals(privilege.getCategory().toLowerCase())) {
            if (StringUtils.hasText(privilege.getShape()) && StringUtils.hasText(privilege.getPoint())) {
                role = String.format(FORMAT_2, privilege.getShape(), privilege.getPoint());
            } else if (StringUtils.hasText(privilege.getShape())) {
                role = String.format(FORMAT_1, privilege.getShape());
            }
        } else {
            if (StringUtils.hasText(privilege.getCategory()) && StringUtils.hasText(privilege.getShape()) && StringUtils.hasText(privilege.getPoint())) {
                role = String.format(FORMAT_3, privilege.getCategory(), privilege.getShape(), privilege.getPoint());
            } else if (StringUtils.hasText(privilege.getCategory()) && StringUtils.hasText(privilege.getShape())) {
                role = String.format(FORMAT_2, privilege.getCategory(), privilege.getShape());
            } else if (StringUtils.hasText(privilege.getCategory())) {
                role = String.format(FORMAT_1, privilege.getCategory());
            }
        }

        GrantedAuthority authority = null;
        if (StringUtils.hasText(role)) {
            authority = new SimpleGrantedAuthority(role.toUpperCase());
        }
        return authority;
    }

    /**
     * 获取权限
     *
     * @param username 用户名
     * @return 权限列表
     */
    private List<PrivilegeDTO> getPrivileges(String username) {
        String json = getAuthority(username);

        ObjectMapper objectMapper = new ObjectMapper();
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(ArrayList.class, PrivilegeDTO.class);
        List<PrivilegeDTO> privileges;
        try {
            privileges = objectMapper.readValue(json, javaType);
        } catch (IOException e) {
            LOGGER.error("Parse JSON String error:", e);
            privileges = new ArrayList();
        }
        return privileges;
    }

    /**
     * 获取权限
     *
     * @param username 用户名
     * @return 权限
     */

    private String getAuthority(String username) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("siteKey", siteId);
            HttpEntity<String> request = new HttpEntity(headers);
            ResponseEntity s = restTemplate.exchange(String.format("%s/authority/%s", url, username), HttpMethod.GET, request, String.class);
            return s.getBody().toString();
        } catch (Exception e) {
            LOGGER.error("Remote Call error:", e);
        }

        return "[]";
    }
}
