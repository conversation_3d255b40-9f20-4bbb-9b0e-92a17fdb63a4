package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.constant.InsightConstants;
import com.yupaopao.risk.insight.repository.model.CepPattern;
import com.yupaopao.risk.insight.repository.model.CepResult;
import com.yupaopao.risk.insight.service.CepMatchResultHandler;
import com.yupaopao.risk.insight.util.CommonUtil;
import com.yupaopao.risk.punish.api.RiskPunishService;
import com.yupaopao.risk.punish.request.BatchPunishRequest;
import com.yupaopao.risk.punish.result.BatchPunishResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yupaopao.risk.insight.constant.InsightConstants.PUNISH_CHANNEL_INSIGHT;

@Slf4j
@Component
public class PunishResultHandler implements CepMatchResultHandler {

    @DubboReference(timeout = 60000)
    private RiskPunishService riskPunishService;


    @Value("${cep.punishColumnsMapping}")
    private String cepPunishDimensionMapping;


    private static final Integer SUCCESS = 1;
    @RedisAutowired("middleware.redis.risk-engine")
    private RedisService redisService;

    private static final Integer TIME = 600;

    private List<String> hardCodeColumns = Arrays.asList("internalReason", "externalReason");

    @Override
    public boolean filter(String type, Object cepResult) {
        //惩罚包 id && 只处理一条
        if (cepResult instanceof CepResult) {
            String batchId = ((CepResult) cepResult).getBatchId();
            String detail = ((CepResult) cepResult).getDetail();
            JSONObject jsonObject = JSONObject.parseObject(detail);
            JSONObject data = jsonObject.getJSONObject("data");
            String eventCode = data.getString("Event");
            JSONObject jsonObj = JSON.parseObject(type);
            String event = jsonObj.getString("event");
            if (event.equals(eventCode)) {
                Object o = redisService.get(batchId);
                if (o == null || "".equals(o)) {
                    redisService.set(batchId, event, TIME);
                    return true;
                }
            }

        } else if (StringUtils.isNumeric(type) && "0".equals(cepResult)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean doHandle(CepResult result, CepPattern pattern) {
        Transaction transaction = Cat.newTransaction("risk.offline.cep", "punish");
        String resultHandler = pattern.getResultHandler();
        BatchPunishRequest request = new BatchPunishRequest();
        //基础参数
        request.setChannel(PUNISH_CHANNEL_INSIGHT);
        request.setBizId(result.getTraceId());
        request.setBizType("cep::" + pattern.getId());
        request.setOperator("system");
        if (StringUtils.isNotEmpty(pattern.getName())) {
            request.setInternalReason("cep: " + pattern.getId() + ":" + pattern.getName());
        } else {
            request.setInternalReason("cep: " + pattern.getId());
        }
        request = buildRequest(result, request, JSONObject.parseObject(resultHandler));
        log.info("cep 惩罚请求:{}", request);
        Response<BatchPunishResult> response = riskPunishService.batchPunish(request);
        log.info("cep 惩罚结果返回:{}", response);
        BatchPunishResult res = response.getResult();
        boolean success = SUCCESS.equals(res.getResult());
        if (res == null || !success) {
            log.warn("惩罚失败: {}, params: {}", response, request);
            transaction.setStatus(response.getCode());
        }
        transaction.complete();
        return success;
    }

    private BatchPunishRequest buildRequest(CepResult result, BatchPunishRequest request, JSONObject jsonObject) {
        String detail = result.getDetail();
        request.setPackageId(Long.valueOf(jsonObject.getString("handlerType")));
        String pkgRuleId = jsonObject.getString("pkgRuleId");
        Map<String, Object> extMap = request.getExtMap() != null ? request.getExtMap() : new HashMap<>();
        JSONObject punishParam = jsonObject.getJSONObject("punishParam");
        Map reqMap = JSONObject.parseObject(JSONObject.toJSONString(request), Map.class);
        punishParam.forEach((k, v) -> {
            if (hardCodeColumns.contains(k)) {
                //对内对外原因等数据
                reqMap.put(k, v);
            } else {
                String value = CommonUtil.read(detail, v + "");
                if (k.contains("extMap") && !StringUtils.isEmpty(value)) {
                    extMap.put(k.replace("extMap.", ""), value);
                } else if (!StringUtils.isEmpty(value)) {
                    reqMap.put(k, value);
                }
            }
        });
        if (StringUtils.isNotEmpty(pkgRuleId)) {
            extMap.put(InsightConstants.EXT_KEY_PATROL_RULE_ID, pkgRuleId);
        }
        Map<String, String> columnMapping = JSON.parseObject(cepPunishDimensionMapping, Map.class);
        //配置设备号手机号用户id
        for (Map.Entry<String, String> entry : columnMapping.entrySet()) {
            String dimension = entry.getKey();
            reqMap.put(dimension, CommonUtil.read(detail, "$." + entry.getValue()));
        }

        BatchPunishRequest batchPunishRequest = JSONObject.parseObject(JSONObject.toJSONString(reqMap), BatchPunishRequest.class);
        batchPunishRequest.setExtMap(extMap);
        return batchPunishRequest;

    }

    public static void main(String[] args) {

        Map<String, String> columnMapping = new HashMap<>();
        columnMapping.put("uid", "userId");
        columnMapping.put("deviceId", "deviceId");
        columnMapping.put("mobile", "mobileNo");
        System.err.println(JSON.toJSONString(columnMapping));

//        log.info("cep 惩罚请求:{}", request);
//        Response<BatchPunishResult> response = riskPunishService.batchPunish(request);
//        log.info("cep 惩罚结果返回:{}", response);
    }

}
