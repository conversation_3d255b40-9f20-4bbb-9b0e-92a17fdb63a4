package com.yupaopao.risk.insight.enums;

public enum AppEnum {
    peerage(10, "比心"),
    YUER(20, "鱼饵"),
    PLANET(30, "小星球"),
    CERTIFICATE(40, "电竞认证"),
    FIREFLIES(70, "萤火"),
    TANG_GUO(80, "咪糖");

    AppEnum(Integer type, String name){
        this.type = type;
        this.name = name;
    }
    private Integer type;
    private String name;

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static AppEnum getAPP(Integer type){
        for (AppEnum value : values()) {
            if (value.type==type) {
                return value;
            }
        }
        return null;
    }
}
