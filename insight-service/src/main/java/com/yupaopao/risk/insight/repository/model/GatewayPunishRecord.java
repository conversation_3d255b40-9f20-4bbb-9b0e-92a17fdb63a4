package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc GatewayPunishRecord
 * @date 2021/10/27
 */
@Table(name = "t_gateway_punish_record")
@Getter
@Setter
public class GatewayPunishRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "record_key")
    private String record;

    @Column(name = "key_type")
    private int type;

    @Column(name = "effect_time")
    private long effectTime;

    @Column(name = "expiration_time")
    private long expirationTime;

    @Column(name = "record_status")
    private int recordStatus;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_by")
    private String createBy;
}
