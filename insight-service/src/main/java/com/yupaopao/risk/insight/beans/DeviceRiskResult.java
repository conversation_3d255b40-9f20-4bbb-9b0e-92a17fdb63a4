package com.yupaopao.risk.insight.beans;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

@Data
public class DeviceRiskResult implements Serializable {
    private static final long serialVersionUID = 640839214653859872L;


    private Map<String, Object> commonDeviceMap;

    private Map<String, String> commonCityMap;
    private Map<String, String> commonIPMap;

    private Set<String> deviceList;

    private List<PieVO> devicePortrait;


    private AtomicInteger highRiskCount = new AtomicInteger(0);
    private AtomicInteger middleRiskCount = new AtomicInteger(0);
    private AtomicInteger lowRiskCount = new AtomicInteger(0);
    private AtomicInteger notRiskCount = new AtomicInteger(0);
    private AtomicInteger notFound = new AtomicInteger(0);
    private AtomicInteger unGradeCount = new AtomicInteger(0);

}
