package com.yupaopao.risk.insight.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Created by Avalon on 2020/2/28 11:54
 */
@Component
@ConfigurationProperties(prefix = "es")
@Setter
@Getter
public class ElasticSearchConfig {

    private String hosts;

    private String username;

    private String password;
}
