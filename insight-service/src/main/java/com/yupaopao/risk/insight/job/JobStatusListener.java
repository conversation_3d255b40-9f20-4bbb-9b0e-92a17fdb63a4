package com.yupaopao.risk.insight.job;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.aries.client.JobExecutionContext;
import com.yupaopao.aries.client.JobListener;
import com.yupaopao.framework.spring.boot.aries.annotation.AriesCronJobListener;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.constant.InsightConstants;
import com.yupaopao.risk.insight.flink.tools.CommonTools;
import com.yupaopao.risk.insight.flink.tools.FlinkRestClient;
import com.yupaopao.risk.insight.repository.mapper.TaskHistoryMapper;
import com.yupaopao.risk.insight.repository.mapper.TaskInfoMapper;
import com.yupaopao.risk.insight.repository.model.TaskHistory;
import com.yupaopao.risk.insight.repository.model.TaskInfo;
import com.yupaopao.risk.insight.util.DingTalkUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@AriesCronJobListener
public class JobStatusListener implements JobListener {

    @Autowired
    private TaskHistoryMapper taskHistoryMapper;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private FlinkRestClient flinkRestClient;

    @Value("${task.dingtalk.webhook}")
    private String webhook;

    @Value("${flink.webUrl}")
    private String webUrl;

    @Value("${flink.nonCron.webUrl}")
    private String nonCronWebUrl;

    @Autowired
    private CommonTools commonTools;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        Example search = new Example(TaskHistory.class);
        Example.Criteria criteria = search.createCriteria();
        criteria.andEqualTo("taskStatus", "RUNNING");

        List<TaskHistory> taskHistories = taskHistoryMapper.selectByExample(search);

        taskHistories.stream()
            .filter(p->StringUtils.isNotBlank(p.getFlinkJobId()))
            .forEach(p->{
                String jobStatus;
                String jobInfoStr;
                Map<String, String> jobAccumulators;
                if (p.getIsCron() == 1) {
                    jobStatus = flinkRestClient.getJobStatus(p.getFlinkJobId());
                    jobInfoStr = flinkRestClient.getJobInfo(p.getFlinkJobId());
                    jobAccumulators = flinkRestClient.getJobAccumulators(p.getFlinkJobId());
                } else {
                    jobStatus = flinkRestClient.getNonCronJobStatus(p.getFlinkJobId());
                    jobInfoStr = flinkRestClient.getNonCronJobInfo(p.getFlinkJobId());
                    jobAccumulators = flinkRestClient.getNonCronJobAccumulators(p.getFlinkJobId());
                }
                p.setTaskStatus(jobStatus);
                JSONObject jobInfo = JSONObject.parseObject(jobInfoStr);
                p.setDuration(jobInfo.containsKey("duration") ? jobInfo.getLong("duration") : 0);
                p.setAccumulators(JSONObject.toJSONString(jobAccumulators));
                if (InsightConstants.FLINK_JOB_STATUS_FAILED.equals(p.getTaskStatus()) || InsightConstants.FLINK_JOB_STATUS_ABORTED.equals(p.getTaskStatus())) {
                    sendNotifyMsg(p);
                }
                if (InsightConstants.FLINK_JOB_STATUS_FAILED.equals(p.getTaskStatus()) && p.getIsCron() == 1) {
                    String status = flinkRestClient.getNonCronJobStatus(p.getFlinkJobId());
                    if (InsightConstants.FLINK_JOB_STATUS_FAILED.equals(status)) {
                        TaskInfo select = this.taskInfoMapper.selectByPrimaryKey(p.getTaskId());
                        if (select.getIsFlatten() == 0) {
                            commonTools.startETLJob(select);
                        }
                    }
                }
                taskHistoryMapper.updateByPrimaryKey(p);
            });
    }

    private void sendNotifyMsg(TaskHistory taskHistory) {
        StringBuilder msg = new StringBuilder();
        JSONObject accumulators = JSONObject.parseObject(taskHistory.getAccumulators());
        msg.append("### 任务状态通知")
            .append("\n")
            .append("+ 任务名: ")
            .append(taskHistory.getName())
            .append("\n")
            .append("+ 任务状态: ")
            .append(taskHistory.getTaskStatus())
            .append("\n")
            .append("+ 开始时间: ")
            .append(InsightDateUtils.getDateStr(taskHistory.getCreateTime(),InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss))
            .append("\n")
            .append("+ 持续时间: ")
            .append(getDurationTime(taskHistory.getDuration()))
            .append("\n")
            .append("+ 读取条数: ")
            .append(accumulators.getOrDefault("numRead", 0))
            .append("\n")
            .append("+ 写入条数: ")
            .append(accumulators.getOrDefault("numWrite", 0))
            .append("\n")
            .append("+ 异常条数: ")
            .append(accumulators.getOrDefault("nErrors", 0))
            .append("\n")
            .append("+ 任务详情: [详情](")
            .append(taskHistory.getIsCron() == 1 ? webUrl : nonCronWebUrl)
            .append(String.format("/#/job/%s/overview", taskHistory.getFlinkJobId()))
            .append(")\n")
        ;
        String[] split = webhook.split(",");
        Arrays.stream(split).forEach(p->DingTalkUtil.sendMarkdownMsg(p, msg.toString(), "任务通知"));
    }

    private String getDurationTime(Long duration) {
        duration = duration / 1000;
        if (duration < 60) {
            return duration + "s";
        }
        duration = duration / 60;
        long second = duration % 60;
        if (duration < 60) {
            return duration + "m " + second + 's';
        }
        duration = duration / 60;
        long min = duration % 60;
        if (duration < 24) {
            return duration + "h " + min + "m " + second + 's';
        }
        duration = duration / 24;
        long hour = duration % 24;
        return duration + "d" + hour + "h " + min + "m " + second + 's';
    }
}
