package com.yupaopao.risk.insight;

import com.yupaopao.platform.common.dto.Code;
import com.yupaopao.risk.insight.enums.ErrorMessage;

public class InsightException extends RuntimeException {
    private String code;

    public InsightException() {
    }

    public InsightException(ErrorMessage code) {
        this(code.getCode(), code.getMsg());
    }

    public InsightException(Code code) {
        this(code.getCode(), code.getMessage());
    }

    public InsightException(String code, String message) {
        super(code + ":" + (message.length() > 2000 ? message.substring(0, 1800) + "......" : message));
        this.code = code;
    }

    public InsightException(ErrorMessage code, Throwable cause) {
        this(code.getCode(), code.getMsg(), cause);
    }

    public InsightException(String code, String message, Throwable cause) {
        super(String.format("%d:%s", code, message), cause);
        this.code = code;
    }

    public String getCode() {
        return code;
    }

}
