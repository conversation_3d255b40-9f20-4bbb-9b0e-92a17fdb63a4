package com.yupaopao.risk.insight.service.beans;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by Avalon on 2020/8/11 19:20
 */

@NoArgsConstructor
@AllArgsConstructor
@Data
public class BehaviorAnalysisData {

    // 待分析数据
    private List<String> data;

    // 数据类型
    private String type;  //userId,deviceId,ip ...  etc.

    private String endDay;//分析数据截止日期

}
