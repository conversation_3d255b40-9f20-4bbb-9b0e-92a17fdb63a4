package com.yupaopao.risk.insight.repository.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "t_user_group_conf_detail")
public class TUserGroupConfDetail implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 用户群code
     */
    @Column(name = "group_code")
    private String groupCode;

    /**
     * 字段code
     */
    @Column(name = "field_code")
    private String fieldCode;

    /**
     * 比较类型
     */
    private String operator;

    /**
     * 聚合函数
     */
    @Column(name = "agg_function")
    private String aggFunction;

    /**
     * 类型：q 查询、s 自条件，g groupBy 
     */
    private String type;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取用户群code
     *
     * @return group_code - 用户群code
     */
    public String getGroupCode() {
        return groupCode;
    }

    /**
     * 设置用户群code
     *
     * @param groupCode 用户群code
     */
    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    /**
     * 获取字段code
     *
     * @return field_code - 字段code
     */
    public String getFieldCode() {
        return fieldCode;
    }

    /**
     * 设置字段code
     *
     * @param fieldCode 字段code
     */
    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    /**
     * 获取比较类型
     *
     * @return operator - 比较类型
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 设置比较类型
     *
     * @param operator 比较类型
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * 获取聚合函数
     *
     * @return agg_function - 聚合函数
     */
    public String getAggFunction() {
        return aggFunction;
    }

    /**
     * 设置聚合函数
     *
     * @param aggFunction 聚合函数
     */
    public void setAggFunction(String aggFunction) {
        this.aggFunction = aggFunction;
    }

    /**
     * 获取类型：q 查询、s 自条件，g groupBy 
     *
     * @return type - 类型：q 查询、s 自条件，g groupBy 
     */
    public String getType() {
        return type;
    }

    /**
     * 设置类型：q 查询、s 自条件，g groupBy 
     *
     * @param type 类型：q 查询、s 自条件，g groupBy 
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", groupCode=").append(groupCode);
        sb.append(", fieldCode=").append(fieldCode);
        sb.append(", operator=").append(operator);
        sb.append(", aggFunction=").append(aggFunction);
        sb.append(", type=").append(type);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}