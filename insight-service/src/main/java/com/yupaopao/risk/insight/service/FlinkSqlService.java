package com.yupaopao.risk.insight.service;

//import com.yupaopao.risk.insight.flink.beans.LocalSQLResponse;
//import com.yupaopao.risk.insight.flink.beans.LocalSqlParams;
//import com.yupaopao.risk.insight.flink.beans.RemoteSQLResponse;
//import com.yupaopao.risk.insight.common.meta.JobParams;
//
//import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-11-20 19:35
 *
 ***/
public interface FlinkSqlService {

//    /***
//     * 远程flink cluster执行sql
//     * @param jobParams
//     * @return
//     */
//    RemoteSQLResponse executeRemote(JobParams jobParams);


//    RemoteSQLResponse executeRemoteDynamicSQL(JobParams jobParams);

//
//    /***
//     * 本地内存flink cluster执行sql
//     * @param sqlParams
//     * @return
//     */
//    LocalSQLResponse executeLocal(LocalSqlParams sqlParams);
//
//    void validateSQL(JobParams jobParams);
//
//    List<String> getTablesFromSQL(String sql);
}
