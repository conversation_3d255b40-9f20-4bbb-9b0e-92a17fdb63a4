package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

/****
 * zengxiangcai
 * 2023/10/16 17:50
 ***/

@Getter
@Setter
@Table(name = "t_risk_feature_cfg")
public class FeatureCfg extends BaseEntity {

    @Column(name = "feature_code")
    private String featureCode; //code编码

    @Column(name = "feature_name")
    private String featureName; //特征名称

    @Column(name = "feature_source")
    private Integer featureSource; //特征来源0：聚合计算 1：画像标签  2: hive/ck聚合标签 3: 数美收集rawdata

    @Column(name = "feature_type")
    private Integer featureType; //数据类型：0：离散 1：连续

    @Column(name = "dependent_feature")
    private Long dependentFeature; //计算提前计算的特征

    private Integer status; // 0 无效 1：有效

    private Integer period; //周期：分钟为单位

    @Column(name = "group_type")
    private Integer groupType; //分组类型(0:userId/1:deviceId/2:encryptedMobile/3:ip/4:shortMobile/5:shortIp),

    @Column(name = "value_type")
    private String valueType; //数据值类型：String,Long,Double

    @Column(name = "feature_sql")
    private String featureSql; //特征计算sql

    @Column(name = "feature_desc")
    private String featureDesc;  //特征描述

    @Column(name = "execute_engine_type")
    private String executeEngineType;  //特征计算执行引擎hive or clickhouse

    @Column(name = "available_situation")
    private String availableSituation;// 适用场景(逗号隔开的eventCode),

    @Column(name = "exclude_situation")
    private String excludeSituation;// 排除场景(逗号隔开),

    private Integer timeliness; //0:实施1:天级别

    private String transformer;



}
