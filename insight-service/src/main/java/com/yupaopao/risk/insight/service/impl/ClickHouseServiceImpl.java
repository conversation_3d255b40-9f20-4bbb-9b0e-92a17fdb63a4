package com.yupaopao.risk.insight.service.impl;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.collect.Maps;
import com.yupaopao.risk.insight.InsightException;
import com.yupaopao.risk.insight.beans.CKImportParam;
import com.yupaopao.risk.insight.beans.DBTableInfo;
import com.yupaopao.risk.insight.common.beans.CkQueryResult;
import com.yupaopao.risk.insight.common.clickhouse.CKConnectionConfig;
import com.yupaopao.risk.insight.common.enums.InsightFlinkDataType;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.enums.ErrorMessage;
import com.yupaopao.risk.insight.repository.mapper.JobInfoMapper;
import com.yupaopao.risk.insight.repository.model.JobInfo;
import com.yupaopao.risk.insight.service.ClickHouseService;
import com.yupaopao.risk.insight.service.HBaseService;
import com.yupaopao.risk.insight.service.TableService;
import com.yupaopao.risk.insight.service.beans.CKPageResponse;
import com.yupaopao.risk.insight.util.ClickHouseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yupaopao.risk.insight.enums.ErrorMessage.CLICKHOUSE_QUERY_ERROR;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-23 17:10
 *
 ***/

@Slf4j
@Service
public class ClickHouseServiceImpl implements ClickHouseService {

    @Autowired
    private HBaseService hBaseService;

    @Autowired
    private JobInfoMapper jobInfoMapper;

    @Autowired
    private TableService tableService;


    @ApolloConfig("clickhouse")
    private Config apolloConfigForCK;

    private CKConnectionConfig ckConnectionCfg;
    private ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Value("${ck.admin:'admin'}")
    private String admins;


    private static final String CHARACTER_LIMIT  = "LIMIT";


    @PostConstruct
    public void init() {
        ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        ckConnectionCfg = new CKConnectionConfig(ckProperties);
        apolloConfigForCK.addChangeListener(elem -> {
            ClickHouseProperties props = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
            ckConnectionCfg.updateCkProperties(props);
        });
    }

    public CKConnectionConfig getCkConfig() {
        return this.ckConnectionCfg;
    }

    @Override
    public Map<String, Object> executeQuery(String sql, Integer jobId, String userName) {
        sql = sql.trim();
        if (sql.endsWith(";")) {
            sql = sql.substring(0, sql.length() - 1);
        }

        String tempLowerCase = sql.toLowerCase();
        boolean isDDL =
                !tempLowerCase.startsWith("select") && !tempLowerCase.startsWith("desc") && !tempLowerCase.startsWith("with");

        if (tempLowerCase.startsWith("select") || tempLowerCase.startsWith("with")) {
            if (!sql.toUpperCase().contains("LIMIT")) {
                sql = sql + " limit 20000"; //最多两万
            }
            if (checkSelectAllColumns(sql)) {
                throw new InsightException(ErrorMessage.WORKSPACE_SQL_LIMIT_ERROR);
            }
        } else if (tempLowerCase.startsWith("desc")) {
            //desc can be executed without limit
        } else if (!admins.contains(userName)) {
            throw new InsightException(ErrorMessage.WORKSPACE_SQL_ILLEGAL_ERROR);
        }


        try {
            if (isDDL) {
                ckConnectionCfg.executeDDL(sql);
                return returnDDLResult(jobId);
            }

            Map<String, Object> result = Maps.newHashMap();
            CkQueryResult<Map<String, String>> queryResult = ckConnectionCfg.executeQuery(sql);
            result.put("columnName", queryResult.getColumnNameList());
            result.put("columnValue", queryResult.getResultList());
            result.put("jobId", jobId);
            executorService.execute(new SQLResultHandler(queryResult.getResultList(), hBaseService, jobInfoMapper, jobId));
            return result;
        } catch (Exception e) {
            log.error("sql execute error: ", e);
            throw new InsightException(CLICKHOUSE_QUERY_ERROR.getCode(), e.getMessage());
        }
    }

    private boolean checkSelectAllColumns(String sql) {
        String reg = "select\\s*[A-Za-z\\d]*(\\.)?\\*\\s*";
        System.err.println(reg);
        Pattern p = Pattern.compile(reg);
        if (p.matcher(sql).find()) {
            return true;
        } else {
            return false;
        }
    }


    private Map<String, Object> returnDDLResult(Integer jobId) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("columnName", Arrays.asList(new String[]{"finished"}));
        Map<String, Object> finishMap = new HashMap<>();
        finishMap.put("finished", "true");
        List<Map<String, Object>> resultList = new ArrayList<>();
        resultList.add(finishMap);
        result.put("columnValue", resultList);
        result.put("jobId", jobId);
        return result;
    }

    @Override
    public Map<String, String> fetchTable() {
        Map<String, String> tables = new HashMap<>();
        String sql = "select name,engine from system.tables where database != 'system' and database in ('default')";
        ckConnectionCfg.executeQuery(sql).getResultList().stream().forEach(elem -> tables.put(elem.get("name"),
                elem.get("engine")));
        return tables;
    }

    @Override
    public Map<String, String> fetchColumns(String tableName) {
        Map<String, String> columns = new HashMap<>();
        if (StringUtils.isEmpty(tableName)) {
            return columns;
        }
        String sql = "desc " + tableName;
        ckConnectionCfg.executeQuery(sql).getResultList().forEach(elem -> columns.put(elem.get("name"), elem.get("type")));
        return columns;
    }

    @Override
    public boolean addColumn(String tableName, Map<String, String> newColumn) {
        StringBuilder sql = new StringBuilder("ALTER TABLE ").append(tableName).append(" ON CLUSTER default");
        newColumn.forEach((key, value) -> {
            sql.append(" ADD COLUMN IF NOT EXISTS ")
                    .append(key)
                    .append(" ")
                    .append(value)
                    .append(",");
        });

        String alterSql = sql.deleteCharAt(sql.length() - 1).toString();
        log.info("alter sql: {}", alterSql);
        return ckConnectionCfg.executeDDL(alterSql);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importTempTable(CKImportParam importParam) throws Exception {
        //写入db表
        tableService.uploadTable(importParam.getDbTableInfo());
        //写入ck表
        //导入ck数据
        //flink type 转为ckType
        List<DBTableInfo.TableColumn> ckColumns = importParam.getDbTableInfo().getTableColumns().stream().map(elem -> {
            DBTableInfo.TableColumn tColumn = new DBTableInfo.TableColumn();
            tColumn.setColumnName(elem.getColumnName());
            tColumn.setType(InsightFlinkDataType.getCkType(elem.getType()));
            return tColumn;
        }).collect(Collectors.toList());
        String createLocalSQL =
                ClickHouseUtils.generateCkLocalReplicatedTableCreateSQL(importParam.getDbTableInfo().getTableName(),
                        ckColumns);
        String createDistributedSQL = ClickHouseUtils.generateCkDistributedTableCreateSQL(importParam.getDbTableInfo().getTableName(),
                ckColumns);

        ckConnectionCfg.executeDDL(createLocalSQL);
        ckConnectionCfg.executeDDL(createDistributedSQL);

        try {
            ckConnectionCfg.executeInsertWithJson(importParam.getDbTableInfo().getTableName(),
                    importParam.getRowJsonDataList());
        } catch (Exception e) {
            log.error("insert into table " + importParam.getDbTableInfo().getTableName() + " error", e);
        }


    }

    @Override
    public void dropDistributedTable(String tableName) throws Exception {
        String dropLocalSQL = ClickHouseUtils.generateLocalDropSQL(tableName);
        String dropDistributed = ClickHouseUtils.generateDistributedDropSQL(tableName);

        try {
            ckConnectionCfg.executeDDL(dropLocalSQL);
            ckConnectionCfg.executeDDL(dropDistributed);
        } finally {
            log.info("prepared to drop table {}, \ndrop local: {} \ndrop distributed: {}",
                    tableName,
                    dropLocalSQL, dropDistributed);
        }
    }

    @Override
    public List<Map<String, String>> executeQuery(String sql) {
        return ckConnectionCfg.executeQuery(sql).getResultList();
    }


    @Override
    public CkQueryResult<List<String>> executeQueryWithList(String sql) {
        return ckConnectionCfg.executeQueryForList(sql);
    }


    @Override
    public List<Map<String, Object>> executeQueryWithObject(String sql) {
        return ckConnectionCfg.executeQueryForObject(sql).getResultList();
    }

    /**
     * first pageNumber is 1
     *
     * @param sql
     * @param pageNumber
     * @param pageSize
     * @return
     */
    @Override
    public CKPageResponse executeQueryWithPage(String sql, Integer pageNumber, Integer pageSize) {
        if (pageNumber == null || pageSize == null || pageNumber < 0 || pageSize < 0) {
            throw new RuntimeException("page info cannot be empty");
        }
        if (sql.trim().endsWith(";")) {
            sql = sql.trim().substring(0, sql.trim().length() - 1);
        }
        List<Map<String, String>> totalResponse = executeQuery("select count(1) totalCount from (" + sql + ") " +
                " as t_ck_page_count");
        Long total = Long.parseLong(totalResponse.get(0).get("totalCount"));
        String pageSql =
                "select * from (" + sql + ") limit " + ((pageNumber - 1) <= 0 ? 0 : (pageNumber - 1)) * pageSize + ", " + pageSize;
        if (total <= 0) {
            return new CKPageResponse(new ArrayList<>(), total, 0);
        }

        List<Map<String, String>> response = executeQuery(pageSql);
        Integer pages = (int) Math.ceil(total * 1.0 / pageSize);
        return new CKPageResponse(response, total, pages);

    }

    @Override
    public boolean executeUpdate(List<String> insertJsonList, String tableName) throws Exception {
        return ckConnectionCfg.executeInsertWithJson(tableName, insertJsonList);
    }

    @Override
    public boolean executeUpdate(String sql) throws Exception {
        return ckConnectionCfg.executeUpdate(sql);
    }


    public static class SQLResultHandler implements Runnable {
        private List<Map<String, String>> responseList;
        private HBaseService hBaseService;
        private JobInfoMapper jobInfoMapper;
        private Integer jobId;

        public SQLResultHandler(List<Map<String, String>> respList, HBaseService hBaseService,
                                JobInfoMapper jobInfoMapper,
                                Integer jobId) {
            this.responseList = respList;
            this.hBaseService = hBaseService;
            this.jobInfoMapper = jobInfoMapper;
            this.jobId = jobId;
        }

        @Override
        public void run() {

            //结果写入hbase中
            hBaseService.addSQLResultToRiskAnalysisResult(responseList, jobId.toString());
            //sql job状态更新
            JobInfo updateJob = new JobInfo();
            updateJob.setId(jobId);
            updateJob.setStatus("COMPLETED");
            Integer affectRow = jobInfoMapper.updateByPrimaryKeySelective(updateJob);
        }
    }

}
