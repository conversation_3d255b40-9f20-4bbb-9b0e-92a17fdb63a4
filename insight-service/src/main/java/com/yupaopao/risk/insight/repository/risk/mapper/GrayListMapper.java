package com.yupaopao.risk.insight.repository.risk.mapper;

import com.yupaopao.risk.insight.repository.risk.bean.GrayList;
import com.yupaopao.risk.insight.repository.risk.bean.QueryGrayList;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface GrayListMapper extends Mapper<GrayList> {

    @Select("<script>" +
            "SELECT t.value AS uid,\n" +
            "       t.author,t.comment,\n" +
            "       t.start_time AS startTime,\n" +
            "       t.expire_time AS expireTime,\n" +
            "       t1.name\n" +
            "FROM risk_gray_list AS t\n" +
            "LEFT JOIN risk_gray_group AS t1 ON t.group_id=t1.id\n" +
            "WHERE t.expire_time >=NOW() " +
            "<if test='queryGrayList.type!=null'>" +
            " and t.type = #{queryGrayList.type}" +
            "</if>"+
            "<if test='queryGrayList.dimension!=null'>" +
            " and t.dimension = #{queryGrayList.dimension}" +
            "</if>"+
            " and t.value in " +
            "<foreach item='item' collection='queryGrayList.ids' separator=',' open='(' close=')'>" +
            "#{item}" +
            "</foreach>"+
            "</script>")
    List<GrayList> queryGrayList(@Param("queryGrayList") QueryGrayList queryGrayList);

    @Select("select value from risk_gray_list where type = 'BLACK' and dimension = 'USERID' and expire_time>now() and" +
            " group_id =#{groupId} order by value asc ")
    List<String> getAllBlackUser(@Param("groupId") Long groupId);

}
