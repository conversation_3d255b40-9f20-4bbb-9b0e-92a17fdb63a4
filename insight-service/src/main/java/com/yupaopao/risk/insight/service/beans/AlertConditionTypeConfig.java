package com.yupaopao.risk.insight.service.beans;

import com.yupaopao.risk.insight.repository.model.AlertConditionType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

/****
 * zengxiangcai
 * 2022/11/15 14:21
 ***/

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AlertConditionTypeConfig {
    private String id;
    private String desc;
    private String promQL;
    private boolean needTooltips;
    private boolean containsN;
    private boolean containsM;

    private String operator;

    private Date updateTime;

    private Integer pageSize;

    private Integer currentPage;

    public AlertConditionType trans2AlertConditionType() {
        AlertConditionType dbType = new AlertConditionType();
        dbType.setId(StringUtils.isEmpty(id) ? null : Long.valueOf(id));
        dbType.setConditionDesc(desc);
        dbType.setPromQL(promQL);
        dbType.setContainsM(containsM ? 1 : 0);
        dbType.setContainsN(containsN ? 1 : 0);
        dbType.setNeedTooltips(needTooltips ? 1 : 0);
        return dbType;
    }

    public static AlertConditionTypeConfig trans2AlertConditionTypeConfig(AlertConditionType dbType) {
        AlertConditionTypeConfig displayType = new AlertConditionTypeConfig();
        displayType.setId(dbType.getId().toString());
        displayType.setDesc(dbType.getConditionDesc());
        displayType.setPromQL(dbType.getPromQL());
        displayType.setContainsM(dbType.getContainsM() == 0 ? false : true);
        displayType.setContainsN(dbType.getContainsN() == 0 ? false : true);
        displayType.setNeedTooltips(dbType.getNeedTooltips() == 0 ? false : true);
        displayType.setUpdateTime(dbType.getUpdateTime());
        displayType.setOperator(dbType.getUpdateBy());
        return displayType;
    }

}
