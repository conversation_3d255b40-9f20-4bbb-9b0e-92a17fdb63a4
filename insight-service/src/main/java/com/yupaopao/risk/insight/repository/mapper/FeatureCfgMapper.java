package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.insight.repository.model.FeatureCfg;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/****
 * zengxiangcai
 * 2023/10/16 17:53
 ***/
public interface FeatureCfgMapper extends Mapper<FeatureCfg> {

    @Select("select feature_code,feature_name from t_risk_feature_cfg where status=1")
    List<FeatureCfg> queryFeatureCodeNames();
}
