package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.BloodRelate;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Set;

public interface BloodRelateMapper extends Mapper<BloodRelate> {


    @Insert("<script>"
            + "insert into t_blood_relate (start_id, end_id) values "
            + "<foreach collection='dstIdSet' item='dstId' separator=','>"
            + "(#{srcId}, #{dstId})"
            + "</foreach>"
            + "</script>")
    void batchInsert(@Param("srcId") Integer srcId, @Param("dstIdSet") Set<Integer> dstIdSet);

    @Delete("<script>"
            + "delete from t_blood_relate where start_id = #{srcId}"
            + "</script>")
    void batchDelete(@Param("srcId") Integer srcId);

    @Select("<script>"
            + "WITH RECURSIVE Upstream AS (\n" +
            "    SELECT start_id, end_id, CONCAT('/', end_id, '/') as path\n" +
            "    FROM t_blood_relate\n" +
            "    INNER JOIN t_blood ON t_blood.id = t_blood_relate.end_id\n" +
            "    WHERE t_blood.name = #{name} AND t_blood.type = #{type}\n" +
            "    UNION ALL\n" +
            "    SELECT b.start_id, b.end_id, CONCAT(u.path, b.end_id, '/')\n" +
            "    FROM t_blood_relate b\n" +
            "    INNER JOIN Upstream u ON u.start_id = b.end_id\n" +
            "    WHERE NOT FIND_IN_SET(b.start_id, REPLACE(u.path, '/', ','))\n" +
            "),\n" +
            "Downstream AS (\n" +
            "    SELECT start_id, end_id, CONCAT('/', start_id, '/') as path\n" +
            "    FROM t_blood_relate\n" +
            "    INNER JOIN t_blood ON t_blood.id = t_blood_relate.start_id\n" +
            "    WHERE t_blood.name = #{name} AND t_blood.type = #{type}\n" +
            "    UNION ALL\n" +
            "    SELECT b.start_id, b.end_id, CONCAT(d.path, b.start_id, '/')\n" +
            "    FROM t_blood_relate b\n" +
            "    INNER JOIN Downstream d ON d.end_id = b.start_id\n" +
            "    WHERE NOT FIND_IN_SET(b.end_id, REPLACE(d.path, '/', ','))\n" +
            ")\n" +
            "SELECT start_id, end_id FROM Upstream\n" +
            "UNION\n" +
            "SELECT start_id, end_id FROM Downstream"
            + "</script>")
    List<BloodRelate> queryBloodLine(@Param("name") String name, @Param("type") String type);

    @Select("WITH RECURSIVE\n" +
            "    Upstream (start_id, end_id, path, depth) AS (SELECT start_id, end_id, CONCAT('/', end_id, '/') as path, 1 as depth\n" +
            "                 FROM t_blood_relate\n" +
            "                 WHERE t_blood_relate.end_id = #{bloodId}\n" +
            "                 UNION ALL\n" +
            "                 SELECT b.start_id, b.end_id, CONCAT(u.path, b.end_id, '/'), u.depth + 1\n" +
            "                 FROM t_blood_relate b\n" +
            "                          INNER JOIN Upstream u ON u.start_id = b.end_id\n" +
            "                 WHERE NOT FIND_IN_SET(b.start_id, REPLACE(u.path, '/', ','))\n" +
            "                 AND u.depth < #{maxDepth}),\n" +
            "    Downstream (start_id, end_id, path, depth) AS (SELECT start_id, end_id, CONCAT('/', start_id, '/') as path, 1 as depth\n" +
            "                   FROM t_blood_relate\n" +
            "                   WHERE t_blood_relate.start_id = #{bloodId}\n" +
            "                   UNION ALL\n" +
            "                   SELECT b.start_id, b.end_id, CONCAT(d.path, b.start_id, '/'), d.depth + 1\n" +
            "                   FROM t_blood_relate b\n" +
            "                            INNER JOIN Downstream d ON d.end_id = b.start_id\n" +
            "                   WHERE NOT FIND_IN_SET(b.end_id, REPLACE(d.path, '/', ','))\n" +
            "                   AND d.depth < #{maxDepth})\n" +
            "SELECT start_id, end_id\n" +
            "FROM Upstream\n" +
            "UNION\n" +
            "SELECT start_id, end_id\n" +
            "FROM Downstream")
    List<BloodRelate> queryAllBloodLineByTarget(@Param("bloodId") Integer bloodId, @Param("maxDepth") Integer maxDepth);

    @Select("WITH RECURSIVE\n" +
            "    Upstream (start_id, end_id, path, depth) AS (SELECT start_id, end_id, CONCAT('/', end_id, '/') as path, 1 as depth\n" +
            "                 FROM t_blood_relate\n" +
            "                 WHERE t_blood_relate.end_id = #{bloodId}\n" +
            "                 UNION ALL\n" +
            "                 SELECT b.start_id, b.end_id, CONCAT(u.path, b.end_id, '/'), u.depth + 1\n" +
            "                 FROM t_blood_relate b\n" +
            "                          INNER JOIN Upstream u ON u.start_id = b.end_id\n" +
            "                 WHERE NOT FIND_IN_SET(b.start_id, REPLACE(u.path, '/', ','))\n" +
            "                 AND u.depth < #{maxDepth})\n" +
            "SELECT start_id, end_id\n" +
            "FROM Upstream")
    List<BloodRelate> queryUpstreamBloodLineByTarget(@Param("bloodId") Integer bloodId, @Param("maxDepth") Integer maxDepth);

    @Select("WITH RECURSIVE\n" +
            "Downstream (start_id, end_id, path, depth) AS (SELECT start_id, end_id, CONCAT('/', start_id, '/') as path, 1 as depth\n" +
            "                   FROM t_blood_relate\n" +
            "                   WHERE t_blood_relate.start_id = #{bloodId}\n" +
            "                   UNION ALL\n" +
            "                   SELECT b.start_id, b.end_id, CONCAT(d.path, b.start_id, '/'), d.depth + 1\n" +
            "                   FROM t_blood_relate b\n" +
            "                            INNER JOIN Downstream d ON d.end_id = b.start_id\n" +
            "                   WHERE NOT FIND_IN_SET(b.end_id, REPLACE(d.path, '/', ','))\n" +
            "                   AND d.depth < #{maxDepth})\n" +
            "SELECT start_id, end_id\n" +
            "FROM Downstream")
    List<BloodRelate> queryDownstreamBloodLineByTarget(@Param("bloodId") Integer bloodId, @Param("maxDepth") Integer maxDepth);
}
