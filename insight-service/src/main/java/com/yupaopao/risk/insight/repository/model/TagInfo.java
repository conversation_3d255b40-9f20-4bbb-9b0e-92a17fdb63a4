package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc Tag
 * @date 2020/4/27
 */
@Getter
@Setter
@Table(name ="t_tag_info")
public class TagInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "name")
    private String name;

    @Column(name = "code")
    private String code;

    @Column(name = "tag_type")
    private Integer type;

    @Column(name = "source")
    private String source;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "external_reason")
    private String externalReason;

    @Column(name = "risk_level")
    private Integer riskLevel;

    @Column(name = "scene")
    private String scene;

    @Column(name = "suggestion")
    private String suggestion;

    @Column(name = "violation_type")
    private Integer violationType;

    @Column(name = "sub_violation_type")
    private Integer subViolationType;

    @Column(name = "update_by")
    private String updateBy;

}
