package com.yupaopao.risk.insight.service;

import com.yupaopao.risk.insight.repository.model.WareHouseInfo;
import com.yupaopao.risk.insight.repository.model.WarehouseDetail;
import com.yupaopao.risk.insight.service.beans.PageResult;

import java.util.List;

public interface WarehouseService {

    PageResult<WareHouseInfo> getWarehouses(WareHouseInfo wareHouseInfo, Integer pageSize, Integer pageNumber);

    PageResult<WarehouseDetail> getWarehouseDetails(WarehouseDetail tableDetail, Integer pageSize, Integer pageNumber);

    List<WareHouseInfo> getWarehouses();

    WareHouseInfo upsertWarehouse(WareHouseInfo tableInfo);

    WarehouseDetail upsertWarehouseDetail(WarehouseDetail warehouseDetail);

    boolean upsertWarehouseDetails(List<WarehouseDetail> warehouseDetails);

    boolean warehouseConnectTest(Integer warehouseId);

    boolean deleteWarehouse(Integer tableId);

    boolean deleteWarehouseDetail(Integer detailId);

}
