package com.yupaopao.risk.insight.enums;

public enum TagRiskLevelEnum {

    SERIOUS("serious", "严重"),
    HIGH("high", "高危"),
    MIDDLE("middle", "中危"),
    SMALL("small", "低危");

    TagRiskLevelEnum(String code, String name){
        this.code = code;
        this.name = name;
    }
    private String code;
    private String name;

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
