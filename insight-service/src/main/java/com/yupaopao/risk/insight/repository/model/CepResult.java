package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-03 15:56
 *
 ***/

@Table(name = "t_cep_result")
@Getter
@Setter
public class CepResult {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name ="pattern_id")
    private Integer patternId;

    @Column(name="s_rule_name")
    private String sRuleName;//模式组合中单个模式名称

    @Column(name="trace_id")
    private String traceId;

    @Column(name="user_id")
    private String userId;

    @Column(name="group_by_column")
    private String groupByColumn;

    @Column(name="group_by_column_value")
    private String groupByColumnValue;

    private String detail;

    @Column(name="batch_id")
    private String batchId;
}
