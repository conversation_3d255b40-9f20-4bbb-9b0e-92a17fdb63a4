package com.yupaopao.risk.insight.service.bloodline;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.beans.BloodLineRelateVO;
import com.yupaopao.risk.insight.enums.BloodTypeEnum;
import com.yupaopao.risk.insight.service.TagService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Avalon on 2024/9/9 16:22
 **/
@Component
public class ThirdToTagFetcher implements BloodLineFetcher {

    @Resource
    private TagService tagService;


    @Override
    public BloodTypeEnum getSrcType() {
        return BloodTypeEnum.THIRD;
    }

    @Override
    public BloodTypeEnum getDstType() {
        return BloodTypeEnum.TAG;
    }

    @Override
    public List<BloodLineRelateVO> fetch() {
        List<BloodLineRelateVO> result = new ArrayList<>();

        List<JSONObject> tagRelateThird = tagService.getTagRelateThird();
        if (tagRelateThird == null) {
            return result;
        }
        tagRelateThird.forEach(tagRelate -> {

            String relateThird = tagRelate.getString("srcName");
            relateThird = relateThird.replaceAll("\"", "").trim();
            if (StringUtils.isEmpty(relateThird)) {
                return;
            }
            BloodLineRelateVO bloodLineRelateVO = new BloodLineRelateVO();
            bloodLineRelateVO.setSrcName(relateThird);
            bloodLineRelateVO.setSrcType(getSrcType());

            bloodLineRelateVO.setDstName(tagRelate.getString("dstName"));
            bloodLineRelateVO.setDstRelateId(tagRelate.getInteger("dstRelateId"));
            bloodLineRelateVO.setDstType(getDstType());
            result.add(bloodLineRelateVO);
        });

        return result;
    }
}
