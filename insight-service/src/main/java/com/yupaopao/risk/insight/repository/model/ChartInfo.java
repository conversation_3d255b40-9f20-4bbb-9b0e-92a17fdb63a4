package com.yupaopao.risk.insight.repository.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Table(name = "t_chart_info")
@Data
public class ChartInfo implements Serializable {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型
     */
    private String type;

    /**
     * 名称
     */
    private String name;

    /**
     * 结果表名
     */
    @Column(name = "table_name")
    private String tableName;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Transient
    private List<ChartDetail> chartDetails;

    @Transient
    private Integer dirId;

    private static final long serialVersionUID = 1L;

}