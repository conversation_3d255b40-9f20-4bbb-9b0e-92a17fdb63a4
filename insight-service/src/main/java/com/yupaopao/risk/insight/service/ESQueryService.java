package com.yupaopao.risk.insight.service;

import java.util.List;
import java.util.Map;

public interface ESQueryService {

    /**
     * 使用 sql 查询es
     * @param sql
     * @param column
     * @return
     */
    List<Map<String, Object>> queryBySql(String sql, List<String> column);

    /**
     * @param param
     * @return
     */
    List<Map<String, Object>> searchBy(Map<String, Object> request, Map<String, Class> queryField, String indexName);
}
