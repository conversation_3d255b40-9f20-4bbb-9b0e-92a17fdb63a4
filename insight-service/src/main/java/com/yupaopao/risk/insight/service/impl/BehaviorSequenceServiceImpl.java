package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.algorithm.AlgorithmRunner;
import com.yupaopao.risk.insight.common.beans.CkQueryResult;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.repository.risk.dao.RiskRepository;
import com.yupaopao.risk.insight.service.BehaviorSequenceService;
import com.yupaopao.risk.insight.service.ClickHouseService;
import com.yupaopao.risk.insight.service.beans.*;
import com.yupaopao.risk.insight.service.beans.HistorySequenceAnalysisResult.ClusterInfo;
import com.yupaopao.risk.insight.service.beans.HistorySequenceAnalysisResult.HistorySequenceInfo;
import com.yupaopao.risk.insight.util.ClickHouseUtils;
import com.yupaopao.risk.insight.util.CommonUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import ru.yandex.clickhouse.response.ClickHouseResponse;

import java.util.*;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-09-17 14:38
 *
 ***/

@Slf4j
@Service
public class BehaviorSequenceServiceImpl implements BehaviorSequenceService {

    @Value("${behavior.sequence.tke.winlength:4}")
    private Integer windowLength;

    @Value("${behavior.sequence.tke.topK:5}")
    private Integer topK;

    @Autowired
    private ClickHouseService clickHouseService;

    @Autowired
    private RiskRepository riskRepository;

    @Override
    public List<SequenceAnalysisResult> realTimeAnalysis(List<String> ids, String startTime, String endTime) {

        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            //近24小时
            startTime = InsightDateUtils.getBeforeDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
            endTime = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
            ;
        } else {
            startTime += " 00:00:00";
            endTime += " 23:59:59";
        }
        List<Map<String,String>> ckResponse = clickHouseService.executeQuery(String.format(USER_EVENT_SEQUENCE_SQL,
                startTime, endTime,
                CommonUtil.sqlInParamsJoin(ids)));

        if (CollectionUtils.isEmpty(ckResponse)) {
            return new ArrayList<>();
        }
        Map<String, String> eventCodeNameMap = getEventCodeNameMapping();

        List<SequenceAnalysisResult> analysisResults = new ArrayList<>();

        for (Map<String,String> row : ckResponse) {
            String userId = row.get("userId");
            String eventList = row.get("eventList");
            SequenceAnalysisResult parseResult = parseEventList(userId, eventList, eventCodeNameMap);
            try {
                List<Map<String, Object>> tkeResult =
                        AlgorithmRunner.runTKE(Arrays.asList(parseResult.getOriginalSequence().split(" ")),
                                windowLength, topK);
                parseResult.setItemSets(parseTKEResultForTOPK(tkeResult, eventCodeNameMap));
                analysisResults.add(parseResult);
            } catch (Exception e) {
                log.error("parse id =" + userId + ", tke error, ", e);
            }
        }
        //查询过去24小时的序列
        return analysisResults;
    }

    private Map<String, String> getEventCodeNameMapping() {
        return riskRepository.queryCodeNameMapping();
    }

    /**
     * 查询前一天历史数据
     *
     * @param ids
     * @return
     */
    @Override
    public HistorySequenceAnalysisResult historyAnalysis(List<String> ids, String startTime, String endTime) {

        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            //默认查询前一天的数据
            startTime = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
            endTime = startTime;
        }
        //查找id所属的clusterId

        String idClusterSql = "select concat(toString(runDay),'_',toString(clusterId)) runDayCluster, groupArray(id) ids\n" +
                "from behavior_cluster_daily\n" +
                "where id in (%s)\n" +
                "  and runDay >='%s' and runDay<='%s' group by runDayCluster";
        List<Map<String,String>> clusterResponse = clickHouseService.executeQuery(String.format(idClusterSql,
                CommonUtil.sqlInParamsJoin(ids), startTime, endTime));

        Map<String, List<String>> runDayClusterIdsMapping = new HashMap<>(); //每日cluster包含的ids
        for (Map<String,String> row : clusterResponse) {
            String idResult = row.get("ids");
            runDayClusterIdsMapping.put(row.get("runDayCluster"), ClickHouseUtils.parseGroupArrayResult(idResult));
        }

        //返回用户：序列，clusterId，clusterId占比
//        List<String> clusterRunDayIds = clusterIdMapping.keySet().stream().map(elem -> generateClusterRunDayKey(runDay,
//                elem)).collect(Collectors.toList());
        Map<String, List<String>> runDayClusterSequenceMap =
                getClusterRunDaySequences(runDayClusterIdsMapping.keySet().stream().collect(Collectors.toList()),
                        startTime, endTime, 5);

        HistorySequenceAnalysisResult analysisResult = new HistorySequenceAnalysisResult();
        List<ClusterInfo> clusters = new ArrayList<>();
        List<HistorySequenceInfo> idSequences = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : runDayClusterIdsMapping.entrySet()) {
            for (String id : entry.getValue()) {
                HistorySequenceInfo res = new HistorySequenceInfo();
                res.setId(id);
                String runDayClusterSplit[] = entry.getKey().split("_");
                Integer clusterId = Integer.parseInt(runDayClusterSplit[1]);
                String runDay = runDayClusterSplit[0];
                res.setClusterId(clusterId);
                res.setRunDay(runDay);
                res.setRunDayCluster(entry.getKey());
                res.setItemSets(runDayClusterSequenceMap.get(entry.getKey()));
                idSequences.add(res);
            }

            HistorySequenceAnalysisResult.ClusterInfo tempClusterInfo =
                    new HistorySequenceAnalysisResult.ClusterInfo();
            tempClusterInfo.setRunDayCluster(entry.getKey());
            tempClusterInfo.setSameClusterSize(entry.getValue().size());
            clusters.add(tempClusterInfo);
        }
        analysisResult.setIdSequences(idSequences.stream().sorted(Comparator.comparing(HistorySequenceInfo::getRunDayCluster
        ).reversed()).collect(Collectors.toList()));
        analysisResult.setTopClusters(clusters.stream().sorted(Comparator.comparing(ClusterInfo::getSameClusterSize
        ).reversed().thenComparing(Comparator.comparing(ClusterInfo::getRunDayCluster).reversed())).collect(Collectors.toList()));
        return analysisResult;
    }

    /**
     * 查询某一个聚类的数据
     *
     * @param requestParam
     * @return
     */
    @Override
    public PageResult<ClusterSequenceResult> fetchCluster(ClusterSequenceParam requestParam) {

        boolean hasUserIdParam = false;

        String clusterTableSql = " select id,runDay,clusterId  from behavior_cluster_daily d where d.runDay>='${startDate}'\n" +
                "   and d.runDay<='${endDate}'\n";

        if (StringUtils.isNotEmpty(requestParam.getId())) {
            clusterTableSql += " and d.id = '" + requestParam.getId() + "'";
            hasUserIdParam = true;
        }
        if (requestParam.getClusterId() != null) {
            clusterTableSql += " and d.clusterId = " + requestParam.getClusterId();
        }

        String sql = "select t1.clusterId,\n" +
                "       t1.runDay,\n" +
                "       sum(if(t1.id!='',1,0)) clusterSize, \n" +
                "       sum(if(t2.id!='',1,0)) violateSize,\n" +
                "       sum(hasAny(t2.types,['FROZEN'])) frozenSize,\n" +
                "       sum(hasAny(t2.types,['BLACKLIST'])) blacklistSize,\n" +
                "       violateSize/clusterSize handleRatio \n" +
                "from (\n" +
                clusterTableSql +
                ") t1 global all left join (\n" +
                "  select id,runDay,groupArray(type) types from behavior_object_detail d2 " +
                "  where d2.runDay>='${startDate}' and d2.runDay<='${endDate}'\n" +
                "    group by id,runDay\n" +
                ") t2 on t1.id = t2.id and t1.runDay = t2.runDay\n" +
                "group by t1.clusterId,t1.runDay\n";


        String orderBy = " order by t1.runDay desc,handleRatio desc, violateSize desc,clusterSize desc, t1.clusterId desc";
        String firstOrder = requestParam.getOrderColumn();
        List<String> orderColumns = getOrderColumns();
        if (StringUtils.isNotEmpty(firstOrder)) {
            orderColumns.remove(firstOrder);
            orderBy = " order by t1.runDay desc," + firstOrder + " desc,";
            for (int i = 0; i < orderColumns.size(); i++) {
                orderBy += orderColumns.get(i) + " desc,";
            }
            orderBy += " t1.clusterId desc";
        }

        sql += orderBy;
        Map<String, Object> sqlArgMap = new HashMap<>();
        sqlArgMap.put("startDate", requestParam.getStartDate());
        sqlArgMap.put("endDate", requestParam.getEndDate());
        String querySql = new StrSubstitutor(sqlArgMap).replace(sql);
        log.info("querySql for behavior cluster : {}", querySql);

        CKPageResponse response = clickHouseService.executeQueryWithPage(querySql,
                requestParam.getPageNum(), requestParam.getPageSize());

        PageResult pageResult = CKPageResponse.initPageResult(response, requestParam.getPageNum());
        if (CKPageResponse.isEmpty(response)) {
            return pageResult;
        }

        List<String> runDayClusterIds = new ArrayList<>();
        List<ClusterSequenceResult> dataList = new ArrayList<>();
        response.getResponse().forEach(elem -> {
            ClusterSequenceResult res = new ClusterSequenceResult();
            res.setClusterId(elem.get("clusterId"));
            res.setRunDay(elem.get("runDay"));
            res.setClusterSize(Integer.parseInt(elem.get("clusterSize")));
            res.setTotalViolateCount(Integer.parseInt(elem.get("violateSize")));
            res.setFrozenUserCount(Integer.parseInt(elem.get("frozenSize")));
            res.setBlacklistUserCount(Integer.parseInt(elem.get("blacklistSize")));
            dataList.add(res);

            runDayClusterIds.add(generateClusterRunDayKey(elem.get(1), elem.get(0)));
        });

        Map<String, List<String>> clusterRunDaySequenceMap = getClusterRunDaySequences(runDayClusterIds,
                requestParam.getStartDate(),
                requestParam.getEndDate()
                , 5);
        Map<String,Map<String,String>> countMap;
        if (hasUserIdParam) {
            //对于有userId的，需要看userId对应的cluster的大小，重新计算
            sqlArgMap.put("runDayClusterIds",CommonUtil.sqlInParamsJoin(runDayClusterIds));
            countMap = getFixedRunDayClusterAggInfo(sqlArgMap);
        }else{
            countMap = new HashMap<>();
        }
        dataList.forEach(elem -> {
            String runDayCluster = generateClusterRunDayKey(elem.getRunDay(), elem.getClusterId());
            elem.setItemSets(clusterRunDaySequenceMap.get(runDayCluster));

            //计算cluster的处罚情况
            if (countMap.containsKey(runDayCluster)) {
                Map<String,String> row = countMap.get(runDayCluster);
                elem.setClusterSize(Integer.parseInt(row.get("clusterSize")));
                elem.setTotalViolateCount(Integer.parseInt(row.get("violateSize")));
                elem.setFrozenUserCount(Integer.parseInt(row.get("frozenSize")));
                elem.setBlacklistUserCount(Integer.parseInt(row.get("blacklistSize")));
            }

        });

        pageResult.setDataList(dataList);
        return pageResult;
    }

    private List<String> getOrderColumns(){
        List<String> orderColumns = new ArrayList<>(Arrays.asList("handleRatio#violateSize#clusterSize".split("#")));
        return orderColumns;
    }

    /***
     * 查询特定日期指定cluster的聚合信息
     * @return
     */
    private Map<String,Map<String,String>> getFixedRunDayClusterAggInfo(Map<String,Object> sqlArgMap){
        String sql = "select t1.clusterId,\n" +
                "       t1.runDay,\n" +
                "       sum(if(t1.id!='',1,0)) clusterSize,\n" +
                "       sum(if(t2.id!='',1,0)) violateSize,\n" +
                "       sum(hasAny(t2.types,['FROZEN'])) frozenSize,\n" +
                "       sum(hasAny(t2.types,['BLACKLIST'])) blacklistSize,\n" +
                "       violateSize/clusterSize handleRatio\n" +
                "from (\n" +
                "        select id,runDay,clusterId  from behavior_cluster_daily d where d.runDay>='${startDate}'\n" +
                "                       and d.runDay<='${endDate}' and concat(toString(runDay),'_',toString" +
                "(clusterId))\n" +
                "     in (${runDayClusterIds})\n" +
                ") t1 global all left join (\n" +
                "  select id,runDay,groupArray(type) types from behavior_object_detail d2 where d2.runDay>='${startDate}'\n" +
                "                                                         and d2.runDay<='${endDate}'\n" +
                "    group by id,runDay\n" +
                ") t2 on t1.id = t2.id and t1.runDay = t2.runDay\n" +
                "group by t1.clusterId,t1.runDay;";
        List<Map<String,String>> clusterCountResp =
                clickHouseService.executeQuery(new StrSubstitutor(sqlArgMap).replace(sql));
        Map<String,Map<String,String>> responseMap = new HashMap<>();
        if(clusterCountResp==null||CollectionUtils.isEmpty(clusterCountResp)){
            return responseMap;
        }
        clusterCountResp.stream().forEach(elem->{
            responseMap.put(elem.get("runDay")+"_"+elem.get("clusterId"),elem);
        });
        return responseMap;
    }

    @Override
    public ExportData fetchClusterIdSequences(Integer clusterId, String runDay) {
        String sql = "select id,eventList from behavior_sequence_daily where runDay =  '%s'\n" +
                "and id global in (\n" +
                "    select id from behavior_cluster_daily where runDay = '%s' and clusterId = %d \n" +
                "        ) ";
        CkQueryResult<List<String>> response = clickHouseService.executeQueryWithList(String.format(sql, runDay, runDay,
                clusterId));

        return new ExportData(response.getColumnNameList(), response.getResultList());

    }

    @Override
    public List<Map<String, String>> getEventList() {
        Map<String, String> eventCodeNameMap = getEventCodeNameMapping();
        List<Map<String, String>> resultList = new ArrayList<>();
        for (Map.Entry<String, String> entry : eventCodeNameMap.entrySet()) {
            Map<String, String> elem = new HashMap<>();
            elem.put("code", entry.getKey());
            elem.put("name", entry.getValue());
            resultList.add(elem);
        }
        return resultList;
    }

    @Override
    public PageResult<EventSequenceResult> searchEventSequence(EventSequenceParam reqParam) {
        String sql = "select s.id,s.eventList,s.countJson,s.runDay from behavior_sequence_daily s where s.runDay >='%s' and s.runDay<='%s'\n" +
                "and hasAll(splitByChar(' ',eventList),splitByChar(',','%s')) = 1\n";

        String orderSql = "order by runDay desc, length(eventList) asc, id asc";

        if (StringUtils.isNotEmpty(reqParam.getOrderEvent())) {
            orderSql = String.format(" order by  JSONExtractInt(countJson, '%s') desc, runDay desc, length(eventList)" +
                    " asc, id asc ", reqParam.getOrderEvent());
        }


        if (CollectionUtils.isEmpty(reqParam.getSearchEvents())) {
            throw new IllegalArgumentException("搜索事件不可为空");
        }
        CKPageResponse response = clickHouseService.executeQueryWithPage(String.format(sql + orderSql,
                reqParam.getStartTime(),
                reqParam.getEndTime(), StringUtils.join(reqParam.getSearchEvents(), ",")),
                reqParam.getPageNum(), reqParam.getPageSize());

        PageResult pageResult = CKPageResponse.initPageResult(response, reqParam.getPageNum());
        if (CKPageResponse.isEmpty(response)) {
            return pageResult;
        }

        //查询用户处罚情况
        List<String> userRunDays =
                response.getResponse().stream().map(elem -> elem.get("id") + "_" + elem.get("runDay")).collect(Collectors.toList());
        Map<String, Map<String, Boolean>> objectDetailMap = getSequenceObjectDetail(userRunDays, reqParam.getStartTime()
                , reqParam.getEndTime());
        Map<String, String> codeNameMapping = getEventCodeNameMapping();
        List<EventSequenceResult> dataList =
                response.getResponse().stream().map(elem -> {

                    String userRunDay = elem.get("id") + "_" + elem.get("runDay");
                    Map<String, Boolean> detailMap = objectDetailMap.get(userRunDay);

                    boolean frozen = detailMap != null && detailMap.containsKey("FROZEN");
                    boolean blacklist = detailMap != null && detailMap.containsKey("BLACKLIST");

                    EventSequenceResult res = new EventSequenceResult(elem.get("id"), elem.get("eventList"),
                            elem.get("countJson"), elem.get("runDay"), frozen, blacklist);

                    String eventList[] = elem.get("eventList").split(" ");
                    String chineseEventList = Arrays.stream(eventList).map(item -> codeNameMapping.containsKey(item) ?
                            codeNameMapping.get(item) :
                            item).collect(Collectors.joining("=>"));
                    res.setEventSequence(chineseEventList);

                    Map<String, Object> countJsonResult = new HashMap<>();
                    Map<String, Object> countJson = JSON.parseObject(elem.get("countJson"));
                    for (Map.Entry<String, Object> countItem : countJson.entrySet()) {
                        countJsonResult.put(codeNameMapping.containsKey(countItem.getKey()) ?
                                codeNameMapping.get(countItem.getKey()) :
                                countItem.getKey(), countItem.getValue());
                    }
                    res.setCountJson(JSON.toJSONString(countJsonResult));
                    return res;

                }).collect(Collectors.toList());
        pageResult.setDataList(dataList);
        return pageResult;
    }

    private Map<String, Map<String, Boolean>> getSequenceObjectDetail(List<String> userRundays, String startDate,
                                                                      String endDate) {
        String sql = "select id,type,runDay from behavior_object_detail where concat(id,'_',toString(runDay)) in (%s)" +
                "\n" +
                "and runDay>='%s' and runDay<='%s'";

        String sqlFormat = String.format(sql, CommonUtil.sqlInParamsJoin(userRundays), startDate, endDate);

        Map<String, Map<String, Boolean>> resultMap = new HashMap<>();
        List<Map<String,String>> response = clickHouseService.executeQuery(sqlFormat);
        if (response == null || CollectionUtils.isEmpty(response)) {
            return resultMap;
        }
        response.stream().forEach(elem -> {
            String userId = elem.get("id");
            String type = elem.get("type");
            String runDay = elem.get("runDay");
            String key = userId + "_" + runDay;
            if (!resultMap.containsKey(key)) {
                resultMap.put(key, new HashMap<>());
            }
            resultMap.get(key).put(type, true);
        });
        return resultMap;
    }

    public Map<String, List<String>> getClusterRunDaySequences(List<String> runDayClusterIds,
                                                               String startDay,
                                                               String endDay,
                                                               Integer topN) {
        List<Map<String,String>> topNClusterSequences = getTopNClusterSequence(runDayClusterIds, startDay, endDay,
                topN);

        Map<String, String> eventCodeNameMap = getEventCodeNameMapping();

        Map<String, List<String>> clusterRunDaySequenceMap = new HashMap<>();
        topNClusterSequences.stream().forEach(row -> {
            List<String> itemSets = ClickHouseUtils.parseGroupArrayResult(row.get("itemList"));
            itemSets =
                    itemSets.stream().map(elem -> replaceSequenceWithChineseName(elem, eventCodeNameMap)).collect(Collectors.toList());
            clusterRunDaySequenceMap.put(row.get("runDayCluster"), itemSets);
        });
        return clusterRunDaySequenceMap;

    }

    /***
     * 每日每个cluster的topN序列
     * @param runDayClusterIds
     * @param startDay
     * @param endDay
     * @param topN
     * @return
     */
    private List<Map<String,String>> getTopNClusterSequence(List<String> runDayClusterIds,
                                                      String startDay,
                                                      String endDay,
                                                      Integer topN) {

        String clusterRunDayJoins = CommonUtil.sqlInParamsJoin(runDayClusterIds);

        String topNClusterSequenceSql = "select runDayCluster, groupArray(%d)(itemSet) itemList\n" +
                "                        from (\n" +
                "                          select concat(toString(runDay),'_',toString(clusterId)) runDayCluster,\n" +
                "                              itemSet\n" +
                "                             from behavior_cluster_sequence\n" +
                "                             where runDay >= '%s' and runDay <= '%s'\n" +
                "                               and runDayCluster in (%s)\n" +
                "                               and itemSet!='' " +
                "                             order by itemCount desc, supportCount desc\n" +
                "                      ) a group by runDayCluster";
        return clickHouseService.executeQuery(String.format(topNClusterSequenceSql,
                topN, startDay, endDay, clusterRunDayJoins));
    }

    /***
     * clusterId+runDay组合成唯一值
     * @param runDay
     * @param clusterId
     * @return
     */
    private String generateClusterRunDayKey(String runDay, String clusterId) {
        return runDay + "_" + clusterId;
    }


    private List<SeqItemSetInfo> parseTKEResultForTOPK(List<Map<String, Object>> tkeResult,
                                                       Map<String, String> eventCodeNameMap) {
        if (CollectionUtils.isEmpty(tkeResult)) {
            return new ArrayList<>();
        }

        List<TKEResult> tkeList = tkeResult.stream().map(elem -> JSON.parseObject(JSON.toJSONString(elem),
                TKEResult.class)).collect(Collectors.toList());
        tkeList.sort(Comparator.comparing(TKEResult::getSupportCount).reversed().thenComparing(TKEResult::getItemCount).reversed());

        List<SeqItemSetInfo> resultList;
        if (tkeList.size() > topK) {
            resultList =
                    tkeList.subList(0, topK).stream().map(elem -> {
                        String chineseItemSet = replaceSequenceWithChineseName(elem.getItemSet(), eventCodeNameMap);
                        return new SeqItemSetInfo(chineseItemSet, elem.getItemCount(), elem.getSupportCount());
                    }).collect(Collectors.toList());
        } else {
            resultList = tkeList.stream().map(elem -> {
                String chineseItemSet = replaceSequenceWithChineseName(elem.getItemSet(), eventCodeNameMap);
                return new SeqItemSetInfo(chineseItemSet, elem.getItemCount(), elem.getSupportCount());
            }).collect(Collectors.toList());
        }
        return resultList;
    }


    private String replaceSequenceWithChineseName(String sequence, Map<String, String> eventCodeNameMap) {
        String[] seqArr = sequence.split(" ");
        List<String> resultList = new ArrayList<>();
        for (String seq : seqArr) {
            resultList.add(eventCodeNameMap.containsKey(seq) ? eventCodeNameMap.get(seq) : seq);
        }
        return StringUtils.join(resultList, "=>");
    }


    private SequenceAnalysisResult parseEventList(String id, String eventSequential, Map<String, String> eventCodeNameMap) {

        List<String> seqList = ClickHouseUtils.parseGroupArrayResult(eventSequential);
        //计算频次
        Map<String, Integer> eventCountMap = new HashMap<>();

        Map<String, List<String>> eventListMap =
                seqList.stream().collect(Collectors.groupingBy(item -> parseTargetEvent(item),
                        Collectors.toList()));

        for (Map.Entry<String, List<String>> item : eventListMap.entrySet()) {
            if (targetEventList.contains(item.getKey())) {
                eventCountMap.put(item.getKey(), (int) item.getValue().stream().distinct().count());
            } else {
                eventCountMap.put(item.getKey(), item.getValue().size());
            }
        }

        //构建sequence

        List<String> dataList = new ArrayList<>();
        for (int i = 0; i < seqList.size(); i++) {
            String curItem = seqList.get(i);
            if (dataList.size() >= 1 && dataList.get(dataList.size() - 1).equals(curItem)) {
                continue;
            }
            dataList.add(curItem);
        }

        //只保留target前缀
        List<String> resultList = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            String e1 = dataList.get(i);
            //如果e1是几个特殊的类型
            String resultEvent = parseTargetEvent(e1);
            int existSize = resultList.size();
            if (existSize > 0 && resultList.get(existSize - 1).equals(resultEvent)) {
                continue;
            } else {
                resultList.add(resultEvent);
            }
        }

        Map<String, Integer> eventNameCountMap = new HashMap<>();
        for (Map.Entry<String, Integer> entry : eventCountMap.entrySet()) {
            eventNameCountMap.put(eventCodeNameMap.get(entry.getKey()), entry.getValue());
        }
        SequenceAnalysisResult result = new SequenceAnalysisResult();
        result.setId(id);
        String eventSequence = StringUtils.join(resultList, " ");
        result.setOriginalSequence(eventSequence);
        result.setCountJson(JSON.toJSONString(eventNameCountMap));//event次数(对im等特殊的是事件关联的不同对象次数)
        return result;
    }


    /**
     * 有targetId的事件解析
     *
     * @param event
     * @return
     */
    private static String parseTargetEvent(String event) {
        Optional<String> resultEvent = targetEventList.stream().filter(e -> event.startsWith(e)).findFirst();
        if (resultEvent.isPresent()) {
            return resultEvent.get();
        } else {
            return event;
        }
    }

    private static List<String> targetEventList = Arrays.asList("im-message",
            "search",
            "user-emoji",
            "user-follow",
            "assign-order-create",
            "chat-room-enter",
            "live-room-enter");

    @Getter
    @Setter
    public static class TKEResult {
        private String itemSet;
        private Integer supportCount;
        private Integer itemCount;
    }


    public static final String USER_EVENT_SEQUENCE_SQL = "select userId, groupArray(events) eventList\n" +
            "from (\n" +
            "         select userId,\n" +
            "                multiIf(eventCode = 'im-message', concat(eventCode, '_', data_targetUid),\n" +
            "                        eventCode = 'search', concat(eventCode, '_', data_content),\n" +
            "                        eventCode = 'user-emoji', concat(eventCode, '_', data_toUid),\n" +
            "                        eventCode = 'user-follow', concat(eventCode, '_', data_toUid),\n" +
            "                        eventCode = 'assign-order-create', concat(eventCode, '_', data_biggieUserId),\n" +
            "                        eventCode = 'chat-room-enter', concat(eventCode, '_', data_chatRoomId),\n" +
            "                        eventCode = 'live-room-enter', concat(eventCode, '_', data_roomId),\n" +
            "                        eventCode\n" +
            "                    )\n" +
            "                    as events\n" +
            "         from risk_hit_log\n" +
            "         where createdAt >= '%s'\n" +
            "           and createdAt <='%s'\n" +
            "           and userId in (%s)\n" +
            "           and data_Business != 'riskPayment'\n" +
            "           and eventCode != 'user-complain'\n" +
            "           and eventCode != 'complain-post'\n" +
            "         order by createdAt\n" +
            "         ) tmp\n" +
            "group by userId";

}
