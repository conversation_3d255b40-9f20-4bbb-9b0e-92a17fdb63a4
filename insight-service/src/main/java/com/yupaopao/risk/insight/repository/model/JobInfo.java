package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Table(name = "t_job_info")
public class JobInfo implements Serializable {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * Flink Job Id
     */
    @Column(name = "job_id")
    private String jobId;

    /**
     * Job类型 STREAM  流处理  BATCH  批处理
     */
    @Column(name = "job_type")
    private String jobType;

    /**
     * 代码类型 SQL SQL作业 JAR Jar类型作业
     */
    @Column(name = "code_type")
    private String codeType;

    /**
     * 名称
     */
    @Column(name = "job_name")
    private String jobName;

    /**
     * 备注
     */
    @Column(name = "remarks")
    private String remarks;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * SQL类型作业 填写SQL文
     */
    private String code;

    /***
     * 自定义的sql作业jobId。(flink 中提交任务的jobId和executionEnv的jobId不同，需预先设置)
     */
    @Column(name = "random_job_id")
    private String randomJobId;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "status")
    private String status;

    @Column(name="workspace_task_id")
    private Integer workspaceTaskId;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
    }

}