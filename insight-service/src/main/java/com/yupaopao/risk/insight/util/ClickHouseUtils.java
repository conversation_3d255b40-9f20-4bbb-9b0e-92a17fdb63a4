package com.yupaopao.risk.insight.util;

import com.yupaopao.risk.insight.beans.DBTableInfo;
import com.yupaopao.risk.insight.beans.DBTableInfo.TableColumn;
import com.yupaopao.risk.insight.common.clickhouse.CKConnectionConfig;
import com.yupaopao.risk.insight.util.sql.ClickHouseAst;
import com.yupaopao.risk.insight.util.sql.ClickHouseLexer;
import com.yupaopao.risk.insight.util.sql.ClickHouseParser;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.atn.PredictionMode;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-28 19:16
 *
 ***/
public class ClickHouseUtils {


    public static Set<String> fetchTableNames(String sql) {
        sql = sql.replaceAll("\"", "");
        sql = sql.replaceAll("\\\\n", "\n");
        ClickHouseLexer lexer = new ClickHouseLexer(CharStreams.fromString(sql));
        lexer.removeErrorListeners();
        CommonTokenStream tokenStream = new CommonTokenStream(lexer);
        ClickHouseParser parser = new ClickHouseParser(tokenStream);
        parser.removeErrorListeners();
        ClickHouseAst visitor = new ClickHouseAst();
        parser.queryStmt().accept(visitor);
        return visitor.getTableNames();
    }


    /***
     * 生成 ck 本地表 create sql
     * @param tableName
     * @param tableColumns
     * @return
     */
    public static String generateCkLocalReplicatedTableCreateSQL(String tableName, List<TableColumn> tableColumns) {
        String localTable = getCkLocalTable(tableName);
        String sql = "CREATE TABLE IF NOT EXISTS " + localTable + " ON CLUSTER default (";
        sql += buildColumnSQL(tableColumns);
        sql += "\n) engine = ReplicatedMergeTree(\n" +
                "    '/clickhouse/tables/" + localTable + "/{shard}',\n" +
                "    '{replica}') \n" +
                " ORDER BY " + tableColumns.get(0).getColumnName() + "" +
                " \nSETTINGS index_granularity= 8192;";
        return sql;
    }

    /***
     * 生成ck 分布式表 create sql
     * @param tableName
     * @param tableColumns
     * @return
     */
    public static String generateCkDistributedTableCreateSQL(String tableName, List<TableColumn> tableColumns) {
        String localTable = getCkLocalTable(tableName);
        String sql = "CREATE TABLE IF NOT EXISTS " + tableName + " ON CLUSTER default (";
        sql += buildColumnSQL(tableColumns);
        sql += "\n) engine = Distributed(default, default, " + localTable + ", rand());";
        return sql;
    }

    /***
     * 生成batch insert sql
     * @param jsonRowList : 表行数据的json格式
     * @param tableName
     * @return
     */
    public static String generatedBatchInsertSQL(List<String> jsonRowList, String tableName) {
        String result = "";
        String inputJsonData = String.join("\n", jsonRowList);
        String insertFormat = "INSERT INTO %s FORMAT JSONEachRow\n%s";
        result = String.format(insertFormat, tableName, inputJsonData);
        return result;
    }

    public static String generateLocalDropSQL(String tableName) {
        String dropLocalSQL = "\n" +
                "drop table if exists " + getCkLocalTable(tableName) + " on cluster default;";
        return dropLocalSQL;
    }

    public static String generateDistributedDropSQL(String tableName) {
        String dropDistributed = "\n" +
                "drop table if exists " + tableName + " on cluster default;";
        return dropDistributed;
    }


    private static String buildColumnSQL(List<TableColumn> tableColumns) {
        String columnSQL = "";
        boolean isFirst = true;
        for (DBTableInfo.TableColumn column : tableColumns) {
            if (!isFirst) {
                columnSQL += ",";
            }
            isFirst = false;
            columnSQL += "\n" + column.getColumnName() + " " + column.getType();
        }
        return columnSQL;
    }

    private static String getCkLocalTable(String tableName) {
        return tableName + "_local";
    }



    public static List<String> parseGroupArrayResult(String resData) {
        if(CKConnectionConfig.isProd()){
            //groupArray for new version
            resData = resData.substring(1, resData.length() - 1);
            String[] seqList = resData.split(", ");
            return Arrays.asList(seqList);
        }else {
            resData = resData.substring(2, resData.length() - 2);
             String[] seqList = resData.split("\",\"");
             return Arrays.asList(seqList);
        }


    }

    public static void main(String[] args) {
        String sql = "\"risk:risk_device_rawdata\"";
        Set<String> tableNames = ClickHouseUtils.fetchTableNames(sql);
        System.out.println(tableNames);
    }

}
