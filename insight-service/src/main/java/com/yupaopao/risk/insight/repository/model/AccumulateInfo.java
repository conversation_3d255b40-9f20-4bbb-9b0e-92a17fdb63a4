package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc AccumulateInfo
 * @date 2020/11/17
 */
@Table(name = "t_accumulate_info")
@Getter
@Setter
public class AccumulateInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "group_key")
    private String groupKey;

    @Column(name = "agg_key")
    private String aggKey;

    @Column(name = "accumulate_function")
    private String function;

    @Column(name = "time_span")
    private long timeSpan;

    @Column(name = "acc_condition")
    private String condition;

    @Column(name = "punish_config")
    private String punishConfig;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "thresholds")
    private String thresholds;

    @Column(name = "accumulate_action")
    private int action;

    @Column(name = "create_by")
    private String createBy;
}
