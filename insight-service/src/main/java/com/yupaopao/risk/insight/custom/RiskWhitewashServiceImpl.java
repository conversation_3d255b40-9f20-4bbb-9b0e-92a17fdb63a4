package com.yupaopao.risk.insight.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.framework.spring.boot.kafka.KafkaProducer;
import com.yupaopao.framework.spring.boot.kafka.annotation.KafkaAutowired;
import com.yupaopao.risk.insight.api.RiskPortraitService;
import com.yupaopao.risk.insight.api.RiskWhitewashService;
import com.yupaopao.risk.insight.bean.*;
import com.yupaopao.risk.insight.common.constants.TagConstants;
import com.yupaopao.risk.insight.constant.InsightConstants;
import com.yupaopao.risk.insight.repository.risk.dao.RiskRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc RiskWhitewashServiceImpl
 * @date 2020/8/3
 */
@Slf4j
@DubboService
public class RiskWhitewashServiceImpl implements RiskWhitewashService {

    @Autowired
    private RiskRepository riskRepository;

    @KafkaAutowired
    private KafkaProducer kafkaProducer;

    @Autowired
    private RiskPortraitService riskPortraitService;

    @Override
    public PortraitResult factorClean(FactorCleanBean factorCleanBean) {
        PortraitResult portraitResult = new PortraitResult(PortraitCode.SUCCESS);
        try {
            if (factorCleanBean.getFactorIds().size() == 0 || factorCleanBean.getParams().size() == 0) {
                portraitResult.setPortraitCode(PortraitCode.ARG_ILLEGAL);
                return portraitResult;
            }

            List<String> params = riskRepository.queryFactorParams(factorCleanBean.getFactorIds());
            for (String param : params) {
                if (!factorCleanBean.getParams().containsKey(param)) {
                    portraitResult.setPortraitCode(PortraitCode.ARG_ILLEGAL).setMsg("参数不合法，params缺少" + param);
                    return portraitResult;
                }
            }

            List<Integer> factorIds = factorCleanBean.getFactorIds();
            List<JSONObject> factor = new ArrayList<>();

            factorIds.forEach(p -> {
                factor.add(new JSONObject().fluentPut("factorId", p));
            });
            JSONObject factorReset = new JSONObject();
            factorReset.put("action", "RESET");
            factorReset.put("factor", factor);
            factorReset.put("params", factorCleanBean.getParams());
            log.info("发送kafka清洗请求：{}", factorReset.toJSONString());
            kafkaProducer.send(InsightConstants.PURGE_TOPIC, factorReset.toJSONString());
            return portraitResult;
        } catch (Exception e) {
            log.error("factor clean error with params: " + JSON.toJSONString(factorCleanBean), e);
            return portraitResult.setPortraitCode(PortraitCode.ERROR);
        }
    }

    @Override
    public PortraitResult whitewash(WhitewashBean whitewashBean) {
        String value = whitewashBean.getValue();
        String valueType = whitewashBean.getValueType();
        PortraitResult portraitResult = new PortraitResult(PortraitCode.SUCCESS);
        if (StringUtils.isBlank(value) || StringUtils.isBlank(valueType)) {
            portraitResult.setPortraitCode(PortraitCode.ARG_ILLEGAL);
        }

        try {
            switch (valueType.toUpperCase()) {
                case "USER":
                    buildWashMsg(value, TagConstants.DIMENSION_USER);
                    break;
                case "DEVICE":
                    buildWashMsg(value, TagConstants.DIMENSION_DEVICE);
                    break;
                case "IP":
                    buildWashMsg(value, TagConstants.DIMENSION_IP);
                    break;
                default:
                    portraitResult.setPortraitCode(PortraitCode.ARG_ILLEGAL);
                    log.info("不支持该类型: {}", valueType);
                    return portraitResult;
            }
            TagCleanBean tagCleanBean = new TagCleanBean();
            tagCleanBean.setValue(value);
            tagCleanBean.setValueType(valueType);
            tagCleanBean.setCleanType("ALL");
            riskPortraitService.cleanPortrait(tagCleanBean);
        } catch (Exception e) {
            log.error("服务异常", e);
            portraitResult.setPortraitCode(PortraitCode.ERROR);
        }

        return portraitResult;
    }

    private void buildWashMsg(String value, String valueKey) {
        List<Long> factorIds = riskRepository.queryFactorId(valueKey);
        List<JSONObject> factor = new ArrayList<>();
        factorIds.forEach(p -> {
            factor.add(new JSONObject().fluentPut("factorId", p));
        });
        JSONObject factorReset = new JSONObject();
        factorReset.put("action", "RESET");
        factorReset.put("factor", factor);
        factorReset.put("params", new JSONObject().fluentPut(valueKey, value));
        log.info("发送kafka清洗请求：{}", factorReset.toJSONString());
        kafkaProducer.send(InsightConstants.PURGE_TOPIC, factorReset.toJSONString());
    }
}
