package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.TUserGroupVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TUserGroupVOMapper extends Mapper<TUserGroupVO> {
    @Select("<script>"
            + "SELECT id,create_time as createTime, update_time as updateTime,create_by as createBy,update_by as updateBy, name, code" +
            ",remark, to_sql as toSql,status,group_num as groupNum FROM t_user_group_conf WHERE 1=1 "
            + "<if test='groupCode!=null'>"
            + " and code LIKE CONCAT('%',CONCAT(#{groupCode},'%')) "
            + "</if>"
            + "<if test='groupName!=null'>"
            + " and name LIKE CONCAT('%',CONCAT(#{groupName},'%')) "
            + "</if>"
            + " order by update_time desc "
            + "</script>")
    List<TUserGroupVO> queryGroupConf(@Param("groupName") String groupName, @Param("groupCode") String groupCode);
}