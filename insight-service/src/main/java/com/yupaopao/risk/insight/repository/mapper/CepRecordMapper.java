package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.CepRecord;
import com.yupaopao.risk.insight.service.beans.CepQueryParams;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-03 16:03
 *
 ***/
public interface CepRecordMapper extends Mapper<CepRecord> {


    @Select("<script>" +
            "select batch_id as batchId " +
            "from t_cep_record\n" +
            "where 1=1" +
            " and create_time between STR_TO_DATE(#{queryParam.startDate}, '%Y-%m-%d %H:%i:%s') " +
            " and STR_TO_DATE(#{queryParam.endDate}, '%Y-%m-%d %H:%i:%s') " +
            "<if test='queryParam.patternId!=null'>" +
            " and pattern_id=#{queryParam.patternId}" +
            "</if>" +
            "<if test='queryParam.SRuleName!=null and queryParam.SRuleName!=\"\" '>" +
            " and s_rule_name=#{queryParam.SRuleName}" +
            "</if>" +
            "<if test='queryParam.userId!=null and queryParam.userId!=\"\" '>" +
            " and user_id=#{queryParam.userId}" +
            "</if>" +
            "<if test='queryParam.groupByColumnValue!=null and queryParam.groupByColumnValue!=\"\" '>" +
            " and group_by_column_value=#{queryParam.groupByColumnValue}" +
            "</if>" +
            "</script>")
    List<String> getCepResultList(@Param("queryParam") CepQueryParams queryParam);
}
