package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.TaskHistory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TaskHistoryMapper extends Mapper<TaskHistory> {




    @Select("<script>" +
            "select t1.task_id,t1.name, t1.task_status from t_task_history t1 join (\n" +
            "select task_id,name,max(id) maxId from t_task_history where task_id in (\n" +
            "  select id from t_task_info where name \n" +
            "  in" +
            "<foreach item='item' collection='taskNames' separator=',' open='(' close=')'>" +
            "#{item}" +
            "</foreach>" +
            ") \n" +
            "and create_time>= date_add(now() ,interval -1 day)\n" +
            "group by task_id,name\n" +
            ") t2 on t1.id = t2.maxId" +
            "</script>")
    List<TaskHistory> getHistoryByNames(@Param("taskNames") List<String> taskNames);
}
