package com.yupaopao.risk.insight.dashboard.service.impl;

import com.yupaopao.risk.insight.dashboard.beans.QueryRequest;
import com.yupaopao.risk.insight.dashboard.service.ChartBaseService;
import com.yupaopao.risk.insight.repository.model.ChartDetail;
import com.yupaopao.risk.insight.repository.model.ChartInfo;
import com.yupaopao.risk.insight.service.ElasticSearchService;
import com.yupaopao.risk.insight.service.beans.AggregationParam;
import com.yupaopao.risk.insight.service.beans.ChartData;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Created by Avalon on 2020/2/28 16:27
 */
@Component
public class PieChart extends ChartBaseService {

    @Resource
    private ElasticSearchService elasticSearchService;

    @Override
    public String name() {
        return "PIE";
    }

    @Override
    public ChartData fetch(AggregationParam param) {
        return elasticSearchService.termsAgg(param);
    }

    @Override
    public AggregationParam convertParam(QueryRequest request, ChartInfo chartInfo) {
        AggregationParam.AggregationParamBuilder builder = AggregationParam.builder();
        if (chartInfo != null) {
            for (ChartDetail detail : chartInfo.getChartDetails()) {
                switch (detail.getAxis()) {
                    case DATE:
                        builder.dateField(detail.getField());
                        break;
                    case DIMENSION:
                        builder.dimensionField(detail.getField());
                        break;
                    case AGG:
                        builder.aggField(detail.getField());
                        break;
                }
            }
        }
        builder.indexName(chartInfo.getTableName())
                .startDate(request.getStartDate())
                .endDate(request.getEndDate());
        return builder.build();
    }
}
