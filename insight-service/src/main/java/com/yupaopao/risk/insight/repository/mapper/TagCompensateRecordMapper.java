package com.yupaopao.risk.insight.repository.mapper;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.beans.QueryCompensateParam;
import com.yupaopao.risk.insight.repository.model.TagCompensateRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TagCompensateRecordMapper extends Mapper<TagCompensateRecord> {

    @Select("<script>"+
            "SELECT\n" +
            "\ttr.creator,\n" +
            "\ttr.update_time AS updateTime,\n" +
            "\ttr.send_topic AS topic,\n" +
            "\ttr.batch_id AS batchId,\n" +
            "\ttd.value_id as rowKey,\n" +
            "\ttd.tag_code as tagCode,\n" +
            "\ttd.tag_value as tagValue,\n" +
            "\ttd.tag_name as tagName\n" +
            "FROM\n" +
            "\tt_tag_compensate_record AS tr,\n" +
            "\tt_tag_compensate_detail AS td \n" +
            "WHERE\n" +
            "\t1 = 1 \n" +
            "\tAND tr.batch_id = td.batch_id \n"
            + "<if test='queryCompensateParam.value!=null'>"
            + " and td.value_id=#{queryCompensateParam.value}"
            + "</if>"
            + "<if test='queryCompensateParam.sendTopic!=null and queryCompensateParam.sendTopic!=\"\" '>"
            + " and tr.send_topic LIKE CONCAT('%',#{queryCompensateParam.sendTopic},'%')"
            + "</if>"
            + "<if test='queryCompensateParam.userName!=null'>"
            + " and tr.creator LIKE CONCAT('%',#{queryCompensateParam.userName},'%')"
            + "</if>"
            + "<if test='queryCompensateParam.code!=null'>"
            + " and td.tag_code = #{queryCompensateParam.code}"
            + "</if>"
            + "<if test='queryCompensateParam.name!=null'>"
            + " and td.tag_name LIKE CONCAT('%',#{queryCompensateParam.name},'%')"
            + "</if>"
            + " order by tr.update_time  desc"
            + "</script>")
    List<JSONObject> getAllCompensateRecord(@Param("queryCompensateParam") QueryCompensateParam queryCompensateParam);


}
