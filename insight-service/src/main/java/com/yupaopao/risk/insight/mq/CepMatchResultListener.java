package com.yupaopao.risk.insight.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.framework.spring.boot.kafka.KafkaProducer;
import com.yupaopao.framework.spring.boot.kafka.annotation.KafkaAutowired;
import com.yupaopao.risk.insight.constant.InsightConstants;
import com.yupaopao.risk.insight.repository.model.CepRecord;
import com.yupaopao.risk.insight.repository.model.CepResult;
import com.yupaopao.risk.insight.service.CepMatchService;
import com.yupaopao.risk.insight.service.CepService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-17 17:37
 *
 ***/

@Slf4j
@Component
public class CepMatchResultListener {

    @Value("${cep.match.postHandler.switch:false}")
    private boolean postHandlerSwitch;

    @Autowired
    private CepService cepService;
    @Autowired
    private CepMatchService cepMatchService;

    @KafkaAutowired
    private KafkaProducer kafkaProducer;


    @KafkaListener(topics = "RISK-INSIGHT-CEP-MATCH")
    public void processMatchResult(ConsumerRecord<String, String> record) {
        try {

            JSONObject obj = JSON.parseObject(record.value());
            Integer ruleId = Integer.valueOf(obj.getString("ruleId"));
            log.info("cep match: {}", record.value());
            CepResult result = new CepResult();
            result.setTraceId(obj.getString("traceId"));
            result.setUserId(obj.getString("userId"));
            result.setDetail(obj.getString("detail"));
            result.setPatternId(ruleId);
            result.setSRuleName(obj.getString("sRuleName"));
            result.setGroupByColumn(obj.getString("groupByColumn"));
            result.setGroupByColumnValue(obj.getString("groupByColumnValue"));
            result.setBatchId(obj.getString("batchId"));
            Long createdTime = obj.getLong("createTime");

            CepRecord cepRecord = new CepRecord();
            cepRecord.setBatchId(obj.getString("batchId"));
            cepRecord.setPatternId(ruleId);
            cepRecord.setSRuleName(obj.getString("sRuleName"));
            cepRecord.setUserId(obj.getString("userId"));
            cepRecord.setGroupByColumn(obj.getString("groupByColumn"));
            cepRecord.setGroupByColumnValue(obj.getString("groupByColumnValue"));
            if(createdTime!=null){
                result.setCreateTime(new Date(createdTime));
                cepRecord.setCreateTime(new Date(createdTime));
            }
            String subBatchId = obj.getString("subBatchId");

            cepService.saveCepRecord(cepRecord);
            cepService.saveCepResult(result);
            //中转 kafka 入 ck
            if (Objects.nonNull(result.getDetail())){
                Map detailMap = JSONObject.parseObject(result.getDetail(), Map.class);
                detailMap.remove("$groupByColumn$");
                detailMap.remove("$ruleId$");
                detailMap.remove("kafkaTopic");
                detailMap.remove("kafkaMsgTime");
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(result));
                jsonObject.remove("detail");
                jsonObject.putAll(detailMap);
                kafkaProducer.send(InsightConstants.RISK_INSIGHT_CEP_MATCH_RECORD_TOPIC, jsonObject.toJSONString());
            }

            //增加开关
            if (postHandlerSwitch) {
                cepMatchService.process(result, subBatchId);
            }

        } catch (Exception e) {
            log.error("save cep match result error: ", e);
        }

    }

}
