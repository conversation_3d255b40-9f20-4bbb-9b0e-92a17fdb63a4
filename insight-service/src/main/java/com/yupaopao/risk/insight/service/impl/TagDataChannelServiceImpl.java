package com.yupaopao.risk.insight.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.insight.beans.TagDataChannelParam;
import com.yupaopao.risk.insight.repository.mapper.DataChannelConfMapper;
import com.yupaopao.risk.insight.repository.model.DataChannelConfVO;
import com.yupaopao.risk.insight.service.TagDataChannelService;
import com.yupaopao.risk.insight.service.beans.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class TagDataChannelServiceImpl implements TagDataChannelService {

    @Autowired
    private DataChannelConfMapper dataChannelConfMapper;

    @Override
    public void save(TagDataChannelParam param) {
        DataChannelConfVO dataChannelConfVO = getDataChannelConfVO(param);
        dataChannelConfMapper.insert(dataChannelConfVO);
    }

    private DataChannelConfVO getDataChannelConfVO(TagDataChannelParam param) {
        DataChannelConfVO dataChannelConfVO = new DataChannelConfVO();
        dataChannelConfVO.setCode(param.getCode());
        dataChannelConfVO.setName(param.getName());
        dataChannelConfVO.setCreateBy(param.getCreateBy());
        dataChannelConfVO.setUpdateBy(param.getUpdateBy());
        dataChannelConfVO.setRemark(param.getRemark());
        dataChannelConfVO.setExtendConf(param.getExtendConf());
        dataChannelConfVO.setType(param.getType());
        Date now = new Date();
        dataChannelConfVO.setCreateTime(now);
        dataChannelConfVO.setUpdateTime(now);
        return dataChannelConfVO;
    }

    @Override
    public void delete(Integer id) {
        dataChannelConfMapper.deleteByPrimaryKey(id);
    }

    @Override
    public DataChannelConfVO upsetRecord(TagDataChannelParam param) {
        DataChannelConfVO dataChannelConfVO = getDataChannelConfVO(param);
        if (param.getId() == null) {
            dataChannelConfVO.setCreateBy(param.getUpdateBy());
            dataChannelConfMapper.insert(dataChannelConfVO);
        } else {
            dataChannelConfVO.setId(param.getId());
            dataChannelConfMapper.updateByPrimaryKeySelective(dataChannelConfVO);
        }
        return dataChannelConfVO;
    }

    @Override
    public PageResult<DataChannelConfVO> query(TagDataChannelParam param, Integer pageSize, Integer pageNumber) {
        PageHelper.startPage(pageNumber, pageSize);
        List<DataChannelConfVO> dataChannelConfVOS = dataChannelConfMapper.query(param);
        PageInfo page = new PageInfo<>(dataChannelConfVOS);
        PageResult<DataChannelConfVO> pageResult = new PageResult<>();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setDataList(dataChannelConfVOS);
        return pageResult;
    }

    @Override
    public List queryAll() {
        return dataChannelConfMapper.selectAll();
    }
}
