package com.yupaopao.risk.insight.service;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.dto.GraphDTO;
import com.yupaopao.risk.insight.repository.model.GraphInfo;

import java.util.List;

public interface GraphService {
    GraphDTO graphQuery(String data) throws Exception;

    GraphDTO graphSingleNodeQuery(String data) throws Exception;

    GraphDTO graphCommonNode(String data) throws Exception;

    Boolean graphSave(String name, List<JSONObject> nodes, List<JSONObject> edges) throws Exception;

    GraphDTO graphLoad(Integer graphId);

    List<GraphInfo> getGraphList();
}
