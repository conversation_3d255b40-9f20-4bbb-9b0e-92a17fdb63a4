package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.TableDetail;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-05 19:02
 *
 ***/
public interface TableDetailMapper extends Mapper<TableDetail> {

    @Insert("<script>" +
            "insert into t_table_detail(name,type,create_by,remarks,t_id)" +
            " values " +
            " <foreach collection='columns' item='item' separator =','>" +
            " (#{item.name},#{item.type},#{item.createBy},#{item.remarks},#{item.tableId}) " +
            " </foreach>" +
            "</script>")
    int insertList(@Param("columns") List<TableDetail> detailList);
}
