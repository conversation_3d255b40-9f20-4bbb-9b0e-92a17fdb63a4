package com.yupaopao.risk.insight.repository.model;

import com.yupaopao.risk.insight.enums.BloodTypeEnum;
import lombok.Data;

import javax.persistence.*;

/**
 * Created by Avalon on 2023/12/14 15:48
 **/
@Data
@Table(name = "t_blood")
public class Blood {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "name")
    private String name;

    @Column(name = "relate_id")
    private Integer relateId;

    @Column(name = "type")
    private BloodTypeEnum type;
}
