package com.yupaopao.risk.insight.repository.risk.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class GrayList implements Serializable {
    private static final long serialVersionUID = 8406350037315204985L;
    private String uid;
    private String author;
    private String comment;
    private String name;
    private String flag;
    private Date startTime;
    private Date expireTime;
}

