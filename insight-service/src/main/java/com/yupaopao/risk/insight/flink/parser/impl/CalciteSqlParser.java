//package com.yupaopao.risk.insight.flink.parser.impl;
//
//import com.yupaopao.risk.insight.beans.DBTableInfo;
//import com.yupaopao.risk.insight.common.meta.JobParams;
//import com.yupaopao.risk.insight.flink.parser.InsightSqlParser;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.calcite.config.Lex;
//import org.apache.calcite.sql.SqlNode;
//import org.apache.calcite.sql.parser.SqlParseException;
//import org.apache.calcite.sql.parser.SqlParser;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//
//
///****
// *
// * @Author: zengxiangcai
// * @Date: 2019-12-05 17:08
// *
// ***/
//
//@Slf4j
//public class CalciteSqlParser implements InsightSqlParser {
//
//
//    @Override
//    public List<String> getTableNames(String sql) {
//        SqlNode sqlNode = parseSelect(sql);
//        Set<String> tables = CalciteIdentifierFinder.extractTableNamesInSql(sqlNode);
//        return new ArrayList<>(tables);
//    }
//
//    @Override
//    public DBTableInfo parseCreate(String sql) {
//        return null;
//    }
//
//    @Override
//    public List<JobParams.TableQueryDatePeriod> getTableQueryDatePeriod(String sql) {
//        SqlNode sqlNode = parseSelect(sql);
//        return CalciteIdentifierFinder.extractDataPeriod(sqlNode,
//                TABLE_QUERY_PERIOD_COLUMN);
//
//    }
//
//    @Override
//    public Map<String, Set<String>> getTableColumns(String sql) {
//        SqlNode sqlNode = parseSelect(sql);
//        return CalciteIdentifierFinder.extractTableColumnsInSQL(sqlNode);
//    }
//
//
//    private SqlNode parseSelect(String sql) {
//        SqlParser.ConfigBuilder configBuilder = SqlParser.configBuilder();
//        configBuilder.setCaseSensitive(true);
//        configBuilder.setLex(Lex.MYSQL);
//        SqlParser.Config config = configBuilder.build();
//        SqlParser parser = SqlParser.create(sql, config);
//        try {
//            return parser.parseQuery();
//        } catch (SqlParseException e) {
//            log.error("get table queryDate period: ", e);
//            throw new IllegalArgumentException("parse sql error: " + e.getMessage());
//        }
//    }
//
//
//}
