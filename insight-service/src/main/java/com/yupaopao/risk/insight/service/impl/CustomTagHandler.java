package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.framework.spring.boot.kafka.KafkaProducer;
import com.yupaopao.framework.spring.boot.kafka.annotation.KafkaAutowired;
import com.yupaopao.risk.insight.constant.InsightConstants;
import com.yupaopao.risk.insight.enums.CepMatchHandlerType;
import com.yupaopao.risk.insight.repository.model.CepPattern;
import com.yupaopao.risk.insight.repository.model.CepResult;
import com.yupaopao.risk.insight.service.CepMatchResultHandler;
import com.yupaopao.risk.insight.service.beans.CustomTagMessage;
import com.yupaopao.risk.insight.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CustomTagHandler implements CepMatchResultHandler {

    @KafkaAutowired
    private KafkaProducer kafkaProducer;

    @Override public boolean filter(String type, Object subBatchId) {
        return CepMatchHandlerType.CUSTOMPORTRAIT.getKey().equals(type) && "0".equals(subBatchId);
    }

    @Override public boolean doHandle(CepResult result, CepPattern pattern) {
        // 处理cep 结果
        log.info("开始处理cep 打标结果");
        if (StringUtils.isEmpty(result.getUserId())) {
            return false;
        }
        JSONObject jsonObj = JSON.parseObject(pattern.getResultHandler());
        String labelCode = CommonUtil.read(jsonObj, "$.data.code");
        String labelName = CommonUtil.read(jsonObj, "$.data.name");
        String groupKey = CommonUtil.read(jsonObj, "$.data.groupKey");

        String groupValue = CommonUtil.read(result.getDetail(), "$." + groupKey);

        CustomTagMessage customTagMessage = new CustomTagMessage();
        customTagMessage.setGroupKey(groupKey);
        customTagMessage.setGroupValue(groupValue);
        customTagMessage.setCode(labelCode);
        customTagMessage.setName(labelName);
        customTagMessage.setType("custom");
        customTagMessage.setValueType("Boolean");
        customTagMessage.setValue(Boolean.TRUE);
        customTagMessage.setCreatedAt(new DateTime().getMillis());
        sendCustomTag(customTagMessage);
        return true;
    }

    private void sendCustomTag(CustomTagMessage message) {
        String value = JSON.toJSONString(message);
        log.debug("发送自定标签命中消息:{}/{}", InsightConstants.CUSTOM_TAG_TOPIC, value);
        try {
            kafkaProducer.send(InsightConstants.CUSTOM_TAG_TOPIC, value);
        } catch (Exception e) {
            log.error("发送自定标签消息失败", e);
        }
    }

}
