package com.yupaopao.risk.insight.repository.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * Copyright (C), 2021, jimmy
 *
 * <AUTHOR>
 * @desc TagLevel
 * @date 2021/12/3
 */
@Getter
@Setter
@Table(name ="t_tag_level")
public class TagLevel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "level_code")
    private String levelCode;

    @Column(name = "tag_id")
    private Integer tagId;

    @Column(name = "tag_code")
    private String tagCode;

    @Column(name = "value_type")
    private String valueType;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_by")
    private String createBy;
}
