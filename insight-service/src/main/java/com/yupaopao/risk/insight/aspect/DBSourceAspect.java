package com.yupaopao.risk.insight.aspect;

import com.yupaopao.risk.insight.config.DBContextHolder;
import com.yupaopao.risk.insight.config.DBSource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/****
 * 多数据源选择
 */

@Component
@Aspect
@Slf4j
public class DBSourceAspect {

    @Pointcut("@within(com.yupaopao.risk.insight.aspect.RiskDBSwitch)")
    private void switchDB() {
    }

    @Before(value = "switchDB()")
    public void beforeDBSwitch(JoinPoint jp) {
        RiskDBSwitch db = jp.getTarget().getClass().getAnnotation(RiskDBSwitch.class);
        if (db != null) {
            DBContextHolder.setDBSource(db.value());
        } else {
            DBContextHolder.setDBSource(DBSource.RISK_INSIGHT);
        }
    }

    @After(value = "switchDB()")
    public void afterDBSwitch(JoinPoint jp) {
        RiskDBSwitch db = jp.getTarget().getClass().getAnnotation(RiskDBSwitch.class);
        if (db != null) {
            DBContextHolder.remove();
        }
    }


}
