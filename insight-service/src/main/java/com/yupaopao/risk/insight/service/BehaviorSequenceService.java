package com.yupaopao.risk.insight.service;

import com.yupaopao.risk.insight.service.beans.*;

import java.util.List;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-09-17 14:38
 * 行为序列分析
 ***/
public interface BehaviorSequenceService {

    List<SequenceAnalysisResult> realTimeAnalysis(List<String> ids, String startTime, String endTime);

    HistorySequenceAnalysisResult historyAnalysis(List<String> ids, String startTime, String endTime);

    PageResult<ClusterSequenceResult> fetchCluster(ClusterSequenceParam requestParam);

    ExportData fetchClusterIdSequences(Integer clusterId, String runDay);

    List<Map<String,String>> getEventList();

    PageResult<EventSequenceResult> searchEventSequence(EventSequenceParam reqParam);
}
