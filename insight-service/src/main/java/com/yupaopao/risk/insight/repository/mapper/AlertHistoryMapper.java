package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.AlertHistory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.security.access.prepost.PreAuthorize;
import tk.mybatis.mapper.common.Mapper;

/****
 * zengxiangcai
 * 2022/11/15 14:38
 ***/
public interface AlertHistoryMapper  extends Mapper<AlertHistory> {


    @Select("select count(1) from t_alert_history where alert_rule_id = #{ruleId} and create_time>=STR_TO_DATE(#{createTime}, '%Y-%m-%d %H:%i:%s')")
    Integer sendHistoryCount(@Param("ruleId") Long ruleId, @Param("createTime") String minCreateTime);
}
