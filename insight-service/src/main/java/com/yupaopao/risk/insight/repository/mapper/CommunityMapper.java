package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.CommunityInfo;
import com.yupaopao.risk.insight.service.beans.CommunityQueryParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-31 16:33
 *
 ***/
public interface CommunityMapper extends Mapper<CommunityInfo> {

    @Select("<script>" +
            "select distinct name,community_label as communityLabel,vertex_count as vertexCount, tags " +
            " as " +
            "tags " +
            "from t_community t\n" +
            " where 1=1 " +
            "<if test='name!=null and name!=\"\" '>" +
            " and name like concat('%',#{name},'%')" +
            "</if>" +
            "<if test='communityLabel!=null  and communityLabel!=\"\" '>" +
            " and community_label = #{communityLabel} " +
            "</if>" +
            " order by vertex_count desc " +
            "</script>")
    List<CommunityInfo> getCommunities(CommunityQueryParam queryParam);

    @Select("<script>" +
            "select t.id ,t.name,t.community_label as communityLabel,vertex_count as vertexCount, tags, min(t" +
            ".create_time) as " +
            "runDay  " +
            "\n" +
            " from t_community t join t_community_detail t1 on t.community_label = t1.community_label\n" +
            " where  t1.vertex_id = #{vertexId}\n " +
            "<if test='name!=null and name!=\"\" '>" +
            " and t.name like concat('%',#{name},'%')" +
            "</if>" +
            "<if test='communityLabel!=null and communityLabel!=\"\" '>" +
            " and t.community_label = #{communityLabel} " +
            "</if>" +
            " group by t.id,t.name,t.community_label,t.vertex_count, t.tags" +
            " order by t.create_time desc, t.id asc " +
            "</script>")
    List<CommunityInfo> getCommunitiesWithVertexId(CommunityQueryParam queryParam);

    @Update("" +
            "update t_community t set t.name = #{name}, t.tags=#{tags},update_time = now() where" +
            " " +
            " community_label=#{communityLabel}" +
            "")
    int editCommunity(CommunityInfo community);

    @Select("" +
            "select  (case when original_label!='' then original_label\n" +
            "        else community_label end) as communityLabel,\n" +
            "        run_day as runDay\n" +
            "from t_community t where t.run_day = #{runDay}" +
            "")
    List<CommunityInfo> getCommunitiesByRunDay(@Param("runDay") String runDay);

    @Select("select distinct tags from t_community where tags!=''")
    List<String> getAllTags();

    @Select("<script>"
            + "select DISTINCT community_label as communityLabel,tags, vertex_count as vertexCount from t_community where run_day = #{queryParam.runDay}"
            + " and community_label in "
            + "<foreach item='item' collection='queryParam.labelList' separator=',' open='(' close=')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    List<CommunityInfo> queryTagByLabelList(@Param("queryParam") CommunityQueryParam queryParam);
}
