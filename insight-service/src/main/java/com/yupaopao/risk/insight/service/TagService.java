package com.yupaopao.risk.insight.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.beans.DisplayTagInfo;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.dto.TagInfoDTO;
import com.yupaopao.risk.insight.repository.model.*;
import com.yupaopao.risk.insight.service.beans.PageResult;

import java.util.List;

public interface TagService {

    PageResult<JSONObject> getTagList(TagInfoDTO tagInfo, Integer pageSize, Integer currentPage);

    PageResult<JSONObject> getTagInfo(CustomTag customTag, Integer pageSize, Integer currentPage);

    TagInfo upsertTag(TagInfo tagInfo);

    CustomTag upsertCustomTag(CustomTag customTag);

    Boolean deleteTag(Integer tagId);

    Boolean deleteCustomTag(Integer customTagId);

    List<CustomTag> getCustomTags();

    List<String> getTagCodes();

    List<JSONObject> getTagListAndDetail();

    boolean tagPatternUntying(Integer customTagId);

    PageResult<TagTypeInfo> getTagTypeList(TagTypeInfo typeInfo, Integer pageSize, Integer currentPage);

    TagTypeInfo upsertTagType(TagTypeInfo typeInfo);

    Boolean deleteTagType(Integer tagId);

    List<TagTypeInfo> getTagTypes();

    TagDetail upsertTagDetail(TagDetail typeInfo);

    TagDetail getTagDetailByTagId(Integer tagId);

    Boolean tagRefresh();

    Boolean tagRecall(String recallInfo);

    List<JSONObject> getTagList(String source);

    JSONObject getTagByCode(String code);

    JSONObject tagAnalyze(JSONObject data);

    List<TagLevel> getTagLevelByTag(Integer tagId);

    TagLevel upsertTagLevel(TagLevel tagInfo);

    Boolean deleteTagLevel(Integer id);

    List<JSONObject> getTagLevelAll();

    List<DisplayTagInfo> getDisplayTagList(String groupKey);
    List<JSONObject> getAllTagViolation();

    List<JSONObject> getAllMultiDimensionTag();

    List<JSONObject> getTagRelateKafka();

    List<JSONObject> getTagRelateThird();

}
