package com.yupaopao.risk.insight.repository.mapper;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.repository.model.TaskInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface TaskInfoMapper extends Mapper<TaskInfo> {

    /**
     * 通过多条件查询任务列表，支持多标签查询
     * @param taskInfo 查询条件
     * @return 任务列表
     */
    @Select("<script>"
            + "select * from t_task_info where 1=1 "
            + "<if test='id != null'> and id = #{id} </if>"
            + "<if test='name != null and name != \"\"'> and name like CONCAT('%', #{name}, '%') </if>"
            + "<if test='cron != null and cron != \"\"'> and cron = #{cron} </if>"
            + "<if test='taskStatus >= 0'> and task_status = #{taskStatus} </if>"
            + "<if test='readerType != null and readerType != \"\"'> and reader_type = #{readerType} </if>"
            + "<if test='writerType != null and writerType != \"\"'> and writer_type = #{writerType} </if>"
            + "<if test='readerProperties != null and readerProperties != \"\"'> and reader_properties like CONCAT('%', #{readerProperties}, '%') </if>"
            + "<if test='writerProperties != null and writerProperties != \"\"'> and writer_properties like CONCAT('%', #{writerProperties}, '%') </if>"
            + "<if test='keyMapping != null and keyMapping != \"\"'> and key_mapping like CONCAT('%', #{keyMapping}, '%') </if>"
            + "<if test='taskDesc != null and taskDesc != \"\"'> and task_desc like CONCAT('%', #{taskDesc}, '%') </if>"
            + "<if test='businessType != null and businessType != \"\"'> and business_type = #{businessType} </if>"
            + "<if test='duration != null and duration != \"\"'> and duration = #{duration} </if>"
            + "<if test='createBy != null and createBy != \"\"'> and create_by like CONCAT('%', #{createBy}, '%') </if>"
            + "<if test='actionTagsList != null and actionTagsList.size() > 0'>"
            + "  <foreach collection='actionTagsList' item='tag' open='and (' close=')' separator=' or '>"
            + "    action_tags like CONCAT('%', #{tag}, '%')"
            + "  </foreach>"
            + "</if>"
            + "<if test='actionTags != null and actionTags != \"\" and (actionTagsList == null or actionTagsList.size() == 0)'>"
            + "  and action_tags like CONCAT('%', #{actionTags}, '%')"
            + "</if>"
            + "and task_status != -1 "
            + "order by create_time desc"
            + "</script>")
    List<TaskInfo> findTasksByMultiConditions(TaskInfo taskInfo);

    @Select("<script>"
            + "select JSON_EXTRACT(reader_properties, '$.topic') as srcName, id as relateId, name as dstName from t_task_info\n" +
            "where reader_type = 'Kafka' and task_status > -1 order by srcName"
            + "</script>")
    List<Map<String,Object>> queryTaskReaderTopic();


    @Select("<script>"
            + "select JSON_EXTRACT(writer_properties, '$.topic') as dstName, id as relateId, name as srcName from t_task_info\n" +
            "where writer_type = 'Kafka' and task_status > -1 order by srcName"
            + "</script>")
    List<Map<String,Object>> queryTaskWriterTopic();


    @Select("<script>"
            + "(select JSON_EXTRACT(reader_properties, '$.customSql') as srcName,reader_properties,  reader_type as srcType, id as dstRelateId, name as dstName\n" +
            "from t_task_info\n" +
            "where reader_type in ('ClickHouse', 'Hive', 'JDBC')\n" +
            "  and task_status > -1\n" +
            "order by srcName)\n" +
            "UNION ALL\n" +
            "select JSON_EXTRACT(reader_properties, '$.table') as srcName,reader_properties,  reader_type as srcType, id as dstRelateId, name as dstName\n" +
            "from t_task_info\n" +
            "where reader_type = 'HBASE'\n" +
            "  and task_status > -1\n" +
            "order by srcName"
            + "</script>")
    List<JSONObject> queryTaskReaderTable();

    @Select("<script>"
            + "(select JSON_EXTRACT(writer_properties, '$.table') as dstName, writer_type as dstType, id as srcRelateId, name as srcName\n" +
            "from t_task_info\n" +
            "where writer_type in ('ClickHouse', 'HBASE', 'Hive')\n" +
            "and task_status > -1\n" +
            "order by srcName)\n" +
            "UNION ALL\n" +
            "select JSON_EXTRACT(writer_properties, '$.tableName') as dstName, writer_type as dstType, id as srcRelateId, name as srcName\n" +
            "from t_task_info\n" +
            "where writer_type = 'JDBC'\n" +
            "and task_status > -1\n" +
            "order by srcName"
            + "</script>")
    List<JSONObject> queryTaskWriterTable();


    @Select("<script>"
            + "select id as srcRelateId, name as srcName, relate_tags as dstName\n" +
            "from t_task_info\n" +
            "where task_status > -1\n" +
            "and relate_tags != '' "
            + "</script>")
    List<JSONObject> queryTaskRelateTags();

}
