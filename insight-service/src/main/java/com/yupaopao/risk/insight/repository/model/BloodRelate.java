package com.yupaopao.risk.insight.repository.model;

import com.yupaopao.risk.insight.enums.BloodTypeEnum;
import lombok.Builder;
import lombok.Data;

import javax.persistence.*;

/**
 * Created by Avalon on 2023/12/14 15:48
 **/
@Data
@Table(name = "t_blood_relate")
public class BloodRelate {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "start_id")
    private Integer startId;

    @Column(name = "end_id")
    private Integer endId;
}
