package com.yupaopao.risk.insight.job;

import com.yupaopao.aries.client.JobExecutionContext;
import com.yupaopao.aries.client.JobListener;
import com.yupaopao.framework.spring.boot.aries.annotation.AriesCronJobListener;
import com.yupaopao.risk.insight.constant.AlertConstants;
import com.yupaopao.risk.insight.service.AlertService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/****
 * zengxiangcai
 * 2022/11/8 16:23
 ***/

@Slf4j
@AriesCronJobListener
public class AlertMinuteCheckJob implements JobListener {

    @Resource
    private AlertService alertService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        alertService.checkAlertByPeriod(AlertConstants.ALERT_PERIOD_MINUTE);
    }
}
