package com.yupaopao.risk.insight.config;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-11-19 11:35
 *
 ***/

@Configuration
public class ConfigurationBean {

    @Bean
    public OkHttpClient httpClient(FlinkConfig config) {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        return builder.retryOnConnectionFailure(true)
                //.addInterceptor(retryInterceptor)
                .connectTimeout(config.getConnectionTimeout(), TimeUnit.MILLISECONDS) //连接超时
                .readTimeout(config.getReadTimeout(), TimeUnit.MILLISECONDS) //读取超时
                .writeTimeout(config.getWriteTimeout(), TimeUnit.MILLISECONDS) //写超时
                .connectionPool(new ConnectionPool(100, 1, TimeUnit.MINUTES))
                .build();
    }

    @Bean
    public RestHighLevelClient restHighLevelClient(ElasticSearchConfig config) {
        List<HttpHost> httpHostList = new ArrayList<>();
        String[] addr = config.getHosts().split(",");
        for (String add : addr) {
            String[] pair = add.split(":");
            httpHostList.add(new HttpHost(pair[0], Integer.valueOf(pair[1]), "http"));
        }
        RestClientBuilder builder = RestClient.builder(httpHostList.toArray(new HttpHost[httpHostList.size()]));
        if (StringUtils.isNotBlank(config.getUsername()) && StringUtils.isNotBlank(config.getPassword())) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(config.getUsername(), config.getPassword()));
            builder.setHttpClientConfigCallback(httpClient -> httpClient.setDefaultCredentialsProvider(credentialsProvider));
        }
//        builder.setMaxRetryTimeoutMillis(10000);
        return new RestHighLevelClient(builder);
    }
}
