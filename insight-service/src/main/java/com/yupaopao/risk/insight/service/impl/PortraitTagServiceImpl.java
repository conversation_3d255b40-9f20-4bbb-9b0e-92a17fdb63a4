package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yupaopao.risk.data.api.RiskDataService;
import com.yupaopao.risk.data.bean.RequestData;
import com.yupaopao.risk.data.bean.ResponseData;
import com.yupaopao.risk.insight.common.beans.commons.Tuple2Info;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.common.constants.InsightCommonConstants;
import com.yupaopao.risk.insight.common.constants.TagConstants;
import com.yupaopao.risk.insight.common.support.HBaseSupport;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.SecurityAlgorithmUtils;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import com.yupaopao.risk.insight.service.ClickHouseService;
import com.yupaopao.risk.insight.service.HBaseService;
import com.yupaopao.risk.insight.service.PortraitTagService;
import com.yupaopao.risk.insight.service.beans.BatchPortraitParam;
import com.yupaopao.risk.insight.service.beans.PortraitTagQueryVO;
import com.yupaopao.risk.insight.service.beans.QueryTag;
import com.yupaopao.risk.insight.service.beans.TagParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.hadoop.hbase.client.HTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import ru.yandex.clickhouse.response.ClickHouseResponse;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PortraitTagServiceImpl implements PortraitTagService {

    private static final String QUERY_KEY = "queryKey";
    private static final String QUERY_VALUE = "queryValue";
    private static final String QUERY_VALUE_LIST = "queryValues";
    private static final String QUERY_TYPE = "queryType";
    private static final String DEFAULT_SYSTEM_CODE = "*";
    private static final String APPROACH_CODE = "portraitQuery";

    private static final String PORTRAIT_SERVICE = "portraitQueryService";
    private static final String SUCCESS_CODE = "000";

    private static final String CLICK_HOUSE = "clickHouse";

    private static Map<String, String> tableCache = Maps.newHashMap();

    static {
        tableCache.put(TagConstants.DIMENSION_USER, "risk_user_tag");
        tableCache.put(TagConstants.DIMENSION_DEVICE, "risk_device_tag");
        tableCache.put(TagConstants.DIMENSION_IP, "risk_ip_tag");
        tableCache.put(TagConstants.DIMENSION_MOBILE, "risk_mobile_tag");
        tableCache.put(TagConstants.DIMENSION_CARD, "risk_card_tag");
    }

    @DubboReference(timeout = 1000)
    RiskDataService riskDataService;

    @Autowired
    ClickHouseService clickHouseService;

    @Autowired
    private HBaseService hBaseService;

    @Value("${mobileTagQueryWithEncrypted:false}")
    private String mobileTagQueryWithEncrypted;


    @Override
    public Map<String, List<QueryTag>> queryTag(PortraitTagQueryVO queryVO) {

        if (CLICK_HOUSE.equals(queryVO.getQueryType())) {
            return getCKTags(queryVO);
        } else {
            List<String> queryTags = new ArrayList<>();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(queryVO.getFilterTags())) {
                queryTags = queryVO.getFilterTags();
            } else {
                queryTags.addAll(TagCacheSupport.getTagTypeColumnMap(queryVO.getDimension()).keySet());
            }
            String tableName = tableCache.get(queryVO.getDimension());

            List<String> ids = queryVO.getValueList();
            Map<String, String> encryptedMobileMap = new HashMap<>();
            boolean mobileNoEncrypted = mobileEncrypted(tableName);
            if (mobileNoEncrypted) {
                queryVO.getValueList().forEach(elem -> {
                    String encrypted = SecurityAlgorithmUtils.getSha256Str(InsightCommonConstants.mobileSalt, elem);
                    encryptedMobileMap.put(encrypted, elem);
                });
                ids = new ArrayList<>(encryptedMobileMap.keySet());
            }


            Map<String, Map<String, Tuple2Info<Object, String>>> res =
                    batchQuerySpecifiedTagsWithTime(ids, queryTags, tableName);
            Map<String, List<QueryTag>> finalTagMap = new HashMap<>();
            res.forEach((k, v) -> {
                if (mobileNoEncrypted) {
                    finalTagMap.put(encryptedMobileMap.get(k), transTagMap2Bean(v));
                } else {
                    finalTagMap.put(k, transTagMap2Bean(v));
                }

            });


            return finalTagMap;
        }

    }

    private boolean mobileEncrypted(String queryTable) {
        return "risk_mobile_tag".equalsIgnoreCase(queryTable) && "true".equalsIgnoreCase(mobileTagQueryWithEncrypted);
    }


    private List<QueryTag> transTagMap2Bean(Map<String, Tuple2Info<Object, String>> tags) {
        List<QueryTag> dataList = new ArrayList<>();
        tags.forEach((k, v) -> {
            TagInfoBO tagCfg = TagCacheSupport.getTagInfoByCode(k);
            if (tagCfg == null) {
                return;
            }
            String strV = v.f0.toString();
            if (!"devicePortrait".equals(k)) {
                if ("false".equals(strV) || "[]".equals(strV) || "{}".equals(strV) || StringUtils.isEmpty(strV)) {
                    return;
                }
            }


            QueryTag tagVO = QueryTag.builder()
                    .value(v.f0.toString())
                    .typeName(tagCfg.getTypeName())
                    .tagName(tagCfg.getName())
                    .updateTime(v.f1)
                    .build();
            dataList.add(tagVO);
        });
        dataList.sort(Comparator.comparing(QueryTag::getTypeName));
        return dataList;
    }


    @Override
    public Map<String, Object> batchQueryPortrait(BatchPortraitParam batchPortraitParam) {
        RequestData requestData = new RequestData();
        requestData.withSystemCode(DEFAULT_SYSTEM_CODE)
                .withApproachCode(APPROACH_CODE);
        requestData.addParam(QUERY_KEY, batchPortraitParam.getDimension());
        requestData.addParam(QUERY_VALUE_LIST, batchPortraitParam.getValue());
        requestData.addParam(QUERY_TYPE, "SIMPLE");
        ResponseData riskData = riskDataService.getRiskData(requestData);
        log.info("批量画像结果:{},{}", JSONObject.toJSONString(batchPortraitParam), JSONObject.toJSONString(riskData));
        if (SUCCESS_CODE.equals(riskData.getCode()) && !CollectionUtils.isEmpty(riskData.getData())) {
            Map<String, Object> portraitData = (Map<String, Object>) riskData.getData().get(PORTRAIT_SERVICE);
            return portraitData;
        }
        return Maps.newHashMap();
    }

    @Override
    public Object getSimpleTag(TagParam tagParam) {
        BatchPortraitParam param = new BatchPortraitParam();
        param.setDimension(tagParam.getDimension());
        param.setValue(tagParam.getValue());
        Map<String, Object> response = batchQueryPortrait(param);
        Object portrait = response.get(tagParam.getValue());
        if (Objects.nonNull(portrait) && portrait instanceof Map) {
            Map portraitMap = (Map) portrait;
            return portraitMap.get(tagParam.getCode());
        }
        return "";
    }

    @Override
    public String questData(RequestData requestData) {
        ResponseData riskData = riskDataService.getRiskData(requestData);
        return JSONObject.toJSONString(riskData);
    }

    //    @Override
    public Map<String, List<QueryTag>> batchQueryHbaseTags(PortraitTagQueryVO queryVO) {
        return null;
//        RequestData requestData = new RequestData();
//        requestData.withSystemCode(DEFAULT_SYSTEM_CODE)
//                .withApproachCode(APPROACH_CODE);
//        requestData.addParam(QUERY_KEY, queryVO.getDimension());
//        requestData.addParam(QUERY_VALUE_LIST, queryVO.getValue());
//        requestData.addParam(QUERY_TYPE, "COMPLEX");
//        ResponseData riskData = riskDataService.getRiskData(requestData);
//        Map<String, List<QueryTag>> result = new HashMap<>();
//
//        if (SUCCESS_CODE.equals(riskData.getCode())) {
//            if (!CollectionUtils.isEmpty(riskData.getData())) {
//                Map<String, Object> portraitData = (Map<String, Object>) riskData.getData().get(PORTRAIT_SERVICE);
//                portraitData.forEach((k, v) -> {
//                    Map<String, Object> map = (Map) v;
//                    List<QueryTag> dataList = new ArrayList<>();
//                    ((Map<?, ?>) v).forEach((tagCode, tagValue)->{
//                        if (!"devicePortrait".equals(tagCode)) {
//                            if (!"false".equals(tagValue + "") && !"[]".equals(tagValue) &&
//                                    !"{}".equals(tagValue) && !StringUtils.isEmpty(tagValue)) {
//
//                                QueryTag tag = QueryTag.builder()
//                                        .tagName(tagCode+"")
//                                        .value(tagValue + "").build();
//                                dataList.add(tag);
//                            }
//                        } else {
//                            setDataV2(dataList, tagCode, tagValue);
//                        }
//                    });
//
//                    result.put(k, dataList);
//                });
//            }
//        }
//        return result;
    }

    private Map<String, List<QueryTag>> getCKTags(PortraitTagQueryVO queryVO) {
        Map<String, List<QueryTag>> returnMap = new HashMap<>();


        if (org.apache.commons.collections.CollectionUtils.isEmpty(queryVO.getValueList())) {
            return returnMap;
        }

        String tableName = tableCache.get(queryVO.getDimension());

        List<String> ids = queryVO.getValueList();
        Map<String, String> encryptedMobileMap = new HashMap<>();
        boolean mobileNoEncrypted = mobileEncrypted(tableName);
        if (mobileNoEncrypted) {
            queryVO.getValueList().forEach(elem -> {
                String encrypted = SecurityAlgorithmUtils.getSha256Str(InsightCommonConstants.mobileSalt, elem);
                encryptedMobileMap.put(encrypted, elem);
            });
            ids = new ArrayList<>(encryptedMobileMap.keySet());
        }

        String sql = "";
        String sqlIds = ids.stream().collect(Collectors.joining("','", "'", "'"));
        if (org.apache.commons.collections.CollectionUtils.isEmpty(queryVO.getFilterTags())) {
            String sqlTemplate = "select * from %s where syncDate = '%s' and rowKey in (%s)";
            sql = String.format(sqlTemplate, tableName, queryVO.getQueryDate(), sqlIds);
        } else {
            String sqlTemplate = "select rowKey, %s from %s where syncDate = '%s' and rowKey in (%s)";
            String cols = queryVO.getFilterTags().stream().collect(Collectors.joining(","));
            sql = String.format(sqlTemplate, cols, tableName,
                    queryVO.getQueryDate(), sqlIds);
        }

        List<Map<String, String>> clickHouseResponse =
                clickHouseService.executeQueryWithPage(sql, 1, 10000).getResponse();


        if (CollectionUtils.isEmpty(clickHouseResponse)) {
            return returnMap;
        }
        //rows
        for (int i = 0; i < clickHouseResponse.size(); i++) {
            convertCkRow2DisplayTags(clickHouseResponse.get(i), returnMap);
        }
        if (mobileNoEncrypted) {
            //mobile tag return
            Map<String, List<QueryTag>> mobileTagReturn = new HashMap<>();
            returnMap.forEach((k, v) -> mobileTagReturn.put(encryptedMobileMap.get(k), v));
            return mobileTagReturn;

        }
        return returnMap;
    }

    private void convertCkRow2DisplayTags(Map<String, String> rowData,
                                          Map<String, List<QueryTag>> resultMap) {
        List<QueryTag> dataList = new ArrayList<>();
        String syncDate = "";
        String id = "";
        for (Map.Entry<String, String> entry: rowData.entrySet()) {

            String name = entry.getKey();
            String value = entry.getValue();
            if ("syncDate".equals(name)) {
                syncDate = value;
                continue;
            }
            if ("rowKey".equals(name)) {
                id = value;
                continue;
            }

            if (!"devicePortrait".equals(name)) {
                if ("0".equals(value) || "[]".equals(value) || "{}".equals(value) || StringUtils.isEmpty(value)) {
                    continue;
                }
            }

            TagInfoBO tagInfoByCode = TagCacheSupport.getTagInfoByCode(name);
            if (Objects.isNull(tagInfoByCode)) {
                log.error("查询不到类型:{}", name);
                continue;
            }
            QueryTag tagVO = QueryTag.builder()
                    .value("boolean".equalsIgnoreCase(tagInfoByCode.getValueType()) ? coverValue(value) : value)
                    .typeName(tagInfoByCode.getTypeName())
                    .tagName(tagInfoByCode.getName())
                    .updateTime(syncDate)
                    .build();
            dataList.add(tagVO);
        }

        if (org.apache.commons.collections.CollectionUtils.isEmpty(dataList)) {
            return;
        }
        dataList.sort(Comparator.comparing(QueryTag::getTypeName));
        resultMap.put(id, dataList);
    }


    private String coverValue(String value) {
        if ("1".equals(value)) {
            return "true";
        } else {
            return "false";
        }
    }

    public List<QueryTag> getHbaseTags(PortraitTagQueryVO queryVO) {
        RequestData requestData = new RequestData();
        requestData.withSystemCode(DEFAULT_SYSTEM_CODE)
                .withApproachCode(APPROACH_CODE);
        requestData.addParam(QUERY_KEY, queryVO.getDimension());
        requestData.addParam(QUERY_VALUE, queryVO.getValue());
        requestData.addParam(QUERY_TYPE, "COMPLEX");
        ResponseData riskData = riskDataService.getRiskData(requestData);
        List<QueryTag> dataList = new ArrayList<>();
        if (SUCCESS_CODE.equals(riskData.getCode())) {
            if (!CollectionUtils.isEmpty(riskData.getData())) {
                Map<String, Object> portraitData = (Map<String, Object>) riskData.getData().get(PORTRAIT_SERVICE);
                portraitData.forEach((k, v) -> {
                    Map<String, Object> map = (Map) v;
                    Object tmpValue = map.get("value");
                    if (!"devicePortrait".equals(k)) {
                        if (!"false".equals(tmpValue + "") && !"[]".equals(tmpValue) &&
                                !"{}".equals(tmpValue) && !StringUtils.isEmpty(tmpValue)) {
                            setData(dataList, map, tmpValue);
                        }
                    } else {
                        setData(dataList, map, tmpValue);
                    }

                });
            }
        }
        return dataList;
    }

    @Override
    public Map<String, Map<String, Object>> batchQuerySpecifiedTags(List<String> ids, List<String> tagCodes, String dimension) {
        try (HTable table = HBaseUtil.getTable(getTableNameByDimension(dimension), hBaseService.getConnection());) {
            return HBaseSupport.getMultiRowTags(ids, tagCodes, table);
        } catch (Exception e) {
            log.error("queryTags error", e);
            return new HashMap<>();
        }
    }


    @Override
    public Map<String, Map<String, Tuple2Info<Object, String>>> batchQuerySpecifiedTagsWithTime(List<String> ids,
                                                                                                List<String> tagCodes,
                                                                                                String tableName) {
        try (HTable table = HBaseUtil.getTable(tableName, hBaseService.getConnection());) {
            return HBaseSupport.getMultiRowTagsWithTime(ids, tagCodes, table);
        } catch (Exception e) {
            log.error("queryTagsWithTime error", e);
            return new HashMap<>();
        }
    }


    private String getTableNameByDimension(String dimension) {
        if ("userId".equalsIgnoreCase(dimension)) {
            return "risk_user_tag";
        }
        if ("deviceId".equalsIgnoreCase(dimension)) {
            return "risk_device_tag";
        }
        if ("clientIp".equalsIgnoreCase(dimension)) {
            return "risk_ip_tag";
        }
        if ("mobileNo".equalsIgnoreCase(dimension)) {
            return "risk_mobile_tag";
        }
        if ("card".equalsIgnoreCase(dimension)) {
            return "risk_card_tag";
        }
        throw new RuntimeException("not support dimension: " + dimension);
    }

    private void setData(List<QueryTag> dataList, Map<String, Object> map, Object tmpValue) {

        QueryTag tag = QueryTag.builder()
                .tagName(map.get("tagName") + "")
                .typeName(map.get("typeName") + "")
                .updateTime(format(map.get("updateTime") + "", "yyyy-MM-dd HH:mm:ss"))
                .value(tmpValue + "")
                .build();
        dataList.add(tag);
    }
//    private void setDataV2(List<QueryTag> dataList, String tagCode, Object tmpValue) {
//
//        QueryTag tag = QueryTag.builder()
//                .tagName(tagCode)
//                .value(tmpValue + "").build();
//
//        dataList.add(tag);
//    }

    public String format(String time, String format) {
        if (StringUtils.isEmpty(time)) {
            return "";
        }
        long newTime = Long.valueOf(time);
        if (newTime > 0L && format != null && !"".equals(format)) {
            Date date = new Date(newTime);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
            return simpleDateFormat.format(date);
        } else {
            return "";
        }
    }
}
