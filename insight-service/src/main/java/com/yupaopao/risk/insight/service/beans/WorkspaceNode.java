package com.yupaopao.risk.insight.service.beans;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-24 16:32
 *
 ***/

@Getter
@Setter
@NoArgsConstructor
public class WorkspaceNode {
    private Integer id;
    private String name;
    private Integer parentId;
    private String createBy;
    private boolean leafNode;
    private String type;


    private List<WorkspaceNode> childList;

    public WorkspaceNode(Integer id, String name, Integer parentId, String createBy,Integer leafNode, String type) {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
        this.createBy = createBy;
        this.leafNode = (leafNode == 1);
        this.childList = new ArrayList<>();
        this.type = type;
    }
}
