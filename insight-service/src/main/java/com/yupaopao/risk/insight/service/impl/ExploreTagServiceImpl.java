package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.beans.DisplayTagInfo;
import com.yupaopao.risk.insight.beans.TagExploreRequest;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.service.ClickHouseService;
import com.yupaopao.risk.insight.service.ExploreTagService;
import com.yupaopao.risk.insight.service.TagService;
import com.yupaopao.risk.insight.util.ClickHouseUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/****
 * zengxiangcai
 * 2022/6/13 17:41
 ***/

@Service
public class ExploreTagServiceImpl implements ExploreTagService {

    @Autowired
    private TagService tagService;

    @Autowired
    private ClickHouseService clickHouseService;

    @Override
    public Map<String, Object> executeTagAnalysis(TagExploreRequest req) {

        Map<String, Object> analysisResult = new HashMap<>();

        //default 如果ids为空,随机选取昨日拒绝的一部分用户
        if (StringUtils.isEmpty(req.getIds())) {
            req.setIds(getDefaultIds(req.getType()));
        }
        switch (req.getValueType()) {
            case "numCategoryData":
                analysisResult.put("categoryDataList", executeCategoryAnalysis(req));
                break;
            case "continuousData":
                analysisResult.putAll(executeContinuousAnalysis(req));
                break;
            case "strCategoryData":
                analysisResult.put("strCategoryDataList", executeStrAnalysis(req));
                break;
            default:
                analysisResult.put("booleanDataList", executeBooleanAnalysis(req));
                break;

        }
        return analysisResult;
    }

    /**
     * 取前一日reject数据前100
     */
    private String getDefaultIds(String type) {
        String sql = "select %s from risk_hit_log\n" +
                "where createdAt between concat(toString(yesterday()),' 00:00:00') and concat(toString(yesterday()),' 23:59:59')\n" +
                "  and level = 'REJECT' and data_riskBaseTraceId=''  and userId!='' and deviceId!='' and " +
                "  userId!='0'\n" +
                "  and multiIf(data_body!='',data_body,data_content!='',data_content,data_images!='',data_images,data_avatar!='',data_avatar,data_signature) = ''\n" +
                "  and eventCode not in ('company-ip-check','safe-device','user-face','user-update-nickname') and result_rule!=403\n" +
                "group by %s\n" +
                "order by count(1) desc\n" +
                "limit 100";
        return clickHouseService.executeQuery(String.format(sql, type, type)).stream().map(elem -> elem.get(
                type)).collect(Collectors.joining("\n"));
    }

    /***
     * 第一步找出所有用户中boolean值占比top20的，然后分析这top20的特征在全天中占比重要成都
     * @param req
     * @return
     */
    private List<Map<String, Object>> executeBooleanAnalysis(TagExploreRequest req) {

        String booleanSql = getBooleanSql(req, null);
        if (StringUtils.isEmpty(booleanSql)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> topList = clickHouseService.executeQueryWithObject(booleanSql);

        List<String> topTags = topList.stream().map(elem -> elem.get("featureName").toString()).collect(Collectors.toList());

        TagExploreRequest allActiveReq = JSON.parseObject(JSON.toJSONString(req), TagExploreRequest.class);
        allActiveReq.setIds(null);
        String allActiveUserDataSql = getBooleanSql(allActiveReq, topTags);

        List<Map<String, Object>> allUserTagList = clickHouseService.executeQueryWithObject(allActiveUserDataSql);
        Map<String, Map<String, Object>> tagRatioMap = allUserTagList.stream()
                .collect(Collectors.toMap(elem -> elem.get("featureName").toString(), Function.identity()));

        //数据合并
        return topList.stream().map(elem -> {
            Map<String, Object> ratioMap = tagRatioMap.get(elem.get("featureName").toString());
            elem.put("yesterdayIdCount", ratioMap.get("idCount"));

            BigDecimal yesterdayOneCount = ratioMap.get("oneCount") != null ? (BigDecimal) ratioMap.get("oneCount") :
                    BigDecimal.ZERO;
            elem.put("yesterdayOneCount", yesterdayOneCount);
            if (yesterdayOneCount.compareTo(BigDecimal.ZERO) <= 0) {
                return null;
            }
            BigDecimal yesterdayTrueRatio = ratioMap.get("trueRatio") != null ? (BigDecimal) ratioMap.get("trueRatio") : BigDecimal.ZERO;
            if (yesterdayTrueRatio != null && yesterdayTrueRatio.compareTo(new BigDecimal("0.0000001")) < 0) {
                return null;
            }

            elem.put("yesterdayTrueRatio", yesterdayTrueRatio);
            BigDecimal idRelateCount = elem.get("oneCount") != null ? (BigDecimal) elem.get("oneCount") :
                    BigDecimal.ZERO;
            BigDecimal importanceRatio =
                    idRelateCount.divide(yesterdayOneCount, 4,
                            RoundingMode.HALF_UP);
            elem.put("importanceRatio", importanceRatio);

            return elem;
        }).filter(elem -> elem != null).collect(Collectors.toList());


    }

    private String getBooleanSql(TagExploreRequest req, List<String> existTags) {
        if (CollectionUtils.isEmpty(existTags)) {
            existTags = getAnalysisTags(req);
        }
        if (CollectionUtils.isEmpty(existTags)) {
            return null;
        }
        String selectSql =
                existTags.stream().map(elem -> String.format(
                        "concat('%s', '#', toString(%s))"
                        , elem,
                        elem)).collect(Collectors.joining(",\n", "select rowKey,arrayJoin([", "]) as features from "));

        String fromTable = getTagTable(req.getType());
        selectSql += fromTable;
        String whereCondition = "\n where syncDate = today() and rowKey ";

        String idCondition = "";
        if (StringUtils.isNotEmpty(req.getIds())) {
            idCondition = transIdsToInCondition(req.getIds());
        } else {
            idCondition = " global in (" + getYesterdayActiveResource(req.getType()) + ")";
        }
        whereCondition += idCondition;
        selectSql += whereCondition;

        String booleanSql = "select featureName,count(distinct rowKey) idCount,\n" +
                "countIf(featureValue=0) zeroCount,\n" +
                "countIf(featureValue=1) oneCount,\n" +
                "oneCount/idCount trueRatio from (\n" +
                " select rowKey, splitByChar('#', features)[1] as featureName,\n" +
                "             toInt32OrZero(splitByChar('#', features)[2]) as featureValue\n" +
                " from (" + selectSql + ")\n" +
                ") group by featureName order by oneCount desc \n" +
                "limit 20";

        return booleanSql;
    }

    /****
     * 获取前一天活跃用户或者设备
     * @param type deviceId or userId
     * @return
     */
    private String getYesterdayActiveResource(String type) {
        return String.format("select %s from risk_hit_log \n" +
                " where createdAt between concat(toString(today()),' 00:00:00') and concat(toString(today()),' 23:59:59') and data_riskBaseTraceId='' and userId!='' " +
                "and userId!='0' and userId!='null' and deviceId!='' \n" +
                " and multiIf(data_body!='',data_body,data_content!='',data_content,data_images!='',data_images,data_avatar!='',data_avatar,data_signature) = ''\n" +
                " and eventCode not in ('company-ip-check','safe-device','user-face','user-update-nickname') \n" +
                " group by %s", type, type);
    }

    private String transIdsToInCondition(String ids) {
        return Arrays.asList(ids.split("\n")).stream().map(elem -> elem.trim()).collect(Collectors.joining(
                "','",
                " in ('"
                , "')"));
    }

    /****
     * 数值分类数据分析
     * 分类柱状图，查看一批用户数值top占比的
     * （制定用户占比比较高，全量用户占比比较低的值）
     * @param req
     * @return
     */
    private List<Map<String, Object>> executeCategoryAnalysis(TagExploreRequest req) {
        String categorySql = getCategorySql(req.getType(), req.getIds(), getAnalysisTags(req));
        if (StringUtils.isEmpty(categorySql)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> featureInfos = clickHouseService.executeQueryWithObject(categorySql);

        return featureInfos;
    }

    private String getCategorySql(String type, String ids, List<String> categoryCodeList) {
        if (CollectionUtils.isEmpty(categoryCodeList)) {
            return null;
        }
        String tempTableA = getCategoryInnerTableSql(type, ids, categoryCodeList);
        String tempTableB = getCategoryInnerTableSql(type, null, categoryCodeList);
        String finalSql = "select a.*, b.idCount as allIdCount, a.idCount / b.idCount categoryRatio\nfrom (\n"
                + tempTableA +
                "\n) a global all" +
                " inner join " +
                "(" + tempTableB + "\n)  b on a.featureName = b.featureName and a.featureValue = b.featureValue\n" +
                "order by categoryRatio desc, a.idCount desc";
        return finalSql;
    }

    private String getCategoryInnerTableSql(String type, String ids, List<String> codes) {
        String selectColumns = codes.stream().map(elem -> String.format("concat('%s', '#', toString(%s))", elem, elem))
                .collect(Collectors.joining(",\n", "select rowKey,arrayJoin([", "]) features \nfrom "));

        String fromTable = getTagTable(type);
        String whereCondition = " \nwhere syncDate = today() and rowKey ";
        if (StringUtils.isEmpty(ids)) {
            whereCondition += " global in (" + getYesterdayActiveResource(type) + ")";
        } else {
            whereCondition += transIdsToInCondition(ids);
        }

        String tempTable = selectColumns + fromTable + whereCondition;

        tempTable = "   select featureName, featureValue, count(distinct rowKey) idCount\n" +
                "         from (\n" +
                "                  select rowKey,\n" +
                "                         splitByChar('#', features)[1] featureName,\n" +
                "                         splitByChar('#', features)[2] featureValue\n" +
                "                  from ("
                + tempTable +
                "         )\n" +
                "                  )\n" +
                "         group by featureName, featureValue";
        return tempTable;
    }

    /****
     * 连续性数据分析
     * 整理出箱线图，然后和全量的做些比较（前一天活跃用户）
     * @param req
     * @return
     */
    private Map<String, Object> executeContinuousAnalysis(TagExploreRequest req) {

        String sqlTemplate = "select featureName,min(featureValue) minV,max(featureValue) maxV,median(featureValue) " +
                "medianV,avg(featureValue) avgV,\n" +
                "       quantile(0.25)(featureValue) q1,quantile(0.75)(featureValue) q3,quantile(0.1)(featureValue) ten1,quantile(0.9)(featureValue) ten9\n" +
                "from (\n" +
                "         select rowKey,\n" +
                "                splitByChar('#',features)[1]                featureName,\n" +
                "                toInt32OrZero(splitByChar('#',features)[2]) featureValue\n" +
                "         from (\n" +
                "                  select rowKey,arrayJoin([\n" +
                "                         %s\n" +
                "                             ]) features\n" +
                "                  from %s\n" +
                "                  where syncDate = today()\n" +
                "                    and rowKey %s\n" +
                "                  )\n" +
                "         ) group by featureName";

        List<String> codes = getAnalysisTags(req);
        if (CollectionUtils.isEmpty(codes)) {
            return new HashMap<>();
        }
        String selectColumns = codes.stream().map(elem -> String.format("concat('%s', '#', toString(%s))", elem, elem))
                .collect(Collectors.joining(",\n"));

        String finalSql = String.format(sqlTemplate, selectColumns,
                getTagTable(req.getType()), transIdsToInCondition(req.getIds()));

        //id关联的特征信息
        List<Map<String, Object>> idRelatedFeatures = clickHouseService.executeQueryWithObject(finalSql);
        //全量关联的特征信息
        String globalIn = " global in (" + getYesterdayActiveResource(req.getType()) + ")";
        String yesterdaySql = String.format(sqlTemplate, selectColumns, getTagTable(req.getType()), globalIn);
        List<Map<String, Object>> yesterdayFeatures = clickHouseService.executeQueryWithObject(yesterdaySql);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("continuousDataList", idRelatedFeatures);
        resultMap.put("yesterdayContinuousDataList", yesterdayFeatures);
        return resultMap;
    }


    /***
     * 字符型分类统计
     * @param req
     * @return
     */
    private List<Map<String, Object>> executeStrAnalysis(TagExploreRequest req) {

        String categorySql = getCategorySql(req.getType(), req.getIds(), getAnalysisTags(req));
        if (StringUtils.isEmpty(categorySql)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> featureInfos = clickHouseService.executeQueryWithObject(categorySql).stream().filter(elem -> {
                    Object featureValue = elem.get("featureValue");
                    return featureValue != null && StringUtils.isNotEmpty(featureValue.toString());
                }).collect(Collectors.toList());

        return featureInfos;

    }


    /****
     * 查询数值类型的tag标签
     * 对所有数值型tag进行distinct，相异的数据量<=10认为是分类型标签
     * @return
     */
    private List<String> getNumCategoryTag(String type) {
        List<DisplayTagInfo> tagList = tagService.getDisplayTagList(type);
        String columns =
                tagList.stream().filter(elem -> Arrays.asList("double", "integer", "long").contains(elem.getValueType().toLowerCase())).map(elem -> elem.getCode())
                        .collect(Collectors.joining("),\ncount(distinct ", "count(distinct ", ")"));

        String allNumTagSql = "select " + columns + "\n from " + getTagTable(type) + " where syncDate=(select max" +
                "(syncDate) from " + getTagTable(type) + ")";
        List<Map<String, String>> numTags = clickHouseService.executeQuery(allNumTagSql);
        if (CollectionUtils.isEmpty(numTags)) {
            return new ArrayList<>();
        }
        List<String> categoryColumns = new ArrayList<>();
        for (Map.Entry<String, String> entry : numTags.get(0).entrySet()) {
            Integer distinctCount = Integer.valueOf(entry.getValue());
            if (distinctCount <= 1) {
                //值都相同的不做分析
                continue;
            }
            if (distinctCount <= 10) {
                //String column = "uniqExact(userAuthStatus)";
                categoryColumns.add(entry.getKey().substring(10, entry.getKey().length() - 1));
            }
        }
        return categoryColumns;
    }

    private List<String> getAnalysisTags(TagExploreRequest req) {

        List<String> tags = null;
        switch (req.getValueType()) {
            case "numCategoryData":
                tags = getNumCategoryTag(req.getType());
                break;
            case "strCategoryData":
                tags = getStrCategoryTag(req.getType());
                break;
            case "continuousData":
                tags = getNumContinuousTag(req.getType());
                break;
            default:
                tags = getBooleanTags(req.getType());
                break;
        }
        if (CollectionUtils.isNotEmpty(tags) && CollectionUtils.isNotEmpty(req.getIgnoreList())) {
            tags.removeAll(req.getIgnoreList());
        }
        return tags;
    }

    private List<String> getStrCategoryTag(String type) {
        List<DisplayTagInfo> tagList = tagService.getDisplayTagList(type);
        String columns =
                tagList.stream().filter(elem -> Arrays.asList("string").contains(elem.getValueType().toLowerCase())).map(elem -> elem.getCode())
                        .collect(Collectors.joining("),\ncount(distinct ", "count(distinct ", ")"));

        String allStrTagSql = "select " + columns + "\n from " + getTagTable(type) + " where syncDate=(select max" +
                "(syncDate) from " + getTagTable(type) + ")";
        List<Map<String, String>> numTags = clickHouseService.executeQuery(allStrTagSql);
        if (CollectionUtils.isEmpty(numTags)) {
            return new ArrayList<>();
        }
        List<String> categoryColumns = new ArrayList<>();
        for (Map.Entry<String, String> entry : numTags.get(0).entrySet()) {
            Integer distinctCount = Integer.valueOf(entry.getValue());
            if (distinctCount <= 1) {
                //值都相同的不做分析
                continue;
            }
            if (distinctCount <= 20) {
                //String column = "uniqExact(userAuthStatus)";
                categoryColumns.add(entry.getKey().substring(10, entry.getKey().length() - 1));
            }
        }
        return categoryColumns;
    }


    private List<String> getNumContinuousTag(String type) {
        List<DisplayTagInfo> tagList = tagService.getDisplayTagList(type);
        String columns =
                tagList.stream().filter(elem -> Arrays.asList("double", "integer", "long").contains(elem.getValueType().toLowerCase())).map(elem -> elem.getCode())
                        .collect(Collectors.joining("),\ncount(distinct ", "count(distinct ", ")"));

        String allNumTagSql = "select " + columns + "\n from " + getTagTable(type) + " where syncDate=(select max" +
                "(syncDate) from " + getTagTable(type) + ")";
        List<Map<String, String>> numTags = clickHouseService.executeQuery(allNumTagSql);
        if (CollectionUtils.isEmpty(numTags)) {
            return new ArrayList<>();
        }
        List<String> continueColumns = new ArrayList<>();
        for (Map.Entry<String, String> entry : numTags.get(0).entrySet()) {
            Integer distinctCount = Integer.valueOf(entry.getValue());
            if (distinctCount <= 1) {
                //值都相同的不做分析
                continue;
            }
            if (distinctCount > 10) {
                //String column = "uniqExact(userAuthStatus)";
                continueColumns.add(entry.getKey().substring(10, entry.getKey().length() - 1));
            }
        }
        return continueColumns;
    }

    private List<String> getBooleanTags(String type) {
        List<DisplayTagInfo> tagList = tagService.getDisplayTagList(type);

        return tagList.stream().filter(elem -> "boolean".equalsIgnoreCase(elem.getValueType())).map(elem -> elem.getCode())
                .collect(Collectors.toList());

    }


    private String getTagTable(String type) {
        switch (type) {
            case "userId":
                return "risk_user_tag";
            case "deviceId":
                return "risk_device_tag";
            default:
                throw new RuntimeException("illegal type");
        }
    }

    public static void main(String[] args) {
        String columns =
                new ArrayList<TagInfoBO>().stream().filter(elem -> Arrays.asList("double", "integer", "long").contains(elem.getValueType().toLowerCase())).map(elem -> elem.getCode())
                        .collect(Collectors.joining("),\ncount(distinct ", "count(distinct ", ")"));

        System.err.println(columns);
    }

}
