package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.WorkspaceInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-24 16:27
 *
 ***/

public interface WorkspaceMapper extends Mapper<WorkspaceInfo> {

    @Select("select id, parent_id,level,create_time,update_time,create_by,valid, leaf_node from t_workspace where " +
            "parent_id = #{parentId} and valid=1")
    List<WorkspaceInfo> getChildList(@Param("parentId") Integer parentId);

    @Update("update t_workspace set valid=0 where parent_id = #{parentId} and valid=1")
    boolean invalidateChildNode(@Param("parentId") Integer parentId);

}
