package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.CepResult;
import com.yupaopao.risk.insight.service.beans.CepQueryParams;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-03 16:03
 *
 ***/
public interface CepResultMapper   extends Mapper<CepResult> {

    @Select("<script>" +
            "select distinct batch_id        batchId " +
            "from t_cep_result\n" +
            "where 1=1" +
            " and create_time between STR_TO_DATE(#{queryParam.startDate}, '%Y-%m-%d %H:%i:%s') " +
            " and STR_TO_DATE(#{queryParam.endDate}, '%Y-%m-%d %H:%i:%s') " +
            "<if test='queryParam.patternId!=null'>" +
            " and pattern_id=#{queryParam.patternId}" +
            "</if>" +
            "<if test='queryParam.SRuleName!=null and queryParam.SRuleName!=\"\" '>" +
            " and s_rule_name=#{queryParam.sRuleName}" +
            "</if>" +
            "<if test='queryParam.userId!=null and queryParam.userId!=\"\" '>" +
            " and user_id=#{queryParam.userId}" +
            "</if>" +
            "<if test='queryParam.groupByColumnValue!=null and queryParam.groupByColumnValue!=\"\" '>" +
            " and group_by_column_value=#{queryParam.groupByColumnValue}" +
            "</if>" +
            "</script>")
    List<String> getCepResultList(@Param("queryParam") CepQueryParams queryParam);


    @Select("<script>" +
            "select batch_id  as  batchId,\n" +
            "                pattern_id   as   patternId,\n" +
            "                user_id     as    userId,\n" +
            "                group_by_column as groupByColumn,\n" +
            "                group_by_column_value as groupByColumnValue,\n" +
            "                min(create_time)  as createTime \n" +
            " from t_cep_result\n" +
            " where 1=1 " +
            " and batch_id in " +
            "<foreach item='item' collection='batchList' separator=',' open='(' close=')'>" +
            "#{item}" +
            "</foreach>" +
            " group by\n" +
            "    batchId,\n" +
            "    patternId,\n" +
            "    groupByColumn,\n" +
            "    groupByColumnValue\n" +
            " order by\n" +
            " createTime desc"+
            "</script>")
    List<CepResult> getResultByBatchId(@Param("batchList") List<String> batchList);
}
