package com.yupaopao.risk.insight.repository.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "t_workspace_task_log")
public class WorkspaceTaskLog implements Serializable {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 作业创建人
     */
    @Column(name = "task_create_by")
    private String taskCreateBy;

    /**
     * 作业修改人
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 作业id
     */
    @Column(name = "task_id")
    private Integer taskId;

    /**
     * 作业名称
     */
    @Column(name = "task_name")
    private String taskName;

    /**
     * 作业对应代码
     */
    private String code;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Integer getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取修改时间
     *
     * @return update_time - 修改时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置修改时间
     *
     * @param updateTime 修改时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取作业创建人
     *
     * @return task_create_by - 作业创建人
     */
    public String getTaskCreateBy() {
        return taskCreateBy;
    }

    /**
     * 设置作业创建人
     *
     * @param taskCreateBy 作业创建人
     */
    public void setTaskCreateBy(String taskCreateBy) {
        this.taskCreateBy = taskCreateBy;
    }

    /**
     * 获取作业修改人
     *
     * @return create_by - 作业修改人
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     * 设置作业修改人
     *
     * @param createBy 作业修改人
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取作业id
     *
     * @return task_id - 作业id
     */
    public Integer getTaskId() {
        return taskId;
    }

    /**
     * 设置作业id
     *
     * @param taskId 作业id
     */
    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    /**
     * 获取作业名称
     *
     * @return task_name - 作业名称
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * 设置作业名称
     *
     * @param taskName 作业名称
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    /**
     * 获取作业对应代码
     *
     * @return code - 作业对应代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置作业对应代码
     *
     * @param code 作业对应代码
     */
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", taskCreateBy=").append(taskCreateBy);
        sb.append(", createBy=").append(createBy);
        sb.append(", taskId=").append(taskId);
        sb.append(", taskName=").append(taskName);
        sb.append(", code=").append(code);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}