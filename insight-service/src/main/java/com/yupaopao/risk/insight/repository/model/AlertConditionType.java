package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/****
 * zengxiangcai
 * 2022/11/15 14:13
 ***/

@Getter
@Setter
@Table(name = "t_alert_condition_type")
public class AlertConditionType {
  /**
   * 主键
   */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "condition_desc")
  private String conditionDesc;

  @Column(name = "prom_ql")
  private String promQL;

  @Column(name = "need_tooltips")
  private Integer needTooltips;

  @Column(name = "contains_n")
  private Integer containsN;

  @Column(name = "contains_m")
  private Integer containsM;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "update_time")
  private Date updateTime;

  @Column(name = "create_by")
  private String createBy;

  @Column(name = "update_by")
  private String updateBy;

  private Integer disabled;


}
