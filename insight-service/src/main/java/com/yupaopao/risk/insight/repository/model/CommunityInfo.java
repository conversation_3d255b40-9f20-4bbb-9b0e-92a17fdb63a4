package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Table;
import java.util.Date;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-31 16:13
 *
 ***/

@Getter
@Setter
@Table(name = "t_community")
public class CommunityInfo {

    private Long id;
    private String name;
    private String communityLabel;
    private Integer vertexCount;
    private String runDay;
    private String tags;  //人工打标
    private String remark;

    private Date createTime;
    private Date updateTime;
    private String createBy;
    private String updateBy;

}
