package com.yupaopao.risk.insight.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc DingTalkUtil
 * @date 2020/11/9
 */
@Slf4j
public class DingTalkUtil {
    private static OkHttpClient okHttpClient = new OkHttpClient();

    public static void sendMarkdownMsg(String webhook, String msg, String title) {
        Map<String, String> markdown = new HashMap<>();
        markdown.put("text", msg);
        markdown.put("title", title);
        JSONObject data = new JSONObject();
        data.put("msgtype", "markdown");
        data.put("markdown", markdown);
        sendMsg(webhook, data.toString());
    }

    private static void sendMsg(String url, String data) {
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), data);

        Request request = new Request.Builder()
            .url(url)
            .post(body)
            .build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response == null || response.body() == null) {
                return;
            }
            log.info("send ding talk, msg: {}", response.body());
        } catch (Exception e) {
            log.error("send ding talk error" , e);
        }
    }
}
