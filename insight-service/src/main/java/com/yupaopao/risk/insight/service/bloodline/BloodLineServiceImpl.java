package com.yupaopao.risk.insight.service.bloodline;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.beans.BloodLineRelateVO;
import com.yupaopao.risk.insight.beans.BloodLineRequestVO;
import com.yupaopao.risk.insight.beans.BloodLineResultVO;
import com.yupaopao.risk.insight.enums.BloodTypeEnum;
import com.yupaopao.risk.insight.repository.mapper.BloodMapper;
import com.yupaopao.risk.insight.repository.mapper.BloodRelateMapper;
import com.yupaopao.risk.insight.repository.model.Blood;
import com.yupaopao.risk.insight.repository.model.BloodRelate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Avalon on 2023/12/14 19:18
 **/
@Service
@Slf4j
public class BloodLineServiceImpl implements BloodLineService {

    @Resource
    private BloodMapper bloodMapper;

    @Resource
    private BloodRelateMapper bloodRelateMapper;

    @Override
    public void updateBloodLine(List<BloodLineRelateVO> bloodLineRelateList) {
        if (bloodLineRelateList == null || bloodLineRelateList.isEmpty()) {
            return;
        }
        Map<String, Integer> bloodMap = bloodMapper.selectAll().stream().collect(Collectors.
                toMap((blood-> String.format("%s-%s", blood.getName(), blood.getType())), Blood::getId));

        Map<Integer, Set<Integer>> bloodRelateMap = new HashMap<>();

        bloodLineRelateList.forEach(bloodLineRelateVO -> {
            Integer srcId = bloodMap.get(String.format("%s-%s", bloodLineRelateVO.getSrcName(), bloodLineRelateVO.getSrcType().name()));
            if (srcId == null) {
                Blood blood = new Blood();
                blood.setName(bloodLineRelateVO.getSrcName());
                blood.setRelateId(bloodLineRelateVO.getSrcRelateId());
                blood.setType(bloodLineRelateVO.getSrcType());
                bloodMapper.insertSelective(blood);
                srcId = blood.getId();
                bloodMap.put(String.format("%s-%s", bloodLineRelateVO.getSrcName(), bloodLineRelateVO.getSrcType().name()), srcId);
            }

            Set<Integer> relateSet = bloodRelateMap.get(srcId);
            if (relateSet == null) {
                relateSet = new HashSet<>();
                bloodRelateMap.put(srcId, relateSet);
            }

            Integer dstId = bloodMap.get(String.format("%s-%s", bloodLineRelateVO.getDstName(), bloodLineRelateVO.getDstType().name()));
            if (dstId == null) {
                Blood blood = new Blood();
                blood.setName(bloodLineRelateVO.getDstName());
                blood.setRelateId(bloodLineRelateVO.getDstRelateId());
                blood.setType(bloodLineRelateVO.getDstType());
                bloodMapper.insertSelective(blood);
                dstId = blood.getId();
                bloodMap.put(String.format("%s-%s", bloodLineRelateVO.getDstName(), bloodLineRelateVO.getDstType().name()), dstId);
            }
            relateSet.add(dstId);
        });

        log.info(JSON.toJSONString(bloodRelateMap));

        bloodRelateMap.forEach((srcId, dstIdSet) -> {
            bloodRelateMapper.batchDelete(srcId);
            bloodRelateMapper.batchInsert(srcId, dstIdSet);
        });
    }

    @Override
    public BloodLineResultVO getBloodLineByTarget(BloodLineRequestVO request) {
        BloodLineResultVO bloodLineResultVO = new BloodLineResultVO();
        if (request == null) {
            return bloodLineResultVO;
        }
        Set<Integer> bloodIdSet = new HashSet<>();
        Integer id = bloodMapper.getIdByNameAndType(request.getName(), request.getType().name());
        if (id == null) {
            return bloodLineResultVO;
        }
        List<BloodRelate> edges = new ArrayList<>();
        switch (request.getDirection()) {
            case 0:
                edges = bloodRelateMapper.queryAllBloodLineByTarget(id, request.getDepth());
                break;
            case 1:
                edges = bloodRelateMapper.queryUpstreamBloodLineByTarget(id, request.getDepth());
                break;
            case -1:
                edges = bloodRelateMapper.queryDownstreamBloodLineByTarget(id, request.getDepth());
                break;
        }
        if (CollectionUtils.isEmpty(edges)) {
            return bloodLineResultVO;
        }

        edges.forEach(blood -> {
            bloodIdSet.add(blood.getStartId());
            bloodIdSet.add(blood.getEndId());
        });
        Example example = new Example(Blood.class);
        example.createCriteria().andIn("id", bloodIdSet);
        List<Blood> nodes = bloodMapper.selectByExample(example);

        bloodLineResultVO.setNodes(nodes);
        bloodLineResultVO.setEdges(edges);
        return bloodLineResultVO;
    }

    @Override
    public List<Blood> getAllBloodList() {
        return bloodMapper.selectAll();
    }
}
