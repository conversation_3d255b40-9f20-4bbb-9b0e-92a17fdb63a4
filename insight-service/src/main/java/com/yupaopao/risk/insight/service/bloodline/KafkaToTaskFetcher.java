package com.yupaopao.risk.insight.service.bloodline;

import com.yupaopao.risk.insight.beans.BloodLineRelateVO;
import com.yupaopao.risk.insight.enums.BloodTypeEnum;
import com.yupaopao.risk.insight.repository.model.Blood;
import com.yupaopao.risk.insight.service.TaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by Avalon on 2023/12/14 15:35
 **/
@Component
public class KafkaToTaskFetcher implements BloodLineFetcher{

    @Resource
    private TaskService taskService;


    @Override
    public BloodTypeEnum getSrcType() {
        return BloodTypeEnum.KAFKA;
    }

    @Override
    public BloodTypeEnum getDstType() {
        return BloodTypeEnum.TASK;
    }

    @Override
    public List<BloodLineRelateVO> fetch() {
        List<Map<String, Object>> topicRelateList = taskService.getTaskReaderTopic();
        List<BloodLineRelateVO> result = new ArrayList<>();
        topicRelateList.forEach(topicRelate -> {
            String topicName = String.valueOf(topicRelate.get("srcName"));
            if (StringUtils.isEmpty(topicName)) {
                return;
            }
            Arrays.asList(topicName.split(",")).forEach(name -> {
                BloodLineRelateVO bloodLineRelateVO = new BloodLineRelateVO();
                name = name.replaceAll("\"", "");
                bloodLineRelateVO.setSrcName(name.trim());
                bloodLineRelateVO.setSrcType(getSrcType());
                bloodLineRelateVO.setDstName(String.valueOf(topicRelate.get("dstName")));
                bloodLineRelateVO.setDstRelateId(topicRelate.get("relateId") == null ? null : Integer.valueOf(String.valueOf(topicRelate.get("relateId"))));
                bloodLineRelateVO.setDstType(getDstType());
                result.add(bloodLineRelateVO);
            });
        });
        return result;
    }
}
