package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.risk.insight.flink.tools.FlinkRestClient;
import com.yupaopao.risk.insight.repository.mapper.AlertRuleMapper;
import com.yupaopao.risk.insight.repository.model.AlertRule;
import com.yupaopao.risk.insight.service.AlertHistoryService;
import com.yupaopao.risk.insight.service.AlertService;
import com.yupaopao.risk.insight.service.PrometheusMetricService;
import com.yupaopao.risk.insight.service.TaskService;
import com.yupaopao.risk.insight.service.alert.AlertManger;
import com.yupaopao.risk.insight.service.alert.ConditionTypeManager;
import com.yupaopao.risk.insight.service.alert.beans.FlinkTaskMetricResult;
import com.yupaopao.risk.insight.service.alert.beans.RuleCondition;
import com.yupaopao.risk.insight.service.beans.AlertRuleParam;
import com.yupaopao.risk.insight.service.beans.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.yupaopao.risk.insight.constant.InsightConstants.ALERT_PAUSE_TIME_PREFIX;

/****
 * zengxiangcai
 * 2022/11/7 16:45
 ***/

@Slf4j
@Service
public class AlertServiceImpl implements AlertService {

    public static final String TASK_RUNNING_STATUS = "RUNNING";
    @Resource
    private TaskService taskService;

    @Resource
    private AlertRuleMapper alertRuleMapper;

    @Resource
    private PrometheusMetricService prometheusMetricService;

    @Resource
    private ConditionTypeManager conditionTypeManager;

    @Resource
    private AlertManger alertManger;

    @Resource
    private FlinkRestClient flinkRestClient;

    @Resource
    private AlertHistoryService alertHistoryService;

    @RedisAutowired("middleware.redis.risk-engine")
    private RedisService redisService;

    @Value("${flinktask.alert.filterRunningJobTypes:4#5}")
    private String filterRunningJobTypes;

    @Override
    public boolean addAlertRule(AlertRuleParam param) {

        AlertRule rule = JSON.parseObject(JSON.toJSONString(param), AlertRule.class);
        rule.setUpdateBy(param.getOperator());
        rule.setUpdateTime(new Date());
        if (rule.getDisabled() == null) {
            rule.setDisabled(0);
        }
        rule.setCreateTime(new Date());
        rule.setCreateBy(param.getOperator());
        int affectRow = alertRuleMapper.insertSelective(rule);
        return affectRow > 0;
    }

    @Override
    public boolean updateAlertRule(AlertRuleParam param) {
        AlertRule rule = JSON.parseObject(JSON.toJSONString(param), AlertRule.class);

        AlertRule dbRule = null;
        if (param.getId() == null) {
            return false;
        }
        dbRule = alertRuleMapper.selectByPrimaryKey(rule);
        if (dbRule == null) {
            return false;
        }

        rule.setUpdateBy(param.getOperator());
        rule.setUpdateTime(new Date());
        rule.setCreateBy(dbRule.getCreateBy());
        rule.setCreateTime(dbRule.getCreateTime());
        rule.setDisabled(dbRule.getDisabled());
        int affectRow = alertRuleMapper.updateByPrimaryKey(rule);
        return affectRow > 0;
    }

    @Override
    public boolean disableAlertRule(Long id, String operator) {
        AlertRule paramRule = new AlertRule();
        paramRule.setId(id);
        AlertRule dbRule = alertRuleMapper.selectByPrimaryKey(paramRule);
        if (dbRule == null) {
            return false;
        }
        AlertRule updateRule = new AlertRule();
        updateRule.setUpdateBy(operator);
        updateRule.setUpdateTime(new Date());
        updateRule.setDisabled(1);
        updateRule.setId(id);
        int affectRow = alertRuleMapper.updateByPrimaryKeySelective(updateRule);
        return affectRow > 0;
    }

    @Override
    public boolean pauseAlertRule(Long id, Long minute) {
        return redisService.set(ALERT_PAUSE_TIME_PREFIX + id, "1", minute * 50);
    }

    @Override
    public PageResult<AlertRule> getRuleList(AlertRuleParam queryParam) {
        PageHelper.startPage(queryParam.getCurrentPage(), queryParam.getPageSize());
        Example search = new Example(AlertRule.class);
        Example.Criteria criteria = search.createCriteria();
        if (queryParam.getId() != null) {
            criteria.andEqualTo("id", queryParam.getId());
        }
        if (StringUtils.isNotEmpty(queryParam.getAlertName())) {
            criteria.andLike("alertName", getLikeParamValue(queryParam.getAlertName()));
        }
        if (StringUtils.isNotEmpty(queryParam.getAlertPeriod())) {
            criteria.andLike("alertPeriod", getLikeParamValue(queryParam.getAlertPeriod()));
        }
        if (StringUtils.isNotEmpty(queryParam.getAlertRemarks())) {
            criteria.andLike("alertRemarks", getLikeParamValue(queryParam.getAlertRemarks()));
        }
        if (StringUtils.isNotEmpty(queryParam.getRelatedTasks())) {
            criteria.andEqualTo("relatedTasks", getLikeParamValue(queryParam.getRelatedTasks()));
        }
        criteria.andEqualTo("disabled", 0);
        search.orderBy("id").asc();
        List<AlertRule> ruleList = alertRuleMapper.selectByExample(search);
        PageInfo page = new PageInfo<>(ruleList);
        PageResult<AlertRule> pageResult = new PageResult<>();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setDataList(ruleList);
        return pageResult;
    }


    private String getLikeParamValue(String param) {
        return "%" + param + "%";
    }

    /***
     * 关联通用任务列表
     * @return
     */
    public List<Map<String, Object>> getDisplayTasks() {
        return taskService.getDisplayTasks();
    }

    @Override
    public void checkAlertByPeriod(String alertPeriod) {
        List<AlertRule> ruleList = getRuleByAlertPeriod(alertPeriod);
        if (CollectionUtils.isEmpty(ruleList)) {
            return;
        }
        String queryTime = String.valueOf(System.currentTimeMillis() / 1000);
        for (AlertRule rule : ruleList) {
            //是否有暂停一定时间的规则
            Object containsKey = redisService.get(ALERT_PAUSE_TIME_PREFIX + rule.getId());
            if (containsKey != null) {
                continue;
            }
            checkAlertCondition(rule, queryTime);
        }

    }

    private void checkAlertCondition(AlertRule rule, String queryTime) {
        List<RuleCondition> conditions = JSON.parseArray(rule.getConditions(), RuleCondition.class);
        if (CollectionUtils.isEmpty(conditions)) {
            return;
        }

        //检查rule每天最大次数
        Integer currCount = alertHistoryService.sendCountPerDay(rule.getId());
        if (currCount > rule.getMaxTimes()) {
            return;
        }
        //多个condition的交集
        Map<RuleCondition, List<FlinkTaskMetricResult>> matchResult = new HashMap<>();
        for (RuleCondition cond : conditions) {
            String promQL = conditionTypeManager.getPromQL(cond.getType());
            if (StringUtils.isEmpty(promQL)) {
                log.warn("no promQL with type : {}, id: {}", cond.getType(), rule.getId());
                return;
            }
            promQL = buildPromQLWithParam(cond, rule, promQL);
            //sql 检查符合条件的数据
            List<FlinkTaskMetricResult> conditionResult = prometheusMetricService.executeInstancePromQL(promQL, queryTime);
            //只要有一个条件的结果是空的整体就不满足
            if (CollectionUtils.isEmpty(conditionResult)) {
                return;
            }
            conditionResult = filterRunningJobs(conditionResult, cond);

            if (CollectionUtils.isEmpty(conditionResult)) {
                return;
            }
            //数字格式化
            formatMetricValue(conditionResult);
            matchResult.put(cond, conditionResult);
        }

        alertManger.sendRuleAlert(matchResult, rule);
    }

    /***
     * 针对波动告警numWrite 是递增的，如果任务没有结束查询的可能是不断变动的指标，正常运行的job排除
     * @param conditionResult
     * @param cond
     * @return
     */
    private List<FlinkTaskMetricResult> filterRunningJobs(List<FlinkTaskMetricResult> conditionResult, RuleCondition cond) {
        //对输出不断递增的离线任务，如果正在运行，可能统计上误差比较大，需要等任务停止了再考虑波动
        if (!Arrays.asList(filterRunningJobTypes.split("#")).contains(cond.getType())) {
            return conditionResult;
        }
        List<String> jobNameList = conditionResult.stream().map(elem -> elem.getJobName()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(jobNameList)) {
            return null;
        }
        List<String> runningTasks =
                taskService.getLatestTaskHistory(jobNameList).stream().filter(elem -> elem.getTaskStatus().equals(
                        TASK_RUNNING_STATUS)).map(elem -> elem.getName()).collect(Collectors.toList());
        conditionResult = conditionResult.stream().filter(elem -> !runningTasks.contains(elem.getJobId())).collect(Collectors.toList());
        return conditionResult;

//        if (Arrays.asList(filterRunningJobTypes.split("#")).contains(cond.getType())) {
//            conditionResult = conditionResult.stream().filter(elem -> {
//                Map<String, String> stateMap = flinkRestClient.getJobStateAndEndTime(elem.getJobId(),
//                        alertManger.getFlinkClusterUrl(elem.getService()));
//                String endTime = stateMap.get("jobEndTime");
//                String state = stateMap.get("jobState");
//                //以防止延迟，任务结束1分钟内或则还未结束不告警，其他告警
//                if (state.equals("RUNNING")) {
//                    return false;
//                }
//                if (!endTime.equals("UNKNOWN") && Long.valueOf(endTime) > 0) {
//                    return System.currentTimeMillis() - Long.valueOf(endTime) > 1000 * 60 * 1;
//                }
//                return true;
//            }).collect(Collectors.toList());
//        }
    }


    private void formatMetricValue(List<FlinkTaskMetricResult> resList) {
        resList.forEach(elem -> {
            if (elem.getValue() != null && elem.getValue().size() > 1) {
                String metricValue = elem.getValue().get(1).toString();
                elem.getValue().set(1, new BigDecimal(metricValue).setScale(2,
                        BigDecimal.ROUND_UP).toString());
            }
        });
    }


    private String buildPromQLWithParam(RuleCondition cond, AlertRule rule, String promQLTemplate) {
        Map<String, Object> param = new HashMap<>();
        param.put("paramN", cond.getParamN());
        param.put("paramM", cond.getParamM());
        param.put("relatedTasks", StringUtils.isNotEmpty(rule.getRelatedTasks()) ? rule.getRelatedTasks() : "~'.+'");
        return new StrSubstitutor(param).replace(promQLTemplate);
    }


    private List<AlertRule> getRuleByAlertPeriod(String alertPeriod) {
        Example search = new Example(AlertRule.class);
        Example.Criteria criteria = search.createCriteria();
        criteria.andEqualTo("alertPeriod", alertPeriod);
        criteria.andEqualTo("disabled", 0);
        List<AlertRule> ruleList = alertRuleMapper.selectByExample(search);
        return ruleList;
    }


}
