package com.yupaopao.risk.insight.service.bloodline;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.beans.BloodLineRelateVO;
import com.yupaopao.risk.insight.enums.BloodTypeEnum;
import com.yupaopao.risk.insight.service.TaskService;
import com.yupaopao.risk.insight.util.BloodLineUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by Avalon on 2023/12/14 15:35
 **/
@Component
public class TaskToTableFetcher implements BloodLineFetcher{

    @Resource
    private TaskService taskService;

    @Override
    public BloodTypeEnum getSrcType() {
        return BloodTypeEnum.TASK;
    }

    @Override
    public BloodTypeEnum getDstType() {
        return BloodTypeEnum.TABLE;
    }

    @Override
    public List<BloodLineRelateVO> fetch() {
        List<BloodLineRelateVO> result = new ArrayList<>();

        List<JSONObject> tableInfoList = taskService.getTaskWriterTable();

        tableInfoList.forEach(tableInfo -> {
            String tableName = tableInfo.getString("dstName");
            if (StringUtils.isEmpty(tableName)) {
                return;
            }
            Arrays.asList(tableName.split(",")).forEach(name -> {
                BloodLineRelateVO bloodLineRelateVO = new BloodLineRelateVO();
                bloodLineRelateVO.setSrcName(tableInfo.getString("srcName"));
                bloodLineRelateVO.setSrcRelateId(tableInfo.getInteger("srcRelateId"));
                bloodLineRelateVO.setSrcType(getSrcType());

                name = name.replaceAll("\"", "");
                bloodLineRelateVO.setDstName(name.trim());
                bloodLineRelateVO.setDstType(BloodLineUtils.convertTaskSourceType(tableInfo.getString("dstType")));
                result.add(bloodLineRelateVO);
            });
        });
        return result;
    }
}
