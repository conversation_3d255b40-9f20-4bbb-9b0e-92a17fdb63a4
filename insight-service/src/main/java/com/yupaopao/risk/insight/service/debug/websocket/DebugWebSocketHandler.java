package com.yupaopao.risk.insight.service.debug.websocket;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.service.debug.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.HashMap;

/**
 * 调试WebSocket处理器
 * 处理前端与后端的实时通信
 */
@Slf4j
@Component
@ServerEndpoint("/api/websocket/debug")
public class DebugWebSocketHandler {
    
    /**
     * 存储所有的WebSocket连接
     * Key: sessionId, Value: WebSocket连接集合
     */
    private static final Map<String, CopyOnWriteArraySet<Session>> sessionMap = new ConcurrentHashMap<>();
    /**
     * 尚未绑定 sessionId 的连接集合（仅做占位，避免被误清理）
     */
    private static final CopyOnWriteArraySet<Session> pendingSessions = new CopyOnWriteArraySet<>();
    
    // 固定端点前缀：/api/websocket ，调试业务：/api/websocket/debug
    // 首条消息绑定 sessionId
    
    /**
     * 处理调试消息
     */
    @OnOpen
    public void onOpen(Session session) {
        try {
            // 初始未绑定，等待首条消息
            log.info("调试WebSocket连接建立: wsSessionId={}", session.getId());
            // 设置闲置超时，避免过早断开
            try { session.setMaxIdleTimeout(10 * 60 * 1000); } catch (Exception ignore) {}
            pendingSessions.add(session);

            // 发送连接确认，提示前端可发送绑定消息
            DebugMessage welcome = DebugMessage.builder()
                    .type(DebugMessageType.CONNECTION_ESTABLISHED)
                    .sessionId(null)
                    .timestamp(System.currentTimeMillis())
                    .data(new java.util.HashMap<String, Object>() {{
                        put("connectionId", session.getId());
                        put("bindRequired", true);
                    }})
                    .build();
            sendMessageToSession(session, welcome);
        } catch (Exception e) {
            log.error("处理WebSocket连接建立异常", e);
        }
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            String sessionId = findBoundSessionId(session);
            if (sessionId == null) {
                // 解析首条消息绑定
                Map<String, Object> obj = JSON.parseObject(message, Map.class);
                Object sid = obj != null ? obj.get("sessionId") : null;
                if (sid instanceof String && ((String) sid).length() > 0) {
                    sessionId = (String) sid;
                    sessionMap.computeIfAbsent(sessionId, k -> new CopyOnWriteArraySet<>()).add(session);
                    pendingSessions.remove(session);
                    log.info("已绑定调试会话: sessionId={}, wsSessionId={}", sessionId, session.getId());
                }
            }
        } catch (Exception e) {
            log.error("处理WebSocket消息异常", e);
        }
    }

    @OnClose
    public void onClose(Session session) {
        try {
            // 从所有会话集合移除此 ws 连接
            for (Map.Entry<String, CopyOnWriteArraySet<Session>> entry : sessionMap.entrySet()) {
                if (entry.getValue().remove(session) && entry.getValue().isEmpty()) {
                    sessionMap.remove(entry.getKey());
                }
            }
            log.info("调试WebSocket连接关闭: wsSessionId={}", session.getId());
        } catch (Exception e) {
            log.error("处理WebSocket连接关闭异常", e);
        }
    }

    @OnError
    public void onError(Session session, Throwable error) {
        log.error("调试WebSocket连接异常: wsSessionId={}", session != null ? session.getId() : "null", error);
        try {
            onClose(session);
        } catch (Exception ignore) {}
    }

    private String findBoundSessionId(Session session) {
        for (Map.Entry<String, CopyOnWriteArraySet<Session>> entry : sessionMap.entrySet()) {
            if (entry.getValue().contains(session)) {
                return entry.getKey();
            }
        }
        return null;
    }
    
    /**
     * 处理心跳消息
     */
    private void handlePingMessage(String sessionId, Session session) {}
    
    /**
     * 处理断点设置消息
     */
    private void handleBreakpointMessage(String sessionId, DebugMessage message) {}
    
    /**
     * 处理执行控制消息
     */
    private void handleExecutionControlMessage(String sessionId, DebugMessage message) {}
    
    /**
     * 处理数据请求消息
     */
    private void handleDataRequestMessage(String sessionId, DebugMessage message, Session session) {}
    
    /**
     * 向指定会话广播消息
     */
    public static void broadcastToSession(String sessionId, DebugMessage message) {
        CopyOnWriteArraySet<Session> sessions = sessionMap.get(sessionId);
        if (sessions != null) {
            for (Session session : sessions) {
                sendMessageToSession(session, message);
            }
        }
    }
    
    /**
     * 向指定Session发送消息
     */
    public static void sendMessageToSession(Session session, DebugMessage message) {
        if (session != null && session.isOpen()) {
            try {
                String jsonMessage = JSON.toJSONString(message);
                session.getBasicRemote().sendText(jsonMessage);
            } catch (IOException e) {
                log.error("发送WebSocket消息失败: sessionId={}", session.getId(), e);
            }
        }
    }
    
    /**
     * 推送节点执行状态更新
     */
    public static void pushNodeStatusUpdate(String sessionId, DebugNodeResult nodeResult) {
        DebugMessage message = DebugMessage.builder()
                .type(DebugMessageType.NODE_STATUS_UPDATE)
                .sessionId(sessionId)
                .timestamp(System.currentTimeMillis())
                .data(nodeResult)
                .build();
        
        broadcastToSession(sessionId, message);
    }
    
    /**
     * 推送执行进度更新
     */
    public static void pushExecutionProgress(String sessionId, Map<String, Object> progress) {
        DebugMessage message = DebugMessage.builder()
                .type(DebugMessageType.EXECUTION_PROGRESS)
                .sessionId(sessionId)
                .timestamp(System.currentTimeMillis())
                .data(progress)
                .build();
        
        broadcastToSession(sessionId, message);
    }
    
    /**
     * 推送会话状态更新
     */
    public static void pushSessionUpdate(String sessionId, Map<String, Object> update) {
        DebugMessage message = DebugMessage.builder()
                .type(DebugMessageType.SESSION_STATE_UPDATE)
                .sessionId(sessionId)
                .timestamp(System.currentTimeMillis())
                .data(update)
                .build();
        
        broadcastToSession(sessionId, message);
    }
    
    /**
     * 推送断点命中事件
     */
    public static void pushBreakpointHit(String sessionId, Map<String, Object> breakpointData) {
        DebugMessage message = DebugMessage.builder()
                .type(DebugMessageType.BREAKPOINT_HIT)
                .sessionId(sessionId)
                .timestamp(System.currentTimeMillis())
                .data(breakpointData)
                .build();
        
        broadcastToSession(sessionId, message);
    }
    
    /**
     * 推送错误信息
     */
    public static void pushError(String sessionId, String error, String details) {
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("error", error);
        errorData.put("details", details);
        
        DebugMessage message = DebugMessage.builder()
                .type(DebugMessageType.ERROR)
                .sessionId(sessionId)
                .timestamp(System.currentTimeMillis())
                .data(errorData)
                .build();
        
        broadcastToSession(sessionId, message);
    }
    
    /**
     * 清理指定会话的WebSocket连接
     */
    public static void clearSession(String sessionId) {
        CopyOnWriteArraySet<Session> sessions = sessionMap.remove(sessionId);
        if (sessions != null) {
            sessions.forEach(session -> {
                try {
                    session.close();
                } catch (IOException e) {
                    log.warn("关闭WebSocket连接失败", e);
                }
            });
        }
    }
    
    /**
     * 获取活跃的会话数量
     */
    public static int getActiveSessionCount() {
        return sessionMap.size();
    }
    
    /**
     * 获取指定会话的连接数量
     */
    public static int getConnectionCount(String sessionId) {
        CopyOnWriteArraySet<Session> sessions = sessionMap.get(sessionId);
        return sessions != null ? sessions.size() : 0;
    }
}
