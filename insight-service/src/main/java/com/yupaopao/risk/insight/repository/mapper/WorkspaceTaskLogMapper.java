package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.WorkspaceTaskLog;
import com.yupaopao.risk.insight.service.beans.WorkspaceLogParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface WorkspaceTaskLogMapper extends Mapper<WorkspaceTaskLog> {



    @Select("<script>"
            +"select * from t_workspace_task_log WHERE 1=1 \n"
            + "<if test='param.taskId!=null'>"
            + " and task_id = #{param.taskId}"
            + "</if>"
            + "<if test='param.taskName!=null'>"
            + " and task_name = #{param.taskName}"
            + "</if>"
            + "<if test='param.startTime!=null'>"
            + " and create_time BETWEEN #{param.startTime} and #{param.endTime}\n"
            + "</if>"
            + "<if test='param.taskCreateBy!=null'>"
            + " and task_create_by = #{param.taskCreateBy}"
            + "</if>"
            + "<if test='param.createBy!=null'>"
            + " and create_by = #{param.createBy}"
            + "</if>"
            + " group by create_time desc"
            + "</script>")
    List<WorkspaceTaskLog> queryLogPage(@Param("param") WorkspaceLogParam param);

}
