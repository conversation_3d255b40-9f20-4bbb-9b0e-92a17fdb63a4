package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Table;
import java.util.Date;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-31 16:13
 *
 ***/

@Getter
@Setter
@Table(name = "t_community_detail")
public class CommunityDetail {

    private Long id;
    private String communityLabel;
    private Date createTime;
    private Date updateTime;
    private String runDay;
    private String vertexId;
    private Integer degree;

}
