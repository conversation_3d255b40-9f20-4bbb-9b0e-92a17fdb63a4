package com.yupaopao.risk.insight.service;

import com.yupaopao.risk.insight.beans.DBTableInfo;
import com.yupaopao.risk.insight.enums.DBTableType;
import com.yupaopao.risk.insight.repository.model.TableDetail;
import com.yupaopao.risk.insight.repository.model.TableInfo;
import com.yupaopao.risk.insight.service.beans.PageResult;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-03 10:53
 * 数仓管理服务
 ***/
public interface TableService {

    /**
     * 检查表是否存在于定义的数仓，t_table
     *
     * @param tableName
     * @return
     */
    boolean existsTable(String tableName);

    /***
     * 获取表信息
     * @param tableName
     * @return
     */
    Optional<DBTableInfo> getTableInfo(String tableName);


    /**
     * 用户上传的自定义表最新id
     * 支持用户上传csv数据生成数仓，每次上传新增一个数仓，用户可以自定义表明，表结构。
     * 上传的数据统一存储到tablestore中，tablestore主键是：tableId,uuid
     * 后续分析查询将该数据传入过滤需要查询的数据
     *
     * @param tableName
     * @return
     */
    Integer getIdForUserDefinedTable(String tableName);

//    TableInfo uploadTable(String sql, DBTableType tableType, String createBy);

    PageResult<TableInfo> getTables(TableInfo tableInfo, Integer pageSize, Integer pageNumber);

    List<String> queryTables(String tableName);

    PageResult<TableDetail> getTableDetails(TableDetail tableDetail, Integer pageSize, Integer pageNumber);

    Set<String> getTableAndDetail();

    List<TableInfo> getTables();

    TableInfo uploadTable(DBTableInfo dbTableInfo);

    TableInfo upsertTable(TableInfo tableInfo);

    TableDetail upsertTableDetail(TableDetail tableDetail);

    boolean saveTableDetails(List<TableDetail> tableDetails);

    boolean deleteTable(Integer tableId)  throws Exception;

    boolean deleteTableDetail(Integer detailId);

}
