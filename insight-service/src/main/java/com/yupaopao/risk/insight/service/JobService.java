package com.yupaopao.risk.insight.service;

import com.yupaopao.risk.insight.common.meta.JobParams;
import com.yupaopao.risk.insight.repository.model.JobInfo;
import com.yupaopao.risk.insight.service.beans.PageResult;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-11-21 10:54
 * sql job 记录到db中
 ***/
public interface JobService {

    /***
     * 保存job信息到db
     * @return
     */
    JobInfo saveSqlJobInfo(JobParams jobParams, boolean needValidate);

    /**
     * 更新job状态
     * @param status
     * @param jobId flink 集群对应的jobId
     * @return
     */
    boolean updateJobStatus(String status, String jobId);

    /***
     * db中job信息
     * @param jobId
     * @return
     */
    JobInfo getJobInfo(String jobId);

//
//    /**
//     * 启动任务
//     * @param jobKey
//     * @return
//     */
//    JobInfo startJob(Integer jobKey);

    /**
     * 中止任务
     * @param jobKey
     * @return
     */
    JobInfo abortJob(Integer jobKey);

    /***
     * 查询db中job列表
     * @param jobInfo
     * @param pageSize
     * @param pageNumber
     * @return
     */
    PageResult<JobInfo> getPagedJobList(JobInfo jobInfo, Integer pageSize, Integer pageNumber);

    /***
     * 查询job异常信息
     * @param jobId flink集群中jobId
     * @return
     */
    String getJobException(String jobId);

//    /**
//     * sql运行前校验
//     * @param jobInfo
//     * @return
//     */
//    JobParams validateAndBuildJobParam(JobInfo jobInfo);

    String getJobStatusFromDB(String id);

}
