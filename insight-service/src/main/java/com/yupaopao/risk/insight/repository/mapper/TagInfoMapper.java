package com.yupaopao.risk.insight.repository.mapper;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.dto.TagInfoDTO;
import com.yupaopao.risk.insight.repository.model.TagInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TagInfoMapper extends Mapper<TagInfo> {
    @Select("<script>"
            + "select info.id as id, info.code as code, info.name as name, info.source as source, info.remarks as remarks, info.create_by as createBy, info.create_time as createTime, type.name as "
            + "typeName, type.id as type"
            + " from t_tag_info as info,t_tag_type as type where"
            + " info.tag_type = type.id "
            + "<if test='tagInfo.id!=null'>"
            + " and id=#{tagInfo.id}"
            + "</if>"
            + "<if test='tagInfo.code!=null and tagInfo.code!=\"\" '>"
            + " and info.code LIKE CONCAT('%',#{tagInfo.code},'%')"
            + "</if>"
            + "<if test='tagInfo.name!=null'>"
            + " and info.name LIKE CONCAT('%',#{tagInfo.name},'%')"
            + "</if>"
            + "<if test='tagInfo.type!=null'>"
            + " and info.tag_type = #{tagInfo.type}"
            + "</if>"
            + "<if test='tagInfo.source!=null'>"
            + " and info.source = #{tagInfo.source}"
            + "</if>"
            + " order by info.create_time desc"
            + "</script>")
    List<JSONObject> getAllTagInfo(@Param("tagInfo") TagInfo tagInfo);


    @Select("<script>"
            + "select tt.*,td.data_content as dataContent,td.value_type as valueType from (select info.id as id," +
            "  info.external_reason as externalReason,info.tag_type as tagType, info.code as code, info.name as name," +
            " info.source as " +
            "source, info.remarks as remarks, info.risk_level as riskLevel, info.scene as scene,info.suggestion as suggestion, " +
            "info.violation_type as violationType,info.sub_violation_type as subViolationType," +
            " info.create_by as createBy, info.create_time as createTime, info.update_by as updateBy, " +
            "info.update_time as updateTime,type.name as \n"
            + " typeName, type.id as type "
            + " from t_tag_info as info join t_tag_type as type on info.tag_type = type.id "
            + " ) tt left join t_tag_detail td on td.tag_id=tt.id where 1=1 "
            + "<if test='tagInfo.id!=null'>"
            + " and tt.id=#{tagInfo.id}"
            + "</if>"
            + "<if test='tagInfo.code!=null and tagInfo.code!=\"\" '>"
            + " and tt.code LIKE CONCAT('%',#{tagInfo.code},'%')"
            + "</if>"
            + "<if test='tagInfo.type!=null'>"
            + " and tt.tagType = #{tagInfo.type}"
            + "</if>"
            + "<if test='tagInfo.source!=null'>"
            + " and tt.source = #{tagInfo.source}"
            + "</if>"
            + "<if test='tagInfo.name!=null'>"
            + " and tt.name LIKE CONCAT('%',#{tagInfo.name},'%')"
            + "</if>"
            + "<if test='tagInfo.domain!=null and tagInfo.domain!=\"\" '>"
            + " and CASE WHEN JSON_VALID(td.data_content) THEN JSON_EXTRACT(td.data_content,'$.groupKey')= #{tagInfo.domain} ELSE '' END"
            + "</if>"
            + "  order by createTime desc"
            + "</script>")
    List<JSONObject> getAllTagInfoPage(@Param("tagInfo") TagInfoDTO tagInfo);


    @Select("<script>"
            + "select tt.*,td.data_content as dataContent,td.value_type as valueType from (select info.id as id," +
            "  info.external_reason as externalReason,info.tag_type as tagType, info.code as code, info.name as name," +
            " info.source as " +
            "source, info.remarks as remarks, info.risk_level as riskLevel, info.scene as scene,info.suggestion as suggestion, " +
            "info.violation_type as violationType,info.sub_violation_type as subViolationType," +
            " info.create_by as createBy, info.create_time as createTime, info.update_by as updateBy, " +
            "info.update_time as updateTime,type.name as \n"
            + " typeName, type.id as type "
            + " from t_tag_info as info join t_tag_type as type on info.tag_type = type.id "
            + " ) tt left join t_tag_detail td on td.tag_id=tt.id where 1=1 "
            + " and tt.code=#{code}"
            + "  order by createTime desc"
            + "</script>")
    JSONObject getTagInfo(@Param("code") String code);


    @Select("<script>"
            + "SELECT info.*,d.value_type as valueType,d.data_content as content FROM t_tag_info as info, t_tag_detail d WHERE info.id=d.tag_id"
            + "<if test='source!=null'>"
            + " and info.source = #{source}"
            + "</if>"
            + "</script>")
    List<JSONObject> getTagInfoBySource(@Param("source") String source);


    @Select("<script>"
            + "select t.id as dstRelateId, t.code as dstName, JSON_EXTRACT(td.data_content, '$.eventCode') as srcName from t_tag_info t\n" +
            "                       inner join t_tag_detail td on t.id = td.tag_id\n" +
            "where source = 4"
            + "</script>")
    List<JSONObject> getAllMultiDimensionTag();


    @Select("<script>" +
            "select\n" +
            "       t.id                                           as dstRelateId,\n" +
            "       t.code                                         as dstName,\n" +
            "       JSON_EXTRACT(t1.data_content, '$.dataChannel') as srcName\n" +
            "from t_tag_info t\n" +
            "         inner join t_tag_detail t1 on (t1.tag_id = t.id)\n" +
            "where t.source in (1, 3)\n" +
            "and JSON_VALID(t1.data_content) = 1\n" +
            "and JSON_EXTRACT(t1.data_content, '$.eventCode') not like '%TianXiang%'" +
            "</script>")
    List<JSONObject> getTagRelateKafka();



    @Select("<script>" +
            "select t.id        as dstRelateId,\n" +
            "       t.code      as dstName,\n" +
            "       JSON_EXTRACT(t1.data_content, '$.eventCode') as srcName\n" +
            "from t_tag_info t\n" +
            "         inner join t_tag_detail t1 on (t1.tag_id = t.id)\n" +
            "where 0 = 0\n" +
            "  and JSON_VALID(t1.data_content) = 1\n" +
            "  and JSON_EXTRACT(t1.data_content, '$.eventCode') like '%TianXiang%'" +
            "</script>")
    List<JSONObject> getTagRelateThird();

}
