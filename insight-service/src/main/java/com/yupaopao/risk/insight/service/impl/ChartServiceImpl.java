package com.yupaopao.risk.insight.service.impl;

import com.yupaopao.risk.insight.InsightException;
import com.yupaopao.risk.insight.dashboard.beans.QueryRequest;
import com.yupaopao.risk.insight.dashboard.service.ChartBase;
import com.yupaopao.risk.insight.enums.ErrorMessage;
import com.yupaopao.risk.insight.repository.mapper.ChartDetailMapper;
import com.yupaopao.risk.insight.repository.mapper.ChartInfoMapper;
import com.yupaopao.risk.insight.repository.model.ChartDetail;
import com.yupaopao.risk.insight.repository.model.ChartInfo;
import com.yupaopao.risk.insight.service.ChartService;
import com.yupaopao.risk.insight.service.DirectoryService;
import com.yupaopao.risk.insight.service.beans.ChartData;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by Avalon on 2020/2/22 16:32
 */
@Service
public class ChartServiceImpl implements ChartService {

    private final Map<String, ChartBase> chartServiceMap = new ConcurrentHashMap<>();

    @Resource
    private ChartInfoMapper chartInfoMapper;

    @Resource
    private ChartDetailMapper chartDetailMapper;

    @Resource
    private List<ChartBase> chartBaseList;

    @Resource
    private DirectoryService directoryService;

    @PostConstruct
    private void init() {
        for (ChartBase chartBase : chartBaseList) {
            chartServiceMap.put(chartBase.name(), chartBase);
        }
    }

    @Override
    public ChartInfo save(ChartInfo chartInfo) {
        if (chartInfo.getId() == null) {
            chartInfoMapper.insertSelective(chartInfo);
            directoryService.linkObj(chartInfo.getDirId(), chartInfo.getId());
        } else {
            chartInfoMapper.updateByPrimaryKeySelective(chartInfo);
        }

        List<ChartDetail> details = chartInfo.getChartDetails();
        if (!CollectionUtils.isEmpty(details)) {
            details.stream().forEach(detail -> {
                if (detail.getChartId() == null) {
                   detail.setChartId(chartInfo.getId());
                }
                detail.setCreatedBy(chartInfo.getCreatedBy());
                if (detail.getId() == null) {
                    chartDetailMapper.insertSelective(detail);
                } else {
                    chartDetailMapper.updateByPrimaryKeySelective(detail);
                }
            });
        }
        return chartInfo;
    }

    @Override
    public ChartInfo getChartInfo(Integer chartId) {
        ChartInfo chartInfo = chartInfoMapper.selectByPrimaryKey(chartId);
        if (chartInfo != null && chartInfo.getId() != null) {
            ChartDetail detail = new ChartDetail();
            detail.setChartId(chartInfo.getId());
            List<ChartDetail> detailList = chartDetailMapper.select(detail);
            if (!CollectionUtils.isEmpty(detailList)) {
                chartInfo.setChartDetails(detailList);
            }
        }
        return chartInfo;
    }

    @Override
    public ChartData fetchData(QueryRequest queryRequest) {
        ChartBase chartBase = chartServiceMap.get(queryRequest.getType());
        if (chartBase == null) {
            throw new InsightException(ErrorMessage.CHART_UNSUPPORTED_ERROR);
        }
        return chartBase.fetchData(queryRequest);
    }
}
