package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.beans.LiveStreamStateVO;
import com.yupaopao.risk.insight.beans.StreamParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface LiveStreamStateVOMapper extends Mapper<LiveStreamStateVO> {

   /* @Select("<script>" +
            "select * from risk_live_stream_state where update_time<#{streamParam.endTime} and update_time>=#{streamParam.startTime}"
            + "<if test='streamParam.state!=null'>"
            + " and detect_state=#{streamParam.state}"
            + "</if>"
            + "<if test='streamParam.state!=null'>"
            + " and stream_type=#{streamParam.type}"
            + "</if>"
            + "  order by update_time desc"
            + "</script>")
*/
    @Select("<script>"
            +"select id,detect_state as detectState, stream_id as streamId,update_time as updateTime, create_time as createTime" +
            " source, retry_count as retryCount, stream_type as streamType, task_id as taskId,url,room_id as roomId, detect_channel as detectChannel" +
            " from risk_live_stream_state where update_time between #{streamParam.startTime} and #{streamParam.endTime}"
            + "<if test='streamParam.state!=null'>"
            + " and detect_state=#{streamParam.state}"
            + "</if>"
            + "<if test='streamParam.type!=null and streamParam.type!=\"\"'>"
            + " and stream_type=#{streamParam.type}"
            + "</if>"
            + "<if test='streamParam.streamId!=null and streamParam.streamId!=\"\"'>"
            + " and stream_id=#{streamParam.streamId}"
            + "</if>"
            + "  order by update_time desc"
            + "</script>")
    List<LiveStreamStateVO> queryStreamIdInfoList(@Param("streamParam") StreamParam streamParam);

}