package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.Blood;
import com.yupaopao.risk.insight.repository.model.TaskInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

public interface BloodMapper extends Mapper<Blood> {

    @Select("<script>"
            + "select id from t_blood where name = #{name} and type = #{type}"
            + "</script>")
    Integer getIdByNameAndType(@Param("name") String name, @Param("type") String type);


}
