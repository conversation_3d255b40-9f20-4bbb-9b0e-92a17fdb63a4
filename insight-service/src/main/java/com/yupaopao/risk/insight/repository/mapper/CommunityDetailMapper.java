package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.CommunityDetail;
import com.yupaopao.risk.insight.repository.model.CommunityInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-31 16:34
 *
 ***/
public interface CommunityDetailMapper extends Mapper<CommunityDetail> {

    @Select("<script>" +
            "select community_label as communityLabel,count(distinct vertex_id) as vertexCount " +
            " from t_community_detail t\n" +
            " where  community_label in " +
            "<foreach item='item' collection='labels' separator=',' open='(' close=')'>" +
            "#{item}" +
            "</foreach>" +
            " group by community_label"+
            "</script>")
    List<CommunityInfo> getLabelCount(@Param("labels") List<String> labels);

    @Select("<script> select distinct community_label from t_community_detail where vertex_id = #{label} </script>")
    List<String> getVertexCommunity(@Param("label") String label);


    @Select("select distinct vertex_id from t_community_detail order by vertex_id asc ")
    List<String> getCommunityVertexes();

    @Update("<script>" +
            " update t_community_detail set is_frozen = #{frozenFlag} " +
            " where  vertex_id in " +
            "<foreach item='item' collection='ids' separator=',' open='(' close=')'>" +
            "#{item}" +
            "</foreach>" +
            "</script>")
    int setFrozenFlag(@Param("ids") List<String> ids, @Param("frozenFlag") Integer frozenFlag);

}
