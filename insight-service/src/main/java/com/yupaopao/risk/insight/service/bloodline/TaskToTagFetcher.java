package com.yupaopao.risk.insight.service.bloodline;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.beans.BloodLineRelateVO;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import com.yupaopao.risk.insight.enums.BloodTypeEnum;
import com.yupaopao.risk.insight.service.TaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Component
public class TaskToTagFetcher implements BloodLineFetcher {

    @Resource
    private TaskService taskService;

    @Override
    public BloodTypeEnum getSrcType() {
        return BloodTypeEnum.TASK;
    }

    @Override
    public BloodTypeEnum getDstType() {
        return BloodTypeEnum.TAG;
    }

    @Override
    public List<BloodLineRelateVO> fetch() {
        List<BloodLineRelateVO> result = new ArrayList<>();

        List<JSONObject> taskRelateTags = taskService.getTaskRelateTags();
        if (taskRelateTags == null) {
            return result;
        }
        taskRelateTags.forEach(taskRelateTag -> {

            String relateTagsStr = taskRelateTag.getString("dstName");
            if (StringUtils.isEmpty(relateTagsStr)) {
                return;
            }
            Arrays.asList(relateTagsStr.split(",")).forEach(relateTag -> {
                TagInfoBO tagInfoBO = TagCacheSupport.getTagInfoByCode(relateTag);
                if (tagInfoBO == null) {
                    return;
                }

                BloodLineRelateVO bloodLineRelateVO = new BloodLineRelateVO();
                bloodLineRelateVO.setSrcName(taskRelateTag.getString("srcName"));
                bloodLineRelateVO.setSrcRelateId(taskRelateTag.getInteger("srcRelateId"));
                bloodLineRelateVO.setSrcType(getSrcType());


                bloodLineRelateVO.setDstName(relateTag);
                bloodLineRelateVO.setDstRelateId(tagInfoBO.getId());
                bloodLineRelateVO.setDstType(getDstType());
                result.add(bloodLineRelateVO);
            });
        });

        return result;
    }



}
