package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.insight.repository.mapper.RiskAwarenessSuspiciousListMapper;
import com.yupaopao.risk.insight.repository.model.JobInfo;
import com.yupaopao.risk.insight.repository.model.SuspiciousInfo;
import com.yupaopao.risk.insight.service.ClickHouseService;
import com.yupaopao.risk.insight.service.RiskAwarenessService;
import com.yupaopao.risk.insight.service.beans.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/****
 * zengxiangcai
 * 2022/4/12 5:00 PM
 ***/

@Service
public class RiskAwarenessServiceImpl implements RiskAwarenessService {

    @Autowired
    private RiskAwarenessSuspiciousListMapper riskAwarenessSuspiciousListMapper;

    @Override
    public PageResult<SuspiciousInfo> getSuspiciousList() {
        String orderBy = " create_time asc ";
        Example search = new Example(JobInfo.class);
        Example.Criteria criteria = search.createCriteria();

        PageHelper.startPage(1, 100, orderBy);
        List<SuspiciousInfo> list = riskAwarenessSuspiciousListMapper.selectByExample(search);
        PageInfo page = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setDataList(list);
        return pageResult;
//
//    List<Map<String, Object>> dbList = clickHouseService.executeQueryList(sql);
//    List<SuspiciousInfo> list =
//            dbList.stream().map(elem-> JSON.parseObject(JSON.toJSONString(elem),SuspiciousInfo.class))
//                    .collect(Collectors.toList());
////    PageInfo page = new PageInfo<>(list);
//    PageResult pageResult = new PageResult();
//    pageResult.setTotal(Long.valueOf(list.size()));
////    pageResult.setPages(page.getPages());
//    pageResult.setDataList(list);
//    return pageResult;
    }

    @Override
    public Boolean addSuspiciousList(SuspiciousInfo request) {
        int affectRow = riskAwarenessSuspiciousListMapper.insertSelective(request);
        return affectRow > 0;
    }

    @Override
    public Boolean editSuspiciousList(SuspiciousInfo request) {
        request.setUpdateTime(new Date());
        int affectRow = riskAwarenessSuspiciousListMapper.updateByPrimaryKeySelective(request);
        return affectRow > 0;
    }
}
