package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/****
 * zengxiangcai
 * 2022/11/15 14:13
 ***/

@Getter
@Setter
@Table(name = "t_alert_history")
public class AlertHistory {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name="alert_rule_id")
    private Long alertRuleId;

    @Column(name = "related_tasks")
    private String relatedTasks;

    @Column(name = "alert_content")
    private String alertContent;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    private String result;



}
