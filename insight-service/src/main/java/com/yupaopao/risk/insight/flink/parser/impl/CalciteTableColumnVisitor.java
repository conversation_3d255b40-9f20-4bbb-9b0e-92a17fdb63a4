//package com.yupaopao.risk.insight.flink.parser.impl;
//
//import org.apache.calcite.sql.*;
//import org.apache.calcite.sql.util.SqlVisitor;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Optional;
//import java.util.Set;
//
///****
// *
// * @Author: zengxiangcai
// * @Date: 2019-12-13 15:43
// *
// ***/
//public class CalciteTableColumnVisitor implements SqlVisitor<Void> {
//
//    private Map<String, Set<String>> tableColumnMap = new HashMap<>();
//
//    /***
//     * select 语句中包含的表以及对应的字段
//     * 每个表的别名不同
//     * select , from ,where ,group ,having ,order
//     * @param sqlSelect
//     * @return
//     */
//    public Void visit(SqlSelect sqlSelect) {
//        if (sqlSelect == null) {
//            return null;
//        }
//        Optional.ofNullable(sqlSelect.getSelectList()).ifPresent(elem->elem.accept(this));
//        Optional.ofNullable(sqlSelect.getFrom()).ifPresent(elem->elem.accept(this));
//        Optional.ofNullable(sqlSelect.getWhere()).ifPresent(elem->elem.accept(this));
//        Optional.ofNullable(sqlSelect.getGroup()).ifPresent(elem->elem.accept(this));
//        Optional.ofNullable(sqlSelect.getHaving()).ifPresent(elem->elem.accept(this));
//        Optional.ofNullable(sqlSelect.getOrderList()).ifPresent(elem->elem.accept(this));
//        return null;
//    }
//
//    @Override
//    public Void visit(SqlLiteral sqlLiteral) {
//        System.err.println("sqlLiteral: "+sqlLiteral);
//        return null;
//    }
//
//    @Override
//    public Void visit(SqlCall sqlCall) {
//        try {
//
//            if (CollectionUtils.isNotEmpty(sqlCall.getOperandList())) {
//                sqlCall.getOperandList().stream().filter(elem->elem!=null).forEach(elem -> elem.accept(this));
//            }
//        }catch (Exception e){
//            e.printStackTrace();
//            System.err.println("exception: "+sqlCall);
//        }
//        return null;
//    }
//
//    @Override
//    public Void visit(SqlNodeList sqlNodeList) {
//        if (sqlNodeList == null) {
//            return null;
//        }
//        if(CollectionUtils.isNotEmpty(sqlNodeList.getList())){
//            sqlNodeList.getList().stream().filter(elem->elem!=null).forEach(elem->elem.accept(this));
//        }
//        return null;
//    }
//
//    @Override
//    public Void visit(SqlIdentifier sqlIdentifier) {
//        System.err.println("sqlIdentifier: "+sqlIdentifier);
//        return null;
//    }
//
//    @Override
//    public Void visit(SqlDataTypeSpec sqlDataTypeSpec) {
//        System.err.println("sqlDataTypeSpec: "+sqlDataTypeSpec);
//        return null;
//    }
//
//    @Override
//    public Void visit(SqlDynamicParam sqlDynamicParam) {
//        System.err.println("sqlDataTypeSpec: "+sqlDynamicParam);
//        return null;
//    }
//
//    @Override
//    public Void visit(SqlIntervalQualifier sqlIntervalQualifier) {
//        System.err.println("sqlIntervalQualifier: "+sqlIntervalQualifier);
//        return null;
//    }
//}
