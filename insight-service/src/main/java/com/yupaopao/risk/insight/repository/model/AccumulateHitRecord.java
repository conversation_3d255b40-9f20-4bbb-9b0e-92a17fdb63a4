package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc AccumulateControl
 * @date 2020/11/18
 */
@Table(name = "t_accumulate_hit_record")
@Getter
@Setter
public class AccumulateHitRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "accumulate_id")
    private Integer accumulateId;

    @Column(name = "record_key")
    private String key;

    @Column(name = "time_span")
    private long timeSpan;

    @Column(name = "thresholds")
    private String thresholds;

    @Column(name = "record_count")
    private long recordCount;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}
