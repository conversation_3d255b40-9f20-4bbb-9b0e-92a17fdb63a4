package com.yupaopao.risk.insight.service.debug;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.repository.model.TaskInfo;
import com.yupaopao.risk.insight.service.TaskService;
import com.yupaopao.risk.insight.service.debug.model.*;
import com.yupaopao.risk.insight.service.debug.websocket.DebugWebSocketHandler;
import com.yupaopao.risk.insight.service.debug.websocket.DebugMessage;
import com.yupaopao.risk.insight.service.debug.model.DebugMessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;

/**
 * 调试服务
 * 统一的调试功能入口
 */
@Slf4j
@Service
public class DebugService {

    @Autowired
    private TaskService taskService;

    @Autowired
    private TopologyParser topologyParser;

    @Autowired
    private LocalExecutionEngine executionEngine;

    @Value("${debug.enabled:true}")
    private boolean debugEnabled;

    @Value("${debug.max-concurrent-sessions:5}")
    private int maxConcurrentSessions;

    /**
     * 启动调试会话
     */
    public DebugSession startDebugSession(DebugConfig config) throws Exception {
        log.info("启动调试会话: taskId={}, sessionName={}", config.getTaskId(), config.getSessionName());

        // 检查调试功能是否启用
        if (!debugEnabled) {
            throw new IllegalStateException("调试功能未启用");
        }

        // 检查并发限制
        List<DebugSession> activeSessions = executionEngine.getActiveSessions();
        if (activeSessions.size() >= maxConcurrentSessions) {
            throw new IllegalStateException("已达到最大并发调试会话数限制: " + maxConcurrentSessions);
        }

        try {
            // 1. 获取任务信息
            TaskInfo taskInfo = taskService.getTaskInfo(config.getTaskId());
            if (taskInfo == null) {
                throw new IllegalArgumentException("任务不存在: " + config.getTaskId());
            }

            // 2. 解析输入数据
            List<Map<String, Object>> inputData = parseInputData(config.getInputDataJson());
            config.setInputData(inputData);

            // 3. 解析执行拓扑
            List<DebugNode> topology = topologyParser.parseTopology(taskInfo);
            if (!topologyParser.validateTopology(topology)) {
                throw new IllegalStateException("执行拓扑验证失败");
            }

            // 4. 创建调试会话
            DebugSession session = executionEngine.createDebugSession(config, topology, inputData);
            session.setTaskInfo(taskInfo);

            // 5. 推送会话创建消息
            Map<String, Object> sessionUpdateData = new HashMap<>();
            sessionUpdateData.put("eventType", "SESSION_CREATED");
            sessionUpdateData.put("session", session);

            DebugWebSocketHandler.broadcastToSession(session.getSessionId(), DebugMessage.builder()
                    .type(DebugMessageType.SESSION_CREATED)
                    .sessionId(session.getSessionId())
                    .timestamp(System.currentTimeMillis())
                    .data(sessionUpdateData)
                    .build());

            log.info("调试会话创建成功: sessionId={}, nodes={}",
                    session.getSessionId(), topology.size());

            return session;

        } catch (Exception e) {
            log.error("启动调试会话失败: taskId={}", config.getTaskId(), e);
            throw e;
        }
    }

    /**
     * 执行调试任务
     */
    public CompletableFuture<DebugExecutionResult> executeDebugTask(String sessionId) {
        log.info("执行调试任务: sessionId={}", sessionId);

        DebugSession session = executionEngine.getDebugSession(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("调试会话不存在: " + sessionId);
        }

        if (session.isRunning()) {
            throw new IllegalStateException("调试任务正在运行中");
        }

        return executionEngine.executeDebugTask(sessionId);
    }

    /**
     * 停止调试会话
     */
    public void stopDebugSession(String sessionId) {
        log.info("停止调试会话: sessionId={}", sessionId);

        executionEngine.stopDebugSession(sessionId);
    }

    /**
     * 获取调试会话信息
     */
    public DebugSession getDebugSession(String sessionId) {
        return executionEngine.getDebugSession(sessionId);
    }

    /**
     * 获取活跃的调试会话列表
     */
    public List<DebugSession> getActiveSessions() {
        return executionEngine.getActiveSessions();
    }

    /**
     * 设置断点
     */
    public void setBreakpoint(String sessionId, String nodeId, boolean enabled) {
        DebugSession session = executionEngine.getDebugSession(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("调试会话不存在: " + sessionId);
        }

        List<String> breakpoints = session.getConfig().getBreakpoints();
        if (breakpoints == null) {
            breakpoints = new ArrayList<>();
            session.getConfig().setBreakpoints(breakpoints);
        }

        if (enabled && !breakpoints.contains(nodeId)) {
            breakpoints.add(nodeId);
        } else if (!enabled) {
            breakpoints.remove(nodeId);
        }

        log.info("断点设置更新: sessionId={}, nodeId={}, enabled={}", sessionId, nodeId, enabled);
    }

    /**
     * 验证输入数据
     */
    public InputValidationResult validateInputData(String inputDataJson) {
        try {
            if (inputDataJson == null || inputDataJson.trim().isEmpty()) {
                return InputValidationResult.builder()
                        .valid(false)
                        .message("输入数据不能为空")
                        .build();
            }

            List<Map<String, Object>> inputData = parseInputData(inputDataJson);

            return InputValidationResult.builder()
                    .valid(true)
                    .recordCount(inputData.size())
                    .dataType("JSON")
                    .message("输入数据格式正确")
                    .build();

        } catch (Exception e) {
            log.error("验证输入数据失败", e);

            return InputValidationResult.builder()
                    .valid(false)
                    .error(e.getMessage())
                    .message("输入数据格式错误，请检查JSON格式")
                    .build();
        }
    }

    /**
     * 获取任务配置摘要
     */
    public TaskSummary getTaskSummary(Integer taskId) {
        TaskInfo taskInfo = taskService.getTaskInfo(taskId);
        if (taskInfo == null) {
            throw new IllegalArgumentException("任务不存在: " + taskId);
        }

        // 解析任务节点
        List<TaskNode> nodes = parseTaskNodes(taskInfo);

        return TaskSummary.builder()
                .taskId(taskId)
                .taskName(taskInfo.getName())
                .readerType(taskInfo.getReaderType())
                .writerType(taskInfo.getWriterType())
                .nodeCount(nodes.size())
                .nodes(nodes)
                .parallelism(taskInfo.getParallelism())
                .build();
    }

    /**
     * 获取调试状态
     */
    public Map<String, Object> getDebugStatus() {
        List<DebugSession> activeSessions = getActiveSessions();

        Map<String, Object> status = new HashMap<>();
        status.put("enabled", debugEnabled);
        status.put("activeSessions", activeSessions.size());
        status.put("maxConcurrentSessions", maxConcurrentSessions);
        status.put("activeWebSocketConnections", DebugWebSocketHandler.getActiveSessionCount());
        status.put("timestamp", System.currentTimeMillis());

        return status;
    }

    // ========== 私有辅助方法 ==========

    /**
     * 解析输入数据
     */
    private List<Map<String, Object>> parseInputData(String inputDataJson) {
        try {
            Object parsed = JSON.parse(inputDataJson);
            List<Map<String, Object>> result = new ArrayList<>();

            if (parsed instanceof List) {
                List<?> list = (List<?>) parsed;
                for (Object item : list) {
                    if (item instanceof Map) {
                        result.add((Map<String, Object>) item);
                    }
                }
            } else if (parsed instanceof Map) {
                result.add((Map<String, Object>) parsed);
            } else {
                throw new IllegalArgumentException("输入数据必须是JSON对象或JSON数组");
            }

            return result;

        } catch (Exception e) {
            log.error("解析输入数据失败: {}", inputDataJson, e);
            throw new IllegalArgumentException("输入数据解析失败: " + e.getMessage());
        }
    }

    /**
     * 解析任务节点
     */
    private List<TaskNode> parseTaskNodes(TaskInfo taskInfo) {
        try {
            List<DebugNode> debugNodes = topologyParser.parseTopology(taskInfo);
            List<TaskNode> taskNodes = new ArrayList<>();

            for (DebugNode debugNode : debugNodes) {
                TaskNode taskNode = TaskNode.builder()
                        .nodeId(debugNode.getNodeId())
                        .nodeName(debugNode.getNodeName())
                        .nodeType(debugNode.getNodeType())
                        .index(debugNode.getIndex())
                        .build();

                taskNodes.add(taskNode);
            }

            return taskNodes;

        } catch (Exception e) {
            log.warn("解析任务节点失败: taskId={}", taskInfo.getId(), e);
            return new ArrayList<>();
        }
    }
}

// ========== 内部数据类 ==========

/**
 * 输入验证结果
 */
@lombok.Data
@lombok.Builder
@lombok.AllArgsConstructor
@lombok.NoArgsConstructor
class InputValidationResult {
    private boolean valid;
    private Integer recordCount;
    private String dataType;
    private String error;
    private String message;
}

/**
 * 任务摘要
 */
@lombok.Data
@lombok.Builder
@lombok.AllArgsConstructor
@lombok.NoArgsConstructor
class TaskSummary {
    private Integer taskId;
    private String taskName;
    private String readerType;
    private String writerType;
    private Integer nodeCount;
    private List<TaskNode> nodes;
    private Integer parallelism;
}

/**
 * 任务节点
 */
@lombok.Data
@lombok.Builder
@lombok.AllArgsConstructor
@lombok.NoArgsConstructor
class TaskNode {
    private String nodeId;
    private String nodeName;
    private String nodeType;
    private Integer index;
}
