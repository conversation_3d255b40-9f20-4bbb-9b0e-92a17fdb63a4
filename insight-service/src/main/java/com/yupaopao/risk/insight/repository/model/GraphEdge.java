package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc GraphNode
 * @date 2020/9/15
 */
@Getter
@Setter
@Table(name = "t_graph_edge")
public class GraphEdge {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "graph_id")
    private Integer graphId;

    @Column(name = "data_id")
    private String dataId;

    @Column(name = "label")
    private String label;

    @Column(name = "edge_source")
    private String source;

    @Column(name = "edge_target")
    private String target;

    @Column(name = "properties")
    private String properties;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}
