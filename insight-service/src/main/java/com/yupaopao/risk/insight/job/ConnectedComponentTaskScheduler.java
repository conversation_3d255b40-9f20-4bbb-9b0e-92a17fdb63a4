package com.yupaopao.risk.insight.job;

import com.yupaopao.aries.client.JobExecutionContext;
import com.yupaopao.aries.client.JobListener;
import com.yupaopao.framework.spring.boot.aries.annotation.AriesCronJobListener;
import com.yupaopao.risk.insight.service.FlinkJobSchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-02 15:57
 * 连通图调度
 ***/
@Slf4j
@AriesCronJobListener
public class ConnectedComponentTaskScheduler  implements JobListener {

    @Autowired
    private FlinkJobSchedulerService flinkJobSchedulerService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        try {
            flinkJobSchedulerService.run("connected-detection.jar",null);
        } catch (Exception e) {
            log.error("run connected-detection.jar failed: ",e);
        }
    }
}
