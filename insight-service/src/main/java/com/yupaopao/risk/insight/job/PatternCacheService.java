package com.yupaopao.risk.insight.job;

import com.alibaba.fastjson.JSON;
import com.yupaopao.aries.client.JobExecutionContext;
import com.yupaopao.aries.client.JobListener;
import com.yupaopao.framework.spring.boot.aries.annotation.AriesCronJobListener;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.risk.insight.common.cep.beans.CepRule;
import com.yupaopao.risk.insight.common.cep.utils.PatternTools;
import com.yupaopao.risk.insight.repository.mapper.CepPatternMapper;
import com.yupaopao.risk.insight.repository.model.CepPattern;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-02 21:36
 *
 ***/

@Slf4j
@AriesCronJobListener
public class PatternCacheService implements JobListener {

    @Autowired
    private CepPatternMapper cepPatternMapper;

    @RedisAutowired("middleware.redis.risk-engine")
    private RedisService redisService;

    //    @Scheduled(cron = "0 0/2 * * * ?")
    public void refreshPatternCache() {
        RedisService.RedisLocker locker = null;
        try {
            log.info("start to cache pattern");
            //两分钟更新一次缓存
            List<CepPattern> patterList = cepPatternMapper.selectAll();
            if (CollectionUtils.isEmpty(patterList)) {
                log.info("no patters");
                return;
            }
            List<CepRule> patternCacheList = patterList.stream().filter(elem -> "ONLINE".equals(elem.getStatus()))
                    .map(elem -> {
                        CepRule cepRule = new CepRule();
                        cepRule.setRuleId(elem.getId().toString());
                        cepRule.setContent(PatternTools.formatPattern(elem.getContent()));
                        cepRule.setGroupByColumns(elem.getGroupByColumn());
                        cepRule.setDataSource(elem.getDataSource());
                        return cepRule;
                    }).collect(Collectors.toList());

            locker = redisService.buildLock("patternUpdateLock", 1, TimeUnit.MINUTES);
            boolean getLock = locker.tryLock();
            if (!getLock) {
                log.info("cache pattern get lock failed");
                return;
            }

            RedisTemplate template = redisService.getRedisTemplate();
            long size = redisService.lGetListSize("allRules");
            //remove all
            template.opsForList().trim("allRules", 1, 0);
            if (CollectionUtils.isEmpty(patternCacheList)) {
                return;
            }
            // add all
            template.opsForList().rightPushAll("allRules", patternCacheList);
            log.info("finished cache patterns, count: {}, rules: {}", patternCacheList.size(), JSON.toJSONString(patternCacheList));
        } catch (Exception e) {
            log.error("cache pattern error", e);
        } finally {
            if (locker != null) {
                locker.release();
            }
        }
    }


    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        refreshPatternCache();
    }

}
