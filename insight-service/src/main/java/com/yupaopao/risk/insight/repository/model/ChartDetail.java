package com.yupaopao.risk.insight.repository.model;

import com.yupaopao.risk.insight.dashboard.beans.AxisEnum;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Table(name = "t_chart_detail")
@Data
public class ChartDetail implements Serializable {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 图表主键
     */
    @Column(name = "chart_id")
    private Integer chartId;

    /**
     * 轴
     */
    private AxisEnum axis;

    /**
     * 字段
     */
    private String field;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建人
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}