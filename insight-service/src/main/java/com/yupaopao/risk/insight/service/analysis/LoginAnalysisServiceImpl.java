package com.yupaopao.risk.insight.service.analysis;

import com.google.common.collect.Maps;
import com.yupaopao.platform.user.auth.api.entity.response.UserAuthDataMaskingDTO;
import com.yupaopao.risk.insight.beans.LoginAnalysisResult;
import com.yupaopao.risk.insight.beans.PieVO;
import com.yupaopao.risk.insight.service.ESQueryService;
import com.yupaopao.risk.insight.service.LoginAnalysisService;
import com.yupaopao.risk.insight.service.PortraitTagService;
import com.yupaopao.risk.insight.service.beans.TagParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class LoginAnalysisServiceImpl implements LoginAnalysisService {

    @Autowired
    ESQueryService esQueryService;
    private static final String INDEX = "risk_login_log_data";
    @Autowired
    private PortraitTagService portraitTagService;

    @Override
    public LoginAnalysisResult analysis(List<UserAuthDataMaskingDTO> mobiles) {
        LoginAnalysisResult loginAnalysisResult = new LoginAnalysisResult();
        Map<String, Object> request = Maps.newHashMap();
        List uidList = new ArrayList();
        for (UserAuthDataMaskingDTO mobile : mobiles) {
            uidList.add(mobile.getUid());
        }
        request.put("data.UserId", uidList);
        List<Map<String, Object>> loginList = esQueryService.searchBy(request, getQueryFiledMap(), INDEX);
        Map<Long, Map<String, Object>> result = Maps.newHashMap();
        for (Map<String, Object> loginMap : loginList) {
            getLastLoginRiskMap(result, loginMap, mobiles);
        }
        //登录原因分布
        Map<String, Integer> loginReasonMap = Maps.newHashMap();
        for (Map<String, Object> map : loginList) {
            analysisLoginRegister(loginReasonMap, map);
        }
        if (uidList.size()>result.size()) {
            loginReasonMap.put("最近未登录", uidList.size()-result.size());
        }
        loginAnalysisResult.setLoginResultList(coverList(loginReasonMap));
        loginAnalysisResult.setLastLoginRiskList(result);
        return loginAnalysisResult;
    }

    public Map<String, Class> getQueryFiledMap() {
        Map<String, Class> queryField = Maps.newHashMap();
        queryField.put("userId", String.class);
        queryField.put("reason", String.class);
        queryField.put("level", String.class);
        queryField.put("result.level", String.class);
        queryField.put("data.preLoginCheck.detail.description", String.class);
        queryField.put("data.preLoginCheck.riskLevel", String.class);
        queryField.put("data.preLoginCheck.detail.hits", List.class);
        queryField.put("createdAt", String.class);
        queryField.put("deviceId", String.class);
        queryField.put("mobileNo", String.class);
        return queryField;
    }

    private List<PieVO> coverList(Map<String, Integer> registerTimeMap) {
        List<PieVO> loginList = new ArrayList<>();
        registerTimeMap.forEach((k, v) -> loginList.add(new PieVO(k, v)));
        return loginList;
    }
    private void analysisLoginRegister(Map<String, Integer> loginReasonMap, Map<String, Object> map) {
        // 注册时间分布
        Object desc = map.get("data.preLoginCheck.detail.description");
        Object reason = map.get("reason");
        Object hitsObj = map.get("data.preLoginCheck.detail.hits");
        if (Objects.nonNull(hitsObj) && ((List) hitsObj).size()>0) {
            List<Map<String, Object>> hits = (List) hitsObj;
            for (Map<String, Object> hit : hits) {
                String descriptionV2 = (String) hit.get("descriptionV2");
                StatisticValue(loginReasonMap, descriptionV2);
            }
        }else if (Objects.nonNull(desc)){
            StatisticValue(loginReasonMap, desc+"");
        }else if (Objects.nonNull(reason)){
            StatisticValue(loginReasonMap, reason+"");
        }else {
            StatisticValue(loginReasonMap, "");
        }
    }
    private void StatisticValue(Map<String, Integer> map, String key) {
        if (map.containsKey(key)) {
            map.put(key, map.get(key) + 1);
        } else {
            map.put(key, 1);
        }
    }
    private void getLastLoginRiskMap(Map<Long,Map<String, Object>> result, Map<String, Object> loginMap, List<UserAuthDataMaskingDTO> mobiles){
        for (UserAuthDataMaskingDTO mobile : mobiles) {
            if (loginMap.get("userId").equals(mobile.getUid()+"")) {
                Map<String, Object> record = Maps.newHashMap();
                record.put("level", loginMap.get("level"));
                record.put("mobileNo", loginMap.get("mobileNo"));
                record.put("reason", loginMap.get("reason"));
                record.put("resultLevel", loginMap.get("result.level"));
                record.put("userId", loginMap.get("userId"));
                record.put("preLoginCheckRiskLevel", loginMap.get("data.preLoginCheck.riskLevel"));
                record.put("deviceId", loginMap.get("deviceId"));
                Object devicePortrait = getDevicePortrait(loginMap);
                if (Objects.nonNull(devicePortrait)){
                    record.put("deviceLevel", String.valueOf(devicePortrait));
                }
                record.put("createdAt", loginMap.get("createdAt"));
                record.put("description", loginMap.get("data.preLoginCheck.detail.description"));
                record.put("hits", loginMap.get("data.preLoginCheck.detail.hits"));
                result.put(mobile.getUid(), record);
                return;
            }
        }
    }
    private Object getDevicePortrait(Map<String, Object> registerMap) {
        TagParam tagParam = new TagParam();
        tagParam.setCode("devicePortrait");
        tagParam.setDimension("deviceId");
        tagParam.setValue(registerMap.get("deviceId")+"");
        return portraitTagService.getSimpleTag(tagParam);
    }
}
