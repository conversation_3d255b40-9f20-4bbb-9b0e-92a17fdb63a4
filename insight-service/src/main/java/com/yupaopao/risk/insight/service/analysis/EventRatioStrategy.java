package com.yupaopao.risk.insight.service.analysis;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.service.ClickHouseService;
import com.yupaopao.risk.insight.service.beans.BehaviorAnalysisData;
import com.yupaopao.risk.insight.service.beans.BehaviorAnalysisResult;
import com.yupaopao.risk.insight.util.ClickHouseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ru.yandex.clickhouse.response.ClickHouseResponse;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-08-12 19:30
 * 事件频次策略：根据每个id近14天的事件占比确定是否某个类型
 ***/

@Slf4j
@Service
public class EventRatioStrategy {

    public static void main(String[] args) {
        System.err.println(createOrderUserSqlTemplate);
    }


    public static final int CK_QUERY_BATCH_SIZE = 500;

    @Autowired
    private ClickHouseService clickHouseService;

    @Autowired
    private StrategyConfig strategyConfig;

    public BehaviorAnalysisResult hitDetection(BehaviorAnalysisData inputData, List<String> events, String behavior) {

        //计算各类型数据所在事件占比
        if (CollectionUtils.isEmpty(inputData.getData()) || CollectionUtils.isEmpty(events)) {
            return new BehaviorAnalysisResult(0, 0, new ArrayList<>());
        }


        String endDay = inputData.getEndDay();
        if (StringUtils.isEmpty(endDay)) {
            endDay = InsightDateUtils.getDateStr(new Date(), InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
        }

        String before7Day = InsightDateUtils.getDateStr(
                DateUtils.addDays(InsightDateUtils.getDateFromString(endDay, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss
                ), -7), InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);


        JSONObject strategyMap = strategyConfig.getStrategies(behavior);
        strategyMap.put("eventCodes", joinList(events));
        strategyMap.put("startTime", before7Day);
        strategyMap.put("endDay", endDay);
        String parameterizedSql = "";

        List<String> detectIds = inputData.getData();

        if ("userId".equalsIgnoreCase(inputData.getType())) {

            parameterizedSql = userSqlTemplate;

            if ("ASSIGN-ORDER".equals(behavior)) {
                parameterizedSql = createOrderUserSqlTemplate;
            }

        } else if ("deviceId".equalsIgnoreCase(inputData.getType())) {
            parameterizedSql = deviceSqlTemplate;
            if ("ASSIGN-ORDER".equals(behavior)) {
                parameterizedSql = createOrderDeviceSqlTemplateForDevice;
            }
        } else {
            throw new IllegalArgumentException("not support type: " + inputData.getType());
        }


        if (CollectionUtils.isEmpty(detectIds) || CollectionUtils.isEmpty(events)) {
            return new BehaviorAnalysisResult(inputData.getData().size(), 0, new ArrayList<>());
        }

        List<String> hitList = fetchHitListWithLimit(detectIds, strategyMap, parameterizedSql);

        return new BehaviorAnalysisResult(inputData.getData().size(), hitList.size(), hitList);
    }

    /**
     * 针对id过多一次性ck查询太慢超时的情况，将id分批次查询
     *
     * @return
     */
    private List<String> fetchHitListWithLimit(List<String> ids, JSONObject sqlParams, String parameterizedSql) {
        List<String> resultList = new ArrayList<>();
        int batchSize = CK_QUERY_BATCH_SIZE;
        int batchLoop = (int) Math.ceil(ids.size() * 1.0 / batchSize);
        for (int i = 0; i < batchLoop; i++) {
            int currentStartIndex = i * batchSize;
            int currentEndIndex = (i + 1) * batchSize;
            if (currentEndIndex > ids.size()) {
                currentEndIndex = ids.size();
            }
            sqlParams.put("ids", joinList(ids.subList(currentStartIndex, currentEndIndex)));
            StrSubstitutor sub = new StrSubstitutor(sqlParams);
            List<Map<String, String>> ratioList = clickHouseService.executeQuery(sub.replace(parameterizedSql));
            List<String> hitList = ratioList.stream().map(elem -> elem.get("id")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hitList)) {
                resultList.addAll(hitList);
            }
        }
        return resultList;
    }


    private String joinList(List<?> objects) {
        String joinStr = "'" + StringUtils.join(objects, "','") + "'";
        return joinStr;
    }


    private static String userSqlTemplate = "select userId as id, sum(if(eventCode in (${eventCodes}), eCount, 0)" +
            ") as targetCount, sum(eCount) as totalCount, " +
            "  targetCount/totalCount as ratio\n" +
            "from (\n" +
            "      select\n" +
            "             userId, eventCode, count(1) eCount\n" +
            "      from risk_hit_log a\n" +
            "      where a.createdAt between '${startTime}' and '${endDay}'\n" +
            "        and userId in (${ids})\n" +
            "      group by userId, eventCode\n" +
            ") group by userId\n" +
            "having ratio> ${minRatio} and targetCount>=${targetCount}";

    private static String deviceSqlTemplate = "select deviceId as id, sum(if(eventCode in (${eventCodes}), eCount, 0)" +
            ") as targetCount, sum(eCount) as totalCount, " +
            "  targetCount/totalCount as ratio\n" +
            "from (\n" +
            "      select\n" +
            "             deviceId, eventCode, count(1) eCount\n" +
            "      from risk_hit_log a\n" +
            "      where a.createdAt between '${startTime}' and '${endDay}'\n" +
            "        and deviceId in (${ids})\n" +
            "      group by deviceId, eventCode\n" +
            ") group by deviceId\n" +
            " having ratio> ${minRatio} and targetCount>=${targetCount}";


//    private static String createOrderSqlTemplate = "select userId as id,count(1) eCount from temp_cep_record where " +
//            "userId in (\n" +
//            "    ${ids}\n" +
//            "    ) and createdAt >='2020-08-03' and createdAt< '2020-08-18' group by userId having eCount>=4";


    private static String createOrderUserSqlTemplate = "select userId as id ,\n" +
            "       sum(if(eventCode in ('assign-order-create','play-order-finish'\n" +
            "                            ), eCount, 0)) as\n" +
            "                                targetCount,\n" +
            "       sum(if(eventCode in ('search'), searchCount, 0)) as\n" +
            "           searchCount,\n" +
            "       searchCount/targetCount searchTargetRatio,\n" +
            "       sum(eCount)              totalCount,\n" +
            "       targetCount / totalCount ratio\n" +
            "from (\n" +
            "      select userId,\n" +
            "             eventCode,\n" +
            "             count(1) eCount,\n" +
            "             if(eventCode='search',count(distinct data_content),0) searchCount\n" +
            "      from risk_hit_log a\n" +
            "      where a.createdAt between '${startTime}' and '${endDay}'\n" +
            "        and userId in (\n" +
            "    ${ids}\n" +
            "          )\n" +
            "      group by userId, eventCode\n" +
            "         )\n" +
            "group by userId\n" +
            "having ratio>=${minRatio} and searchTargetRatio>=${searchMinRatio} and searchTargetRatio<=${searchMaxRatio}";


    private static String createOrderDeviceSqlTemplateForDevice = "select deviceId as id ,\n" +
            "       sum(if(eventCode in ('assign-order-create','play-order-finish'\n" +
            "                            ), eCount, 0)) as\n" +
            "                                targetCount,\n" +
            "       sum(if(eventCode in ('search'), searchCount, 0)) as\n" +
            "           searchCount,\n" +
            "       searchCount/targetCount searchTargetRatio,\n" +
            "       sum(eCount)              totalCount,\n" +
            "       targetCount / totalCount ratio\n" +
            "from (\n" +
            "      select deviceId,\n" +
            "             eventCode,\n" +
            "             count(1) eCount,\n" +
            "             if(eventCode='search',count(distinct data_content),0) searchCount\n" +
            "      from risk_hit_log a\n" +
            "      where a.createdAt between '${startTime}' and '${endDay}'\n" +
            "        and deviceId in (\n" +
            "    ${ids}\n" +
            "          )\n" +
            "      group by deviceId, eventCode\n" +
            "         )\n" +
            "group by deviceId\n" +
            "having ratio>=${minRatio} and searchTargetRatio>=${searchMinRatio} and searchTargetRatio<=${searchMaxRatio}";


}
