package com.yupaopao.risk.insight.exception;


/**
 * 系统运行时异常
 *
 * <AUTHOR>
 * @date 2018-08-20
 */
public class BusinessException extends RuntimeException {

    private String code;
    private String msg;

    public BusinessException(ErrorMessage errorMessage) {
        this.code = errorMessage.getCode();
        this.msg = errorMessage.getMsg();
    }

    public BusinessException(ErrorMessage errorMessage, Throwable cause) {
        super(cause);
        this.code = errorMessage.getCode();
        this.msg = errorMessage.getMsg();
    }

    public BusinessException(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
