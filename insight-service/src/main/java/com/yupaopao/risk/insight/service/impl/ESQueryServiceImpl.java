package com.yupaopao.risk.insight.service.impl;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.collect.Maps;
import com.yupaopao.risk.insight.service.ESQueryService;
import com.yupaopao.risk.insight.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

@Slf4j
@Component
public class ESQueryServiceImpl implements ESQueryService {

    private RestHighLevelClient client;

    @ApolloConfig("flink.es.audit")
    private Config apolloConfig;


    @Override
    public List<Map<String, Object>> queryBySql(String sql, List<String> column) {
        return null;
    }

    @Override
    public List<Map<String, Object>> searchBy(Map<String, Object> request, Map<String, Class> queryField, String indexName) {
        log.info("查询索引:{},{}", indexName, request);
        List<Map<String, Object>> result = new ArrayList();
        SearchRequest searchRequest = new SearchRequest(indexName);
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        Iterator<Map.Entry<String, Object>> iterator = request.entrySet().iterator();
        for (;iterator.hasNext();){
            Map.Entry<String, Object> next = iterator.next();
            Object value = next.getValue();
            if (value instanceof Collection){
                TermsQueryBuilder termsQueryBuilder = QueryBuilders.termsQuery(next.getKey(), (List)value);
                query.must(termsQueryBuilder);
            }else {
                query.must(QueryBuilders.termQuery(next.getKey(), value));
            }

        }
        searchRequest.indicesOptions(IndicesOptions.fromOptions(true, true, true, false));
        searchRequest.source().query(query);
        searchRequest.source().size(10000);
        try {
            SearchResponse search = getClient().search(searchRequest);
            if (Objects.nonNull(search) && search.status() == RestStatus.OK) {
                SearchHits hits = search.getHits();
                Iterator<SearchHit> hitIterator = hits.iterator();
                for (;hitIterator.hasNext();){
                    Map<String, Object> sourceAsMap = hitIterator.next().getSourceAsMap();
                    Map<String, Object> record = Maps.newHashMap();
                    queryField.forEach((k,v)->{
                        record.put(k, CommonUtil.read(sourceAsMap, k, v));
                    });
                    result.add(record);
                }
            }
        } catch (IOException e) {
            log.error("es 查询 索引出错:{},{}",indexName,request, e);
        }
        log.info("查询结果:request:{}, {}", request, result);
        return result;
    }


    public RestHighLevelClient getClient() {
        if (Objects.isNull(client)) {
            synchronized (ESQueryServiceImpl.class){
                if (Objects.nonNull(client)) {
                    return client;
                }
                String url = apolloConfig.getProperty("es.hosts", "test-es.yupaopao.com:80");
                String userName = apolloConfig.getProperty("es.username", "");
                String passWord = apolloConfig.getProperty("es.password", "");
                log.info("初始化 生产环境 es 客户端:{},{},{}", url, userName, passWord);
                String[] split = url.split(":");
                HttpHost httpHost = new HttpHost(split[0], Integer.valueOf(split[1]));
                RestClientBuilder builder = RestClient.builder(httpHost);
                if (StringUtils.isNotBlank(userName) && StringUtils.isNotBlank(passWord)) {
                    final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                    credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(userName, passWord));
                    builder.setHttpClientConfigCallback(httpClient -> httpClient.setDefaultCredentialsProvider(credentialsProvider));
                }
                builder.setMaxRetryTimeoutMillis(10000);
                client = new RestHighLevelClient(builder);
            }
        }
        return client;
    }

}
