package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.service.PrometheusMetricService;
import com.yupaopao.risk.insight.service.alert.beans.FlinkTaskMetricResult;
import com.yupaopao.risk.insight.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/****
 * zengxiangcai
 * 2022/11/9 14:01
 ***/


@Slf4j
@Service
public class PrometheusMetricServiceImpl implements PrometheusMetricService {


    @Resource
    private OkHttpClient okHttpClient;

    @Value("${prometheusHost:http://*************:9090/}")
    private String prometheusHost;

    public static String instantUrl = "api/v1/query";
    public static String rangeUrl = "api/v1/query_range";


    /***
     *
     * @param query prometheus 语句
     * @param queryTime 查询时间,秒
     * @return
     */
    public List<FlinkTaskMetricResult> executeInstancePromQL(String query, String queryTime) {

        FormBody.Builder builder = new FormBody.Builder();
        builder.add("time", queryTime); //传入时间为当前秒
        builder.add("query", query);


        Request request = new Request.Builder()
                .url(prometheusHost + instantUrl)
                .post(builder.build()).build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
        } catch (Exception e) {
            log.error("query prometheus error ,query=" + query, e);
        }
        if (response == null || !response.isSuccessful()) {
            log.error("query " + query + " failed with response: " + response);
            return new ArrayList<>();
        }
        return parseResult(response);
    }

    //    {
//        "status": "success",
//            "data": {
//        "resultType": "vector",
//                "result": [
//        {
//            "metric": {
//            "cluster": "prod-hangzhou-k8s06",
//                    "cluster_id": "bu-hangzhou-prod06",
//                    "cluster_name": "杭州业务prod",
//                    "endpoint": "metrics",
//                    "env": "prod",
//                    "host": "************",
//                    "instance": "************:9249",
//                    "job": "risk-flink",
//                    "job_id": "6b283c7942b9defe390d497d08321305",
//                    "job_name": "audit-metric",
//                    "namespace": "flink-risk",
//                    "pod": "risk-flink-1.13.3-58478977b5-pc5v4",
//                    "prometheus": "open-telemetry/bu-hangzhou-prod06",
//                    "service": "risk-flink"
//        },
//            "value": [
//            1667975551.751,
//                    "3"
//                ]
//        }
//        ]
//    }
//    }
    private static List<FlinkTaskMetricResult> parseResult(Response prometheusResult) {

        try {
            String body = prometheusResult.body().string();
            JSONObject jsonObj = JSONObject.parseObject(body);
            String resultType = CommonUtil.read(jsonObj, "$.data.resultType", String.class);
            if ("vector".equals(resultType)) {
                JSONArray jsonArray = CommonUtil.read(jsonObj, "$.data.result", JSONArray.class);
                //符合条件的指标。
                List<FlinkTaskMetricResult> matchList = jsonArray.stream().map(elem -> {
                    JSONObject metricInfo = (JSONObject) elem;
                    ;
                    FlinkTaskMetricResult metricResult = JSON.parseObject(metricInfo.getJSONObject("metric").toJSONString(),
                            FlinkTaskMetricResult.class);
                    metricResult.setValue(metricInfo.getJSONArray("value"));
                    return metricResult;
                }).collect(Collectors.toList());
                return matchList;
            } else if ("matrix".equals(resultType)) {
                //range 查询范围
            }

        } catch (Exception e) {
            log.error("parse prometheus result error " + prometheusResult, e);
        }
        return new ArrayList<>();
    }


    public static void main(String[] args) {
        OkHttpClient.Builder builder1 = new OkHttpClient.Builder();
        OkHttpClient okHttpClient1 = builder1.build();
        String baseUrl = "http://*************:9090/";
        String instanceUrl = "api/v1/query";
        String rangeUrl = "api/v1/query_range";
        String query = "";

        FormBody.Builder builder = new FormBody.Builder();
        builder.add("time", String.valueOf(System.currentTimeMillis() / 1000));
        builder.add("query", "changes(flink_jobmanager_job_numberOfCompletedCheckpoints{service=\"risk-flink\"," +
                "job_name='audit-metric'}[10m])<=3");


        Request request = new Request.Builder()
                .url(baseUrl + instanceUrl)
                .post(builder.build()).build();
        Response response = null;
        try {
            response = okHttpClient1.newCall(request).execute();
            List<FlinkTaskMetricResult> matchList = parseResult(response);

            System.err.println(matchList);
        } catch (Exception e) {
            log.error("query prometheus error", e);
        }
    }




}
