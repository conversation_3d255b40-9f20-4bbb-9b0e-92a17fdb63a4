package com.yupaopao.risk.insight.constant;

import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.Map;

/****
 * zengxiangcai
 * 2022/8/17 13:59
 ***/
public class RiskOverviewConstants {

  public static String GRAPH_EDGE_TABLE = "graph_edge";

  public static String HAS_DEVICE_LABEL = "hasDevice";

  public static String HAS_IP_LABEL = "hasIp";
  public static String firstLevelRelateSql = "select userId,count(distinct deviceId) deviceCount,count(distinct clientIp) ipCount\n" +
          "from risk_hit_log where  createdAt>= addDays(now(),-7) and userId!='' " +
          " and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "and deviceId!='' and clientIp!='' and clientIp!='0.0.0.0'\n" +
          "and userId in (\n" +
          "    ${sqlUidListStr}" +
          ")\n" +
          "group by userId";

  public static String firstLevelRelateDataSql = "select userId,\n" +
          "arrayDistinct(groupArray(deviceId)) deviceIdList,\n" +
          "arrayDistinct(groupArray(clientIp)) clientIpList\n" +
          "from risk_hit_log where  createdAt>= addDays(now(),-7) and userId!='' " +
          " and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "and deviceId!='' and clientIp!='' and clientIp!='0.0.0.0'\n" +
          "and userId in (\n" +
          "    ${sqlUidListStr}" +
          ")\n" +
          "group by userId";

  public static String secondLevelDeviceData = " select deviceId,\n" +
          "                arrayDistinct(groupArray(userId)) userList,\n" +
          "                arrayDistinct(groupArray(clientIp)) ipList\n" +
          "         from risk_hit_log\n" +
          "         where createdAt >= addDays(now(), ${limitDay})\n" +
          "           and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "           and userId != ''\n" +
          "           and deviceId != ''\n" +
          "           and clientIp != ''\n" +
          "           and clientIp != '0.0.0.0'\n" +
          "           and deviceId  in (${strDeviceList})\n" +
          "         group by deviceId";

  public static String secondLevelDeviceRelateUserData = " select deviceId,\n" +
          "                arrayDistinct(groupArray(userId)) userList\n" +
          "         from risk_hit_log\n" +
          "         where createdAt >= addDays(now(), ${limitDay})\n" +
          "           and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "           and userId != ''\n" +
          "           and deviceId != ''\n" +
          "           and deviceId  in (${strDeviceList})\n" +
          "         group by deviceId";

  public static String secondLevelDeviceRelateIpData = " select deviceId,\n" +
          "                arrayDistinct(groupArray(clientIp)) ipList\n" +
          "         from risk_hit_log\n" +
          "         where createdAt >= addDays(now(), ${limitDay})\n" +
          "           and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "           and deviceId != ''\n" +
          "           and clientIp != ''\n" +
          "           and clientIp != '0.0.0.0'\n" +
          "           and deviceId  in (${strDeviceList})\n" +
          "         group by deviceId";

  public static String secondLevelIpData = " select clientIp,\n" +
          "                arrayDistinct(groupArray(userId)) userList,\n" +
          "                arrayDistinct(groupArray(deviceId)) deviceList\n" +
          "         from risk_hit_log\n" +
          "         where createdAt >= addDays(now(), ${limitDay})\n" +
          "           and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "           and userId != ''\n" +
          "           and deviceId != ''\n" +
          "           and clientIp != ''\n" +
          "           and clientIp != '0.0.0.0'\n" +
          "           and clientIp in (${strIpList})\n" +
          "         group by clientIp";

  public static String secondLevelIpRelateUserData = " select clientIp,\n" +
          "                arrayDistinct(groupArray(userId)) userList\n" +
          "         from risk_hit_log\n" +
          "         where createdAt >= addDays(now(), ${limitDay})\n" +
          "           and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "           and userId != ''\n" +
          "           and clientIp != ''\n" +
          "           and clientIp != '0.0.0.0'\n" +
          "           and clientIp in (${strIpList})\n" +
          "         group by clientIp";


  public static String secondLevelIpRelateDeviceData = " select clientIp,\n" +
          "                arrayDistinct(groupArray(deviceId)) deviceList\n" +
          "         from risk_hit_log\n" +
          "         where createdAt >= addDays(now(), ${limitDay})\n" +
          "           and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "           and deviceId != ''\n" +
          "           and deviceId != '0'\n" +
          "           and clientIp != ''\n" +
          "           and clientIp != '0.0.0.0'\n" +
          "           and clientIp in (${strIpList})\n" +
          "         group by clientIp";

  public static String secondLevelDeviceRelateSql = "select userId,length(arrayDistinct(arrayFlatten(groupArray(userList)))) " +
          "userCount,\n" +
          "       length(arrayDistinct(arrayFlatten(groupArray(userList)))) ipCount\n" +
          "from (\n" +
          "         select deviceId,\n" +
          "                arrayDistinct(groupArray(userId)) userList,\n" +
          "                arrayDistinct(groupArray(clientIp)) ipList\n" +
          "         from risk_hit_log\n" +
          "         where createdAt >= addDays(now(), ${limitDay})\n" +
          "           and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "           and userId != ''\n" +
          "           and clientIp != ''\n" +
          "           and clientIp != '0.0.0.0'\n" +
          "           and deviceId global in (select distinct deviceId\n" +
          "                                   from risk_hit_log\n" +
          "                                   where createdAt >= addDays(now(), ${limitDay})\n" +
          "                                     and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "                                     and userId != ''\n" +
          "                                     and deviceId != ''\n" +
          "                                     and clientIp != ''\n" +
          "                                     and clientIp != '0.0.0.0'\n" +
          "                                     and userId in (\n" +
          "                                                    ${sqlUidListStr}" +
          "                                       )\n" +
          "                                   )\n" +
          "         group by deviceId\n" +
          " ) a global all inner join (\n" +
          "    select distinct deviceId,userId\n" +
          "    from risk_hit_log\n" +
          "    where createdAt >= addDays(now(), ${limitDay})\n" +
          "      and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "      and userId != ''\n" +
          "      and deviceId != ''\n" +
          "      and clientIp != ''\n" +
          "      and clientIp != '0.0.0.0'\n" +
          "      and userId in (\n" +
          "        ${sqlUidListStr}" +
          "        )\n" +
          ") b on a.deviceId = b.deviceId\n" +
          "group by userId\n" +
          ";";
  public static String secondLevelIpRelateSql = "\n" +
          "select userId,length(arrayDistinct(arrayFlatten(groupArray(userList)))) userCount,\n" +
          "       length(arrayDistinct(arrayFlatten(groupArray(deviceList)))) deviceCount\n" +
          "from (\n" +
          "         select clientIp,\n" +
          "                arrayDistinct(groupArray(userId)) userList,\n" +
          "                arrayDistinct(groupArray(deviceId)) deviceList\n" +
          "         from risk_hit_log\n" +
          "         where createdAt >= addDays(now(), ${limitDay})\n" +
          "           and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "           and userId != ''\n" +
          "           and clientIp != ''\n" +
          "           and clientIp != '0.0.0.0'\n" +
          "           and clientIp global in (select distinct clientIp\n" +
          "                                   from risk_hit_log\n" +
          "                                   where createdAt >= addDays(now(), ${limitDay})\n" +
          "                                     and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "                                     and userId != ''\n" +
          "                                     and deviceId != ''\n" +
          "                                     and clientIp != ''\n" +
          "                                     and clientIp != '0.0.0.0'\n" +
          "                                     and userId in (\n" +
          "                                                   ${sqlUidListStr} " +
          "                                       )\n" +
          "         )\n" +
          "         group by clientIp\n" +
          "         ) a global all inner join (\n" +
          "    select distinct clientIp,userId\n" +
          "    from risk_hit_log\n" +
          "    where createdAt >= addDays(now(), ${limitDay})\n" +
          "      and kafkaTopic='RISK-ONLINE-RESULT-LOG' \n" +
          "      and userId != ''\n" +
          "      and deviceId != ''\n" +
          "      and clientIp != ''\n" +
          "      and clientIp != '0.0.0.0'\n" +
          "      and userId in (\n" +
          "     ${sqlUidListStr} " +
          "        )\n" +
          "    ) b on a.clientIp = b.clientIp\n" +
          "group by userId\n";


  public static String firstLevelRelateDetailSql = "select deviceId,clientIp,count(1) activeCount\n" +
          "from risk_hit_log\n" +
          "where createdAt >= addDays(now(), ${limitDay})\n" +
          "  and kafkaTopic = 'RISK-ONLINE-RESULT-LOG'\n" +
          "  and userId != ''\n" +
          "  and deviceId != '0'\n" +
//          "  and deviceId != ''\n" +
//          "  and clientIp != ''\n" +
          "  and clientIp != '0.0.0.0'\n" +
          "  and userId ='${uid}'\n" +
          "group by deviceId,clientIp\n" +
          "order by activeCount desc,deviceId,clientIp limit 500";

  public static String secondLevelDeviceRelateDetailSql = "select deviceId,userId,clientIp, count(1) activeCount\n" +
          "from risk_hit_log\n" +
          "where createdAt >= addDays(now(), ${limitDay})\n" +
          "  and kafkaTopic = 'RISK-ONLINE-RESULT-LOG'\n" +
//          "  and clientIp != '0.0.0.0'\n" +
          "  and deviceId global in (select distinct deviceId\n" +
          "                          from risk_hit_log\n" +
          "                          where createdAt >= addDays(now(), ${limitDay})\n" +
          "                            and kafkaTopic = 'RISK-ONLINE-RESULT-LOG'\n" +
          "                            and userId != ''\n" +
          "                            and deviceId != ''\n" +
          "                            and deviceId != '0'\n" +
          "                            and userId ='${uid}'\n" +
          ")\n" +
          " group by deviceId, userId,clientIp\n" +
          "order by activeCount desc,deviceId, userId,clientIp limit 500";

  public static String secondLevelIpRelateDetailSql = "\n" +
          "select clientIp,userId,deviceId, count(1) activeCount\n" +
          "from risk_hit_log\n" +
          "where createdAt >= addDays(now(), ${limitDay})\n" +
          "  and kafkaTopic = 'RISK-ONLINE-RESULT-LOG'\n" +
          "  and clientIp global in (select distinct clientIp\n" +
          "                          from risk_hit_log\n" +
          "                          where createdAt >= addDays(now(), ${limitDay})\n" +
          "                            and kafkaTopic = 'RISK-ONLINE-RESULT-LOG'\n" +
//          "                            and userId != ''\n" +
//            "                            and deviceId != ''\n" +
          "                            and clientIp != ''\n" +
          "                            and clientIp != '0.0.0.0'\n" +
          "                            and userId = '${uid}')\n" +
          "  group by clientIp, deviceId,userId\n" +
          "order by activeCount desc,clientIp,userId,deviceId limit 500\n";


  // 用户最近n天活跃的top2设备
  public static String userActiveDeviceSql = "" +
          "select userId,deviceId,count(1) from risk_hit_log where createdAt between addDays(now(),${limitDay}) and" +
          " now() and deviceId!=''\n" +
          "      and kafkaTopic = 'RISK-ONLINE-RESULT-LOG'\n" +
          "      and userId in (\n" +
          "     ${sqlUidListStr} " +
          "        )\n" + "" +
          " group by userId,deviceId\n" +
          "order by count(1) desc limit 2 by userId";

  public static String synchroTrapDetailSql = "" +
          "select id,ccCount,createdAt  from synchrotrap_daily where createdAt>=addDays(now(),-180) \n" +
          "and concat(connectedLabel,'_',toString(createdAt)) global in (\n" +
          "  select concat(connectedLabel,'_',toString(createdAt)) from synchrotrap_daily where createdAt>=addDays(now(),-180) \n" +
          "  and id = '${uid}' \n" +
          "  and actionType = '${actionType}' order by toInt32OrZero(ccCount) desc limit 1\n" +
          ") limit 300;";

}
