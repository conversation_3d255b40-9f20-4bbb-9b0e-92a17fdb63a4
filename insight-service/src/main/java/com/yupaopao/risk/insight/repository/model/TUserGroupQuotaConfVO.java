package com.yupaopao.risk.insight.repository.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "t_user_group_quota_conf")
public class TUserGroupQuotaConfVO implements Serializable {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 指标编码
     */
    private String code;

    /**
     * 指标名称
     */
    private String name;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 指标大盘映射地址
     */
    @Column(name = "map_url")
    private String mapUrl;

    /**
     * 指标采集SQL
     */
    @Column(name = "sql_text")
    private String sqlText;

    /**
     * 指标映射字段
     */
    @Column(name = "field_map")
    private String fieldMap;

    private static final long serialVersionUID = 1L;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Integer getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取指标编码
     *
     * @return code - 指标编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置指标编码
     *
     * @param code 指标编码
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取指标名称
     *
     * @return name - 指标名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置指标名称
     *
     * @param name 指标名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新人
     *
     * @return update_by - 更新人
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置更新人
     *
     * @param updateBy 更新人
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取指标大盘映射地址
     *
     * @return map_url - 指标大盘映射地址
     */
    public String getMapUrl() {
        return mapUrl;
    }

    /**
     * 设置指标大盘映射地址
     *
     * @param mapUrl 指标大盘映射地址
     */
    public void setMapUrl(String mapUrl) {
        this.mapUrl = mapUrl;
    }

    /**
     * 获取指标采集SQL
     *
     * @return sql_text - 指标采集SQL
     */
    public String getSqlText() {
        return sqlText;
    }

    /**
     * 设置指标采集SQL
     *
     * @param sqlText 指标采集SQL
     */
    public void setSqlText(String sqlText) {
        this.sqlText = sqlText;
    }

    /**
     * 获取指标映射字段
     *
     * @return field_map - 指标映射字段
     */
    public String getFieldMap() {
        return fieldMap;
    }

    /**
     * 设置指标映射字段
     *
     * @param fieldMap 指标映射字段
     */
    public void setFieldMap(String fieldMap) {
        this.fieldMap = fieldMap;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", code=").append(code);
        sb.append(", name=").append(name);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", mapUrl=").append(mapUrl);
        sb.append(", sqlText=").append(sqlText);
        sb.append(", fieldMap=").append(fieldMap);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}