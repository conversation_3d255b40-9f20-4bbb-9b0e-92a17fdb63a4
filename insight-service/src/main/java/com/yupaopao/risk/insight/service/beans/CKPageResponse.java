package com.yupaopao.risk.insight.service.beans;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import ru.yandex.clickhouse.response.ClickHouseResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-24 13:41
 *
 ***/

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CKPageResponse {
    private List<Map<String, String>> response;
    private Long total;
    private Integer pages;


    public static PageResult initPageResult(CKPageResponse ckPageResponse, Integer currPage) {
        PageResult pageResult = new PageResult();
        pageResult.setDataList(new ArrayList());
        if (ckPageResponse == null) {
            pageResult.setTotal(0L);
            pageResult.setPages(0);
            return pageResult;
        }
        pageResult.setCurrentPage(currPage);
        pageResult.setTotal(ckPageResponse.getResponse() == null ? 0L : ckPageResponse.getTotal());
        pageResult.setPages(ckPageResponse.getResponse() == null ? 0 : ckPageResponse.getPages());
        return pageResult;
    }

    public static boolean isEmpty(CKPageResponse resp) {
        if (resp == null) {
            return true;
        }
        if (resp.getResponse() == null) {
            return true;
        }
        if (CollectionUtils.isEmpty(resp.getResponse())) {
            return true;
        }
        return false;
    }

}
