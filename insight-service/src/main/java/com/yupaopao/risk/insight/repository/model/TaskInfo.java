package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc TaskInfo
 * @date 2020/9/24
 */
@Getter
@Setter
@Table(name ="t_task_info")
public class TaskInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "name")
    private String name;

    @Column(name = "task_status")
    private int taskStatus;

    @Column(name = "parallelism")
    private int parallelism = 1;

    @Column(name = "ck_interval")
    private int interval = 60;

    @Column(name = "ck_min_pause")
    private int minPause = 60;

    @Column(name = "timeout")
    private int timeout = 600;

    @Column(name = "reader_id")
    private int readerId;

    @Column(name = "reader_parallelism")
    private int readerParallelism;

    @Column(name = "reader_properties")
    private String readerProperties;

    @Column(name = "reader_type")
    private String readerType;

    @Column(name = "cron")
    private String cron;

    @Column(name = "delay_percent")
    private Integer delayPercent;

    @Column(name = "job_id")
    private String jobId;

    @Column(name = "writer_id")
    private int writerId;

    @Column(name = "writer_parallelism")
    private int writerParallelism;

    @Column(name = "writer_properties")
    private String writerProperties;

    @Column(name = "writer_type")
    private String writerType;

    // 失败是否重试
    @Column(name = "is_flatten")
    private int isFlatten;

    @Column(name = "key_mapping")
    private String keyMapping;

    @Column(name = "task_desc")
    private String taskDesc;

    @Column(name = "relate_tags")
    private String relateTags;

    @Column(name = "business_type")
    private String businessType;

    @Column(name = "action_tags")
    private String actionTags;

    // 非数据库字段，用于多标签查询
    @Transient
    private List<String> actionTagsList;

    @Column(name = "duration")
    private String duration;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_by")
    private String createBy;
}
