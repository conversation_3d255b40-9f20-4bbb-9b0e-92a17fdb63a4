package com.yupaopao.risk.insight.beans;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-27 17:02
 * clickhouse 导入数据格式
 ***/

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CKImportParam {
    private DBTableInfo dbTableInfo; //表结构信息
    private List<String> rowJsonDataList; //每行数据以json格式保存在list中
}
