package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

/****
 * zengxiangcai
 * 2023/10/24 15:32
 ***/

@Getter
@Setter
@Table(name = "t_strategy_task_result")
public class StrategyAutoTaskResult extends BaseEntity {

    private Long id;

    @Column(name = "run_id")
    private String runId;

    @Column(name = "task_id")
    private Long taskId; //任务id

    @Column(name = "sample_batchid")
    private String sampleBatchId; //样本批次id

    @Column(name = "raw_result")
    private String rawResult;//分析模型原始条件表达式(特征组合)

    @Column(name = "result_img")
    private String resultImg;//结果决策树img

    @Column(name = "result_sql")
    private String resultSql;//提取的检验sql

    @Column(name = "flink_jobid")
    private String flinkJobId; // 启动的flinkJobId

    /***
     * 正常流程是：
     * 0--> 3 --> 2 or
     * 0--> 3 --> 1
     */
    @Column(name = "run_status")
    private Integer runStatus; // 0: 已经启动 1: 成功 2:失败 3: 模型运行中

    @Column(name = "run_params")
    private String runParams;


}
