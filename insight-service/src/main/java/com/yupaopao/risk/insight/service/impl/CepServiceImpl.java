package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.repository.mapper.CepPatternMapper;
import com.yupaopao.risk.insight.repository.mapper.CepRecordMapper;
import com.yupaopao.risk.insight.repository.mapper.CepResultMapper;
import com.yupaopao.risk.insight.repository.model.CepPattern;
import com.yupaopao.risk.insight.repository.model.CepRecord;
import com.yupaopao.risk.insight.repository.model.CepResult;
import com.yupaopao.risk.insight.service.CepService;
import com.yupaopao.risk.insight.service.HBaseService;
import com.yupaopao.risk.insight.service.beans.CepDisplayResult;
import com.yupaopao.risk.insight.service.beans.CepQueryParams;
import com.yupaopao.risk.insight.service.beans.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-29 17:16
 *
 ***/

@Slf4j
@Service
public class CepServiceImpl implements CepService {

    @Autowired
    private CepPatternMapper cepPatternMapper;
    @Autowired
    private CepResultMapper cepResultMapper;

    @Autowired
    private CepRecordMapper cepRecordMapper;

    @Autowired
    private HBaseService hBaseService;

    @Value("${cep.match.handlers:[]}")
    private String cepResultHandlers;

    @Override
    public PageResult<CepPattern> getPatterns(CepPattern queryParam, Integer pageSize, Integer currentPage) {
        String orderBy = " update_time desc ";
        Example search = new Example(CepPattern.class);
        Example.Criteria criteria = search.createCriteria();
        if (queryParam.getId() != null) {
            criteria.andEqualTo("id", queryParam.getId());
        } else {
            if (StringUtils.isNotEmpty(queryParam.getName())) {
                criteria.andCondition("name like '" + "%" + queryParam.getName() + "%'");
            }
            if (StringUtils.isNotEmpty(queryParam.getStatus())) {
                List<String> statusList = Arrays.asList(queryParam.getStatus().split(","));
                criteria.andIn("status", statusList);
            } else {
                criteria.andIn("status", Arrays.asList("ONLINE", "INIT"));
            }
            if (StringUtils.isNotEmpty(queryParam.getGroupByColumn())) {
                criteria.andEqualTo("groupByColumn", queryParam.getGroupByColumn());
            }
        }

        PageHelper.startPage(currentPage, pageSize, orderBy);
        List<CepPattern> list = cepPatternMapper.selectByExample(search);
        PageInfo page = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setDataList(list);
        return pageResult;
    }

    @Override
    public void savePattern(CepPattern pattern) {
        CepPattern dbPattern = null;
        if (pattern.getId() != null) {
            //select
            dbPattern = cepPatternMapper.selectByPrimaryKey(pattern);
        }
        if (dbPattern != null) {
            //update
            updatePattern(pattern);
        } else {
            // insert
            //检查名称是否存在
            Example checkSearch = new Example(CepPattern.class);
            Example.Criteria criteria = checkSearch.createCriteria();
            if (StringUtils.isNotEmpty(pattern.getName())) {
                criteria.andEqualTo("name", pattern.getName());
            }
            List<CepPattern> list = cepPatternMapper.selectByExample(checkSearch);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new RuntimeException("模式名称重复");
            }
            pattern.setCreateTime(new Date());
            pattern.setUpdateTime(new Date());
            if (StringUtils.isEmpty(pattern.getStatus())) {
                pattern.setStatus("INIT");
            }
            cepPatternMapper.insertSelective(pattern);
        }

    }


    @Override
    public PageResult<CepDisplayResult> getCepResult(CepQueryParams queryParam, Integer pageSize, Integer currentPage) {
        String orderBy = " create_time desc ";

        PageHelper.startPage(currentPage, pageSize, orderBy);

        Date now = InsightDateUtils.getCurrentDateZero();
        if (StringUtils.isEmpty(queryParam.getStartDate())) {
            queryParam.setStartDate(InsightDateUtils.getDateStr(DateUtils.addDays(now, -6), InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));
        } else {
            queryParam.setStartDate(queryParam.getStartDate() + " 00:00:00");
        }
        if (StringUtils.isEmpty(queryParam.getEndDate())) {
            queryParam.setEndDate(InsightDateUtils.getDateStr(now, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd) + " 23:59:59");
        } else {
            queryParam.setEndDate(queryParam.getEndDate() + " 23:59:59");
        }
        List<String> batchIdList = cepRecordMapper.getCepResultList(queryParam);
        if (CollectionUtils.isEmpty(batchIdList)) {
            PageInfo page = new PageInfo<>(batchIdList);
            PageResult pageResult = new PageResult();
            pageResult.setCurrentPage(page.getPageNum());
            pageResult.setTotal(page.getTotal());
            pageResult.setPages(page.getPages());
            pageResult.setDataList(new ArrayList());
            return pageResult;
        }

        List<CepResult> list = cepResultMapper.getResultByBatchId(batchIdList);


        //根据模式id获取模式名
        List<CepPattern> patternList = cepPatternMapper.selectAll();
        Map<Integer, String> patternIdNameMap = patternList.stream().collect(Collectors.toMap(CepPattern::getId,
                CepPattern::getName));
        List<CepDisplayResult> displayList = list.stream().map(elem -> {
            CepDisplayResult tempResult = new CepDisplayResult();
            BeanUtils.copyProperties(elem, tempResult, "patterName");
            tempResult.setPatternName(patternIdNameMap.get(elem.getPatternId()));
            return tempResult;
        }).collect(Collectors.toList());
        for (CepDisplayResult cepDisplayResult : displayList) {
            if ("mobileNo".equals(cepDisplayResult.getGroupByColumn())) {
                cepDisplayResult.setGroupByColumnValue("***");
            }
        }
        PageInfo page = new PageInfo<>(batchIdList);
        PageResult pageResult = new PageResult();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setDataList(displayList);
        return pageResult;
    }

    @Override
    public boolean updatePattern(CepPattern newPattern) {
        newPattern.setUpdateTime(new Date());
        int affectRow = cepPatternMapper.updateByPrimaryKeySelective(newPattern);
        return affectRow == 1;
    }

    @Override
    public JSONArray getResultHandlerConfig() {
        if (StringUtils.isEmpty(cepResultHandlers)) {
            return new JSONArray();
        }
        JSONArray arr = JSON.parseArray(cepResultHandlers);
        return arr;
    }

    @Override
    public boolean saveCepResult(CepResult cepResult) {
        Example search = new Example(CepResult.class);
        Example.Criteria criteria = search.createCriteria();
        criteria.andEqualTo("batchId", cepResult.getBatchId());
        criteria.andEqualTo("traceId", cepResult.getTraceId());
        List<CepResult> existList = cepResultMapper.selectByExample(search);
        if (CollectionUtils.isNotEmpty(existList)) {
            return false;
        }
        cepResultMapper.insertSelective(cepResult);
        return true;
    }

    @Override
    public boolean saveCepRecord(CepRecord cepRecord) {
        try {
            Example search = new Example(CepRecord.class);
            Example.Criteria criteria = search.createCriteria();
            criteria.andEqualTo("batchId", cepRecord.getBatchId());
            List<CepRecord> cepRecords = cepRecordMapper.selectByExample(search);
            if (CollectionUtils.isNotEmpty(cepRecords)) {
                for (CepRecord record : cepRecords) {
                    if (!StringUtils.isEmpty(cepRecord.getUserId()) && !cepRecord.getUserId().equals(record.getUserId())) {
                        cepRecord.setId(record.getId());
                        cepRecordMapper.updateByPrimaryKeySelective(cepRecord);
                        return true;
                    } else {
                        return false;
                    }
                }
                return false;
            }
            cepRecordMapper.insertSelective(cepRecord);

            return true;
        }catch (Exception e){
            //batch_id唯一性冲突忽略
            if(e!=null && e.getMessage()!=null && e.getMessage().contains("Duplicate entry")){
                return true;
            }
            log.error("save cep record error: " + JSON.toJSONString(cepRecord), e);
            return false;
        }
    }

    @Override
    public CepPattern getPattern(Integer patternId) {
        CepPattern search = new CepPattern();
        search.setId(patternId);
        return cepPatternMapper.selectByPrimaryKey(search);
    }

    @Override
    public Map<String, Object> getMatchResultDetail(String batchId) {
        Example search = new Example(CepResult.class);
        Example.Criteria criteria = search.createCriteria();
        criteria.andEqualTo("batchId", batchId);
        search.orderBy("createTime").asc();
        List<CepResult> dbList = cepResultMapper.selectByExample(search);
        if (CollectionUtils.isEmpty(dbList)) {
            return new HashMap<>();
        }
        Map<String, List<CepResult>> patternDetailMap = new LinkedHashMap<>();
        List<String> patternNameList = new ArrayList<>();
        Integer patternId = dbList.get(0).getPatternId();
        for (CepResult element : dbList) {
            JSONObject jsonObject = JSONObject.parseObject(element.getDetail());
            JSONObject data = jsonObject.getJSONObject("data");
            if (data.containsKey("Mobile")) {
                data.put("Mobile", "***");
            }
            if (jsonObject.containsKey("mobileNo")) {
                jsonObject.remove("mobileNo");
                jsonObject.put("mobileNo", "***");
            }
            if (data.containsKey("userMobile")) {
                JSONObject userMobile = data.getJSONObject("userMobile");
                userMobile.put("mobile", "***");
            }
            if (data.containsKey("toUserMobile")) {
                JSONObject toUserMobile = data.getJSONObject("toUserMobile");
                toUserMobile.put("mobile", "***");
            }
            element.setDetail(jsonObject.toJSONString());
            if ("mobileNo".equals(element.getGroupByColumn())) {
                element.setGroupByColumnValue("***");
            }
            List<CepResult> patternResult = patternDetailMap.get(element.getSRuleName());
            if (patternResult == null) {
                patternNameList.add(element.getSRuleName());
                patternResult = new ArrayList<>();
                patternDetailMap.put(element.getSRuleName(), patternResult);
            }
            patternResult.add(element);
        }
        //patternNameList按照模式内容排序
        CepPattern currentPattern = getPattern(patternId);
        patternNameList = orderPatternNames(patternNameList, currentPattern.getContent());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("patternNameList", patternNameList);
        resultMap.put("patternDetailMap", patternDetailMap);
        return resultMap;
    }

    private List<String> orderPatternNames(List<String> patternNames, String content) {
        List<String> resultList = new ArrayList<>();

        Map<String, Integer> indexMap = new TreeMap<>();
        for (String subPatternName : patternNames) {
            indexMap.put(subPatternName, content.indexOf("\"" + subPatternName + "\""));
        }
        List<Map.Entry<String, Integer>> list = new ArrayList<Map.Entry<String, Integer>>(indexMap.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<String, Integer>>() {
            //升序排序
            public int compare(Map.Entry<String, Integer> o1,
                               Map.Entry<String, Integer> o2) {
                return o1.getValue().compareTo(o2.getValue());
            }

        });
        for (Map.Entry<String, Integer> elem : list) {
            resultList.add(elem.getKey());
        }
        return resultList;
    }


}
