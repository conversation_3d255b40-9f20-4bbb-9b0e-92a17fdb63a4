package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-24 19:01
 * 工作台任务
 ***/
@Getter
@Setter
@Table(name = "t_workspace_task")
public class WorkspaceTaskInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="workspace_id")
    private Integer workspaceId; //工作台中任务对应的节点id

    private String code;//任务内容

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "type")
    private String type;
}
