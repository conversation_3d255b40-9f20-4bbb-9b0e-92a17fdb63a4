package com.yupaopao.risk.insight.flink.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.ReadContext;
import com.yupaopao.risk.insight.common.meta.JobParams;
import com.yupaopao.risk.insight.common.thread.InsightFlinkThreadFactory;
import com.yupaopao.risk.insight.config.FlinkConfig;
import com.yupaopao.risk.insight.config.JarConfiguration;
import com.yupaopao.risk.insight.constant.InsightConstants;
import com.yupaopao.risk.insight.flink.beans.RemoteSQLResponse;
import com.yupaopao.risk.insight.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class FlinkRestClient {

    @Autowired
    private OkHttpClient okHttpClient;
    @Autowired
    private FlinkConfig flinkConfig;
    @Autowired
    private JarConfiguration jarConfiguration;

    public static final String DEFAULT_RETURN_JOB_STATUS = "FAILED";

    private volatile static Map<String, String> jarIdMap = new HashMap<>();


    @Scheduled(fixedRate = 120000L, initialDelay = 0L)
    private void refreshJarMapping() {
        Request request = new Request.Builder()
                .url(flinkConfig.getWebUrl() + "/jars")
                .get().build();
        Response response = null;
        try {
            response = getLongCallHttpClient().newCall(request).execute();
            JSONObject json = JSONObject.parseObject(response.body().string());
            JSONArray jarArray = json.getJSONArray("files");
            Map<String, String> tmpJarIdMap = new HashMap<>();
            for (int i = 0; i < jarArray.size(); i++) {
                JSONObject item = jarArray.getJSONObject(i);
                if (tmpJarIdMap.containsKey(item.getString("name"))) {
                    continue;
                }
                tmpJarIdMap.put(item.getString("name"), item.getString("id"));
            }
            if (MapUtils.isNotEmpty(tmpJarIdMap)) {
                jarIdMap = tmpJarIdMap;
            }
        } catch (Exception e) {
            log.error("fetch all jars info occurs error: ", e);
        }
    }

//    private String templateSqlJarId;
//
//    @PostConstruct
//    public void init() {
//        templateSqlJarId = getJarId(flinkConfig.getTemplateSqlFile());
//        ConfigService.getAppConfig().addChangeListener(event -> {
//            if (event.isChanged("flink.cluster.templateSqlFile")) {
//                ConfigChange change = event.getChange("flink.cluster.templateSqlFile");
//                templateSqlJarId = getJarId(change.getNewValue());
//            }
//        });
//    }


    public String upload(String filePath) {


        File jarFile = new File(filePath);

        String fileName = jarFile.getName();
        log.info("the upload jar file is: {}", fileName);
        //create("application/x-java-archive", "");
        RequestBody body = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("jarfile", fileName, RequestBody.create(MediaType.parse("application/x-java-archive"), jarFile)
                )
                .build();
        Request request = new Request.Builder()
                .url(flinkConfig.getWebUrl() + "/jars/upload")
                .post(body)
                .build();
        try {
            Response response = getLongCallHttpClient().newCall(request).execute();
            String jsonResp = response.body().string();
            log.info("jar {} upload response: {}", fileName, jsonResp);

            JSONObject json = JSONObject.parseObject(jsonResp);
            String jobName = json.getString("filename");
            File uploadedJarFile = new File(jobName);
            jobName = uploadedJarFile.getName();

            log.info("uploaded file: {}", jobName);
            return jobName;
        } catch (Exception e) {
            log.info("uploaded failed, file: " + fileName, e);
        }
        return null;
    }


    public String run(String file) {
        Map<String, Object> jsonParams = new HashMap<>();
        jsonParams.put("entryClass", jarConfiguration.getMainclass());
        jsonParams.put("parallelism", null);
        jsonParams.put("programArgs", null);
        jsonParams.put("savepointPath", null);
        jsonParams.put("allowNonRestoredState", null);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(jsonParams));

        Request request = new Request.Builder()
                .url(flinkConfig.getWebUrl() + "/jars/" + file + "/run?entry-class=" + jarConfiguration.getMainclass())
                .post(body).build();
        try {
            Response response = getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("the run result is: {}", responseStr);
            JSONObject json = JSONObject.parseObject(responseStr);
            String jobId = json.getString("jobid");
            return jobId;
        } catch (Exception e) {
            log.error("run job error, jobName: " + file, e);
        }
        return null;
    }

    public String runByJarId(String jarId) {
        return this.runByJarIdWithArgs(jarId, null);
    }

    public String runByJarIdWithArgs(String jarId, String args) {
        return this.runByJarIdWithArgsAndUrl(jarId, args, flinkConfig.getWebUrl());
    }

    public String runNonCronByJarIdWithArgs(String jarId, String args) {
        return this.runByJarIdWithArgsAndUrl(jarId, args, flinkConfig.getNonCronWebUrl());
    }

    public String runByJarIdWithArgsAndUrl(String jarId, String args, String webUrl) {
        Map<String, Object> jsonParams = new HashMap<>();
        jsonParams.put("parallelism", null);
        jsonParams.put("programArgs", args);
        jsonParams.put("savepointPath", null);
        jsonParams.put("allowNonRestoredState", null);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(jsonParams));

        Request request = new Request.Builder()
                .url(webUrl + "/jars/" + jarId + "/run")
                .post(body).build();
        try {
            Response response = getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("the run result is: {}", responseStr);
            JSONObject json = JSONObject.parseObject(responseStr);
            return json.getString("jobid");
        } catch (Exception e) {
            log.error("run job error: ", e);
        }
        return null;
    }

    public String runByJarIdWithArgsAndSavepoint(String jarId, String args, String savepoint) {
        Map<String, Object> jsonParams = new HashMap<>();
        jsonParams.put("parallelism", null);
        jsonParams.put("programArgs", args);
        jsonParams.put("savepointPath", savepoint);
        jsonParams.put("allowNonRestoredState", null);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(jsonParams));

        Request request = new Request.Builder()
                .url(flinkConfig.getNonCronWebUrl() + "/jars/" + jarId + "/run")
                .post(body).build();
        try {
            Response response = getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("the run result is: {}", responseStr);
            JSONObject json = JSONObject.parseObject(responseStr);
            return json.getString("jobid");
        } catch (Exception e) {
            log.error("run job error: ", e);
        }
        return null;
    }

    public RemoteSQLResponse runSql(JobParams params) {
        String jarName;
        if ("stream".equals(params.getJobType().toLowerCase().trim())) {
            jarName = flinkConfig.getTemplateStreamSqlFile();
        } else {
            jarName = flinkConfig.getTemplateSqlFile();
        }
        return runSql(params, jarName);
    }

    public RemoteSQLResponse runSql(JobParams params, String jarName) {
        try {
            params.setBucketPerSplit(flinkConfig.getBucketSplitThread());
            Map<String, Object> jsonParams = new HashMap<>();
            //jsonParams.put("entryClass", "com.yupaopao.risk.insight.flink.job.sql.SqlMainJob");
            int parallelism = "stream".equals(StringUtils.isEmpty(params.getJobType())
                    ? "" : params.getJobType().toLowerCase().trim())
                    ? flinkConfig.getStreamSqlParallelism() : flinkConfig.getSqlParallelism();
            jsonParams.put("parallelism", parallelism);
            jsonParams.put("programArgs", URLEncoder.encode(JSON.toJSONString(params), "UTF-8"));
            jsonParams.put("savepointPath", null);
            jsonParams.put("allowNonRestoredState", null);
            String sendParam = JSON.toJSONString(jsonParams);
            log.info("sql run param: {}", sendParam);
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), sendParam);
            String templateJarId = getJarId(jarName);
            Request request = new Request.Builder()
                    .url(flinkConfig.getWebUrl() + "/jars/" + templateJarId + "/run")
                    .post(body).build();

            Response response = this.getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("the run sql response is: {}, randomJobId: {}", responseStr, params.getRandomJobId());
            JSONObject json = JSONObject.parseObject(responseStr);
            String jobId = json.getString("jobid");
            if (StringUtils.isEmpty(jobId)) {
                return RemoteSQLResponse.buildFailResponse(jobId, params.getRandomJobId(), responseStr);
            } else {
                return RemoteSQLResponse.buildSuccessResponse(jobId, params.getRandomJobId(), "job executing");
            }

        } catch (Exception e) {
            log.error("run sql error, params:  " + params.toMap(), e);
            return RemoteSQLResponse.buildFailResponse(null, params.getRandomJobId(), e.getMessage());
        }
    }

    public String getJobStatus(String jobId) {
        return getJobStatusWithUrl(jobId, flinkConfig.getWebUrl());
    }

    public String getNonCronJobStatus(String jobId) {
        return getJobStatusWithUrl(jobId, flinkConfig.getNonCronWebUrl());
    }

    /***
     * 目前系统对应的前端状态是：
     * COMPLETED
     * ABORTED
     * FAILED
     * INIT
     * RUNNING
     * flink 进去结果返回的是：
     * see {@link org.apache.flink.runtime.rest.messages.job.JobExecutionResultResponseBody}
     * @param jobId
     * @return
     */
    public String getJobStatusWithUrl(String jobId, String webUrl) {
        Request request = new Request.Builder()
                .url(webUrl + "/jobs/" + jobId + "/execution-result")
                .get().build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response == null) {
                return DEFAULT_RETURN_JOB_STATUS;
            }
            String jsonResponse = response.body().string();
            log.info("the job {} execution response is {}", jobId, jsonResponse);

            JSONObject json = JSONObject.parseObject(jsonResponse);
            ReadContext ctx = JsonPath.parse(json);
            String queueStatus = ctx.read("$.status.id");
            String applicationStatus = CommonUtil.read(json, "$.job\\-execution\\-result.application\\-status");

            return transStatus(queueStatus, applicationStatus);
        } catch (Exception e) {
            log.error("get job execution result error, jobId= " + jobId, e);
            return DEFAULT_RETURN_JOB_STATUS;
        }
    }

    public String getJobInfo(String jobId) {
        return getJobInfoWithUrl(jobId, flinkConfig.getWebUrl());
    }

    public String getNonCronJobInfo(String jobId) {
        return getJobInfoWithUrl(jobId, flinkConfig.getNonCronWebUrl());
    }

    public String getJobInfoWithUrl(String jobId, String webUrl) {
        Request request = new Request.Builder()
                .url(webUrl + "/jobs/" + jobId)
                .get().build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response == null || response.body() == null) {
                return "{}";
            }
            String jsonResponse = response.body().string();
            log.info("the job {} info response is {}", jobId, jsonResponse);
            return jsonResponse;
        } catch (Exception e) {
            log.error("get job result error, jobId= " + jobId, e);
            return "{}";
        }
    }

    public String getLatestCheckpointPath(String jobId) {
        Request request = new Request.Builder()
                .url(flinkConfig.getNonCronWebUrl() + "/jobs/" + jobId + "/checkpoints")
                .get().build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response == null || response.body() == null) {
                return "";
            }
            String jsonResponse = response.body().string();

            JSONObject json = JSONObject.parseObject(jsonResponse);
            return CommonUtil.read(json, "$.latest.completed.external_path", String.class);
        } catch (Exception e) {
            log.error("get job accumulators result error, jobId= " + jobId, e);
            return "";
        }
    }

    public Map<String, String> getJobAccumulators(String jobId) {
        return getJobAccumulatorsWithUrl(jobId, flinkConfig.getWebUrl());
    }

    public Map<String, String> getNonCronJobAccumulators(String jobId) {
        return getJobAccumulatorsWithUrl(jobId, flinkConfig.getNonCronWebUrl());
    }

    public Map<String, String> getJobAccumulatorsWithUrl(String jobId, String url) {
        Map<String, String> accumulators = new HashMap<>();
        Request request = new Request.Builder()
                .url(url + "/jobs/" + jobId + "/accumulators")
                .get().build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response == null || response.body() == null) {
                return accumulators;
            }
            String jsonResponse = response.body().string();

            JSONObject json = JSONObject.parseObject(jsonResponse);
            JSONArray accArray = json.getJSONArray("user-task-accumulators");
            for (int i = 0; i < accArray.size(); i++) {
                JSONObject item = accArray.getJSONObject(i);
                accumulators.put(item.getString("name"), item.getString("value"));
            }
        } catch (Exception e) {
            log.error("get job accumulators result error, jobId= " + jobId, e);
        }
        return accumulators;
    }


    private static String transStatus(String queueStatus, String applicationStatus) {
        String resultStatus = "";
        if (StringUtils.isNotEmpty(applicationStatus)) {
            switch (applicationStatus) {
                case "SUCCEEDED":
                    resultStatus = InsightConstants.FLINK_JOB_STATUS_COMPLETED;
                    break;
                case "FAILED":
                    resultStatus = InsightConstants.FLINK_JOB_STATUS_FAILED;
                    break;
                case "CANCELED":
                    resultStatus = InsightConstants.FLINK_JOB_STATUS_ABORTED;
                    break;
                case "UNKNOWN":
                    break;
            }
        }
        if (StringUtils.isEmpty(resultStatus) && StringUtils.isNotEmpty(queueStatus)) {
            if ("COMPLETED".equals(queueStatus)) {
                resultStatus = InsightConstants.FLINK_JOB_STATUS_COMPLETED;
            } else {
                resultStatus = InsightConstants.FLINK_JOB_STATUS_RUNNING;
            }
        }
        if (StringUtils.isEmpty(resultStatus)) {
            resultStatus = InsightConstants.FLINK_JOB_STATUS_FAILED;
        }
        return resultStatus;
    }


    public boolean abortJob(String jobId) {
        Request request = new Request.Builder()
                .url(flinkConfig.getWebUrl() + "/jobs/" + jobId + "?mode=cancel")
                .patch(RequestBody.create(MediaType.parse("application/json"), "{}")).build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            return response != null;
        } catch (Exception e) {
            log.error("abort job occurs error, jobId= " + jobId, e);
            return false;
        }
    }

    public boolean abortNonCronJob(String jobId) {
        Request request = new Request.Builder()
                .url(flinkConfig.getNonCronWebUrl() + "/jobs/" + jobId + "?mode=cancel")
                .patch(RequestBody.create(MediaType.parse("application/json"), "{}")).build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            return response != null;
        } catch (Exception e) {
            log.error("abort job occurs error, jobId= " + jobId, e);
            return false;
        }
    }

    public String getJarId(String jarName) {
        String webUrl = flinkConfig.getWebUrl();
        return getJarIdWithUrl(jarName, webUrl);
    }

    public String getNonCronJarId(String jarName) {
        String webUrl = flinkConfig.getNonCronWebUrl();
        return getJarIdWithUrl(jarName, webUrl);
    }

    public String getJarIdWithUrl(String jarName, String webUrl) {
        if (jarIdMap.containsKey(jarName)) {
            return jarIdMap.get(jarName);
        }
        Request request = new Request.Builder()
                .url(webUrl + "/jars")
                .get().build();
        Response response = null;
        try {
            response = getLongCallHttpClient().newCall(request).execute();
            JSONObject json = JSONObject.parseObject(response.body().string());
            return CommonUtil.read(json, "$.files[name = '" + jarName + "'][0].id");
        } catch (Exception e) {
            log.error("get jar id occurs fail: jarName: " + jarName, e);
            throw new IllegalArgumentException("cannot find the jar with name: " + jarName);
        }
    }

    public Integer getAvailableSlots() {
        String webUrl = flinkConfig.getWebUrl();
        Request request = new Request.Builder()
                .url(webUrl + "/overview")
                .get().build();
        Response response = null;
        try {
            response = getLongCallHttpClient().newCall(request).execute();
            JSONObject json = JSONObject.parseObject(response.body().string());
            return CommonUtil.read(json, "$.slots\\-available", Integer.class);
        } catch (Exception e) {
            log.error("get flink available slots occurs error, ", e);
            throw new IllegalArgumentException("get flink available slots occurs error");
        }
    }


    /***
     * noexception {"root-exception":null,"timestamp":null,"all-exceptions":[],"truncated":false}
     * @param jobId
     * @return
     */
    public String getExceptionInfo(String jobId) {
        Request request = new Request.Builder()
                .url(flinkConfig.getWebUrl() + "/jobs/" + jobId + "/exceptions")
                .get().build();
        try {
            Response response = okHttpClient.newCall(request).execute();
            String responseStr = response.body().string();
            JSONObject jsonObj = JSONObject.parseObject(responseStr);
            boolean noRootException = jsonObj.get("root-exception") == null;
            boolean noAllException = jsonObj.get("all-exceptions") == null;
            if (!noAllException && jsonObj.get("all-exceptions") instanceof List) {
                JSONArray errorArr = jsonObj.getJSONArray("all-exceptions");
                if (errorArr == null || errorArr.isEmpty()) {
                    noAllException = true;
                }
            }
            if (noRootException && noAllException) {
                //无异常信息
                return "no exception";
            }
            return responseStr;
        } catch (IOException e) {
            log.error("get job exception error: ", e);
            return e.getMessage();
        }
    }


    /***
     * fllink 任务提交耗时较长，才用长超时的链接
     * @return
     */
    private OkHttpClient getLongCallHttpClient() {

        return okHttpClient.newBuilder()
                .connectTimeout(60, TimeUnit.SECONDS) //连接超时
                .readTimeout(120, TimeUnit.SECONDS) //读取超时
                .writeTimeout(120, TimeUnit.SECONDS) //写超时
                .build();

    }


    /***
     * 自动升级流程：
     * 调用savepoint with cancel 记录返回的savepoint trigger
     * 查询savepoint的具体位置
     * 调用run with savepoint
     * @param jobId
     * @throws Exception
     */
    public String cancelJobWithSavepoint(String jobId) {
        try {
            String webUrl = flinkConfig.getWebUrl();

            Map<String, Object> sendParam = new HashMap<>();
            //sendParam.put("target-directory", "/flink/upgrade-release");
            sendParam.put("cancel-job", true);
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(sendParam));
            Request request = new Request.Builder()
                    .url(webUrl + "/jobs/" + jobId + "/savepoints")
                    .post(body).build();
            Response response = getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("cancelJobWithSavepoint response: {}", responseStr);
            if (StringUtils.isEmpty(responseStr)) {
                throw new RuntimeException("cancel job with empty response");
            }
            JSONObject jsonObj = JSONObject.parseObject(responseStr);
            String savepointTriggerId = jsonObj.getString("request-id");
            if (StringUtils.isEmpty(savepointTriggerId)) {
                throw new RuntimeException("cancel job without savepoint trigger, response: " + responseStr);
            }
            return savepointTriggerId;
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e1) {
            log.error("cancelJobWithSavepoint failed, jobId= " + jobId, e1);
            throw new RuntimeException("cancelJobWithSavepoint: " + e1.getMessage());
        }
    }

    /**
     * 根据triggerId获取某个job的savepoint具体位置
     *
     * @param jobId
     * @param savepointTriggerId
     * @return
     */
    public String getSavepointLocation(String jobId, String savepointTriggerId) {
        try {
            String webUrl = flinkConfig.getWebUrl();
            Request request = new Request.Builder()
                    .url(webUrl + "/jobs/" + jobId + "/savepoints/" + savepointTriggerId)
                    .get().build();
            Response response = getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("get savepoint location response: {}", responseStr);
            if (StringUtils.isEmpty(responseStr)) {
                throw new RuntimeException("cancel job with empty response");
            }
            JSONObject jsonObj = JSONObject.parseObject(responseStr);
            String savepointStatus = (String) JSONPath.read(responseStr, "$.status.id");
            if ("COMPLETED".equalsIgnoreCase(savepointStatus)) {
                String savepointLocation = (String) JSONPath.eval(responseStr, "$.operation.location");
                if (StringUtils.isEmpty(savepointLocation)) {
                    throw new RuntimeException("cannot get savepoint location, please check");
                }
                return savepointLocation;
            }
            return "";
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e1) {
            log.error("getSavepointLocation failed: jobId=" + jobId + ",savepointTrigger=" + savepointTriggerId, e1);
            throw new RuntimeException("getSavepointLocation failed: " + e1.getMessage());
        }
    }

    /**
     * 通过savepoint 重新运行job
     *
     * @param newJarFileName
     * @param savepointLocation
     * @return
     */
    public String runWithSavepoint(String newJarFileName, String savepointLocation) {
        try {
            String webUrl = flinkConfig.getWebUrl();
            Map<String, Object> jsonParams = new HashMap<>();
            jsonParams.put("entryClass", null);
            jsonParams.put("parallelism", null);
            jsonParams.put("programArgs", null);
            jsonParams.put("savepointPath", savepointLocation);
            jsonParams.put("allowNonRestoredState", null);
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(jsonParams));
            String jarId = getJarId(newJarFileName);
            if (StringUtils.isEmpty(jarId)) {
                throw new RuntimeException("getJarId error");
            }
            Request request = new Request.Builder()
                    .url(webUrl + "/jars/" + jarId + "/run")
                    .post(body).build();
            Response response = getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("runWithSavePoint response: {}", responseStr);
            JSONObject json = JSONObject.parseObject(responseStr);
            String jobId = json.getString("jobid");
            if (StringUtils.isEmpty(jobId)) {
                throw new RuntimeException("runWithSavepoint failed: response: " + responseStr);
            }
            return jobId;
        } catch (RuntimeException e1) {
            throw e1;
        } catch (Exception e) {
            log.error("runWithSavepoint: jarFileName=" + newJarFileName + ", savepointLocation=" + savepointLocation, e);
            throw new RuntimeException("runWithSavepoint: " + e.getMessage());
        }
    }

    public Map<String, String> getJobStateAndEndTime(String jobId, String webUrl) {
        Request request = new Request.Builder()
                .url(webUrl + "/jobs/" + jobId)
                .get().build();
        Response response = null;
        Map<String, String> res = new HashMap<>();
        res.put("jobState", "UNKNOWN");
        res.put("jobEndTime", "UNKNOWN");
        try {
            response = okHttpClient.newCall(request).execute();
            if (response == null) {
                return res;
            }
            String jsonResponse = response.body().string();
            log.info("the job {} info response is {}", jobId, jsonResponse);
            JSONObject json = JSONObject.parseObject(jsonResponse);
            if (!json.containsKey("state")) {
                //查询有异常
                return res;
            }
            String jobState = CommonUtil.read(json, "$.state", String.class); // RUNNING/FINISHED
            String jobEndTime = CommonUtil.read(json, "$.end-time", String.class); // -1 表示未完成
            res.put("jobState", jobState);
            res.put("jobEndTime", jobEndTime);
            return res;
        } catch (Exception e) {
            log.error("get job info error, jobId= " + jobId, e);
            return res;
        }
    }

    public static void main(String[] args) throws IOException {
        String webUrl = "https://test-flink.yupaopao.com";
        Request request = new Request.Builder()
                .url(webUrl + "/overview")
                .get().build();
        Response response = null;
        response = new OkHttpClient().newCall(request).execute();
        JSONObject json = JSONObject.parseObject(response.body().string());
        System.out.println(CommonUtil.read(json, "$.slots\\-available"));

//       String jsonResponse = " {\"status\":{\"id\":\"IN_PROGRESS\"}}";
//        jsonResponse = " {\"status\":{\"id\":\"COMPLETED\"},\"job-execution-result\":{\"id\":\"a14249831242a9e6a5ab4cf0b95b4be8\",\"application-status\":\"SUCCEEDED\",\"accumulator-results\":{},\"net-runtime\":5925}}";
//        JSONObject json = JSONObject.parseObject(jsonResponse);
//        ReadContext ctx = JsonPath.parse(json);
//        String queueStatus = CommonUtil.read(json,"$.status.id");
//        String applicationStatus = null;
//         applicationStatus = CommonUtil.read(json,"$.job\\-execution\\-result.application\\-status");;
////        if(json.get("job-execution-result")!=null){
////            applicationStatus = ctx.read("$.job-execution-result.application-status");
////        }
//        System.err.println(transStatus(queueStatus,applicationStatus)); ;
    }

}
