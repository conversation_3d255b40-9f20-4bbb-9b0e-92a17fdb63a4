package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/****
 * zengxiangcai
 * 2022/11/8 11:41
 ***/


@Getter
@Setter
@Table(name = "t_alert_rule")
public class AlertRule {
  /**
   * 主键
   */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "alert_name")
  private String alertName;

  @Column(name = "alert_period")
  private String alertPeriod;

  @Column(name = "alert_users")
  private String alertUsers;

  @Column(name = "alert_remarks")
  private String alertRemarks;

  @Column(name = "related_tasks")
  private String relatedTasks;

  private String conditions;

  private Integer disabled;

  @Column(name = "create_time")
  private Date createTime;

  @Column(name = "update_time")
  private Date updateTime;

  @Column(name = "create_by")
  private String createBy;

  @Column(name = "update_by")
  private String updateBy;

  @Column(name="max_times")
  private Integer maxTimes;// 一个告警一天最大发送次数
}
