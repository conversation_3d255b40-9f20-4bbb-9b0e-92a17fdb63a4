package com.yupaopao.risk.insight.service.beans;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-31 16:21
 *
 ***/

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CommunityQueryParam {
    private String name;
    private String communityLabel;
    private String originalLabel;
    private String vertexId;
    private Integer pageSize;
    private Integer currentPage;
    private String runDay;
    private List labelList;

}
