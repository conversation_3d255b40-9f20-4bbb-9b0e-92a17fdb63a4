package com.yupaopao.risk.insight.service.analysis;

import com.yupaopao.risk.insight.service.beans.BehaviorAnalysisData;
import com.yupaopao.risk.insight.service.beans.BehaviorAnalysisResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * Created by Avalon on 2020/8/11 20:08
 */
@Component
public class RealtimeOrderAnalysis implements AtomBehaviorAnalysis {


    @Autowired
    private EventRatioStrategy eventRatioStrategy;

    @Override
    public String behavior() {
        return ConstantsForBehavior.REALTIME_ORDER;
    }

    @Override
    public BehaviorAnalysisResult analyze(BehaviorAnalysisData data) {

        return eventRatioStrategy.hitDetection(data, Arrays.asList("realtime-order-accept-pre",
                "realtime-order-accept-post"), behavior());

    }

}
