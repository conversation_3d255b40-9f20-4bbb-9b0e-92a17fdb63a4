package com.yupaopao.risk.insight.repository.risk.dao;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.common.model.Factor;
import com.yupaopao.risk.insight.aspect.RiskDBSwitch;
import com.yupaopao.risk.insight.config.DBSource;
import com.yupaopao.risk.insight.repository.risk.bean.GrayList;
import com.yupaopao.risk.insight.repository.risk.bean.QueryGrayList;
import com.yupaopao.risk.insight.repository.risk.mapper.EventMapper;
import com.yupaopao.risk.insight.repository.risk.mapper.FactorMapper;
import com.yupaopao.risk.insight.repository.risk.mapper.GrayListMapper;
import com.yupaopao.risk.insight.repository.risk.mapper.RuleMapper;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Select;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RiskDBSwitch(DBSource.RISK)
@Repository
public class RiskRepository {

    @Autowired
    private FactorMapper complainMapper;


    @Autowired
    private GrayListMapper grayListMapper;

    @Autowired
    private EventMapper eventMapper;

    @Autowired
    private RuleMapper ruleMapper;

    public List<Long> queryFactorId(String groupKey) {
        return complainMapper.queryFactorIdByGroupKey(groupKey);
    }

    public List<String> queryFactorParams(List<Integer> factorIdList) {
        List<String> params = new ArrayList<>();
        for (Integer id : factorIdList) {
            Factor factor = complainMapper.selectByPrimaryKey((long) id);
            if (factor == null) {
                continue;
            }
            params.addAll(Arrays.stream(factor.getGroupKey().split(",")).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
        }
        return params;
    }

    public Map<String, String> queryCodeNameMapping() {
        return eventMapper.queryCodeName().stream().collect(Collectors.toMap(Event::getCode, Event::getName));
    }

    public List<GrayList> queryBlackList(QueryGrayList queryGrayList) {
        return grayListMapper.queryGrayList(queryGrayList);
    }

    public List<Event> queryEvents() {
        return eventMapper.selectAll();
    }

    public List<Event> selectEventsBy(Event event) {
        return eventMapper.select(event);
    }

    public List<String> getAllBlackUser(Long groupId) {
        return grayListMapper.getAllBlackUser(groupId);
    }

    public List<JSONObject> querySyncStraRelateTag() {
        return ruleMapper.querySyncStraRelateTag();
    }
    public List<JSONObject> queryAsyncStraRelateTag() {
        return ruleMapper.queryAsyncStraRelateTag();
    }

    public List<String> queryPortraitRelateAttr() {
        return ruleMapper.queryPortraitRelateAttr();
    }


    public List<JSONObject> queryTagRelateSyncStra() {
        return ruleMapper.queryTagRelateSyncStra();
    }

    public List<JSONObject> queryTagRelateAsyncStra() {
        return ruleMapper.queryTagRelateAsyncStra();
    }

    public List<JSONObject> queryTagPid() {
        return ruleMapper.queryTagPid();
    }

    public List<Integer> queryCustomTagPid() {
        return ruleMapper.queryCustomTagPid();
    }


}
