package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;


/****
 * zengxiangcai
 * 2023/10/24 15:08
 ***/

@Getter
@Setter
@Table(name = "t_strategy_task")
public class StrategyAutomationTask extends BaseEntity {


    @Column(name="task_name")
    private String taskName;

    private String situation; //场景

    @Column(name="exclude_features")
    private String excludeFeatures;

    @Column(name="latest_sample_batchid")
    private String latestSampleBatchId; //最新上传的样本batchId
}
