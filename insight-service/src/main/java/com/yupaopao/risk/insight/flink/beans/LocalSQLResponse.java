package com.yupaopao.risk.insight.flink.beans;

import com.yupaopao.risk.insight.constant.InsightConstants;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-11-20 10:42
 *
 ***/

@Getter
@Setter
public class LocalSQLResponse {
    private String code = "success";//success ,failed
    private List<String> columns;
    private List<List<Object>> rowList;
    private String message;

    public static LocalSQLResponse buildSuccessResponse(){
        LocalSQLResponse resp = new LocalSQLResponse();
        resp.setCode(InsightConstants.FLINK_SQL_SUCCESS);
        return resp;
    }
    public static LocalSQLResponse buildFailedResponse(){
        LocalSQLResponse resp = new LocalSQLResponse();
        resp.setCode(InsightConstants.FLINK_SQL_FAIL);
        return resp;
    }

    public boolean isFailed(){
        return InsightConstants.FLINK_SQL_FAIL.equalsIgnoreCase(code);
    }

}
