package com.yupaopao.risk.insight.service;

import com.yupaopao.risk.insight.service.beans.DecisionTreeAnalysisParam;

/****
 *
 * 决策树分析
 * zengxiangcai
 * 2023/10/23 01:31
 *
 ***/



public interface DecisionTreeAnalysisService {


    /***
     *
     * 编辑分析任务
     * @return
     *
     */
    boolean editAnalysisTask(DecisionTreeAnalysisParam analysisParam);

    /****
     * 添加样本
     * @return
     */
    boolean addSample();

    /****
     * 启动分析任务，每启动一次都会有不同的结果
     * @return
     */

    boolean startAnalysis();


    /***
     * 获取分析结果
     * @return
     */
    boolean getAnalysisResultList(String taskId);



}
