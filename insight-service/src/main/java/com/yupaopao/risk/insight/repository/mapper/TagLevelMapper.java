package com.yupaopao.risk.insight.repository.mapper;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.repository.model.TagInfo;
import com.yupaopao.risk.insight.repository.model.TagLevel;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
public interface TagLevelMapper extends Mapper<TagLevel> {

    @Select("select t1.id as id, t1.level_code as levelCode, t1.remarks as remarks,t2.name as name from t_tag_level t1 left join t_tag_info t2 on t1.tag_id=t2.id")
    List<JSONObject> getAllTagLevelInfo();

}
