package com.yupaopao.risk.insight.beans;

import com.yupaopao.risk.insight.service.beans.QueryTag;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class PortraitRow implements Serializable {

    private Map<String, List<QueryTag>> userPortraitList;
    private Map<String, List<QueryTag>> commonDevicePortraitList;
    private Map<String, List<QueryTag>> commonIPPortraitList;
}
