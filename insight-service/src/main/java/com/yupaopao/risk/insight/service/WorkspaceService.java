package com.yupaopao.risk.insight.service;

import com.yupaopao.risk.insight.repository.model.WorkspaceTaskInfo;
import com.yupaopao.risk.insight.repository.model.WorkspaceTaskLog;
import com.yupaopao.risk.insight.service.beans.WorkspaceLogParam;
import com.yupaopao.risk.insight.service.beans.WorkspaceNode;
import com.yupaopao.risk.insight.service.beans.WorkspaceRequestParam;

import java.util.List;
import java.util.Optional;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-24 16:28
 *
 ***/
public interface WorkspaceService {

    List<WorkspaceNode> getRootNode();

    Integer addNode(WorkspaceRequestParam param);

    boolean saveTWTaskInfo(WorkspaceRequestParam param, String userName);

    boolean changeNodeName(Integer id, String newName);

    boolean deleteNode(Integer id);

    Object startJobFromWorkspace(WorkspaceRequestParam param);

    String getSQLInfoByFileId(Integer fileId);

    Optional<WorkspaceTaskInfo> getLatestTask(Integer workspaceId);

    List<WorkspaceTaskLog> queryWorkspaceTaskLog(WorkspaceLogParam workspaceLogParam, Integer pageSize, Integer pageNumber);

}
