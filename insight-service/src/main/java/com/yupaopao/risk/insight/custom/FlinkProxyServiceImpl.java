package com.yupaopao.risk.insight.custom;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.api.FlinkProxyService;
import com.yupaopao.risk.insight.bean.HttpRequest;
import com.yupaopao.risk.insight.bean.HttpResponse;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@DubboService(interfaceClass = FlinkProxyService.class, timeout = 45000)
@Slf4j
public class FlinkProxyServiceImpl implements FlinkProxyService {

    @Resource
    private OkHttpClient okHttpClient;

    @Override
    public HttpResponse httpProxy(HttpRequest request) {
        Request okHttpRequest = convertToOkHttpRequest(request);
        try (Response response = okHttpClient.newCall(okHttpRequest).execute()) {
            return convertToHttpResponse(response);
        } catch (IOException e) {
            log.error("http proxy execute error", e);
            return null;
        }
    }

    private HttpResponse convertToHttpResponse(Response response) throws IOException {
        HttpResponse httpResponse = new HttpResponse();

        // 设置状态码和描述
        httpResponse.setStatusCode(response.code());
        httpResponse.setStatusMessage(response.message());

        // 设置响应头
        Map<String, List<String>> headers = new HashMap<>();
        for (String name : response.headers().names()) {
            headers.put(name, response.headers().values(name));
        }
        httpResponse.setHeaders(headers);

        // 设置响应体
        if (response.body() != null) {
            httpResponse.setBody(response.body().string());
        }

        // 设置协议版本
        httpResponse.setProtocol(response.protocol().toString());

        // 设置扩展属性（如果有需要）
        httpResponse.setAttributes(new HashMap<>());

        return httpResponse;
    }

    private Request convertToOkHttpRequest(HttpRequest httpRequest) {
        Request.Builder builder = new Request.Builder();

        // 设置URL
        String url = httpRequest.getUrl();
        if (httpRequest.getParameters() != null && !httpRequest.getParameters().isEmpty()) {
            url = appendParametersToUrl(url, httpRequest.getParameters());
        }
        builder.url(url);

        // 设置请求方法和请求体
        String method = httpRequest.getMethod().toUpperCase();
        RequestBody body = null;
        if (httpRequest.getBody() != null) {
            // 获取Content-Type
            MediaType mediaType = MediaType.parse(getContentType(httpRequest.getHeaders()));
            body = RequestBody.create(mediaType, httpRequest.getBody());
        } else if (requiresRequestBody(method)) {
            body = RequestBody.create(null, new byte[0]);
        }

        builder.method(method, body);

        // 设置请求头
        if (httpRequest.getHeaders() != null) {
            for (Map.Entry<String, List<String>> entry : httpRequest.getHeaders().entrySet()) {
                String headerName = entry.getKey();
                for (String headerValue : entry.getValue()) {
                    builder.addHeader(headerName, headerValue);
                }
            }
        }

        return builder.build();
    }


    private String appendParametersToUrl(String url, Map<String, List<String>> parameters) {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        for (Map.Entry<String, List<String>> entry : parameters.entrySet()) {
            String key = entry.getKey();
            for (String value : entry.getValue()) {
                urlBuilder.addQueryParameter(key, value);
            }
        }
        return urlBuilder.build().toString();
    }

    private String getContentType(Map<String, List<String>> headers) {
        if (headers != null) {
            List<String> contentTypeValues = headers.get("Content-Type");
            if (contentTypeValues != null && !contentTypeValues.isEmpty()) {
                return contentTypeValues.get(0);
            }
        }
        return "application/json";
    }

    private boolean requiresRequestBody(String method) {
        return method.equals("POST") || method.equals("PUT") || method.equals("PATCH") || method.equals("DELETE");
    }
}
