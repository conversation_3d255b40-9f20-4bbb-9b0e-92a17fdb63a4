package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.*;

@Table(name = "t_directory")
@Setter
@Getter
public class Directory implements Serializable {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 父节点ID
     */
    @Column(name = "parent_id")
    private Integer parentId;

    /**
     * 文件名
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 是否目录
     */
    @Column(name = "dir")
    private Integer dir;

    /**
     * 关联资源ID
     */
    @Column(name = "link_id")
    private Integer linkId;

    private static final long serialVersionUID = 1L;

    @Transient
    private List<Directory> children = new ArrayList<>();
}