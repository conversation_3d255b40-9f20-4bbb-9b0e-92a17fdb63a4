package com.yupaopao.risk.insight.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.yupaopao.risk.insight.common.property.connection.OSSProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;

/****
 * zengxiangcai
 * 2023/10/26 15:12
 ***/
public class OSSUtils {

    static OSSProperties config = OSSProperties.getProperties(PropertyType.OSS);

    static OSS ossClient = new OSSClientBuilder().build(config.getEndpoint(), config.getAccessKeyId(),
            config.getAccessKeySecret());


    public static InputStream downloadAnalysisResult(String fileName) {
        return getFileStream(getImgBasePath() + fileName);
    }
    private static InputStream getFileStream(String path){
        OSSObject ossObject = ossClient.getObject(config.getBucket(), path);
        return ossObject.getObjectContent();
    }


    private static String getImgBasePath() {
        String activeProfile = System.getenv("spring.profiles.active");
        if (StringUtils.isEmpty(activeProfile)) {
            activeProfile = System.getProperty("spring.profiles.active");
        }
        if (activeProfile != null && activeProfile.startsWith("pro")) {
            return "risk-flink/prod/imgs/";
        } else {
            return "risk-flink/test/imgs/";
        }
    }



}
