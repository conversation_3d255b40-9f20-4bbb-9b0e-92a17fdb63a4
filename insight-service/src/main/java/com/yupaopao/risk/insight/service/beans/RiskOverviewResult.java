package com.yupaopao.risk.insight.service.beans;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.*;

/****
 * zengxiangcai
 * 2022/7/28 12:10
 ***/

@Getter
@Setter
@AllArgsConstructor
public class RiskOverviewResult {

  public RiskOverviewResult(){
    this.timeInfo = new HashMap<>();
    this.clusterInfo = new HashMap<>();
    this.thirdModelInfo = new HashMap<>();
    this.userRiskTags = new HashSet<>(1);
    this.relateDeviceRiskTag = new HashMap<>(1);
  }

  private Map<String,Object> timeInfo;

  private Map<String,Object> clusterInfo;

  private Map<String,Object> thirdModelInfo;

  private Set<String> userRiskTags;

  private Map<String,Set<String>> relateDeviceRiskTag;


}
