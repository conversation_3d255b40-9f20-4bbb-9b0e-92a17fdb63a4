package com.yupaopao.risk.insight.service.debug;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.service.debug.model.*;
import com.yupaopao.risk.insight.service.debug.websocket.DebugWebSocketHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 本地执行引擎
 * 不依赖Flink，在本地顺序执行各个调试节点
 */
@Slf4j
@Component
public class LocalExecutionEngine {

    @Autowired
    private DebugWebSocketHandler webSocketHandler;

    // 存储活跃的调试会话
    private final Map<String, DebugSession> activeSessions = new ConcurrentHashMap<>();

    /**
     * 创建调试会话
     */
    public DebugSession createDebugSession(DebugConfig config, List<DebugNode> topology, List<Map<String, Object>> inputData) {
        String sessionId = generateSessionId(config.getTaskId());

        DebugSession session = DebugSession.builder()
                .sessionId(sessionId)
                .config(config)
                .inputData(inputData)
                .executionTopology(topology)
                .status(DebugSessionStatus.CREATED)
                .createTime(System.currentTimeMillis())
                .nodeResults(new ConcurrentHashMap<>())
                .build();

        activeSessions.put(sessionId, session);

        log.info("创建调试会话成功: sessionId={}, nodes={}", sessionId, topology.size());
        return session;
    }

    /**
     * 执行调试任务
     */
    public CompletableFuture<DebugExecutionResult> executeDebugTask(String sessionId) {
        DebugSession session = activeSessions.get(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("调试会话不存在: " + sessionId);
        }

        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("开始执行调试任务: sessionId={}", sessionId);

                // 更新会话状态
                session.setStatus(DebugSessionStatus.RUNNING);
                session.setStartTime(System.currentTimeMillis());
                pushSessionUpdate(sessionId, "EXECUTION_STARTED", session);

                // 执行所有节点
                DebugExecutionResult result = executeNodes(session);

                // 更新会话状态
                session.setStatus(result.isSuccess() ? DebugSessionStatus.COMPLETED : DebugSessionStatus.FAILED);
                session.setEndTime(System.currentTimeMillis());
                session.setExecutionResult(result);

                pushSessionUpdate(sessionId, "EXECUTION_COMPLETED", result);

                log.info("调试任务执行完成: sessionId={}, status={}", sessionId, result.getStatus());
                return result;

            } catch (Exception e) {
                log.error("调试任务执行失败: sessionId={}", sessionId, e);

                session.setStatus(DebugSessionStatus.FAILED);
                session.setEndTime(System.currentTimeMillis());

                DebugExecutionResult errorResult = DebugExecutionResult.builder()
                        .sessionId(sessionId)
                        .status(DebugExecutionStatus.FAILED)
                        .errorMessage(e.getMessage())
                        .finishTime(System.currentTimeMillis())
                        .build();

                session.setExecutionResult(errorResult);
                pushSessionUpdate(sessionId, "EXECUTION_FAILED", errorResult);

                return errorResult;
            }
        });
    }

    /**
     * 执行所有节点
     */
    private DebugExecutionResult executeNodes(DebugSession session) {
        String sessionId = session.getSessionId();
        List<DebugNode> topology = session.getExecutionTopology();
        List<Map<String, Object>> currentData = new ArrayList<>(session.getInputData());
        List<DebugNodeResult> nodeResults = new ArrayList<>();

        long executionStartTime = System.currentTimeMillis();

        try {
            // 逐个执行节点
            for (DebugNode node : topology) {
                if (!node.isEnabled()) {
                    log.info("跳过禁用的节点: {}", node.getNodeId());
                    continue;
                }

                // 检查断点
                if (hasBreakpoint(session, node.getNodeId())) {
                    handleBreakpoint(sessionId, node, currentData);
                }

                // 执行单个节点
                DebugNodeResult nodeResult = executeNode(session, node, currentData);
                nodeResults.add(nodeResult);
                session.addNodeResult(node.getNodeId(), nodeResult);

                // 推送节点状态更新
                DebugWebSocketHandler.pushNodeStatusUpdate(sessionId, nodeResult);

                // 如果节点执行失败，决定是否继续
                if (nodeResult.getStatus() == DebugNodeStatus.FAILED) {
                    // 对于调试模式，可以选择继续执行其他节点
                    log.warn("节点执行失败，继续执行下一个节点: {}", node.getNodeId());

                    // 更新当前数据为空列表，避免后续节点崩溃
                    currentData = new ArrayList<>();
                } else {
                    // 更新当前数据为节点的输出
                    currentData = nodeResult.getOutputSample() != null ?
                            new ArrayList<>(nodeResult.getOutputSample()) : currentData;
                }

                // 推送执行进度
                pushExecutionProgress(sessionId, nodeResults.size(), topology.size());
            }

            long executionEndTime = System.currentTimeMillis();

            // 构建最终结果
            return DebugExecutionResult.builder()
                    .sessionId(sessionId)
                    .status(hasFailedNodes(nodeResults) ? DebugExecutionStatus.FAILED : DebugExecutionStatus.COMPLETED)
                    .startTime(executionStartTime)
                    .finishTime(executionEndTime)
                    .totalDuration(executionEndTime - executionStartTime)
                    .nodeResults(nodeResults)
                    .finalOutput(currentData)
                    .performanceStats(buildPerformanceStats(nodeResults))
                    .summary(buildExecutionSummary(nodeResults))
                    .build();

        } catch (Exception e) {
            log.error("执行节点过程中发生异常: sessionId={}", sessionId, e);
            throw e;
        }
    }

    /**
     * 执行单个节点
     */
    private DebugNodeResult executeNode(DebugSession session, DebugNode node, List<Map<String, Object>> inputData) {
        String sessionId = session.getSessionId();
        String nodeId = node.getNodeId();
        long startTime = System.currentTimeMillis();

        log.info("开始执行节点: sessionId={}, nodeId={}, inputCount={}",
                sessionId, nodeId, inputData.size());

        DebugNodeResult.DebugNodeResultBuilder resultBuilder = DebugNodeResult.builder()
                .nodeId(nodeId)
                .nodeName(node.getNodeName())
                .nodeType(node.getNodeType())
                .startTime(startTime)
                .hasBreakpoint(hasBreakpoint(session, nodeId))
                .processedCount(0L)
                .successCount(0L)
                .failedCount(0L);

        try {
            // 推送节点开始状态
            pushNodeStatusUpdate(sessionId, resultBuilder.status(DebugNodeStatus.RUNNING).build());

            // 收集输入样本
            List<Map<String, Object>> inputSample = collectSample(inputData, 10);
            resultBuilder.inputSample(inputSample);

            // 根据节点类型执行不同的处理逻辑
            List<Map<String, Object>> outputData = executeNodeLogic(node, inputData);

            // 收集输出样本
            List<Map<String, Object>> outputSample = collectSample(outputData, 10);
            resultBuilder.outputSample(outputSample);

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 构建成功的结果
            DebugNodeResult result = resultBuilder
                    .status(DebugNodeStatus.COMPLETED)
                    .processedCount((long) inputData.size())
                    .successCount((long) outputData.size())
                    .failedCount(0L)
                    .endTime(endTime)
                    .duration(duration)
                    .metrics(buildNodeMetrics(inputData.size(), outputData.size(), duration))
                    .build();

            log.info("节点执行成功: nodeId={}, input={}, output={}, duration={}ms",
                    nodeId, inputData.size(), outputData.size(), duration);

            return result;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.error("节点执行失败: nodeId={}", nodeId, e);

            // 构建失败的结果
            return resultBuilder
                    .status(DebugNodeStatus.FAILED)
                    .processedCount((long) inputData.size())
                    .successCount(0L)
                    .failedCount((long) inputData.size())
                    .endTime(endTime)
                    .duration(duration)
                    .errorMessage(e.getMessage())
                    .errorStackTrace(getStackTrace(e))
                    .build();
        }
    }

    /**
     * 根据节点类型执行具体的处理逻辑
     */
    private List<Map<String, Object>> executeNodeLogic(DebugNode node, List<Map<String, Object>> inputData) {
        String nodeType = node.getNodeType().toLowerCase();
        Map<String, Object> config = node.getNodeConfig();

        switch (nodeType) {
            case "reader":
                return executeReaderNode(config, inputData);
            case "writer":
                return executeWriterNode(config, inputData);
            case "mapping":
                return executeMappingNode(config, inputData);
            case "groovy":
                return executeGroovyNode(config, inputData);
            case "filter":
                return executeFilterNode(config, inputData);
            case "switch":
                return executeSwitchNode(config, inputData);
            case "idempotent":
                return executeIdempotentNode(config, inputData);
            case "enrichment":
                return executeEnrichmentNode(config, inputData);
            default:
                log.warn("未支持的节点类型: {}, 直接返回输入数据", nodeType);
                return new ArrayList<>(inputData);
        }
    }

    /**
     * 执行字段映射节点
     */
    private List<Map<String, Object>> executeMappingNode(Map<String, Object> config, List<Map<String, Object>> inputData) {
        Object columnConfig = config.get("column");
        if (columnConfig == null) {
            return new ArrayList<>(inputData);
        }

        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> input : inputData) {
            Map<String, Object> output = new HashMap<>();

            if (columnConfig instanceof Map) {
                Map<String, Object> mappings = (Map<String, Object>) columnConfig;

                for (Map.Entry<String, Object> entry : mappings.entrySet()) {
                    String targetField = entry.getKey();
                    Object sourceConfig = entry.getValue();

                    if (sourceConfig instanceof String) {
                        // 简单字段映射
                        String sourceField = (String) sourceConfig;
                        output.put(targetField, input.get(sourceField));
                    } else if (sourceConfig instanceof Map) {
                        // 复杂字段映射
                        Map<String, Object> fieldConfig = (Map<String, Object>) sourceConfig;
                        String sourceField = (String) fieldConfig.get("name");
                        Object defaultValue = fieldConfig.get("default");

                        Object value = input.get(sourceField);
                        if (value == null && defaultValue != null) {
                            value = defaultValue;
                        }
                        output.put(targetField, value);
                    }
                }
            }

            result.add(output);
        }

        return result;
    }

    /**
     * 执行Groovy脚本节点
     */
    private List<Map<String, Object>> executeGroovyNode(Map<String, Object> config, List<Map<String, Object>> inputData) {
        String groovyScript = (String) config.get("groovy");

        // 简化实现：在真实环境中这里应该集成GroovyExecutor
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> input : inputData) {
            Map<String, Object> output = new HashMap<>(input);

            // 模拟Groovy脚本执行
            output.put("debug_groovy_processed", true);
            output.put("debug_groovy_timestamp", System.currentTimeMillis());
            output.put("debug_groovy_script_length", groovyScript != null ? groovyScript.length() : 0);

            result.add(output);
        }

        return result;
    }

    /**
     * 执行过滤节点
     */
    private List<Map<String, Object>> executeFilterNode(Map<String, Object> config, List<Map<String, Object>> inputData) {
        String condition = (String) config.get("condition");

        // 简化实现：在真实环境中这里应该使用表达式引擎
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> input : inputData) {
            // 简单的过滤逻辑示例
            boolean pass = evaluateFilterCondition(input, condition);

            if (pass) {
                result.add(new HashMap<>(input));
            }
        }

        return result;
    }

    /**
     * 执行条件分支节点
     */
    private List<Map<String, Object>> executeSwitchNode(Map<String, Object> config, List<Map<String, Object>> inputData) {
        // Switch节点的处理比较复杂，这里提供简化实现
        // 在真实环境中需要根据条件执行不同的分支逻辑
        return new ArrayList<>(inputData);
    }

    /**
     * 执行幂等处理节点
     */
    private List<Map<String, Object>> executeIdempotentNode(Map<String, Object> config, List<Map<String, Object>> inputData) {
        String idempotentKey = (String) config.get("idempotentKey");

        // 简化实现：基于指定字段去重
        if (idempotentKey == null) {
            return new ArrayList<>(inputData);
        }

        Set<Object> seenKeys = new HashSet<>();
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> input : inputData) {
            Object keyValue = input.get(idempotentKey);
            if (keyValue != null && !seenKeys.contains(keyValue)) {
                seenKeys.add(keyValue);
                result.add(new HashMap<>(input));
            }
        }

        return result;
    }

    /**
     * 执行数据增强节点
     */
    private List<Map<String, Object>> executeEnrichmentNode(Map<String, Object> config, List<Map<String, Object>> inputData) {
        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> input : inputData) {
            Map<String, Object> output = new HashMap<>(input);

            // 添加调试信息
            output.put("debug_enriched", true);
            output.put("debug_enriched_time", System.currentTimeMillis());

            result.add(output);
        }

        return result;
    }

    /**
     * 简化的过滤条件评估
     */
    private boolean evaluateFilterCondition(Map<String, Object> input, String condition) {
        // 简化实现：总是返回true
        // 在真实环境中应该使用表达式引擎如SPEL或MVEL
        return true;
    }

    /**
     * 检查是否有断点
     */
    private boolean hasBreakpoint(DebugSession session, String nodeId) {
        return session.getConfig().isEnableBreakpoints() &&
               session.getConfig().getBreakpoints() != null &&
               session.getConfig().getBreakpoints().contains(nodeId);
    }

    /**
     * 处理断点
     */
    private void handleBreakpoint(String sessionId, DebugNode node, List<Map<String, Object>> currentData) {
        log.info("命中断点: sessionId={}, nodeId={}", sessionId, node.getNodeId());

        Map<String, Object> breakpointData = new HashMap<>();
        breakpointData.put("nodeId", node.getNodeId());
        breakpointData.put("nodeName", node.getNodeName());
        breakpointData.put("hitTime", System.currentTimeMillis());
        breakpointData.put("currentDataSample", collectSample(currentData, 5));

        DebugWebSocketHandler.pushBreakpointHit(sessionId, breakpointData);
    }

    /**
     * 停止调试会话
     */
    public void stopDebugSession(String sessionId) {
        DebugSession session = activeSessions.remove(sessionId);
        if (session != null) {
            session.setStatus(DebugSessionStatus.STOPPED);
            session.setEndTime(System.currentTimeMillis());

            log.info("停止调试会话: sessionId={}", sessionId);
            pushSessionUpdate(sessionId, "SESSION_DESTROYED", null);
        }
    }

    /**
     * 获取调试会话
     */
    public DebugSession getDebugSession(String sessionId) {
        return activeSessions.get(sessionId);
    }

    /**
     * 获取所有活跃会话
     */
    public List<DebugSession> getActiveSessions() {
        return new ArrayList<>(activeSessions.values());
    }

    // ========== 辅助方法 ==========

    private String generateSessionId(Integer taskId) {
        return "debug_" + taskId + "_" + System.currentTimeMillis();
    }

    private void pushSessionUpdate(String sessionId, String eventType, Object data) {
        Map<String, Object> updateData = new HashMap<>();
        updateData.put("eventType", eventType);
        updateData.put("sessionId", sessionId);
        updateData.put("timestamp", System.currentTimeMillis());
        if (data != null) {
            updateData.put("data", data);
        }

        DebugWebSocketHandler.pushSessionUpdate(sessionId, updateData);
    }

    private void pushNodeStatusUpdate(String sessionId, DebugNodeResult nodeResult) {
        webSocketHandler.pushNodeStatusUpdate(sessionId, nodeResult);
    }

    private void pushExecutionProgress(String sessionId, int completedNodes, int totalNodes) {
        Map<String, Object> progress = new HashMap<>();
        progress.put("completedNodes", completedNodes);
        progress.put("totalNodes", totalNodes);
        progress.put("progressPercentage", totalNodes > 0 ? (double) completedNodes / totalNodes * 100 : 0);
        progress.put("timestamp", System.currentTimeMillis());

        DebugWebSocketHandler.pushExecutionProgress(sessionId, progress);
    }

    private List<Map<String, Object>> collectSample(List<Map<String, Object>> data, int maxSize) {
        if (data == null || data.isEmpty()) {
            return new ArrayList<>();
        }

        return data.size() <= maxSize ?
                new ArrayList<>(data) :
                new ArrayList<>(data.subList(0, maxSize));
    }

    private Map<String, Object> buildNodeMetrics(int inputCount, int outputCount, long duration) {
        double throughput = duration > 0 ? (double) inputCount / duration * 1000 : 0;

        Map<String, Object> metrics = new HashMap<>();
        metrics.put("inputCount", inputCount);
        metrics.put("outputCount", outputCount);
        metrics.put("duration", duration);
        metrics.put("throughput", throughput);
        metrics.put("dataReduction", inputCount > 0 ? (double) (inputCount - outputCount) / inputCount * 100 : 0);

        return metrics;
    }

    private boolean hasFailedNodes(List<DebugNodeResult> nodeResults) {
        return nodeResults.stream().anyMatch(result -> result.getStatus() == DebugNodeStatus.FAILED);
    }

    private Map<String, Object> buildPerformanceStats(List<DebugNodeResult> nodeResults) {
        long totalDuration = nodeResults.stream().mapToLong(r -> r.getDuration() != null ? r.getDuration() : 0).sum();
        double avgDuration = nodeResults.isEmpty() ? 0 : (double) totalDuration / nodeResults.size();

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalNodes", nodeResults.size());
        stats.put("totalDuration", totalDuration);
        stats.put("averageDuration", avgDuration);
        stats.put("successNodes", nodeResults.stream().filter(r -> r.getStatus() == DebugNodeStatus.COMPLETED).count());
        stats.put("failedNodes", nodeResults.stream().filter(r -> r.getStatus() == DebugNodeStatus.FAILED).count());

        return stats;
    }

    private DebugExecutionSummary buildExecutionSummary(List<DebugNodeResult> nodeResults) {
        DebugExecutionSummary summary = new DebugExecutionSummary();
        summary.setTotalNodes(nodeResults.size());
        summary.setSuccessNodes((int) nodeResults.stream().filter(r -> r.getStatus() == DebugNodeStatus.COMPLETED).count());
        summary.setFailedNodes((int) nodeResults.stream().filter(r -> r.getStatus() == DebugNodeStatus.FAILED).count());
        summary.setTotalProcessedRecords(nodeResults.stream().mapToLong(r -> r.getProcessedCount() != null ? r.getProcessedCount() : 0).sum());
        return summary;
    }

    private String getStackTrace(Exception e) {
        java.io.StringWriter sw = new java.io.StringWriter();
        e.printStackTrace(new java.io.PrintWriter(sw));
        return sw.toString();
    }

    /**
     * 执行Reader节点
     * 在调试模式下，Reader节点直接返回用户提供的输入数据
     */
    private List<Map<String, Object>> executeReaderNode(Map<String, Object> config, List<Map<String, Object>> inputData) {
        log.info("执行Reader节点，类型: {}, 输入数据: {}条记录",
                config.get("readerType"), inputData.size());

        // 在调试模式下，Reader节点直接返回调试配置中提供的输入数据
        // 这样可以避免实际连接到Kafka、ClickHouse等外部数据源

        List<Map<String, Object>> result = new ArrayList<>();

        // 为每条数据添加一些Reader节点的元信息
        for (Map<String, Object> record : inputData) {
            Map<String, Object> enrichedRecord = new HashMap<>(record);
            enrichedRecord.put("_debug_reader_type", config.get("readerType"));
            enrichedRecord.put("_debug_reader_timestamp", System.currentTimeMillis());
            enrichedRecord.put("_debug_reader_id", config.get("readerId"));
            result.add(enrichedRecord);
        }

        log.info("Reader节点执行完成，输出: {}条记录", result.size());
        return result;
    }

    /**
     * 执行Writer节点
     * 在调试模式下，Writer节点只是模拟写入，记录将要写入的数据
     */
    private List<Map<String, Object>> executeWriterNode(Map<String, Object> config, List<Map<String, Object>> inputData) {
        log.info("执行Writer节点，类型: {}, 输入数据: {}条记录",
                config.get("writerType"), inputData.size());

        // 在调试模式下，Writer节点不会真正写入数据
        // 只是模拟写入过程，记录将要写入的内容

        List<Map<String, Object>> result = new ArrayList<>();

        for (Map<String, Object> record : inputData) {
            Map<String, Object> writeResult = new HashMap<>();
            writeResult.put("_debug_write_status", "simulated");
            writeResult.put("_debug_writer_type", config.get("writerType"));
            writeResult.put("_debug_writer_id", config.get("writerId"));
            writeResult.put("_debug_write_timestamp", System.currentTimeMillis());
            writeResult.put("_debug_original_data", record);

            // 模拟写入的数据大小
            String serialized = JSON.toJSONString(record);
            writeResult.put("_debug_data_size", serialized.length());

            result.add(writeResult);
        }

        log.info("Writer节点执行完成，模拟写入: {}条记录", result.size());
        return result;
    }
}
