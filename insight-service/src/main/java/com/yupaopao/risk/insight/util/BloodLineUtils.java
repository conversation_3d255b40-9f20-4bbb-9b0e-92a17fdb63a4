package com.yupaopao.risk.insight.util;

import com.yupaopao.risk.insight.enums.BloodTypeEnum;

/**
 * Created by Avalon on 2024/9/11 16:45
 **/
public class BloodLineUtils {

    public static BloodTypeEnum convertTaskSourceType(String taskSourceType) {
        switch (taskSourceType) {
            case "Kafka":
                return BloodTypeEnum.KAFKA;
            case "JDBC":
                return BloodTypeEnum.MYSQL;
            case "HBase":
                return BloodTypeEnum.HBASE;
            case "ODPS":
                return BloodTypeEnum.HIVE;
            case "ClickHouse":
                return BloodTypeEnum.TABLE;
            default:
                return null;
        }
    }

}
