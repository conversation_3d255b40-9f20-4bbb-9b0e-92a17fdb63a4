package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.TableInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-05 18:53
 *
 ***/
public interface TableMapper extends Mapper<TableInfo> {

    @Select("select name from t_table where name LIKE CONCAT('%',#{name},'%')")
    List<String> queryTables(@Param("name") String name);

    @Select("SELECT name FROM t_table\n" +
            "WHERE type = 'TEMP'\n" +
            "AND update_time < (NOW() - INTERVAL 60 DAY)")
    List<String> listTempTables();

    @Delete("delete from t_table where name = #{name}")
    void dropTable(@Param("name") String name);

}
