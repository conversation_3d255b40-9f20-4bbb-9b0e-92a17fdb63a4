package com.yupaopao.risk.insight.service.impl;


import com.alibaba.fastjson.JSON;
import com.yupaopao.arthur.api.dto.BlacklistDto;
import com.yupaopao.arthur.api.service.ArthurBlacklistService;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.service.ArthurGatewayBlackListService;
import com.yupaopao.risk.insight.service.beans.GatewayBlackListDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ArthurGatewayBlackListServiceImpl implements ArthurGatewayBlackListService {

    @Value("${insight.arthur.tenant:risk}")
    private String tenant;

    @DubboReference
    private ArthurBlacklistService arthurBlacklistService;


    /****
     * 添加黑名单
     * msgCode相关 @link （https://ts.yupaopao.com/workspaces/5f62e99e6ae6d90011ed69e2/docs/5fec2cf162fdcc000125a951）
     * @param listDto
     * @return
     */
    public boolean addBlackListBatch(List<GatewayBlackListDTO> listDto) {
        List<BlacklistDto> insertList =
                listDto.stream().filter(elem -> elem.isValidateDto()).map(elem -> elem.transToBlackListDto()).collect(Collectors.toList());
        if (insertList.size() != listDto.size()) {
            log.error("invalid input data, please check");
            throw new RuntimeException("invalid input data, please check");
        }
        try {
            Response<Void> response = arthurBlacklistService.addBlacklist(insertList, tenant);
            System.err.println(response);
            log.info("response from arthur: {}", response);
            if (response != null && response.isSuccess()) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("add blacklist to arthur error,params: " + JSON.toJSONString(listDto), e);
            e.printStackTrace();
        }
        return false;
    }

    public boolean deleteBlackList(GatewayBlackListDTO dto) {
        try {
            Response<Void> response = arthurBlacklistService.deleteBlacklist(dto.getSecurityScene(),
                    dto.getGatewayType(), dto.getBlacklistType(), dto.getBlacklistValue());
            log.info("response from arthur: {}", response);
            return true;
        } catch (Exception e) {
            log.error("delete blacklist according to arthur error, params=" + dto, e);
            return false;
        }
    }
}
