package com.yupaopao.risk.insight.service.beans;

import lombok.Getter;
import lombok.Setter;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/****
 * zengxiangcai
 * 2023/11/15 17:02
 ***/


@Getter
@Setter
public class StrategyAutomationSampleTunningParam {
    private String ids; //增加的样本id

    private String label; //增加样本类别

    private String operator;

    private Long taskId;

    public List<String> getIdList(){
        List<String> uids =
                Arrays.asList(ids.split("[\r|\n|\\s]+")).stream().filter(elem -> !StringUtils.isEmpty(elem)).collect(Collectors.toList());
        return uids;
    }
}
