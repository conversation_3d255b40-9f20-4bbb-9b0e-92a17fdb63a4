package com.yupaopao.risk.insight.service.analysis;

import com.yupaopao.platform.passport.response.AccountDto;
import com.yupaopao.risk.insight.service.AccountService;
import com.yupaopao.risk.insight.service.beans.BehaviorAnalysisData;
import com.yupaopao.risk.insight.service.beans.BehaviorAnalysisResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-08-18 13:52
 * 冻结信息分析
 ***/

@Service
public class FrozenInfoAnalysis implements AtomBehaviorAnalysis {

    @Autowired
    private AccountService accountService;

    @Override
    public String behavior() {
        return ConstantsForBehavior.FROZEN_USER;
    }

    @Override
    public BehaviorAnalysisResult analyze(BehaviorAnalysisData inputData) {
        if(!"userId".equalsIgnoreCase(inputData.getType())) {
            return new BehaviorAnalysisResult(0, 0, new ArrayList<>());
        }
        //检查用户是否冻结
        List<Long> uidList = inputData.getData().parallelStream().map(elem -> Long.parseLong(elem)).collect(Collectors.toList());
        List<AccountDto> accountList = accountService.getAccountListByUIds(uidList);
        List<String> frozenUIds =
                accountList.parallelStream().filter(elem -> elem.getFreezeStatus() == 1).map(elem -> String.valueOf(elem.getUid())).collect(Collectors.toList());

        return new BehaviorAnalysisResult(inputData.getData().size(), frozenUIds.size(), frozenUIds);
    }

}
