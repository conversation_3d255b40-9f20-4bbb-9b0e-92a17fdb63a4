package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * Copyright (C), 2021, jimmy
 *
 * <AUTHOR>
 * @desc OperatorHistory
 * @date 2021/12/29
 */
@Table(name = "t_version_manager")
@Getter
@Setter
public class VersionManager {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "operator_id")
    private Integer operatorId;

    @Column(name = "method")
    private String method;

    @Column(name = "scene")
    private String scene;

    @Column(name = "operator")
    private String operator;

    @Column(name = "request")
    private String request;

    @Column(name = "response")
    private String response;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;
}
