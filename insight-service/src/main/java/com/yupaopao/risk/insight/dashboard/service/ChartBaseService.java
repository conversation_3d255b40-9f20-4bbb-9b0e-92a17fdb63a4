package com.yupaopao.risk.insight.dashboard.service;

import com.yupaopao.risk.insight.InsightException;
import com.yupaopao.risk.insight.dashboard.beans.QueryRequest;
import com.yupaopao.risk.insight.enums.ErrorMessage;
import com.yupaopao.risk.insight.repository.model.ChartInfo;
import com.yupaopao.risk.insight.service.ChartService;
import com.yupaopao.risk.insight.service.beans.AggregationParam;
import com.yupaopao.risk.insight.service.beans.ChartData;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by Avalon on 2020/2/28 18:28
 */
@Service
public abstract class ChartBaseService implements ChartBase {

    @Resource
    private ChartService chartService;

    @Override
    public ChartData fetchData(QueryRequest request) {
        ChartInfo chartInfo = chartService.getChartInfo(request.getChartId());
        if (chartInfo == null) {
            throw new InsightException(ErrorMessage.CHART_DATASOURCE_NOT_EXIST_ERROR);
        }
        AggregationParam param = convertParam(request, chartInfo);
        return fetch(param);
    }

    abstract public ChartData fetch(AggregationParam param);

    abstract public AggregationParam convertParam(QueryRequest request, ChartInfo chartInfo);

}
