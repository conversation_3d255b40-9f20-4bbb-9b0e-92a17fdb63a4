package com.yupaopao.risk.insight.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.platform.common.dto.Response;
import com.yupaopao.risk.insight.constant.InsightConstants;
import com.yupaopao.risk.punish.api.RiskPunishService;
import com.yupaopao.risk.punish.request.BatchPunishRequest;
import com.yupaopao.risk.punish.result.BatchPunishResult;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.yupaopao.risk.insight.constant.InsightConstants.PUNISH_CHANNEL_INSIGHT;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-10-31 14:56
 *
 ***/

@Slf4j
@Component
public class PunishDataListener {


    @DubboReference(timeout = 60000)
    private RiskPunishService riskPunishService;

    @Value("${offline.punish.pkg.config}")
    private String punishPkgConfig;

    /***
     * 接收不同类型的待处罚数据，调用惩罚接口
     */
    @KafkaListener(topics = "RISK-INSIGHT-PUNISH")
    public void sendPunishData(ConsumerRecord<String, String> record) {
        log.info("data received from RISK-INSIGHT-PUNISH: {}", record.value());
        try {
            //能直接映射的字段直接映射，其他的字段手动设置
            BatchPunishRequest request = JSON.parseObject(record.value(), BatchPunishRequest.class);

            JSONObject otherData = JSON.parseObject(record.value());

            //构建惩罚数据
            request.setBizId("OFFLINE-DAILY");
            request.setChannel(PUNISH_CHANNEL_INSIGHT);
            if(request.getPackageId()==null){
                request.setPackageId(otherData.getLong("punishPackageId"));
            }
            if(StringUtils.isEmpty(request.getInternalReason())){
                request.setInternalReason(otherData.getString("remark"));
            }
            if(StringUtils.isEmpty(request.getOperator())){
                request.setOperator("System");
            }

            buildExtraParams(request, otherData);

            log.info("punish request: {}", request);

            Response<BatchPunishResult> response = riskPunishService.batchPunish(request);

            log.info("punish request: {} , response: {} ", request, response);

        } catch (Exception e) {
            log.error("process punish data error: data= " + record.value(), e);
        }
    }

    private void buildExtraParams(BatchPunishRequest req, JSONObject otherData) {

        //构建巡检ruleId
        Long patrolRuleId = otherData.getLong("ruleId");

        //构建remark
        String remark = req.getInternalReason();


        String cfgRemark = "";
        Long cfgRuleId = null;

        //每个pkg配置的一些关联信息
        String pkgId = otherData.getString("punishPackageId");
        JSONObject cfg = JSON.parseObject(punishPkgConfig);
        if (cfg.containsKey(pkgId)) {
            PkgConfig pkgCfg = JSON.parseObject(JSON.toJSONString(cfg.get(pkgId)), PkgConfig.class);
            cfgRuleId = pkgCfg.getRuleId();
            cfgRemark = pkgCfg.getRemark();
        }

        req.setInternalReason(StringUtils.isNotEmpty(remark) ? remark : cfgRemark);


        patrolRuleId = patrolRuleId != null ? patrolRuleId : cfgRuleId;

        Map<String, Object> extMap = new HashMap<>();
        req.setExtMap(extMap);
        if (patrolRuleId != null) {
            extMap.put(InsightConstants.EXT_KEY_PATROL_RULE_ID, patrolRuleId);
        }
        extMap.put("reasonData", otherData.getString("reasonData"));

        for(Map.Entry<String,Object> entry: otherData.entrySet()){
            String key = entry.getKey();
            String prefix = "ext_";
            if(key.startsWith(prefix)){
                String extKey = key.substring(prefix.length());
                extMap.put(extKey,entry.getValue());
            }
        }
    }

//
//    @Getter
//    @Setter
//    public static class PunishData {
//        private String deviceId;
//        private Long uid;
//        //        private String clientIp;
//        private Long punishPackageId;
//        private String remark;
//        private String externalReason;
//        private Long ruleId;
//
//        private String mobile;
//
//        private String reasonData;
//
//        private  String bizType;
//
//        private String operator;
//
//    }

    @Getter
    @Setter
    public static class PkgConfig {
        private String remark;
        private Long ruleId;
    }
}
