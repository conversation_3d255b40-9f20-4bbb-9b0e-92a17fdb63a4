package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/****
 * zengxiangcai
 * 2022/5/12 13:54
 ***/
@Getter
@Setter
@Table(name = "t_algorithm_config")
public class AlgorithmConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "type")
    private String type;

    @Column(name = "sub_type")
    private String subType;

    @Column(name = "sub_type_name")
    private String subTypeName;

    @Column(name = "resource")
    private String resource;

    @Column(name = "period_hour")
    private Integer periodHour;

    @Column(name = "data_sql")
    private String dataSql;

    @Column(name = "disabled")
    private Integer disabled; // 1: 失效 0：有效


    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_by")
    private String createBy;


    @Column(name = "update_by")
    private String updateBy;

}
