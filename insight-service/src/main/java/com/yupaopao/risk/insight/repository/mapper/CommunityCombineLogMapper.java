package com.yupaopao.risk.insight.repository.mapper;

import com.yupaopao.risk.insight.repository.model.CommunityCombineLog;
import com.yupaopao.risk.insight.repository.model.CommunityInfo;
import com.yupaopao.risk.insight.service.beans.CommunityQueryParam;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-31 16:33
 *
 ***/
public interface CommunityCombineLogMapper extends Mapper<CommunityCombineLog> {

//    @Select("<script>" +
//            "select name,community_label as communityLabel,vertex_count as vertexCount, tags, min(t.run_day) as runDay " +
//            "from t_community t\n" +
//            " where 1=1 " +
//            "<if test='name!=null and name!=\"\" '>" +
//            " and name like concat('%',#{name},'%')" +
//            "</if>" +
//            "<if test='communityLabel!=null  and communityLabel!=\"\" '>" +
//            " and community_label = #{communityLabel} " +
//            "</if>" +
//            " group by name,communityLabel,vertexCount,tags " +
//            " order by t.run_day desc" +
//            "</script>")
//    List<CommunityInfo> getCommunities(CommunityQueryParam queryParam);

//    @Select("<script>" +
//            "select t.name,t.community_label as communityLabel,vertex_count as vertexCount, tags, min(t.run_day) as " +
//            "runDay  " +
//            "\n" +
//            " from t_community t join t_community_detail t1 on t.community_label = t1.community_label\n" +
//            " where  t1.vertex_id = #{vertexId}\n " +
//            "<if test='name!=null and name!=\"\" '>" +
//            " and t.name like concat('%',#{name},'%')" +
//            "</if>" +
//            "<if test='communityLabel!=null and communityLabel!=\"\" '>" +
//            " and t.community_label = #{communityLabel} " +
//            "</if>" +
//            " group by t.name,t.community_label,t.vertex_count, t.tags" +
//            " order by t.run_day desc " +
//            "</script>")
//    List<CommunityInfo> getCommunitiesWithVertexId(CommunityQueryParam queryParam);

//    @Update("" +
//            "update t_community t set t.name = #{name}, t.remark = #{remark} where community_label=#{communityLabel}" +
//            "")
//    int editCommunity(CommunityInfo community);
//
//    @Select("" +
//            "select  (case when original_label!='' then original_label\n" +
//            "        else community_label end) as communityLabel,\n" +
//            "        run_day as runDay\n" +
//            "from t_community t where t.run_day = #{runDay}" +
//            "")
//    List<CommunityInfo> getCommunitiesByRunDay(@Param("runDay") String runDay);
}
