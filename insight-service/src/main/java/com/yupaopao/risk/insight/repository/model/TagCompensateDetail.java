package com.yupaopao.risk.insight.repository.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "t_tag_compensate_detail")
public class TagCompensateDetail implements Serializable {
    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 对象ID
     */
    @Column(name = "value_id")
    private String valueId;

    /**
     * 批次ID
     */
    @Column(name = "batch_id")
    private String batchId;

    /**
     * 标签编码
     */
    @Column(name = "tag_code")
    private String tagCode;

    /**
     * 标签值
     */
    @Column(name = "tag_value")
    private String tagValue;

    /**
     * 标签名称
     */
    @Column(name = "tag_name")
    private String tagName;

    private static final long serialVersionUID = 1L;

    /**
     * 获取id
     *
     * @return id - id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取对象ID
     *
     * @return value_id - 对象ID
     */
    public String getValueId() {
        return valueId;
    }

    /**
     * 设置对象ID
     *
     * @param valueId 对象ID
     */
    public void setValueId(String valueId) {
        this.valueId = valueId;
    }

    /**
     * 获取批次ID
     *
     * @return batch_id - 批次ID
     */
    public String getBatchId() {
        return batchId;
    }

    /**
     * 设置批次ID
     *
     * @param batchId 批次ID
     */
    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    /**
     * 获取标签编码
     *
     * @return tag_code - 标签编码
     */
    public String getTagCode() {
        return tagCode;
    }

    /**
     * 设置标签编码
     *
     * @param tagCode 标签编码
     */
    public void setTagCode(String tagCode) {
        this.tagCode = tagCode;
    }

    /**
     * 获取标签值
     *
     * @return tag_value - 标签值
     */
    public String getTagValue() {
        return tagValue;
    }

    /**
     * 设置标签值
     *
     * @param tagValue 标签值
     */
    public void setTagValue(String tagValue) {
        this.tagValue = tagValue;
    }

    /**
     * 获取标签名称
     *
     * @return tag_name - 标签名称
     */
    public String getTagName() {
        return tagName;
    }

    /**
     * 设置标签名称
     *
     * @param tagName 标签名称
     */
    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", valueId=").append(valueId);
        sb.append(", batchId=").append(batchId);
        sb.append(", tagCode=").append(tagCode);
        sb.append(", tagValue=").append(tagValue);
        sb.append(", tagName=").append(tagName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}