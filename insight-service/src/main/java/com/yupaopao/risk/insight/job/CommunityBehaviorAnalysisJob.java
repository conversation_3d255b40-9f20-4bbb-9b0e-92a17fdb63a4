package com.yupaopao.risk.insight.job;

import com.alibaba.fastjson.JSON;
import com.yupaopao.aries.client.JobExecutionContext;
import com.yupaopao.aries.client.JobListener;
import com.yupaopao.framework.spring.boot.aries.annotation.AriesCronJobListener;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.service.BehaviorAnalysisService;
import com.yupaopao.risk.insight.service.CommunityBehaviorAnalysisService;
import com.yupaopao.risk.insight.service.CommunityService;
import com.yupaopao.risk.insight.service.analysis.ConstantsForBehavior;
import com.yupaopao.risk.insight.service.beans.BehaviorAnalysisResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-08-18 15:17
 *
 ***/

@Slf4j
@AriesCronJobListener
public class CommunityBehaviorAnalysisJob implements JobListener {


    private String runDay;

    @Autowired
    private CommunityBehaviorAnalysisService communityBehaviorAnalysisService;

    @Autowired
    private CommunityService communityService;

    @Autowired
    private BehaviorAnalysisService behaviorAnalysisService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        //查询当天的社区
        try {
            runDay = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);

            log.info("start to analysis day: {}", runDay);

            //分析<=runDay当日新增的所有节点
            List<Map<String, String>> communityList = communityService.getCommunitiesByRunDay(runDay);
            if (CollectionUtils.isNotEmpty(communityList)) {
                communityList.stream().forEach(elem -> {
                    String connectedLabel = elem.get("connectedLabel");
                    communityBehaviorAnalysisService.detectBehavior(connectedLabel, runDay);
                });
            }
            log.info("start to analysis frozenUsers...");
            processFrozenUsers();
            log.info("all community behavior analysis finished...");
        } catch (Exception e) {
            log.error("community behavior analysis job error ", e);
        }
    }

    private void processFrozenUsers() {
        List<Map<String, String>> userIdLabelList = communityService.getCommunityUserVertexes();
        if (CollectionUtils.isNotEmpty(userIdLabelList)) {
            List<String> userIdList = new ArrayList<>();
            Map<String, String> idLabelMap = new HashMap<>();
            for (Map<String, String> idLabel : userIdLabelList) {
                String vertexId = idLabel.get("vertexId");
                String connectedLabel = idLabel.get("connectedLabel");
                userIdList.add(vertexId);
                idLabelMap.put(vertexId, connectedLabel);
            }
            Map<String, BehaviorAnalysisResult> resultMap =
                    behaviorAnalysisService.detectBehavior(userIdList,
                            Arrays.asList(ConstantsForBehavior.FROZEN_USER));
            if (resultMap != null && resultMap.containsKey(ConstantsForBehavior.FROZEN_USER)) {
                BehaviorAnalysisResult analysisResult = resultMap.get(ConstantsForBehavior.FROZEN_USER);
                if (analysisResult != null && CollectionUtils.isNotEmpty(analysisResult.getData())) {

                    List<String> insertJsonList = analysisResult.getData().stream().map(elem -> {
                        Map<String, String> row = new HashMap<>();
                        row.put("type", ConstantsForBehavior.FROZEN_USER);
                        row.put("vertexId", elem);
                        row.put("connectedLabel", idLabelMap.get(elem));
                        row.put("runDay", runDay);
                        return JSON.toJSONString(row);
                    }).collect(Collectors.toList());
                    communityService.insertFrozenUser(insertJsonList);
                }
            }
        }
    }
}
