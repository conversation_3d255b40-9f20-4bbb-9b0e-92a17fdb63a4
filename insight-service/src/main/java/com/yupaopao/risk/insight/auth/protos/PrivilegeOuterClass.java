// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/main/resources/privilege.proto

package com.yupaopao.risk.insight.auth.protos;

public final class PrivilegeOuterClass {
  private PrivilegeOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface GetUserPrivilegeAllParamOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protos.GetUserPrivilegeAllParam)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *域控
     * </pre>
     *
     * <code>string domain = 1;</code>
     * @return The domain.
     */
    String getDomain();
    /**
     * <pre>
     *域控
     * </pre>
     *
     * <code>string domain = 1;</code>
     * @return The bytes for domain.
     */
    com.google.protobuf.ByteString
        getDomainBytes();

    /**
     * <pre>
     * 站点唯一键
     * </pre>
     *
     * <code>string siteKey = 2;</code>
     * @return The siteKey.
     */
    String getSiteKey();
    /**
     * <pre>
     * 站点唯一键
     * </pre>
     *
     * <code>string siteKey = 2;</code>
     * @return The bytes for siteKey.
     */
    com.google.protobuf.ByteString
        getSiteKeyBytes();
  }
  /**
   * Protobuf type {@code protos.GetUserPrivilegeAllParam}
   */
  public static final class GetUserPrivilegeAllParam extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protos.GetUserPrivilegeAllParam)
      GetUserPrivilegeAllParamOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetUserPrivilegeAllParam.newBuilder() to construct.
    private GetUserPrivilegeAllParam(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetUserPrivilegeAllParam() {
      domain_ = "";
      siteKey_ = "";
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetUserPrivilegeAllParam();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetUserPrivilegeAllParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              String s = input.readStringRequireUtf8();

              domain_ = s;
              break;
            }
            case 18: {
              String s = input.readStringRequireUtf8();

              siteKey_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllParam_descriptor;
    }

    @Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PrivilegeOuterClass.GetUserPrivilegeAllParam.class, PrivilegeOuterClass.GetUserPrivilegeAllParam.Builder.class);
    }

    public static final int DOMAIN_FIELD_NUMBER = 1;
    private volatile Object domain_;
    /**
     * <pre>
     *域控
     * </pre>
     *
     * <code>string domain = 1;</code>
     * @return The domain.
     */
    @Override
    public String getDomain() {
      Object ref = domain_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        domain_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *域控
     * </pre>
     *
     * <code>string domain = 1;</code>
     * @return The bytes for domain.
     */
    @Override
    public com.google.protobuf.ByteString
        getDomainBytes() {
      Object ref = domain_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        domain_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SITEKEY_FIELD_NUMBER = 2;
    private volatile Object siteKey_;
    /**
     * <pre>
     * 站点唯一键
     * </pre>
     *
     * <code>string siteKey = 2;</code>
     * @return The siteKey.
     */
    @Override
    public String getSiteKey() {
      Object ref = siteKey_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        siteKey_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 站点唯一键
     * </pre>
     *
     * <code>string siteKey = 2;</code>
     * @return The bytes for siteKey.
     */
    @Override
    public com.google.protobuf.ByteString
        getSiteKeyBytes() {
      Object ref = siteKey_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        siteKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getDomainBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, domain_);
      }
      if (!getSiteKeyBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, siteKey_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getDomainBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, domain_);
      }
      if (!getSiteKeyBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, siteKey_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PrivilegeOuterClass.GetUserPrivilegeAllParam)) {
        return super.equals(obj);
      }
      PrivilegeOuterClass.GetUserPrivilegeAllParam other = (PrivilegeOuterClass.GetUserPrivilegeAllParam) obj;

      if (!getDomain()
          .equals(other.getDomain())) return false;
      if (!getSiteKey()
          .equals(other.getSiteKey())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + DOMAIN_FIELD_NUMBER;
      hash = (53 * hash) + getDomain().hashCode();
      hash = (37 * hash) + SITEKEY_FIELD_NUMBER;
      hash = (53 * hash) + getSiteKey().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PrivilegeOuterClass.GetUserPrivilegeAllParam prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protos.GetUserPrivilegeAllParam}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:protos.GetUserPrivilegeAllParam)
        PrivilegeOuterClass.GetUserPrivilegeAllParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllParam_descriptor;
      }

      @Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PrivilegeOuterClass.GetUserPrivilegeAllParam.class, PrivilegeOuterClass.GetUserPrivilegeAllParam.Builder.class);
      }

      // Construct using PrivilegeOuterClass.GetUserPrivilegeAllParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        domain_ = "";

        siteKey_ = "";

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllParam_descriptor;
      }

      @Override
      public PrivilegeOuterClass.GetUserPrivilegeAllParam getDefaultInstanceForType() {
        return PrivilegeOuterClass.GetUserPrivilegeAllParam.getDefaultInstance();
      }

      @Override
      public PrivilegeOuterClass.GetUserPrivilegeAllParam build() {
        PrivilegeOuterClass.GetUserPrivilegeAllParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public PrivilegeOuterClass.GetUserPrivilegeAllParam buildPartial() {
        PrivilegeOuterClass.GetUserPrivilegeAllParam result = new PrivilegeOuterClass.GetUserPrivilegeAllParam(this);
        result.domain_ = domain_;
        result.siteKey_ = siteKey_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PrivilegeOuterClass.GetUserPrivilegeAllParam) {
          return mergeFrom((PrivilegeOuterClass.GetUserPrivilegeAllParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PrivilegeOuterClass.GetUserPrivilegeAllParam other) {
        if (other == PrivilegeOuterClass.GetUserPrivilegeAllParam.getDefaultInstance()) return this;
        if (!other.getDomain().isEmpty()) {
          domain_ = other.domain_;
          onChanged();
        }
        if (!other.getSiteKey().isEmpty()) {
          siteKey_ = other.siteKey_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        PrivilegeOuterClass.GetUserPrivilegeAllParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (PrivilegeOuterClass.GetUserPrivilegeAllParam) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private Object domain_ = "";
      /**
       * <pre>
       *域控
       * </pre>
       *
       * <code>string domain = 1;</code>
       * @return The domain.
       */
      public String getDomain() {
        Object ref = domain_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          domain_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *域控
       * </pre>
       *
       * <code>string domain = 1;</code>
       * @return The bytes for domain.
       */
      public com.google.protobuf.ByteString
          getDomainBytes() {
        Object ref = domain_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          domain_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *域控
       * </pre>
       *
       * <code>string domain = 1;</code>
       * @param value The domain to set.
       * @return This builder for chaining.
       */
      public Builder setDomain(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        domain_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *域控
       * </pre>
       *
       * <code>string domain = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDomain() {

        domain_ = getDefaultInstance().getDomain();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *域控
       * </pre>
       *
       * <code>string domain = 1;</code>
       * @param value The bytes for domain to set.
       * @return This builder for chaining.
       */
      public Builder setDomainBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        domain_ = value;
        onChanged();
        return this;
      }

      private Object siteKey_ = "";
      /**
       * <pre>
       * 站点唯一键
       * </pre>
       *
       * <code>string siteKey = 2;</code>
       * @return The siteKey.
       */
      public String getSiteKey() {
        Object ref = siteKey_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          siteKey_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 站点唯一键
       * </pre>
       *
       * <code>string siteKey = 2;</code>
       * @return The bytes for siteKey.
       */
      public com.google.protobuf.ByteString
          getSiteKeyBytes() {
        Object ref = siteKey_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          siteKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 站点唯一键
       * </pre>
       *
       * <code>string siteKey = 2;</code>
       * @param value The siteKey to set.
       * @return This builder for chaining.
       */
      public Builder setSiteKey(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        siteKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 站点唯一键
       * </pre>
       *
       * <code>string siteKey = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSiteKey() {

        siteKey_ = getDefaultInstance().getSiteKey();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 站点唯一键
       * </pre>
       *
       * <code>string siteKey = 2;</code>
       * @param value The bytes for siteKey to set.
       * @return This builder for chaining.
       */
      public Builder setSiteKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        siteKey_ = value;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:protos.GetUserPrivilegeAllParam)
    }

    // @@protoc_insertion_point(class_scope:protos.GetUserPrivilegeAllParam)
    private static final PrivilegeOuterClass.GetUserPrivilegeAllParam DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new PrivilegeOuterClass.GetUserPrivilegeAllParam();
    }

    public static PrivilegeOuterClass.GetUserPrivilegeAllParam getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetUserPrivilegeAllParam>
        PARSER = new com.google.protobuf.AbstractParser<GetUserPrivilegeAllParam>() {
      @Override
      public GetUserPrivilegeAllParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetUserPrivilegeAllParam(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetUserPrivilegeAllParam> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<GetUserPrivilegeAllParam> getParserForType() {
      return PARSER;
    }

    @Override
    public PrivilegeOuterClass.GetUserPrivilegeAllParam getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetUserPrivilegeAllReplyOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protos.GetUserPrivilegeAllReply)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
     */
    java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege>
        getPrivilegeList();
    /**
     * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
     */
    PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege getPrivilege(int index);
    /**
     * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
     */
    int getPrivilegeCount();
    /**
     * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
     */
    java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeAllReply.PrivilegeOrBuilder>
        getPrivilegeOrBuilderList();
    /**
     * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
     */
    PrivilegeOuterClass.GetUserPrivilegeAllReply.PrivilegeOrBuilder getPrivilegeOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protos.GetUserPrivilegeAllReply}
   */
  public static final class GetUserPrivilegeAllReply extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protos.GetUserPrivilegeAllReply)
      GetUserPrivilegeAllReplyOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetUserPrivilegeAllReply.newBuilder() to construct.
    private GetUserPrivilegeAllReply(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetUserPrivilegeAllReply() {
      privilege_ = java.util.Collections.emptyList();
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetUserPrivilegeAllReply();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetUserPrivilegeAllReply(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                privilege_ = new java.util.ArrayList<PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege>();
                mutable_bitField0_ |= 0x00000001;
              }
              privilege_.add(
                  input.readMessage(PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          privilege_ = java.util.Collections.unmodifiableList(privilege_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_descriptor;
    }

    @Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PrivilegeOuterClass.GetUserPrivilegeAllReply.class, PrivilegeOuterClass.GetUserPrivilegeAllReply.Builder.class);
    }

    public interface ApiOrBuilder extends
        // @@protoc_insertion_point(interface_extends:protos.GetUserPrivilegeAllReply.Api)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 接口唯一键
       * </pre>
       *
       * <code>string api_key = 1;</code>
       * @return The apiKey.
       */
      String getApiKey();
      /**
       * <pre>
       * 接口唯一键
       * </pre>
       *
       * <code>string api_key = 1;</code>
       * @return The bytes for apiKey.
       */
      com.google.protobuf.ByteString
          getApiKeyBytes();

      /**
       * <pre>
       *接口名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      String getName();
      /**
       * <pre>
       *接口名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      com.google.protobuf.ByteString
          getNameBytes();

      /**
       * <pre>
       *地址
       * </pre>
       *
       * <code>string url = 3;</code>
       * @return The url.
       */
      String getUrl();
      /**
       * <pre>
       *地址
       * </pre>
       *
       * <code>string url = 3;</code>
       * @return The bytes for url.
       */
      com.google.protobuf.ByteString
          getUrlBytes();

      /**
       * <pre>
       *方法
       * </pre>
       *
       * <code>string method = 4;</code>
       * @return The method.
       */
      String getMethod();
      /**
       * <pre>
       *方法
       * </pre>
       *
       * <code>string method = 4;</code>
       * @return The bytes for method.
       */
      com.google.protobuf.ByteString
          getMethodBytes();
    }
    /**
     * Protobuf type {@code protos.GetUserPrivilegeAllReply.Api}
     */
    public static final class Api extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:protos.GetUserPrivilegeAllReply.Api)
        ApiOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Api.newBuilder() to construct.
      private Api(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Api() {
        apiKey_ = "";
        name_ = "";
        url_ = "";
        method_ = "";
      }

      @Override
      @SuppressWarnings({"unused"})
      protected Object newInstance(
          UnusedPrivateParameter unused) {
        return new Api();
      }

      @Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      private Api(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        this();
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        com.google.protobuf.UnknownFieldSet.Builder unknownFields =
            com.google.protobuf.UnknownFieldSet.newBuilder();
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                String s = input.readStringRequireUtf8();

                apiKey_ = s;
                break;
              }
              case 18: {
                String s = input.readStringRequireUtf8();

                name_ = s;
                break;
              }
              case 26: {
                String s = input.readStringRequireUtf8();

                url_ = s;
                break;
              }
              case 34: {
                String s = input.readStringRequireUtf8();

                method_ = s;
                break;
              }
              default: {
                if (!parseUnknownField(
                    input, unknownFields, extensionRegistry, tag)) {
                  done = true;
                }
                break;
              }
            }
          }
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(this);
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e).setUnfinishedMessage(this);
        } finally {
          this.unknownFields = unknownFields.build();
          makeExtensionsImmutable();
        }
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_Api_descriptor;
      }

      @Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_Api_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.class, PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder.class);
      }

      public static final int API_KEY_FIELD_NUMBER = 1;
      private volatile Object apiKey_;
      /**
       * <pre>
       * 接口唯一键
       * </pre>
       *
       * <code>string api_key = 1;</code>
       * @return The apiKey.
       */
      @Override
      public String getApiKey() {
        Object ref = apiKey_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          apiKey_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 接口唯一键
       * </pre>
       *
       * <code>string api_key = 1;</code>
       * @return The bytes for apiKey.
       */
      @Override
      public com.google.protobuf.ByteString
          getApiKeyBytes() {
        Object ref = apiKey_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          apiKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int NAME_FIELD_NUMBER = 2;
      private volatile Object name_;
      /**
       * <pre>
       *接口名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      @Override
      public String getName() {
        Object ref = name_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          name_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *接口名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      @Override
      public com.google.protobuf.ByteString
          getNameBytes() {
        Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int URL_FIELD_NUMBER = 3;
      private volatile Object url_;
      /**
       * <pre>
       *地址
       * </pre>
       *
       * <code>string url = 3;</code>
       * @return The url.
       */
      @Override
      public String getUrl() {
        Object ref = url_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          url_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *地址
       * </pre>
       *
       * <code>string url = 3;</code>
       * @return The bytes for url.
       */
      @Override
      public com.google.protobuf.ByteString
          getUrlBytes() {
        Object ref = url_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          url_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int METHOD_FIELD_NUMBER = 4;
      private volatile Object method_;
      /**
       * <pre>
       *方法
       * </pre>
       *
       * <code>string method = 4;</code>
       * @return The method.
       */
      @Override
      public String getMethod() {
        Object ref = method_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          method_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *方法
       * </pre>
       *
       * <code>string method = 4;</code>
       * @return The bytes for method.
       */
      @Override
      public com.google.protobuf.ByteString
          getMethodBytes() {
        Object ref = method_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          method_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!getApiKeyBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, apiKey_);
        }
        if (!getNameBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
        }
        if (!getUrlBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 3, url_);
        }
        if (!getMethodBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 4, method_);
        }
        unknownFields.writeTo(output);
      }

      @Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!getApiKeyBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, apiKey_);
        }
        if (!getNameBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
        }
        if (!getUrlBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, url_);
        }
        if (!getMethodBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, method_);
        }
        size += unknownFields.getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @Override
      public boolean equals(final Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof PrivilegeOuterClass.GetUserPrivilegeAllReply.Api)) {
          return super.equals(obj);
        }
        PrivilegeOuterClass.GetUserPrivilegeAllReply.Api other = (PrivilegeOuterClass.GetUserPrivilegeAllReply.Api) obj;

        if (!getApiKey()
            .equals(other.getApiKey())) return false;
        if (!getName()
            .equals(other.getName())) return false;
        if (!getUrl()
            .equals(other.getUrl())) return false;
        if (!getMethod()
            .equals(other.getMethod())) return false;
        if (!unknownFields.equals(other.unknownFields)) return false;
        return true;
      }

      @Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + API_KEY_FIELD_NUMBER;
        hash = (53 * hash) + getApiKey().hashCode();
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
        hash = (37 * hash) + URL_FIELD_NUMBER;
        hash = (53 * hash) + getUrl().hashCode();
        hash = (37 * hash) + METHOD_FIELD_NUMBER;
        hash = (53 * hash) + getMethod().hashCode();
        hash = (29 * hash) + unknownFields.hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(PrivilegeOuterClass.GetUserPrivilegeAllReply.Api prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code protos.GetUserPrivilegeAllReply.Api}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:protos.GetUserPrivilegeAllReply.Api)
          PrivilegeOuterClass.GetUserPrivilegeAllReply.ApiOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_Api_descriptor;
        }

        @Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_Api_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.class, PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder.class);
        }

        // Construct using PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
          }
        }
        @Override
        public Builder clear() {
          super.clear();
          apiKey_ = "";

          name_ = "";

          url_ = "";

          method_ = "";

          return this;
        }

        @Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_Api_descriptor;
        }

        @Override
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.Api getDefaultInstanceForType() {
          return PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.getDefaultInstance();
        }

        @Override
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.Api build() {
          PrivilegeOuterClass.GetUserPrivilegeAllReply.Api result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @Override
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.Api buildPartial() {
          PrivilegeOuterClass.GetUserPrivilegeAllReply.Api result = new PrivilegeOuterClass.GetUserPrivilegeAllReply.Api(this);
          result.apiKey_ = apiKey_;
          result.name_ = name_;
          result.url_ = url_;
          result.method_ = method_;
          onBuilt();
          return result;
        }

        @Override
        public Builder clone() {
          return super.clone();
        }
        @Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            Object value) {
          return super.setField(field, value);
        }
        @Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            Object value) {
          return super.addRepeatedField(field, value);
        }
        @Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof PrivilegeOuterClass.GetUserPrivilegeAllReply.Api) {
            return mergeFrom((PrivilegeOuterClass.GetUserPrivilegeAllReply.Api)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(PrivilegeOuterClass.GetUserPrivilegeAllReply.Api other) {
          if (other == PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.getDefaultInstance()) return this;
          if (!other.getApiKey().isEmpty()) {
            apiKey_ = other.apiKey_;
            onChanged();
          }
          if (!other.getName().isEmpty()) {
            name_ = other.name_;
            onChanged();
          }
          if (!other.getUrl().isEmpty()) {
            url_ = other.url_;
            onChanged();
          }
          if (!other.getMethod().isEmpty()) {
            method_ = other.method_;
            onChanged();
          }
          this.mergeUnknownFields(other.unknownFields);
          onChanged();
          return this;
        }

        @Override
        public final boolean isInitialized() {
          return true;
        }

        @Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          PrivilegeOuterClass.GetUserPrivilegeAllReply.Api parsedMessage = null;
          try {
            parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            parsedMessage = (PrivilegeOuterClass.GetUserPrivilegeAllReply.Api) e.getUnfinishedMessage();
            throw e.unwrapIOException();
          } finally {
            if (parsedMessage != null) {
              mergeFrom(parsedMessage);
            }
          }
          return this;
        }

        private Object apiKey_ = "";
        /**
         * <pre>
         * 接口唯一键
         * </pre>
         *
         * <code>string api_key = 1;</code>
         * @return The apiKey.
         */
        public String getApiKey() {
          Object ref = apiKey_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            apiKey_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <pre>
         * 接口唯一键
         * </pre>
         *
         * <code>string api_key = 1;</code>
         * @return The bytes for apiKey.
         */
        public com.google.protobuf.ByteString
            getApiKeyBytes() {
          Object ref = apiKey_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            apiKey_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 接口唯一键
         * </pre>
         *
         * <code>string api_key = 1;</code>
         * @param value The apiKey to set.
         * @return This builder for chaining.
         */
        public Builder setApiKey(
            String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          apiKey_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 接口唯一键
         * </pre>
         *
         * <code>string api_key = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearApiKey() {

          apiKey_ = getDefaultInstance().getApiKey();
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 接口唯一键
         * </pre>
         *
         * <code>string api_key = 1;</code>
         * @param value The bytes for apiKey to set.
         * @return This builder for chaining.
         */
        public Builder setApiKeyBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          apiKey_ = value;
          onChanged();
          return this;
        }

        private Object name_ = "";
        /**
         * <pre>
         *接口名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return The name.
         */
        public String getName() {
          Object ref = name_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            name_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <pre>
         *接口名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return The bytes for name.
         */
        public com.google.protobuf.ByteString
            getNameBytes() {
          Object ref = name_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            name_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *接口名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @param value The name to set.
         * @return This builder for chaining.
         */
        public Builder setName(
            String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          name_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *接口名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearName() {

          name_ = getDefaultInstance().getName();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *接口名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @param value The bytes for name to set.
         * @return This builder for chaining.
         */
        public Builder setNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          name_ = value;
          onChanged();
          return this;
        }

        private Object url_ = "";
        /**
         * <pre>
         *地址
         * </pre>
         *
         * <code>string url = 3;</code>
         * @return The url.
         */
        public String getUrl() {
          Object ref = url_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            url_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <pre>
         *地址
         * </pre>
         *
         * <code>string url = 3;</code>
         * @return The bytes for url.
         */
        public com.google.protobuf.ByteString
            getUrlBytes() {
          Object ref = url_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            url_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *地址
         * </pre>
         *
         * <code>string url = 3;</code>
         * @param value The url to set.
         * @return This builder for chaining.
         */
        public Builder setUrl(
            String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          url_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *地址
         * </pre>
         *
         * <code>string url = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearUrl() {

          url_ = getDefaultInstance().getUrl();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *地址
         * </pre>
         *
         * <code>string url = 3;</code>
         * @param value The bytes for url to set.
         * @return This builder for chaining.
         */
        public Builder setUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          url_ = value;
          onChanged();
          return this;
        }

        private Object method_ = "";
        /**
         * <pre>
         *方法
         * </pre>
         *
         * <code>string method = 4;</code>
         * @return The method.
         */
        public String getMethod() {
          Object ref = method_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            method_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <pre>
         *方法
         * </pre>
         *
         * <code>string method = 4;</code>
         * @return The bytes for method.
         */
        public com.google.protobuf.ByteString
            getMethodBytes() {
          Object ref = method_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            method_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *方法
         * </pre>
         *
         * <code>string method = 4;</code>
         * @param value The method to set.
         * @return This builder for chaining.
         */
        public Builder setMethod(
            String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          method_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *方法
         * </pre>
         *
         * <code>string method = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearMethod() {

          method_ = getDefaultInstance().getMethod();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *方法
         * </pre>
         *
         * <code>string method = 4;</code>
         * @param value The bytes for method to set.
         * @return This builder for chaining.
         */
        public Builder setMethodBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          method_ = value;
          onChanged();
          return this;
        }
        @Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:protos.GetUserPrivilegeAllReply.Api)
      }

      // @@protoc_insertion_point(class_scope:protos.GetUserPrivilegeAllReply.Api)
      private static final PrivilegeOuterClass.GetUserPrivilegeAllReply.Api DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new PrivilegeOuterClass.GetUserPrivilegeAllReply.Api();
      }

      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Api getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Api>
          PARSER = new com.google.protobuf.AbstractParser<Api>() {
        @Override
        public Api parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return new Api(input, extensionRegistry);
        }
      };

      public static com.google.protobuf.Parser<Api> parser() {
        return PARSER;
      }

      @Override
      public com.google.protobuf.Parser<Api> getParserForType() {
        return PARSER;
      }

      @Override
      public PrivilegeOuterClass.GetUserPrivilegeAllReply.Api getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface PrivilegeOrBuilder extends
        // @@protoc_insertion_point(interface_extends:protos.GetUserPrivilegeAllReply.Privilege)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       *权限唯一健
       * </pre>
       *
       * <code>string privilege_key = 1;</code>
       * @return The privilegeKey.
       */
      String getPrivilegeKey();
      /**
       * <pre>
       *权限唯一健
       * </pre>
       *
       * <code>string privilege_key = 1;</code>
       * @return The bytes for privilegeKey.
       */
      com.google.protobuf.ByteString
          getPrivilegeKeyBytes();

      /**
       * <pre>
       *名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      String getName();
      /**
       * <pre>
       *名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      com.google.protobuf.ByteString
          getNameBytes();

      /**
       * <pre>
       *父健
       * </pre>
       *
       * <code>string parent_key = 3;</code>
       * @return The parentKey.
       */
      String getParentKey();
      /**
       * <pre>
       *父健
       * </pre>
       *
       * <code>string parent_key = 3;</code>
       * @return The bytes for parentKey.
       */
      com.google.protobuf.ByteString
          getParentKeyBytes();

      /**
       * <pre>
       *权限类
       * </pre>
       *
       * <code>string category = 4;</code>
       * @return The category.
       */
      String getCategory();
      /**
       * <pre>
       *权限类
       * </pre>
       *
       * <code>string category = 4;</code>
       * @return The bytes for category.
       */
      com.google.protobuf.ByteString
          getCategoryBytes();

      /**
       * <pre>
       *权限型
       * </pre>
       *
       * <code>string shape = 5;</code>
       * @return The shape.
       */
      String getShape();
      /**
       * <pre>
       *权限型
       * </pre>
       *
       * <code>string shape = 5;</code>
       * @return The bytes for shape.
       */
      com.google.protobuf.ByteString
          getShapeBytes();

      /**
       * <pre>
       *权限点
       * </pre>
       *
       * <code>string point = 6;</code>
       * @return The point.
       */
      String getPoint();
      /**
       * <pre>
       *权限点
       * </pre>
       *
       * <code>string point = 6;</code>
       * @return The bytes for point.
       */
      com.google.protobuf.ByteString
          getPointBytes();

      /**
       * <pre>
       *权限类型
       * </pre>
       *
       * <code>string type = 7;</code>
       * @return The type.
       */
      String getType();
      /**
       * <pre>
       *权限类型
       * </pre>
       *
       * <code>string type = 7;</code>
       * @return The bytes for type.
       */
      com.google.protobuf.ByteString
          getTypeBytes();

      /**
       * <pre>
       *图标
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return The icon.
       */
      String getIcon();
      /**
       * <pre>
       *图标
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return The bytes for icon.
       */
      com.google.protobuf.ByteString
          getIconBytes();

      /**
       * <pre>
       *权重
       * </pre>
       *
       * <code>int32 sort_value = 9;</code>
       * @return The sortValue.
       */
      int getSortValue();

      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
       */
      java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Api>
          getApiList();
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
       */
      PrivilegeOuterClass.GetUserPrivilegeAllReply.Api getApi(int index);
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
       */
      int getApiCount();
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
       */
      java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeAllReply.ApiOrBuilder>
          getApiOrBuilderList();
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
       */
      PrivilegeOuterClass.GetUserPrivilegeAllReply.ApiOrBuilder getApiOrBuilder(
          int index);
    }
    /**
     * Protobuf type {@code protos.GetUserPrivilegeAllReply.Privilege}
     */
    public static final class Privilege extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:protos.GetUserPrivilegeAllReply.Privilege)
        PrivilegeOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Privilege.newBuilder() to construct.
      private Privilege(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Privilege() {
        privilegeKey_ = "";
        name_ = "";
        parentKey_ = "";
        category_ = "";
        shape_ = "";
        point_ = "";
        type_ = "";
        icon_ = "";
        api_ = java.util.Collections.emptyList();
      }

      @Override
      @SuppressWarnings({"unused"})
      protected Object newInstance(
          UnusedPrivateParameter unused) {
        return new Privilege();
      }

      @Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      private Privilege(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        this();
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        int mutable_bitField0_ = 0;
        com.google.protobuf.UnknownFieldSet.Builder unknownFields =
            com.google.protobuf.UnknownFieldSet.newBuilder();
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                String s = input.readStringRequireUtf8();

                privilegeKey_ = s;
                break;
              }
              case 18: {
                String s = input.readStringRequireUtf8();

                name_ = s;
                break;
              }
              case 26: {
                String s = input.readStringRequireUtf8();

                parentKey_ = s;
                break;
              }
              case 34: {
                String s = input.readStringRequireUtf8();

                category_ = s;
                break;
              }
              case 42: {
                String s = input.readStringRequireUtf8();

                shape_ = s;
                break;
              }
              case 50: {
                String s = input.readStringRequireUtf8();

                point_ = s;
                break;
              }
              case 58: {
                String s = input.readStringRequireUtf8();

                type_ = s;
                break;
              }
              case 66: {
                String s = input.readStringRequireUtf8();

                icon_ = s;
                break;
              }
              case 72: {

                sortValue_ = input.readInt32();
                break;
              }
              case 82: {
                if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                  api_ = new java.util.ArrayList<PrivilegeOuterClass.GetUserPrivilegeAllReply.Api>();
                  mutable_bitField0_ |= 0x00000001;
                }
                api_.add(
                    input.readMessage(PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.parser(), extensionRegistry));
                break;
              }
              default: {
                if (!parseUnknownField(
                    input, unknownFields, extensionRegistry, tag)) {
                  done = true;
                }
                break;
              }
            }
          }
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(this);
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e).setUnfinishedMessage(this);
        } finally {
          if (((mutable_bitField0_ & 0x00000001) != 0)) {
            api_ = java.util.Collections.unmodifiableList(api_);
          }
          this.unknownFields = unknownFields.build();
          makeExtensionsImmutable();
        }
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_Privilege_descriptor;
      }

      @Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_Privilege_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.class, PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder.class);
      }

      public static final int PRIVILEGE_KEY_FIELD_NUMBER = 1;
      private volatile Object privilegeKey_;
      /**
       * <pre>
       *权限唯一健
       * </pre>
       *
       * <code>string privilege_key = 1;</code>
       * @return The privilegeKey.
       */
      @Override
      public String getPrivilegeKey() {
        Object ref = privilegeKey_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          privilegeKey_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *权限唯一健
       * </pre>
       *
       * <code>string privilege_key = 1;</code>
       * @return The bytes for privilegeKey.
       */
      @Override
      public com.google.protobuf.ByteString
          getPrivilegeKeyBytes() {
        Object ref = privilegeKey_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          privilegeKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int NAME_FIELD_NUMBER = 2;
      private volatile Object name_;
      /**
       * <pre>
       *名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      @Override
      public String getName() {
        Object ref = name_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int PARENT_KEY_FIELD_NUMBER = 3;
      private volatile java.lang.Object parentKey_;
      /**
       * <pre>
       *父健
       * </pre>
       *
       * <code>string parent_key = 3;</code>
       * @return The parentKey.
       */
      @java.lang.Override
      public java.lang.String getParentKey() {
        java.lang.Object ref = parentKey_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          parentKey_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *父健
       * </pre>
       *
       * <code>string parent_key = 3;</code>
       * @return The bytes for parentKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getParentKeyBytes() {
        java.lang.Object ref = parentKey_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          parentKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int CATEGORY_FIELD_NUMBER = 4;
      private volatile java.lang.Object category_;
      /**
       * <pre>
       *权限类
       * </pre>
       *
       * <code>string category = 4;</code>
       * @return The category.
       */
      @java.lang.Override
      public java.lang.String getCategory() {
        java.lang.Object ref = category_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          category_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *权限类
       * </pre>
       *
       * <code>string category = 4;</code>
       * @return The bytes for category.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getCategoryBytes() {
        java.lang.Object ref = category_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          category_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int SHAPE_FIELD_NUMBER = 5;
      private volatile java.lang.Object shape_;
      /**
       * <pre>
       *权限型
       * </pre>
       *
       * <code>string shape = 5;</code>
       * @return The shape.
       */
      @java.lang.Override
      public java.lang.String getShape() {
        java.lang.Object ref = shape_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          shape_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *权限型
       * </pre>
       *
       * <code>string shape = 5;</code>
       * @return The bytes for shape.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getShapeBytes() {
        java.lang.Object ref = shape_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          shape_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int POINT_FIELD_NUMBER = 6;
      private volatile java.lang.Object point_;
      /**
       * <pre>
       *权限点
       * </pre>
       *
       * <code>string point = 6;</code>
       * @return The point.
       */
      @java.lang.Override
      public java.lang.String getPoint() {
        java.lang.Object ref = point_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          point_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *权限点
       * </pre>
       *
       * <code>string point = 6;</code>
       * @return The bytes for point.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getPointBytes() {
        java.lang.Object ref = point_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          point_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int TYPE_FIELD_NUMBER = 7;
      private volatile java.lang.Object type_;
      /**
       * <pre>
       *权限类型
       * </pre>
       *
       * <code>string type = 7;</code>
       * @return The type.
       */
      @java.lang.Override
      public java.lang.String getType() {
        java.lang.Object ref = type_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          type_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *权限类型
       * </pre>
       *
       * <code>string type = 7;</code>
       * @return The bytes for type.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getTypeBytes() {
        java.lang.Object ref = type_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          type_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int ICON_FIELD_NUMBER = 8;
      private volatile java.lang.Object icon_;
      /**
       * <pre>
       *图标
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return The icon.
       */
      @java.lang.Override
      public java.lang.String getIcon() {
        java.lang.Object ref = icon_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          icon_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *图标
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return The bytes for icon.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getIconBytes() {
        java.lang.Object ref = icon_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          icon_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int SORT_VALUE_FIELD_NUMBER = 9;
      private int sortValue_;
      /**
       * <pre>
       *权重
       * </pre>
       *
       * <code>int32 sort_value = 9;</code>
       * @return The sortValue.
       */
      @java.lang.Override
      public int getSortValue() {
        return sortValue_;
      }

      public static final int API_FIELD_NUMBER = 10;
      private java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Api> api_;
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
       */
      @java.lang.Override
      public java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Api> getApiList() {
        return api_;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
       */
      @java.lang.Override
      public java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeAllReply.ApiOrBuilder>
          getApiOrBuilderList() {
        return api_;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
       */
      @java.lang.Override
      public int getApiCount() {
        return api_.size();
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
       */
      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeAllReply.Api getApi(int index) {
        return api_.get(index);
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
       */
      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeAllReply.ApiOrBuilder getApiOrBuilder(
          int index) {
        return api_.get(index);
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!getPrivilegeKeyBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, privilegeKey_);
        }
        if (!getNameBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
        }
        if (!getParentKeyBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 3, parentKey_);
        }
        if (!getCategoryBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 4, category_);
        }
        if (!getShapeBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 5, shape_);
        }
        if (!getPointBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 6, point_);
        }
        if (!getTypeBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 7, type_);
        }
        if (!getIconBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 8, icon_);
        }
        if (sortValue_ != 0) {
          output.writeInt32(9, sortValue_);
        }
        for (int i = 0; i < api_.size(); i++) {
          output.writeMessage(10, api_.get(i));
        }
        unknownFields.writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!getPrivilegeKeyBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, privilegeKey_);
        }
        if (!getNameBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
        }
        if (!getParentKeyBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, parentKey_);
        }
        if (!getCategoryBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, category_);
        }
        if (!getShapeBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, shape_);
        }
        if (!getPointBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, point_);
        }
        if (!getTypeBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, type_);
        }
        if (!getIconBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, icon_);
        }
        if (sortValue_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(9, sortValue_);
        }
        for (int i = 0; i < api_.size(); i++) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(10, api_.get(i));
        }
        size += unknownFields.getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege)) {
          return super.equals(obj);
        }
        PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege other = (PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege) obj;

        if (!getPrivilegeKey()
            .equals(other.getPrivilegeKey())) return false;
        if (!getName()
            .equals(other.getName())) return false;
        if (!getParentKey()
            .equals(other.getParentKey())) return false;
        if (!getCategory()
            .equals(other.getCategory())) return false;
        if (!getShape()
            .equals(other.getShape())) return false;
        if (!getPoint()
            .equals(other.getPoint())) return false;
        if (!getType()
            .equals(other.getType())) return false;
        if (!getIcon()
            .equals(other.getIcon())) return false;
        if (getSortValue()
            != other.getSortValue()) return false;
        if (!getApiList()
            .equals(other.getApiList())) return false;
        if (!unknownFields.equals(other.unknownFields)) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + PRIVILEGE_KEY_FIELD_NUMBER;
        hash = (53 * hash) + getPrivilegeKey().hashCode();
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
        hash = (37 * hash) + PARENT_KEY_FIELD_NUMBER;
        hash = (53 * hash) + getParentKey().hashCode();
        hash = (37 * hash) + CATEGORY_FIELD_NUMBER;
        hash = (53 * hash) + getCategory().hashCode();
        hash = (37 * hash) + SHAPE_FIELD_NUMBER;
        hash = (53 * hash) + getShape().hashCode();
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getType().hashCode();
        hash = (37 * hash) + ICON_FIELD_NUMBER;
        hash = (53 * hash) + getIcon().hashCode();
        hash = (37 * hash) + SORT_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getSortValue();
        if (getApiCount() > 0) {
          hash = (37 * hash) + API_FIELD_NUMBER;
          hash = (53 * hash) + getApiList().hashCode();
        }
        hash = (29 * hash) + unknownFields.hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code protos.GetUserPrivilegeAllReply.Privilege}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:protos.GetUserPrivilegeAllReply.Privilege)
          PrivilegeOuterClass.GetUserPrivilegeAllReply.PrivilegeOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_Privilege_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_Privilege_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.class, PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder.class);
        }

        // Construct using PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
            getApiFieldBuilder();
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          privilegeKey_ = "";

          name_ = "";

          parentKey_ = "";

          category_ = "";

          shape_ = "";

          point_ = "";

          type_ = "";

          icon_ = "";

          sortValue_ = 0;

          if (apiBuilder_ == null) {
            api_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            apiBuilder_.clear();
          }
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_Privilege_descriptor;
        }

        @java.lang.Override
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege getDefaultInstanceForType() {
          return PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.getDefaultInstance();
        }

        @java.lang.Override
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege build() {
          PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege buildPartial() {
          PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege result = new PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege(this);
          int from_bitField0_ = bitField0_;
          result.privilegeKey_ = privilegeKey_;
          result.name_ = name_;
          result.parentKey_ = parentKey_;
          result.category_ = category_;
          result.shape_ = shape_;
          result.point_ = point_;
          result.type_ = type_;
          result.icon_ = icon_;
          result.sortValue_ = sortValue_;
          if (apiBuilder_ == null) {
            if (((bitField0_ & 0x00000001) != 0)) {
              api_ = java.util.Collections.unmodifiableList(api_);
              bitField0_ = (bitField0_ & ~0x00000001);
            }
            result.api_ = api_;
          } else {
            result.api_ = apiBuilder_.build();
          }
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege) {
            return mergeFrom((PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege other) {
          if (other == PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.getDefaultInstance()) return this;
          if (!other.getPrivilegeKey().isEmpty()) {
            privilegeKey_ = other.privilegeKey_;
            onChanged();
          }
          if (!other.getName().isEmpty()) {
            name_ = other.name_;
            onChanged();
          }
          if (!other.getParentKey().isEmpty()) {
            parentKey_ = other.parentKey_;
            onChanged();
          }
          if (!other.getCategory().isEmpty()) {
            category_ = other.category_;
            onChanged();
          }
          if (!other.getShape().isEmpty()) {
            shape_ = other.shape_;
            onChanged();
          }
          if (!other.getPoint().isEmpty()) {
            point_ = other.point_;
            onChanged();
          }
          if (!other.getType().isEmpty()) {
            type_ = other.type_;
            onChanged();
          }
          if (!other.getIcon().isEmpty()) {
            icon_ = other.icon_;
            onChanged();
          }
          if (other.getSortValue() != 0) {
            setSortValue(other.getSortValue());
          }
          if (apiBuilder_ == null) {
            if (!other.api_.isEmpty()) {
              if (api_.isEmpty()) {
                api_ = other.api_;
                bitField0_ = (bitField0_ & ~0x00000001);
              } else {
                ensureApiIsMutable();
                api_.addAll(other.api_);
              }
              onChanged();
            }
          } else {
            if (!other.api_.isEmpty()) {
              if (apiBuilder_.isEmpty()) {
                apiBuilder_.dispose();
                apiBuilder_ = null;
                api_ = other.api_;
                bitField0_ = (bitField0_ & ~0x00000001);
                apiBuilder_ =
                  com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                     getApiFieldBuilder() : null;
              } else {
                apiBuilder_.addAllMessages(other.api_);
              }
            }
          }
          this.mergeUnknownFields(other.unknownFields);
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege parsedMessage = null;
          try {
            parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            parsedMessage = (PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege) e.getUnfinishedMessage();
            throw e.unwrapIOException();
          } finally {
            if (parsedMessage != null) {
              mergeFrom(parsedMessage);
            }
          }
          return this;
        }
        private int bitField0_;

        private java.lang.Object privilegeKey_ = "";
        /**
         * <pre>
         *权限唯一健
         * </pre>
         *
         * <code>string privilege_key = 1;</code>
         * @return The privilegeKey.
         */
        public java.lang.String getPrivilegeKey() {
          java.lang.Object ref = privilegeKey_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            privilegeKey_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *权限唯一健
         * </pre>
         *
         * <code>string privilege_key = 1;</code>
         * @return The bytes for privilegeKey.
         */
        public com.google.protobuf.ByteString
            getPrivilegeKeyBytes() {
          java.lang.Object ref = privilegeKey_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            privilegeKey_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *权限唯一健
         * </pre>
         *
         * <code>string privilege_key = 1;</code>
         * @param value The privilegeKey to set.
         * @return This builder for chaining.
         */
        public Builder setPrivilegeKey(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          privilegeKey_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限唯一健
         * </pre>
         *
         * <code>string privilege_key = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearPrivilegeKey() {

          privilegeKey_ = getDefaultInstance().getPrivilegeKey();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限唯一健
         * </pre>
         *
         * <code>string privilege_key = 1;</code>
         * @param value The bytes for privilegeKey to set.
         * @return This builder for chaining.
         */
        public Builder setPrivilegeKeyBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          privilegeKey_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object name_ = "";
        /**
         * <pre>
         *名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return The name.
         */
        public java.lang.String getName() {
          java.lang.Object ref = name_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            name_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return The bytes for name.
         */
        public com.google.protobuf.ByteString
            getNameBytes() {
          java.lang.Object ref = name_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            name_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @param value The name to set.
         * @return This builder for chaining.
         */
        public Builder setName(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          name_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearName() {

          name_ = getDefaultInstance().getName();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @param value The bytes for name to set.
         * @return This builder for chaining.
         */
        public Builder setNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          name_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object parentKey_ = "";
        /**
         * <pre>
         *父健
         * </pre>
         *
         * <code>string parent_key = 3;</code>
         * @return The parentKey.
         */
        public java.lang.String getParentKey() {
          java.lang.Object ref = parentKey_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            parentKey_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *父健
         * </pre>
         *
         * <code>string parent_key = 3;</code>
         * @return The bytes for parentKey.
         */
        public com.google.protobuf.ByteString
            getParentKeyBytes() {
          java.lang.Object ref = parentKey_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            parentKey_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *父健
         * </pre>
         *
         * <code>string parent_key = 3;</code>
         * @param value The parentKey to set.
         * @return This builder for chaining.
         */
        public Builder setParentKey(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          parentKey_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *父健
         * </pre>
         *
         * <code>string parent_key = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearParentKey() {

          parentKey_ = getDefaultInstance().getParentKey();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *父健
         * </pre>
         *
         * <code>string parent_key = 3;</code>
         * @param value The bytes for parentKey to set.
         * @return This builder for chaining.
         */
        public Builder setParentKeyBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          parentKey_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object category_ = "";
        /**
         * <pre>
         *权限类
         * </pre>
         *
         * <code>string category = 4;</code>
         * @return The category.
         */
        public java.lang.String getCategory() {
          java.lang.Object ref = category_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            category_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *权限类
         * </pre>
         *
         * <code>string category = 4;</code>
         * @return The bytes for category.
         */
        public com.google.protobuf.ByteString
            getCategoryBytes() {
          java.lang.Object ref = category_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            category_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *权限类
         * </pre>
         *
         * <code>string category = 4;</code>
         * @param value The category to set.
         * @return This builder for chaining.
         */
        public Builder setCategory(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          category_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限类
         * </pre>
         *
         * <code>string category = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearCategory() {

          category_ = getDefaultInstance().getCategory();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限类
         * </pre>
         *
         * <code>string category = 4;</code>
         * @param value The bytes for category to set.
         * @return This builder for chaining.
         */
        public Builder setCategoryBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          category_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object shape_ = "";
        /**
         * <pre>
         *权限型
         * </pre>
         *
         * <code>string shape = 5;</code>
         * @return The shape.
         */
        public java.lang.String getShape() {
          java.lang.Object ref = shape_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            shape_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *权限型
         * </pre>
         *
         * <code>string shape = 5;</code>
         * @return The bytes for shape.
         */
        public com.google.protobuf.ByteString
            getShapeBytes() {
          java.lang.Object ref = shape_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            shape_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *权限型
         * </pre>
         *
         * <code>string shape = 5;</code>
         * @param value The shape to set.
         * @return This builder for chaining.
         */
        public Builder setShape(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          shape_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限型
         * </pre>
         *
         * <code>string shape = 5;</code>
         * @return This builder for chaining.
         */
        public Builder clearShape() {

          shape_ = getDefaultInstance().getShape();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限型
         * </pre>
         *
         * <code>string shape = 5;</code>
         * @param value The bytes for shape to set.
         * @return This builder for chaining.
         */
        public Builder setShapeBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          shape_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object point_ = "";
        /**
         * <pre>
         *权限点
         * </pre>
         *
         * <code>string point = 6;</code>
         * @return The point.
         */
        public java.lang.String getPoint() {
          java.lang.Object ref = point_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            point_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *权限点
         * </pre>
         *
         * <code>string point = 6;</code>
         * @return The bytes for point.
         */
        public com.google.protobuf.ByteString
            getPointBytes() {
          java.lang.Object ref = point_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            point_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *权限点
         * </pre>
         *
         * <code>string point = 6;</code>
         * @param value The point to set.
         * @return This builder for chaining.
         */
        public Builder setPoint(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          point_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限点
         * </pre>
         *
         * <code>string point = 6;</code>
         * @return This builder for chaining.
         */
        public Builder clearPoint() {

          point_ = getDefaultInstance().getPoint();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限点
         * </pre>
         *
         * <code>string point = 6;</code>
         * @param value The bytes for point to set.
         * @return This builder for chaining.
         */
        public Builder setPointBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          point_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object type_ = "";
        /**
         * <pre>
         *权限类型
         * </pre>
         *
         * <code>string type = 7;</code>
         * @return The type.
         */
        public java.lang.String getType() {
          java.lang.Object ref = type_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            type_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *权限类型
         * </pre>
         *
         * <code>string type = 7;</code>
         * @return The bytes for type.
         */
        public com.google.protobuf.ByteString
            getTypeBytes() {
          java.lang.Object ref = type_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            type_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *权限类型
         * </pre>
         *
         * <code>string type = 7;</code>
         * @param value The type to set.
         * @return This builder for chaining.
         */
        public Builder setType(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          type_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限类型
         * </pre>
         *
         * <code>string type = 7;</code>
         * @return This builder for chaining.
         */
        public Builder clearType() {

          type_ = getDefaultInstance().getType();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限类型
         * </pre>
         *
         * <code>string type = 7;</code>
         * @param value The bytes for type to set.
         * @return This builder for chaining.
         */
        public Builder setTypeBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          type_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object icon_ = "";
        /**
         * <pre>
         *图标
         * </pre>
         *
         * <code>string icon = 8;</code>
         * @return The icon.
         */
        public java.lang.String getIcon() {
          java.lang.Object ref = icon_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            icon_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *图标
         * </pre>
         *
         * <code>string icon = 8;</code>
         * @return The bytes for icon.
         */
        public com.google.protobuf.ByteString
            getIconBytes() {
          java.lang.Object ref = icon_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            icon_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *图标
         * </pre>
         *
         * <code>string icon = 8;</code>
         * @param value The icon to set.
         * @return This builder for chaining.
         */
        public Builder setIcon(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          icon_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *图标
         * </pre>
         *
         * <code>string icon = 8;</code>
         * @return This builder for chaining.
         */
        public Builder clearIcon() {

          icon_ = getDefaultInstance().getIcon();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *图标
         * </pre>
         *
         * <code>string icon = 8;</code>
         * @param value The bytes for icon to set.
         * @return This builder for chaining.
         */
        public Builder setIconBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          icon_ = value;
          onChanged();
          return this;
        }

        private int sortValue_ ;
        /**
         * <pre>
         *权重
         * </pre>
         *
         * <code>int32 sort_value = 9;</code>
         * @return The sortValue.
         */
        @java.lang.Override
        public int getSortValue() {
          return sortValue_;
        }
        /**
         * <pre>
         *权重
         * </pre>
         *
         * <code>int32 sort_value = 9;</code>
         * @param value The sortValue to set.
         * @return This builder for chaining.
         */
        public Builder setSortValue(int value) {

          sortValue_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权重
         * </pre>
         *
         * <code>int32 sort_value = 9;</code>
         * @return This builder for chaining.
         */
        public Builder clearSortValue() {

          sortValue_ = 0;
          onChanged();
          return this;
        }

        private java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Api> api_ =
          java.util.Collections.emptyList();
        private void ensureApiIsMutable() {
          if (!((bitField0_ & 0x00000001) != 0)) {
            api_ = new java.util.ArrayList<PrivilegeOuterClass.GetUserPrivilegeAllReply.Api>(api_);
            bitField0_ |= 0x00000001;
           }
        }

        private com.google.protobuf.RepeatedFieldBuilderV3<
            PrivilegeOuterClass.GetUserPrivilegeAllReply.Api, PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder, PrivilegeOuterClass.GetUserPrivilegeAllReply.ApiOrBuilder> apiBuilder_;

        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Api> getApiList() {
          if (apiBuilder_ == null) {
            return java.util.Collections.unmodifiableList(api_);
          } else {
            return apiBuilder_.getMessageList();
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public int getApiCount() {
          if (apiBuilder_ == null) {
            return api_.size();
          } else {
            return apiBuilder_.getCount();
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.Api getApi(int index) {
          if (apiBuilder_ == null) {
            return api_.get(index);
          } else {
            return apiBuilder_.getMessage(index);
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public Builder setApi(
            int index, PrivilegeOuterClass.GetUserPrivilegeAllReply.Api value) {
          if (apiBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureApiIsMutable();
            api_.set(index, value);
            onChanged();
          } else {
            apiBuilder_.setMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public Builder setApi(
            int index, PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder builderForValue) {
          if (apiBuilder_ == null) {
            ensureApiIsMutable();
            api_.set(index, builderForValue.build());
            onChanged();
          } else {
            apiBuilder_.setMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public Builder addApi(PrivilegeOuterClass.GetUserPrivilegeAllReply.Api value) {
          if (apiBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureApiIsMutable();
            api_.add(value);
            onChanged();
          } else {
            apiBuilder_.addMessage(value);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public Builder addApi(
            int index, PrivilegeOuterClass.GetUserPrivilegeAllReply.Api value) {
          if (apiBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureApiIsMutable();
            api_.add(index, value);
            onChanged();
          } else {
            apiBuilder_.addMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public Builder addApi(
            PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder builderForValue) {
          if (apiBuilder_ == null) {
            ensureApiIsMutable();
            api_.add(builderForValue.build());
            onChanged();
          } else {
            apiBuilder_.addMessage(builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public Builder addApi(
            int index, PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder builderForValue) {
          if (apiBuilder_ == null) {
            ensureApiIsMutable();
            api_.add(index, builderForValue.build());
            onChanged();
          } else {
            apiBuilder_.addMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public Builder addAllApi(
            java.lang.Iterable<? extends PrivilegeOuterClass.GetUserPrivilegeAllReply.Api> values) {
          if (apiBuilder_ == null) {
            ensureApiIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, api_);
            onChanged();
          } else {
            apiBuilder_.addAllMessages(values);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public Builder clearApi() {
          if (apiBuilder_ == null) {
            api_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000001);
            onChanged();
          } else {
            apiBuilder_.clear();
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public Builder removeApi(int index) {
          if (apiBuilder_ == null) {
            ensureApiIsMutable();
            api_.remove(index);
            onChanged();
          } else {
            apiBuilder_.remove(index);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder getApiBuilder(
            int index) {
          return getApiFieldBuilder().getBuilder(index);
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.ApiOrBuilder getApiOrBuilder(
            int index) {
          if (apiBuilder_ == null) {
            return api_.get(index);  } else {
            return apiBuilder_.getMessageOrBuilder(index);
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeAllReply.ApiOrBuilder>
             getApiOrBuilderList() {
          if (apiBuilder_ != null) {
            return apiBuilder_.getMessageOrBuilderList();
          } else {
            return java.util.Collections.unmodifiableList(api_);
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder addApiBuilder() {
          return getApiFieldBuilder().addBuilder(
              PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.getDefaultInstance());
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder addApiBuilder(
            int index) {
          return getApiFieldBuilder().addBuilder(
              index, PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.getDefaultInstance());
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeAllReply.Api api = 10;</code>
         */
        public java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder>
             getApiBuilderList() {
          return getApiFieldBuilder().getBuilderList();
        }
        private com.google.protobuf.RepeatedFieldBuilderV3<
            PrivilegeOuterClass.GetUserPrivilegeAllReply.Api, PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder, PrivilegeOuterClass.GetUserPrivilegeAllReply.ApiOrBuilder>
            getApiFieldBuilder() {
          if (apiBuilder_ == null) {
            apiBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                PrivilegeOuterClass.GetUserPrivilegeAllReply.Api, PrivilegeOuterClass.GetUserPrivilegeAllReply.Api.Builder, PrivilegeOuterClass.GetUserPrivilegeAllReply.ApiOrBuilder>(
                    api_,
                    ((bitField0_ & 0x00000001) != 0),
                    getParentForChildren(),
                    isClean());
            api_ = null;
          }
          return apiBuilder_;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:protos.GetUserPrivilegeAllReply.Privilege)
      }

      // @@protoc_insertion_point(class_scope:protos.GetUserPrivilegeAllReply.Privilege)
      private static final PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege();
      }

      public static PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Privilege>
          PARSER = new com.google.protobuf.AbstractParser<Privilege>() {
        @java.lang.Override
        public Privilege parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return new Privilege(input, extensionRegistry);
        }
      };

      public static com.google.protobuf.Parser<Privilege> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Privilege> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int PRIVILEGE_FIELD_NUMBER = 1;
    private java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege> privilege_;
    /**
     * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
     */
    @java.lang.Override
    public java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege> getPrivilegeList() {
      return privilege_;
    }
    /**
     * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeAllReply.PrivilegeOrBuilder>
        getPrivilegeOrBuilderList() {
      return privilege_;
    }
    /**
     * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
     */
    @java.lang.Override
    public int getPrivilegeCount() {
      return privilege_.size();
    }
    /**
     * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
     */
    @java.lang.Override
    public PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege getPrivilege(int index) {
      return privilege_.get(index);
    }
    /**
     * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
     */
    @java.lang.Override
    public PrivilegeOuterClass.GetUserPrivilegeAllReply.PrivilegeOrBuilder getPrivilegeOrBuilder(
        int index) {
      return privilege_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < privilege_.size(); i++) {
        output.writeMessage(1, privilege_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < privilege_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, privilege_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PrivilegeOuterClass.GetUserPrivilegeAllReply)) {
        return super.equals(obj);
      }
      PrivilegeOuterClass.GetUserPrivilegeAllReply other = (PrivilegeOuterClass.GetUserPrivilegeAllReply) obj;

      if (!getPrivilegeList()
          .equals(other.getPrivilegeList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPrivilegeCount() > 0) {
        hash = (37 * hash) + PRIVILEGE_FIELD_NUMBER;
        hash = (53 * hash) + getPrivilegeList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeAllReply parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PrivilegeOuterClass.GetUserPrivilegeAllReply prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protos.GetUserPrivilegeAllReply}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:protos.GetUserPrivilegeAllReply)
        PrivilegeOuterClass.GetUserPrivilegeAllReplyOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PrivilegeOuterClass.GetUserPrivilegeAllReply.class, PrivilegeOuterClass.GetUserPrivilegeAllReply.Builder.class);
      }

      // Construct using PrivilegeOuterClass.GetUserPrivilegeAllReply.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPrivilegeFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (privilegeBuilder_ == null) {
          privilege_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          privilegeBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeAllReply_descriptor;
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeAllReply getDefaultInstanceForType() {
        return PrivilegeOuterClass.GetUserPrivilegeAllReply.getDefaultInstance();
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeAllReply build() {
        PrivilegeOuterClass.GetUserPrivilegeAllReply result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeAllReply buildPartial() {
        PrivilegeOuterClass.GetUserPrivilegeAllReply result = new PrivilegeOuterClass.GetUserPrivilegeAllReply(this);
        int from_bitField0_ = bitField0_;
        if (privilegeBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            privilege_ = java.util.Collections.unmodifiableList(privilege_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.privilege_ = privilege_;
        } else {
          result.privilege_ = privilegeBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PrivilegeOuterClass.GetUserPrivilegeAllReply) {
          return mergeFrom((PrivilegeOuterClass.GetUserPrivilegeAllReply)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PrivilegeOuterClass.GetUserPrivilegeAllReply other) {
        if (other == PrivilegeOuterClass.GetUserPrivilegeAllReply.getDefaultInstance()) return this;
        if (privilegeBuilder_ == null) {
          if (!other.privilege_.isEmpty()) {
            if (privilege_.isEmpty()) {
              privilege_ = other.privilege_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePrivilegeIsMutable();
              privilege_.addAll(other.privilege_);
            }
            onChanged();
          }
        } else {
          if (!other.privilege_.isEmpty()) {
            if (privilegeBuilder_.isEmpty()) {
              privilegeBuilder_.dispose();
              privilegeBuilder_ = null;
              privilege_ = other.privilege_;
              bitField0_ = (bitField0_ & ~0x00000001);
              privilegeBuilder_ =
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPrivilegeFieldBuilder() : null;
            } else {
              privilegeBuilder_.addAllMessages(other.privilege_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        PrivilegeOuterClass.GetUserPrivilegeAllReply parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (PrivilegeOuterClass.GetUserPrivilegeAllReply) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege> privilege_ =
        java.util.Collections.emptyList();
      private void ensurePrivilegeIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          privilege_ = new java.util.ArrayList<PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege>(privilege_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege, PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder, PrivilegeOuterClass.GetUserPrivilegeAllReply.PrivilegeOrBuilder> privilegeBuilder_;

      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege> getPrivilegeList() {
        if (privilegeBuilder_ == null) {
          return java.util.Collections.unmodifiableList(privilege_);
        } else {
          return privilegeBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public int getPrivilegeCount() {
        if (privilegeBuilder_ == null) {
          return privilege_.size();
        } else {
          return privilegeBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege getPrivilege(int index) {
        if (privilegeBuilder_ == null) {
          return privilege_.get(index);
        } else {
          return privilegeBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public Builder setPrivilege(
          int index, PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege value) {
        if (privilegeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegeIsMutable();
          privilege_.set(index, value);
          onChanged();
        } else {
          privilegeBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public Builder setPrivilege(
          int index, PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder builderForValue) {
        if (privilegeBuilder_ == null) {
          ensurePrivilegeIsMutable();
          privilege_.set(index, builderForValue.build());
          onChanged();
        } else {
          privilegeBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public Builder addPrivilege(PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege value) {
        if (privilegeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegeIsMutable();
          privilege_.add(value);
          onChanged();
        } else {
          privilegeBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public Builder addPrivilege(
          int index, PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege value) {
        if (privilegeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegeIsMutable();
          privilege_.add(index, value);
          onChanged();
        } else {
          privilegeBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public Builder addPrivilege(
          PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder builderForValue) {
        if (privilegeBuilder_ == null) {
          ensurePrivilegeIsMutable();
          privilege_.add(builderForValue.build());
          onChanged();
        } else {
          privilegeBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public Builder addPrivilege(
          int index, PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder builderForValue) {
        if (privilegeBuilder_ == null) {
          ensurePrivilegeIsMutable();
          privilege_.add(index, builderForValue.build());
          onChanged();
        } else {
          privilegeBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public Builder addAllPrivilege(
          java.lang.Iterable<? extends PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege> values) {
        if (privilegeBuilder_ == null) {
          ensurePrivilegeIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, privilege_);
          onChanged();
        } else {
          privilegeBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public Builder clearPrivilege() {
        if (privilegeBuilder_ == null) {
          privilege_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          privilegeBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public Builder removePrivilege(int index) {
        if (privilegeBuilder_ == null) {
          ensurePrivilegeIsMutable();
          privilege_.remove(index);
          onChanged();
        } else {
          privilegeBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder getPrivilegeBuilder(
          int index) {
        return getPrivilegeFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public PrivilegeOuterClass.GetUserPrivilegeAllReply.PrivilegeOrBuilder getPrivilegeOrBuilder(
          int index) {
        if (privilegeBuilder_ == null) {
          return privilege_.get(index);  } else {
          return privilegeBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeAllReply.PrivilegeOrBuilder>
           getPrivilegeOrBuilderList() {
        if (privilegeBuilder_ != null) {
          return privilegeBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(privilege_);
        }
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder addPrivilegeBuilder() {
        return getPrivilegeFieldBuilder().addBuilder(
            PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.getDefaultInstance());
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder addPrivilegeBuilder(
          int index) {
        return getPrivilegeFieldBuilder().addBuilder(
            index, PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.getDefaultInstance());
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeAllReply.Privilege privilege = 1;</code>
       */
      public java.util.List<PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder>
           getPrivilegeBuilderList() {
        return getPrivilegeFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege, PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder, PrivilegeOuterClass.GetUserPrivilegeAllReply.PrivilegeOrBuilder>
          getPrivilegeFieldBuilder() {
        if (privilegeBuilder_ == null) {
          privilegeBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege, PrivilegeOuterClass.GetUserPrivilegeAllReply.Privilege.Builder, PrivilegeOuterClass.GetUserPrivilegeAllReply.PrivilegeOrBuilder>(
                  privilege_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          privilege_ = null;
        }
        return privilegeBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:protos.GetUserPrivilegeAllReply)
    }

    // @@protoc_insertion_point(class_scope:protos.GetUserPrivilegeAllReply)
    private static final PrivilegeOuterClass.GetUserPrivilegeAllReply DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new PrivilegeOuterClass.GetUserPrivilegeAllReply();
    }

    public static PrivilegeOuterClass.GetUserPrivilegeAllReply getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetUserPrivilegeAllReply>
        PARSER = new com.google.protobuf.AbstractParser<GetUserPrivilegeAllReply>() {
      @java.lang.Override
      public GetUserPrivilegeAllReply parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetUserPrivilegeAllReply(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetUserPrivilegeAllReply> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetUserPrivilegeAllReply> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public PrivilegeOuterClass.GetUserPrivilegeAllReply getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetUserPrivilegeTreeParamOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protos.GetUserPrivilegeTreeParam)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *域控
     * </pre>
     *
     * <code>string domain = 1;</code>
     * @return The domain.
     */
    java.lang.String getDomain();
    /**
     * <pre>
     *域控
     * </pre>
     *
     * <code>string domain = 1;</code>
     * @return The bytes for domain.
     */
    com.google.protobuf.ByteString
        getDomainBytes();

    /**
     * <pre>
     * 站点唯一键
     * </pre>
     *
     * <code>string siteKey = 2;</code>
     * @return The siteKey.
     */
    java.lang.String getSiteKey();
    /**
     * <pre>
     * 站点唯一键
     * </pre>
     *
     * <code>string siteKey = 2;</code>
     * @return The bytes for siteKey.
     */
    com.google.protobuf.ByteString
        getSiteKeyBytes();
  }
  /**
   * Protobuf type {@code protos.GetUserPrivilegeTreeParam}
   */
  public static final class GetUserPrivilegeTreeParam extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protos.GetUserPrivilegeTreeParam)
      GetUserPrivilegeTreeParamOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetUserPrivilegeTreeParam.newBuilder() to construct.
    private GetUserPrivilegeTreeParam(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetUserPrivilegeTreeParam() {
      domain_ = "";
      siteKey_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetUserPrivilegeTreeParam();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetUserPrivilegeTreeParam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();

              domain_ = s;
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              siteKey_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeParam_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeParam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PrivilegeOuterClass.GetUserPrivilegeTreeParam.class, PrivilegeOuterClass.GetUserPrivilegeTreeParam.Builder.class);
    }

    public static final int DOMAIN_FIELD_NUMBER = 1;
    private volatile java.lang.Object domain_;
    /**
     * <pre>
     *域控
     * </pre>
     *
     * <code>string domain = 1;</code>
     * @return The domain.
     */
    @java.lang.Override
    public java.lang.String getDomain() {
      java.lang.Object ref = domain_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        domain_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *域控
     * </pre>
     *
     * <code>string domain = 1;</code>
     * @return The bytes for domain.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDomainBytes() {
      java.lang.Object ref = domain_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        domain_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SITEKEY_FIELD_NUMBER = 2;
    private volatile java.lang.Object siteKey_;
    /**
     * <pre>
     * 站点唯一键
     * </pre>
     *
     * <code>string siteKey = 2;</code>
     * @return The siteKey.
     */
    @java.lang.Override
    public java.lang.String getSiteKey() {
      java.lang.Object ref = siteKey_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        siteKey_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 站点唯一键
     * </pre>
     *
     * <code>string siteKey = 2;</code>
     * @return The bytes for siteKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSiteKeyBytes() {
      java.lang.Object ref = siteKey_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        siteKey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!getDomainBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, domain_);
      }
      if (!getSiteKeyBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, siteKey_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!getDomainBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, domain_);
      }
      if (!getSiteKeyBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, siteKey_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PrivilegeOuterClass.GetUserPrivilegeTreeParam)) {
        return super.equals(obj);
      }
      PrivilegeOuterClass.GetUserPrivilegeTreeParam other = (PrivilegeOuterClass.GetUserPrivilegeTreeParam) obj;

      if (!getDomain()
          .equals(other.getDomain())) return false;
      if (!getSiteKey()
          .equals(other.getSiteKey())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + DOMAIN_FIELD_NUMBER;
      hash = (53 * hash) + getDomain().hashCode();
      hash = (37 * hash) + SITEKEY_FIELD_NUMBER;
      hash = (53 * hash) + getSiteKey().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PrivilegeOuterClass.GetUserPrivilegeTreeParam prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protos.GetUserPrivilegeTreeParam}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:protos.GetUserPrivilegeTreeParam)
        PrivilegeOuterClass.GetUserPrivilegeTreeParamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeParam_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeParam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PrivilegeOuterClass.GetUserPrivilegeTreeParam.class, PrivilegeOuterClass.GetUserPrivilegeTreeParam.Builder.class);
      }

      // Construct using PrivilegeOuterClass.GetUserPrivilegeTreeParam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        domain_ = "";

        siteKey_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeParam_descriptor;
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeParam getDefaultInstanceForType() {
        return PrivilegeOuterClass.GetUserPrivilegeTreeParam.getDefaultInstance();
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeParam build() {
        PrivilegeOuterClass.GetUserPrivilegeTreeParam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeParam buildPartial() {
        PrivilegeOuterClass.GetUserPrivilegeTreeParam result = new PrivilegeOuterClass.GetUserPrivilegeTreeParam(this);
        result.domain_ = domain_;
        result.siteKey_ = siteKey_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PrivilegeOuterClass.GetUserPrivilegeTreeParam) {
          return mergeFrom((PrivilegeOuterClass.GetUserPrivilegeTreeParam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PrivilegeOuterClass.GetUserPrivilegeTreeParam other) {
        if (other == PrivilegeOuterClass.GetUserPrivilegeTreeParam.getDefaultInstance()) return this;
        if (!other.getDomain().isEmpty()) {
          domain_ = other.domain_;
          onChanged();
        }
        if (!other.getSiteKey().isEmpty()) {
          siteKey_ = other.siteKey_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        PrivilegeOuterClass.GetUserPrivilegeTreeParam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (PrivilegeOuterClass.GetUserPrivilegeTreeParam) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private java.lang.Object domain_ = "";
      /**
       * <pre>
       *域控
       * </pre>
       *
       * <code>string domain = 1;</code>
       * @return The domain.
       */
      public java.lang.String getDomain() {
        java.lang.Object ref = domain_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          domain_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *域控
       * </pre>
       *
       * <code>string domain = 1;</code>
       * @return The bytes for domain.
       */
      public com.google.protobuf.ByteString
          getDomainBytes() {
        java.lang.Object ref = domain_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          domain_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *域控
       * </pre>
       *
       * <code>string domain = 1;</code>
       * @param value The domain to set.
       * @return This builder for chaining.
       */
      public Builder setDomain(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        domain_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *域控
       * </pre>
       *
       * <code>string domain = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDomain() {

        domain_ = getDefaultInstance().getDomain();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *域控
       * </pre>
       *
       * <code>string domain = 1;</code>
       * @param value The bytes for domain to set.
       * @return This builder for chaining.
       */
      public Builder setDomainBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        domain_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object siteKey_ = "";
      /**
       * <pre>
       * 站点唯一键
       * </pre>
       *
       * <code>string siteKey = 2;</code>
       * @return The siteKey.
       */
      public java.lang.String getSiteKey() {
        java.lang.Object ref = siteKey_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          siteKey_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 站点唯一键
       * </pre>
       *
       * <code>string siteKey = 2;</code>
       * @return The bytes for siteKey.
       */
      public com.google.protobuf.ByteString
          getSiteKeyBytes() {
        java.lang.Object ref = siteKey_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          siteKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 站点唯一键
       * </pre>
       *
       * <code>string siteKey = 2;</code>
       * @param value The siteKey to set.
       * @return This builder for chaining.
       */
      public Builder setSiteKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }

        siteKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 站点唯一键
       * </pre>
       *
       * <code>string siteKey = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSiteKey() {

        siteKey_ = getDefaultInstance().getSiteKey();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 站点唯一键
       * </pre>
       *
       * <code>string siteKey = 2;</code>
       * @param value The bytes for siteKey to set.
       * @return This builder for chaining.
       */
      public Builder setSiteKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

        siteKey_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:protos.GetUserPrivilegeTreeParam)
    }

    // @@protoc_insertion_point(class_scope:protos.GetUserPrivilegeTreeParam)
    private static final PrivilegeOuterClass.GetUserPrivilegeTreeParam DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new PrivilegeOuterClass.GetUserPrivilegeTreeParam();
    }

    public static PrivilegeOuterClass.GetUserPrivilegeTreeParam getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetUserPrivilegeTreeParam>
        PARSER = new com.google.protobuf.AbstractParser<GetUserPrivilegeTreeParam>() {
      @java.lang.Override
      public GetUserPrivilegeTreeParam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetUserPrivilegeTreeParam(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetUserPrivilegeTreeParam> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetUserPrivilegeTreeParam> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public PrivilegeOuterClass.GetUserPrivilegeTreeParam getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GetUserPrivilegeTreeReplyOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protos.GetUserPrivilegeTreeReply)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
     */
    java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege>
        getPrivilegeList();
    /**
     * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
     */
    PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege getPrivilege(int index);
    /**
     * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
     */
    int getPrivilegeCount();
    /**
     * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
     */
    java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder>
        getPrivilegeOrBuilderList();
    /**
     * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
     */
    PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder getPrivilegeOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protos.GetUserPrivilegeTreeReply}
   */
  public static final class GetUserPrivilegeTreeReply extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protos.GetUserPrivilegeTreeReply)
      GetUserPrivilegeTreeReplyOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use GetUserPrivilegeTreeReply.newBuilder() to construct.
    private GetUserPrivilegeTreeReply(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private GetUserPrivilegeTreeReply() {
      privilege_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new GetUserPrivilegeTreeReply();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private GetUserPrivilegeTreeReply(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                privilege_ = new java.util.ArrayList<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege>();
                mutable_bitField0_ |= 0x00000001;
              }
              privilege_.add(
                  input.readMessage(PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          privilege_ = java.util.Collections.unmodifiableList(privilege_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PrivilegeOuterClass.GetUserPrivilegeTreeReply.class, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Builder.class);
    }

    public interface ApiOrBuilder extends
        // @@protoc_insertion_point(interface_extends:protos.GetUserPrivilegeTreeReply.Api)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 接口唯一键
       * </pre>
       *
       * <code>string api_key = 1;</code>
       * @return The apiKey.
       */
      java.lang.String getApiKey();
      /**
       * <pre>
       * 接口唯一键
       * </pre>
       *
       * <code>string api_key = 1;</code>
       * @return The bytes for apiKey.
       */
      com.google.protobuf.ByteString
          getApiKeyBytes();

      /**
       * <pre>
       *接口名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      java.lang.String getName();
      /**
       * <pre>
       *接口名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      com.google.protobuf.ByteString
          getNameBytes();

      /**
       * <pre>
       *地址
       * </pre>
       *
       * <code>string url = 3;</code>
       * @return The url.
       */
      java.lang.String getUrl();
      /**
       * <pre>
       *地址
       * </pre>
       *
       * <code>string url = 3;</code>
       * @return The bytes for url.
       */
      com.google.protobuf.ByteString
          getUrlBytes();

      /**
       * <pre>
       *方法
       * </pre>
       *
       * <code>string method = 4;</code>
       * @return The method.
       */
      java.lang.String getMethod();
      /**
       * <pre>
       *方法
       * </pre>
       *
       * <code>string method = 4;</code>
       * @return The bytes for method.
       */
      com.google.protobuf.ByteString
          getMethodBytes();
    }
    /**
     * Protobuf type {@code protos.GetUserPrivilegeTreeReply.Api}
     */
    public static final class Api extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:protos.GetUserPrivilegeTreeReply.Api)
        ApiOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Api.newBuilder() to construct.
      private Api(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Api() {
        apiKey_ = "";
        name_ = "";
        url_ = "";
        method_ = "";
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Api();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      private Api(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        this();
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        com.google.protobuf.UnknownFieldSet.Builder unknownFields =
            com.google.protobuf.UnknownFieldSet.newBuilder();
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                java.lang.String s = input.readStringRequireUtf8();

                apiKey_ = s;
                break;
              }
              case 18: {
                java.lang.String s = input.readStringRequireUtf8();

                name_ = s;
                break;
              }
              case 26: {
                java.lang.String s = input.readStringRequireUtf8();

                url_ = s;
                break;
              }
              case 34: {
                java.lang.String s = input.readStringRequireUtf8();

                method_ = s;
                break;
              }
              default: {
                if (!parseUnknownField(
                    input, unknownFields, extensionRegistry, tag)) {
                  done = true;
                }
                break;
              }
            }
          }
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(this);
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e).setUnfinishedMessage(this);
        } finally {
          this.unknownFields = unknownFields.build();
          makeExtensionsImmutable();
        }
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_Api_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_Api_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.class, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder.class);
      }

      public static final int API_KEY_FIELD_NUMBER = 1;
      private volatile java.lang.Object apiKey_;
      /**
       * <pre>
       * 接口唯一键
       * </pre>
       *
       * <code>string api_key = 1;</code>
       * @return The apiKey.
       */
      @java.lang.Override
      public java.lang.String getApiKey() {
        java.lang.Object ref = apiKey_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          apiKey_ = s;
          return s;
        }
      }
      /**
       * <pre>
       * 接口唯一键
       * </pre>
       *
       * <code>string api_key = 1;</code>
       * @return The bytes for apiKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getApiKeyBytes() {
        java.lang.Object ref = apiKey_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          apiKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int NAME_FIELD_NUMBER = 2;
      private volatile java.lang.Object name_;
      /**
       * <pre>
       *接口名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      @java.lang.Override
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *接口名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int URL_FIELD_NUMBER = 3;
      private volatile java.lang.Object url_;
      /**
       * <pre>
       *地址
       * </pre>
       *
       * <code>string url = 3;</code>
       * @return The url.
       */
      @java.lang.Override
      public java.lang.String getUrl() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          url_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *地址
       * </pre>
       *
       * <code>string url = 3;</code>
       * @return The bytes for url.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getUrlBytes() {
        java.lang.Object ref = url_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          url_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int METHOD_FIELD_NUMBER = 4;
      private volatile java.lang.Object method_;
      /**
       * <pre>
       *方法
       * </pre>
       *
       * <code>string method = 4;</code>
       * @return The method.
       */
      @java.lang.Override
      public java.lang.String getMethod() {
        java.lang.Object ref = method_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          method_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *方法
       * </pre>
       *
       * <code>string method = 4;</code>
       * @return The bytes for method.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getMethodBytes() {
        java.lang.Object ref = method_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          method_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!getApiKeyBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, apiKey_);
        }
        if (!getNameBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
        }
        if (!getUrlBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 3, url_);
        }
        if (!getMethodBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 4, method_);
        }
        unknownFields.writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!getApiKeyBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, apiKey_);
        }
        if (!getNameBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
        }
        if (!getUrlBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, url_);
        }
        if (!getMethodBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, method_);
        }
        size += unknownFields.getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api)) {
          return super.equals(obj);
        }
        PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api other = (PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api) obj;

        if (!getApiKey()
            .equals(other.getApiKey())) return false;
        if (!getName()
            .equals(other.getName())) return false;
        if (!getUrl()
            .equals(other.getUrl())) return false;
        if (!getMethod()
            .equals(other.getMethod())) return false;
        if (!unknownFields.equals(other.unknownFields)) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + API_KEY_FIELD_NUMBER;
        hash = (53 * hash) + getApiKey().hashCode();
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
        hash = (37 * hash) + URL_FIELD_NUMBER;
        hash = (53 * hash) + getUrl().hashCode();
        hash = (37 * hash) + METHOD_FIELD_NUMBER;
        hash = (53 * hash) + getMethod().hashCode();
        hash = (29 * hash) + unknownFields.hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code protos.GetUserPrivilegeTreeReply.Api}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:protos.GetUserPrivilegeTreeReply.Api)
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.ApiOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_Api_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_Api_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.class, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder.class);
        }

        // Construct using PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          apiKey_ = "";

          name_ = "";

          url_ = "";

          method_ = "";

          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_Api_descriptor;
        }

        @java.lang.Override
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api getDefaultInstanceForType() {
          return PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.getDefaultInstance();
        }

        @java.lang.Override
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api build() {
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api buildPartial() {
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api result = new PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api(this);
          result.apiKey_ = apiKey_;
          result.name_ = name_;
          result.url_ = url_;
          result.method_ = method_;
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api) {
            return mergeFrom((PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api other) {
          if (other == PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.getDefaultInstance()) return this;
          if (!other.getApiKey().isEmpty()) {
            apiKey_ = other.apiKey_;
            onChanged();
          }
          if (!other.getName().isEmpty()) {
            name_ = other.name_;
            onChanged();
          }
          if (!other.getUrl().isEmpty()) {
            url_ = other.url_;
            onChanged();
          }
          if (!other.getMethod().isEmpty()) {
            method_ = other.method_;
            onChanged();
          }
          this.mergeUnknownFields(other.unknownFields);
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api parsedMessage = null;
          try {
            parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            parsedMessage = (PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api) e.getUnfinishedMessage();
            throw e.unwrapIOException();
          } finally {
            if (parsedMessage != null) {
              mergeFrom(parsedMessage);
            }
          }
          return this;
        }

        private java.lang.Object apiKey_ = "";
        /**
         * <pre>
         * 接口唯一键
         * </pre>
         *
         * <code>string api_key = 1;</code>
         * @return The apiKey.
         */
        public java.lang.String getApiKey() {
          java.lang.Object ref = apiKey_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            apiKey_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         * 接口唯一键
         * </pre>
         *
         * <code>string api_key = 1;</code>
         * @return The bytes for apiKey.
         */
        public com.google.protobuf.ByteString
            getApiKeyBytes() {
          java.lang.Object ref = apiKey_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            apiKey_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         * 接口唯一键
         * </pre>
         *
         * <code>string api_key = 1;</code>
         * @param value The apiKey to set.
         * @return This builder for chaining.
         */
        public Builder setApiKey(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          apiKey_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 接口唯一键
         * </pre>
         *
         * <code>string api_key = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearApiKey() {

          apiKey_ = getDefaultInstance().getApiKey();
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 接口唯一键
         * </pre>
         *
         * <code>string api_key = 1;</code>
         * @param value The bytes for apiKey to set.
         * @return This builder for chaining.
         */
        public Builder setApiKeyBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          apiKey_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object name_ = "";
        /**
         * <pre>
         *接口名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return The name.
         */
        public java.lang.String getName() {
          java.lang.Object ref = name_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            name_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *接口名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return The bytes for name.
         */
        public com.google.protobuf.ByteString
            getNameBytes() {
          java.lang.Object ref = name_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            name_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *接口名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @param value The name to set.
         * @return This builder for chaining.
         */
        public Builder setName(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          name_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *接口名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearName() {

          name_ = getDefaultInstance().getName();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *接口名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @param value The bytes for name to set.
         * @return This builder for chaining.
         */
        public Builder setNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          name_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object url_ = "";
        /**
         * <pre>
         *地址
         * </pre>
         *
         * <code>string url = 3;</code>
         * @return The url.
         */
        public java.lang.String getUrl() {
          java.lang.Object ref = url_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            url_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *地址
         * </pre>
         *
         * <code>string url = 3;</code>
         * @return The bytes for url.
         */
        public com.google.protobuf.ByteString
            getUrlBytes() {
          java.lang.Object ref = url_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            url_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *地址
         * </pre>
         *
         * <code>string url = 3;</code>
         * @param value The url to set.
         * @return This builder for chaining.
         */
        public Builder setUrl(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          url_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *地址
         * </pre>
         *
         * <code>string url = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearUrl() {

          url_ = getDefaultInstance().getUrl();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *地址
         * </pre>
         *
         * <code>string url = 3;</code>
         * @param value The bytes for url to set.
         * @return This builder for chaining.
         */
        public Builder setUrlBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          url_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object method_ = "";
        /**
         * <pre>
         *方法
         * </pre>
         *
         * <code>string method = 4;</code>
         * @return The method.
         */
        public java.lang.String getMethod() {
          java.lang.Object ref = method_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            method_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *方法
         * </pre>
         *
         * <code>string method = 4;</code>
         * @return The bytes for method.
         */
        public com.google.protobuf.ByteString
            getMethodBytes() {
          java.lang.Object ref = method_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            method_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *方法
         * </pre>
         *
         * <code>string method = 4;</code>
         * @param value The method to set.
         * @return This builder for chaining.
         */
        public Builder setMethod(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          method_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *方法
         * </pre>
         *
         * <code>string method = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearMethod() {

          method_ = getDefaultInstance().getMethod();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *方法
         * </pre>
         *
         * <code>string method = 4;</code>
         * @param value The bytes for method to set.
         * @return This builder for chaining.
         */
        public Builder setMethodBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          method_ = value;
          onChanged();
          return this;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:protos.GetUserPrivilegeTreeReply.Api)
      }

      // @@protoc_insertion_point(class_scope:protos.GetUserPrivilegeTreeReply.Api)
      private static final PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api();
      }

      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Api>
          PARSER = new com.google.protobuf.AbstractParser<Api>() {
        @java.lang.Override
        public Api parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return new Api(input, extensionRegistry);
        }
      };

      public static com.google.protobuf.Parser<Api> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Api> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface PrivilegeOrBuilder extends
        // @@protoc_insertion_point(interface_extends:protos.GetUserPrivilegeTreeReply.Privilege)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       *权限唯一健
       * </pre>
       *
       * <code>string privilege_key = 1;</code>
       * @return The privilegeKey.
       */
      java.lang.String getPrivilegeKey();
      /**
       * <pre>
       *权限唯一健
       * </pre>
       *
       * <code>string privilege_key = 1;</code>
       * @return The bytes for privilegeKey.
       */
      com.google.protobuf.ByteString
          getPrivilegeKeyBytes();

      /**
       * <pre>
       *名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      java.lang.String getName();
      /**
       * <pre>
       *名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      com.google.protobuf.ByteString
          getNameBytes();

      /**
       * <pre>
       *父健
       * </pre>
       *
       * <code>string parent_key = 3;</code>
       * @return The parentKey.
       */
      java.lang.String getParentKey();
      /**
       * <pre>
       *父健
       * </pre>
       *
       * <code>string parent_key = 3;</code>
       * @return The bytes for parentKey.
       */
      com.google.protobuf.ByteString
          getParentKeyBytes();

      /**
       * <pre>
       *权限类
       * </pre>
       *
       * <code>string category = 4;</code>
       * @return The category.
       */
      java.lang.String getCategory();
      /**
       * <pre>
       *权限类
       * </pre>
       *
       * <code>string category = 4;</code>
       * @return The bytes for category.
       */
      com.google.protobuf.ByteString
          getCategoryBytes();

      /**
       * <pre>
       *权限型
       * </pre>
       *
       * <code>string shape = 5;</code>
       * @return The shape.
       */
      java.lang.String getShape();
      /**
       * <pre>
       *权限型
       * </pre>
       *
       * <code>string shape = 5;</code>
       * @return The bytes for shape.
       */
      com.google.protobuf.ByteString
          getShapeBytes();

      /**
       * <pre>
       *权限点
       * </pre>
       *
       * <code>string point = 6;</code>
       * @return The point.
       */
      java.lang.String getPoint();
      /**
       * <pre>
       *权限点
       * </pre>
       *
       * <code>string point = 6;</code>
       * @return The bytes for point.
       */
      com.google.protobuf.ByteString
          getPointBytes();

      /**
       * <pre>
       *权限类型
       * </pre>
       *
       * <code>string type = 7;</code>
       * @return The type.
       */
      java.lang.String getType();
      /**
       * <pre>
       *权限类型
       * </pre>
       *
       * <code>string type = 7;</code>
       * @return The bytes for type.
       */
      com.google.protobuf.ByteString
          getTypeBytes();

      /**
       * <pre>
       *图标
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return The icon.
       */
      java.lang.String getIcon();
      /**
       * <pre>
       *图标
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return The bytes for icon.
       */
      com.google.protobuf.ByteString
          getIconBytes();

      /**
       * <pre>
       *权重
       * </pre>
       *
       * <code>int32 sort_value = 9;</code>
       * @return The sortValue.
       */
      int getSortValue();

      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
       */
      java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api>
          getApiList();
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
       */
      PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api getApi(int index);
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
       */
      int getApiCount();
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
       */
      java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.ApiOrBuilder>
          getApiOrBuilderList();
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
       */
      PrivilegeOuterClass.GetUserPrivilegeTreeReply.ApiOrBuilder getApiOrBuilder(
          int index);

      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
       */
      java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege>
          getChildrenList();
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
       */
      PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege getChildren(int index);
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
       */
      int getChildrenCount();
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
       */
      java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder>
          getChildrenOrBuilderList();
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
       */
      PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder getChildrenOrBuilder(
          int index);
    }
    /**
     * Protobuf type {@code protos.GetUserPrivilegeTreeReply.Privilege}
     */
    public static final class Privilege extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:protos.GetUserPrivilegeTreeReply.Privilege)
        PrivilegeOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Privilege.newBuilder() to construct.
      private Privilege(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Privilege() {
        privilegeKey_ = "";
        name_ = "";
        parentKey_ = "";
        category_ = "";
        shape_ = "";
        point_ = "";
        type_ = "";
        icon_ = "";
        api_ = java.util.Collections.emptyList();
        children_ = java.util.Collections.emptyList();
      }

      @java.lang.Override
      @SuppressWarnings({"unused"})
      protected java.lang.Object newInstance(
          UnusedPrivateParameter unused) {
        return new Privilege();
      }

      @java.lang.Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      private Privilege(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        this();
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        int mutable_bitField0_ = 0;
        com.google.protobuf.UnknownFieldSet.Builder unknownFields =
            com.google.protobuf.UnknownFieldSet.newBuilder();
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                java.lang.String s = input.readStringRequireUtf8();

                privilegeKey_ = s;
                break;
              }
              case 18: {
                java.lang.String s = input.readStringRequireUtf8();

                name_ = s;
                break;
              }
              case 26: {
                java.lang.String s = input.readStringRequireUtf8();

                parentKey_ = s;
                break;
              }
              case 34: {
                java.lang.String s = input.readStringRequireUtf8();

                category_ = s;
                break;
              }
              case 42: {
                java.lang.String s = input.readStringRequireUtf8();

                shape_ = s;
                break;
              }
              case 50: {
                java.lang.String s = input.readStringRequireUtf8();

                point_ = s;
                break;
              }
              case 58: {
                java.lang.String s = input.readStringRequireUtf8();

                type_ = s;
                break;
              }
              case 66: {
                java.lang.String s = input.readStringRequireUtf8();

                icon_ = s;
                break;
              }
              case 72: {

                sortValue_ = input.readInt32();
                break;
              }
              case 82: {
                if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                  api_ = new java.util.ArrayList<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api>();
                  mutable_bitField0_ |= 0x00000001;
                }
                api_.add(
                    input.readMessage(PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.parser(), extensionRegistry));
                break;
              }
              case 90: {
                if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                  children_ = new java.util.ArrayList<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege>();
                  mutable_bitField0_ |= 0x00000002;
                }
                children_.add(
                    input.readMessage(PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.parser(), extensionRegistry));
                break;
              }
              default: {
                if (!parseUnknownField(
                    input, unknownFields, extensionRegistry, tag)) {
                  done = true;
                }
                break;
              }
            }
          }
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(this);
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e).setUnfinishedMessage(this);
        } finally {
          if (((mutable_bitField0_ & 0x00000001) != 0)) {
            api_ = java.util.Collections.unmodifiableList(api_);
          }
          if (((mutable_bitField0_ & 0x00000002) != 0)) {
            children_ = java.util.Collections.unmodifiableList(children_);
          }
          this.unknownFields = unknownFields.build();
          makeExtensionsImmutable();
        }
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_Privilege_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_Privilege_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.class, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder.class);
      }

      public static final int PRIVILEGE_KEY_FIELD_NUMBER = 1;
      private volatile java.lang.Object privilegeKey_;
      /**
       * <pre>
       *权限唯一健
       * </pre>
       *
       * <code>string privilege_key = 1;</code>
       * @return The privilegeKey.
       */
      @java.lang.Override
      public java.lang.String getPrivilegeKey() {
        java.lang.Object ref = privilegeKey_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          privilegeKey_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *权限唯一健
       * </pre>
       *
       * <code>string privilege_key = 1;</code>
       * @return The bytes for privilegeKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getPrivilegeKeyBytes() {
        java.lang.Object ref = privilegeKey_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          privilegeKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int NAME_FIELD_NUMBER = 2;
      private volatile java.lang.Object name_;
      /**
       * <pre>
       *名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      @java.lang.Override
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *名称
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int PARENT_KEY_FIELD_NUMBER = 3;
      private volatile java.lang.Object parentKey_;
      /**
       * <pre>
       *父健
       * </pre>
       *
       * <code>string parent_key = 3;</code>
       * @return The parentKey.
       */
      @java.lang.Override
      public java.lang.String getParentKey() {
        java.lang.Object ref = parentKey_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          parentKey_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *父健
       * </pre>
       *
       * <code>string parent_key = 3;</code>
       * @return The bytes for parentKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getParentKeyBytes() {
        java.lang.Object ref = parentKey_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          parentKey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int CATEGORY_FIELD_NUMBER = 4;
      private volatile java.lang.Object category_;
      /**
       * <pre>
       *权限类
       * </pre>
       *
       * <code>string category = 4;</code>
       * @return The category.
       */
      @java.lang.Override
      public java.lang.String getCategory() {
        java.lang.Object ref = category_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          category_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *权限类
       * </pre>
       *
       * <code>string category = 4;</code>
       * @return The bytes for category.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getCategoryBytes() {
        java.lang.Object ref = category_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          category_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int SHAPE_FIELD_NUMBER = 5;
      private volatile java.lang.Object shape_;
      /**
       * <pre>
       *权限型
       * </pre>
       *
       * <code>string shape = 5;</code>
       * @return The shape.
       */
      @java.lang.Override
      public java.lang.String getShape() {
        java.lang.Object ref = shape_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          shape_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *权限型
       * </pre>
       *
       * <code>string shape = 5;</code>
       * @return The bytes for shape.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getShapeBytes() {
        java.lang.Object ref = shape_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          shape_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int POINT_FIELD_NUMBER = 6;
      private volatile java.lang.Object point_;
      /**
       * <pre>
       *权限点
       * </pre>
       *
       * <code>string point = 6;</code>
       * @return The point.
       */
      @java.lang.Override
      public java.lang.String getPoint() {
        java.lang.Object ref = point_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          point_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *权限点
       * </pre>
       *
       * <code>string point = 6;</code>
       * @return The bytes for point.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getPointBytes() {
        java.lang.Object ref = point_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          point_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int TYPE_FIELD_NUMBER = 7;
      private volatile java.lang.Object type_;
      /**
       * <pre>
       *权限类型
       * </pre>
       *
       * <code>string type = 7;</code>
       * @return The type.
       */
      @java.lang.Override
      public java.lang.String getType() {
        java.lang.Object ref = type_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          type_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *权限类型
       * </pre>
       *
       * <code>string type = 7;</code>
       * @return The bytes for type.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getTypeBytes() {
        java.lang.Object ref = type_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          type_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int ICON_FIELD_NUMBER = 8;
      private volatile java.lang.Object icon_;
      /**
       * <pre>
       *图标
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return The icon.
       */
      @java.lang.Override
      public java.lang.String getIcon() {
        java.lang.Object ref = icon_;
        if (ref instanceof java.lang.String) {
          return (java.lang.String) ref;
        } else {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          icon_ = s;
          return s;
        }
      }
      /**
       * <pre>
       *图标
       * </pre>
       *
       * <code>string icon = 8;</code>
       * @return The bytes for icon.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getIconBytes() {
        java.lang.Object ref = icon_;
        if (ref instanceof java.lang.String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          icon_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int SORT_VALUE_FIELD_NUMBER = 9;
      private int sortValue_;
      /**
       * <pre>
       *权重
       * </pre>
       *
       * <code>int32 sort_value = 9;</code>
       * @return The sortValue.
       */
      @java.lang.Override
      public int getSortValue() {
        return sortValue_;
      }

      public static final int API_FIELD_NUMBER = 10;
      private java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api> api_;
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
       */
      @java.lang.Override
      public java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api> getApiList() {
        return api_;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
       */
      @java.lang.Override
      public java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.ApiOrBuilder>
          getApiOrBuilderList() {
        return api_;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
       */
      @java.lang.Override
      public int getApiCount() {
        return api_.size();
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
       */
      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api getApi(int index) {
        return api_.get(index);
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
       */
      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.ApiOrBuilder getApiOrBuilder(
          int index) {
        return api_.get(index);
      }

      public static final int CHILDREN_FIELD_NUMBER = 11;
      private java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege> children_;
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
       */
      @java.lang.Override
      public java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege> getChildrenList() {
        return children_;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
       */
      @java.lang.Override
      public java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder>
          getChildrenOrBuilderList() {
        return children_;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
       */
      @java.lang.Override
      public int getChildrenCount() {
        return children_.size();
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
       */
      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege getChildren(int index) {
        return children_.get(index);
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
       */
      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder getChildrenOrBuilder(
          int index) {
        return children_.get(index);
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!getPrivilegeKeyBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, privilegeKey_);
        }
        if (!getNameBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
        }
        if (!getParentKeyBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 3, parentKey_);
        }
        if (!getCategoryBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 4, category_);
        }
        if (!getShapeBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 5, shape_);
        }
        if (!getPointBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 6, point_);
        }
        if (!getTypeBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 7, type_);
        }
        if (!getIconBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 8, icon_);
        }
        if (sortValue_ != 0) {
          output.writeInt32(9, sortValue_);
        }
        for (int i = 0; i < api_.size(); i++) {
          output.writeMessage(10, api_.get(i));
        }
        for (int i = 0; i < children_.size(); i++) {
          output.writeMessage(11, children_.get(i));
        }
        unknownFields.writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!getPrivilegeKeyBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, privilegeKey_);
        }
        if (!getNameBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
        }
        if (!getParentKeyBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, parentKey_);
        }
        if (!getCategoryBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, category_);
        }
        if (!getShapeBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, shape_);
        }
        if (!getPointBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, point_);
        }
        if (!getTypeBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, type_);
        }
        if (!getIconBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, icon_);
        }
        if (sortValue_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeInt32Size(9, sortValue_);
        }
        for (int i = 0; i < api_.size(); i++) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(10, api_.get(i));
        }
        for (int i = 0; i < children_.size(); i++) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(11, children_.get(i));
        }
        size += unknownFields.getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege)) {
          return super.equals(obj);
        }
        PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege other = (PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege) obj;

        if (!getPrivilegeKey()
            .equals(other.getPrivilegeKey())) return false;
        if (!getName()
            .equals(other.getName())) return false;
        if (!getParentKey()
            .equals(other.getParentKey())) return false;
        if (!getCategory()
            .equals(other.getCategory())) return false;
        if (!getShape()
            .equals(other.getShape())) return false;
        if (!getPoint()
            .equals(other.getPoint())) return false;
        if (!getType()
            .equals(other.getType())) return false;
        if (!getIcon()
            .equals(other.getIcon())) return false;
        if (getSortValue()
            != other.getSortValue()) return false;
        if (!getApiList()
            .equals(other.getApiList())) return false;
        if (!getChildrenList()
            .equals(other.getChildrenList())) return false;
        if (!unknownFields.equals(other.unknownFields)) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + PRIVILEGE_KEY_FIELD_NUMBER;
        hash = (53 * hash) + getPrivilegeKey().hashCode();
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
        hash = (37 * hash) + PARENT_KEY_FIELD_NUMBER;
        hash = (53 * hash) + getParentKey().hashCode();
        hash = (37 * hash) + CATEGORY_FIELD_NUMBER;
        hash = (53 * hash) + getCategory().hashCode();
        hash = (37 * hash) + SHAPE_FIELD_NUMBER;
        hash = (53 * hash) + getShape().hashCode();
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getType().hashCode();
        hash = (37 * hash) + ICON_FIELD_NUMBER;
        hash = (53 * hash) + getIcon().hashCode();
        hash = (37 * hash) + SORT_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getSortValue();
        if (getApiCount() > 0) {
          hash = (37 * hash) + API_FIELD_NUMBER;
          hash = (53 * hash) + getApiList().hashCode();
        }
        if (getChildrenCount() > 0) {
          hash = (37 * hash) + CHILDREN_FIELD_NUMBER;
          hash = (53 * hash) + getChildrenList().hashCode();
        }
        hash = (29 * hash) + unknownFields.hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code protos.GetUserPrivilegeTreeReply.Privilege}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:protos.GetUserPrivilegeTreeReply.Privilege)
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_Privilege_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_Privilege_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.class, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder.class);
        }

        // Construct using PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
            getApiFieldBuilder();
            getChildrenFieldBuilder();
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          privilegeKey_ = "";

          name_ = "";

          parentKey_ = "";

          category_ = "";

          shape_ = "";

          point_ = "";

          type_ = "";

          icon_ = "";

          sortValue_ = 0;

          if (apiBuilder_ == null) {
            api_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            apiBuilder_.clear();
          }
          if (childrenBuilder_ == null) {
            children_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            childrenBuilder_.clear();
          }
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_Privilege_descriptor;
        }

        @java.lang.Override
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege getDefaultInstanceForType() {
          return PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.getDefaultInstance();
        }

        @java.lang.Override
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege build() {
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege buildPartial() {
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege result = new PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege(this);
          int from_bitField0_ = bitField0_;
          result.privilegeKey_ = privilegeKey_;
          result.name_ = name_;
          result.parentKey_ = parentKey_;
          result.category_ = category_;
          result.shape_ = shape_;
          result.point_ = point_;
          result.type_ = type_;
          result.icon_ = icon_;
          result.sortValue_ = sortValue_;
          if (apiBuilder_ == null) {
            if (((bitField0_ & 0x00000001) != 0)) {
              api_ = java.util.Collections.unmodifiableList(api_);
              bitField0_ = (bitField0_ & ~0x00000001);
            }
            result.api_ = api_;
          } else {
            result.api_ = apiBuilder_.build();
          }
          if (childrenBuilder_ == null) {
            if (((bitField0_ & 0x00000002) != 0)) {
              children_ = java.util.Collections.unmodifiableList(children_);
              bitField0_ = (bitField0_ & ~0x00000002);
            }
            result.children_ = children_;
          } else {
            result.children_ = childrenBuilder_.build();
          }
          onBuilt();
          return result;
        }

        @java.lang.Override
        public Builder clone() {
          return super.clone();
        }
        @java.lang.Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.setField(field, value);
        }
        @java.lang.Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @java.lang.Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @java.lang.Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, java.lang.Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @java.lang.Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            java.lang.Object value) {
          return super.addRepeatedField(field, value);
        }
        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege) {
            return mergeFrom((PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege other) {
          if (other == PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.getDefaultInstance()) return this;
          if (!other.getPrivilegeKey().isEmpty()) {
            privilegeKey_ = other.privilegeKey_;
            onChanged();
          }
          if (!other.getName().isEmpty()) {
            name_ = other.name_;
            onChanged();
          }
          if (!other.getParentKey().isEmpty()) {
            parentKey_ = other.parentKey_;
            onChanged();
          }
          if (!other.getCategory().isEmpty()) {
            category_ = other.category_;
            onChanged();
          }
          if (!other.getShape().isEmpty()) {
            shape_ = other.shape_;
            onChanged();
          }
          if (!other.getPoint().isEmpty()) {
            point_ = other.point_;
            onChanged();
          }
          if (!other.getType().isEmpty()) {
            type_ = other.type_;
            onChanged();
          }
          if (!other.getIcon().isEmpty()) {
            icon_ = other.icon_;
            onChanged();
          }
          if (other.getSortValue() != 0) {
            setSortValue(other.getSortValue());
          }
          if (apiBuilder_ == null) {
            if (!other.api_.isEmpty()) {
              if (api_.isEmpty()) {
                api_ = other.api_;
                bitField0_ = (bitField0_ & ~0x00000001);
              } else {
                ensureApiIsMutable();
                api_.addAll(other.api_);
              }
              onChanged();
            }
          } else {
            if (!other.api_.isEmpty()) {
              if (apiBuilder_.isEmpty()) {
                apiBuilder_.dispose();
                apiBuilder_ = null;
                api_ = other.api_;
                bitField0_ = (bitField0_ & ~0x00000001);
                apiBuilder_ =
                  com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                     getApiFieldBuilder() : null;
              } else {
                apiBuilder_.addAllMessages(other.api_);
              }
            }
          }
          if (childrenBuilder_ == null) {
            if (!other.children_.isEmpty()) {
              if (children_.isEmpty()) {
                children_ = other.children_;
                bitField0_ = (bitField0_ & ~0x00000002);
              } else {
                ensureChildrenIsMutable();
                children_.addAll(other.children_);
              }
              onChanged();
            }
          } else {
            if (!other.children_.isEmpty()) {
              if (childrenBuilder_.isEmpty()) {
                childrenBuilder_.dispose();
                childrenBuilder_ = null;
                children_ = other.children_;
                bitField0_ = (bitField0_ & ~0x00000002);
                childrenBuilder_ =
                  com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                     getChildrenFieldBuilder() : null;
              } else {
                childrenBuilder_.addAllMessages(other.children_);
              }
            }
          }
          this.mergeUnknownFields(other.unknownFields);
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege parsedMessage = null;
          try {
            parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            parsedMessage = (PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege) e.getUnfinishedMessage();
            throw e.unwrapIOException();
          } finally {
            if (parsedMessage != null) {
              mergeFrom(parsedMessage);
            }
          }
          return this;
        }
        private int bitField0_;

        private java.lang.Object privilegeKey_ = "";
        /**
         * <pre>
         *权限唯一健
         * </pre>
         *
         * <code>string privilege_key = 1;</code>
         * @return The privilegeKey.
         */
        public java.lang.String getPrivilegeKey() {
          java.lang.Object ref = privilegeKey_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            privilegeKey_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *权限唯一健
         * </pre>
         *
         * <code>string privilege_key = 1;</code>
         * @return The bytes for privilegeKey.
         */
        public com.google.protobuf.ByteString
            getPrivilegeKeyBytes() {
          java.lang.Object ref = privilegeKey_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            privilegeKey_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *权限唯一健
         * </pre>
         *
         * <code>string privilege_key = 1;</code>
         * @param value The privilegeKey to set.
         * @return This builder for chaining.
         */
        public Builder setPrivilegeKey(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          privilegeKey_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限唯一健
         * </pre>
         *
         * <code>string privilege_key = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearPrivilegeKey() {

          privilegeKey_ = getDefaultInstance().getPrivilegeKey();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限唯一健
         * </pre>
         *
         * <code>string privilege_key = 1;</code>
         * @param value The bytes for privilegeKey to set.
         * @return This builder for chaining.
         */
        public Builder setPrivilegeKeyBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          privilegeKey_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object name_ = "";
        /**
         * <pre>
         *名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return The name.
         */
        public java.lang.String getName() {
          java.lang.Object ref = name_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            name_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return The bytes for name.
         */
        public com.google.protobuf.ByteString
            getNameBytes() {
          java.lang.Object ref = name_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            name_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @param value The name to set.
         * @return This builder for chaining.
         */
        public Builder setName(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          name_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearName() {

          name_ = getDefaultInstance().getName();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *名称
         * </pre>
         *
         * <code>string name = 2;</code>
         * @param value The bytes for name to set.
         * @return This builder for chaining.
         */
        public Builder setNameBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          name_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object parentKey_ = "";
        /**
         * <pre>
         *父健
         * </pre>
         *
         * <code>string parent_key = 3;</code>
         * @return The parentKey.
         */
        public java.lang.String getParentKey() {
          java.lang.Object ref = parentKey_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            parentKey_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *父健
         * </pre>
         *
         * <code>string parent_key = 3;</code>
         * @return The bytes for parentKey.
         */
        public com.google.protobuf.ByteString
            getParentKeyBytes() {
          java.lang.Object ref = parentKey_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            parentKey_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *父健
         * </pre>
         *
         * <code>string parent_key = 3;</code>
         * @param value The parentKey to set.
         * @return This builder for chaining.
         */
        public Builder setParentKey(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          parentKey_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *父健
         * </pre>
         *
         * <code>string parent_key = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearParentKey() {

          parentKey_ = getDefaultInstance().getParentKey();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *父健
         * </pre>
         *
         * <code>string parent_key = 3;</code>
         * @param value The bytes for parentKey to set.
         * @return This builder for chaining.
         */
        public Builder setParentKeyBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          parentKey_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object category_ = "";
        /**
         * <pre>
         *权限类
         * </pre>
         *
         * <code>string category = 4;</code>
         * @return The category.
         */
        public java.lang.String getCategory() {
          java.lang.Object ref = category_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            category_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *权限类
         * </pre>
         *
         * <code>string category = 4;</code>
         * @return The bytes for category.
         */
        public com.google.protobuf.ByteString
            getCategoryBytes() {
          java.lang.Object ref = category_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            category_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *权限类
         * </pre>
         *
         * <code>string category = 4;</code>
         * @param value The category to set.
         * @return This builder for chaining.
         */
        public Builder setCategory(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          category_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限类
         * </pre>
         *
         * <code>string category = 4;</code>
         * @return This builder for chaining.
         */
        public Builder clearCategory() {

          category_ = getDefaultInstance().getCategory();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限类
         * </pre>
         *
         * <code>string category = 4;</code>
         * @param value The bytes for category to set.
         * @return This builder for chaining.
         */
        public Builder setCategoryBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          category_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object shape_ = "";
        /**
         * <pre>
         *权限型
         * </pre>
         *
         * <code>string shape = 5;</code>
         * @return The shape.
         */
        public java.lang.String getShape() {
          java.lang.Object ref = shape_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            shape_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *权限型
         * </pre>
         *
         * <code>string shape = 5;</code>
         * @return The bytes for shape.
         */
        public com.google.protobuf.ByteString
            getShapeBytes() {
          java.lang.Object ref = shape_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            shape_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *权限型
         * </pre>
         *
         * <code>string shape = 5;</code>
         * @param value The shape to set.
         * @return This builder for chaining.
         */
        public Builder setShape(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          shape_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限型
         * </pre>
         *
         * <code>string shape = 5;</code>
         * @return This builder for chaining.
         */
        public Builder clearShape() {

          shape_ = getDefaultInstance().getShape();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限型
         * </pre>
         *
         * <code>string shape = 5;</code>
         * @param value The bytes for shape to set.
         * @return This builder for chaining.
         */
        public Builder setShapeBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          shape_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object point_ = "";
        /**
         * <pre>
         *权限点
         * </pre>
         *
         * <code>string point = 6;</code>
         * @return The point.
         */
        public java.lang.String getPoint() {
          java.lang.Object ref = point_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            point_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *权限点
         * </pre>
         *
         * <code>string point = 6;</code>
         * @return The bytes for point.
         */
        public com.google.protobuf.ByteString
            getPointBytes() {
          java.lang.Object ref = point_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            point_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *权限点
         * </pre>
         *
         * <code>string point = 6;</code>
         * @param value The point to set.
         * @return This builder for chaining.
         */
        public Builder setPoint(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          point_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限点
         * </pre>
         *
         * <code>string point = 6;</code>
         * @return This builder for chaining.
         */
        public Builder clearPoint() {

          point_ = getDefaultInstance().getPoint();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限点
         * </pre>
         *
         * <code>string point = 6;</code>
         * @param value The bytes for point to set.
         * @return This builder for chaining.
         */
        public Builder setPointBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          point_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object type_ = "";
        /**
         * <pre>
         *权限类型
         * </pre>
         *
         * <code>string type = 7;</code>
         * @return The type.
         */
        public java.lang.String getType() {
          java.lang.Object ref = type_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            type_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *权限类型
         * </pre>
         *
         * <code>string type = 7;</code>
         * @return The bytes for type.
         */
        public com.google.protobuf.ByteString
            getTypeBytes() {
          java.lang.Object ref = type_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            type_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *权限类型
         * </pre>
         *
         * <code>string type = 7;</code>
         * @param value The type to set.
         * @return This builder for chaining.
         */
        public Builder setType(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          type_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限类型
         * </pre>
         *
         * <code>string type = 7;</code>
         * @return This builder for chaining.
         */
        public Builder clearType() {

          type_ = getDefaultInstance().getType();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权限类型
         * </pre>
         *
         * <code>string type = 7;</code>
         * @param value The bytes for type to set.
         * @return This builder for chaining.
         */
        public Builder setTypeBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          type_ = value;
          onChanged();
          return this;
        }

        private java.lang.Object icon_ = "";
        /**
         * <pre>
         *图标
         * </pre>
         *
         * <code>string icon = 8;</code>
         * @return The icon.
         */
        public java.lang.String getIcon() {
          java.lang.Object ref = icon_;
          if (!(ref instanceof java.lang.String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            java.lang.String s = bs.toStringUtf8();
            icon_ = s;
            return s;
          } else {
            return (java.lang.String) ref;
          }
        }
        /**
         * <pre>
         *图标
         * </pre>
         *
         * <code>string icon = 8;</code>
         * @return The bytes for icon.
         */
        public com.google.protobuf.ByteString
            getIconBytes() {
          java.lang.Object ref = icon_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b =
                com.google.protobuf.ByteString.copyFromUtf8(
                    (java.lang.String) ref);
            icon_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <pre>
         *图标
         * </pre>
         *
         * <code>string icon = 8;</code>
         * @param value The icon to set.
         * @return This builder for chaining.
         */
        public Builder setIcon(
            java.lang.String value) {
          if (value == null) {
    throw new NullPointerException();
  }

          icon_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *图标
         * </pre>
         *
         * <code>string icon = 8;</code>
         * @return This builder for chaining.
         */
        public Builder clearIcon() {

          icon_ = getDefaultInstance().getIcon();
          onChanged();
          return this;
        }
        /**
         * <pre>
         *图标
         * </pre>
         *
         * <code>string icon = 8;</code>
         * @param value The bytes for icon to set.
         * @return This builder for chaining.
         */
        public Builder setIconBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);

          icon_ = value;
          onChanged();
          return this;
        }

        private int sortValue_ ;
        /**
         * <pre>
         *权重
         * </pre>
         *
         * <code>int32 sort_value = 9;</code>
         * @return The sortValue.
         */
        @java.lang.Override
        public int getSortValue() {
          return sortValue_;
        }
        /**
         * <pre>
         *权重
         * </pre>
         *
         * <code>int32 sort_value = 9;</code>
         * @param value The sortValue to set.
         * @return This builder for chaining.
         */
        public Builder setSortValue(int value) {

          sortValue_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         *权重
         * </pre>
         *
         * <code>int32 sort_value = 9;</code>
         * @return This builder for chaining.
         */
        public Builder clearSortValue() {

          sortValue_ = 0;
          onChanged();
          return this;
        }

        private java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api> api_ =
          java.util.Collections.emptyList();
        private void ensureApiIsMutable() {
          if (!((bitField0_ & 0x00000001) != 0)) {
            api_ = new java.util.ArrayList<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api>(api_);
            bitField0_ |= 0x00000001;
           }
        }

        private com.google.protobuf.RepeatedFieldBuilderV3<
            PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder, PrivilegeOuterClass.GetUserPrivilegeTreeReply.ApiOrBuilder> apiBuilder_;

        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api> getApiList() {
          if (apiBuilder_ == null) {
            return java.util.Collections.unmodifiableList(api_);
          } else {
            return apiBuilder_.getMessageList();
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public int getApiCount() {
          if (apiBuilder_ == null) {
            return api_.size();
          } else {
            return apiBuilder_.getCount();
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api getApi(int index) {
          if (apiBuilder_ == null) {
            return api_.get(index);
          } else {
            return apiBuilder_.getMessage(index);
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public Builder setApi(
            int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api value) {
          if (apiBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureApiIsMutable();
            api_.set(index, value);
            onChanged();
          } else {
            apiBuilder_.setMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public Builder setApi(
            int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder builderForValue) {
          if (apiBuilder_ == null) {
            ensureApiIsMutable();
            api_.set(index, builderForValue.build());
            onChanged();
          } else {
            apiBuilder_.setMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public Builder addApi(PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api value) {
          if (apiBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureApiIsMutable();
            api_.add(value);
            onChanged();
          } else {
            apiBuilder_.addMessage(value);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public Builder addApi(
            int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api value) {
          if (apiBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureApiIsMutable();
            api_.add(index, value);
            onChanged();
          } else {
            apiBuilder_.addMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public Builder addApi(
            PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder builderForValue) {
          if (apiBuilder_ == null) {
            ensureApiIsMutable();
            api_.add(builderForValue.build());
            onChanged();
          } else {
            apiBuilder_.addMessage(builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public Builder addApi(
            int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder builderForValue) {
          if (apiBuilder_ == null) {
            ensureApiIsMutable();
            api_.add(index, builderForValue.build());
            onChanged();
          } else {
            apiBuilder_.addMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public Builder addAllApi(
            java.lang.Iterable<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api> values) {
          if (apiBuilder_ == null) {
            ensureApiIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, api_);
            onChanged();
          } else {
            apiBuilder_.addAllMessages(values);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public Builder clearApi() {
          if (apiBuilder_ == null) {
            api_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000001);
            onChanged();
          } else {
            apiBuilder_.clear();
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public Builder removeApi(int index) {
          if (apiBuilder_ == null) {
            ensureApiIsMutable();
            api_.remove(index);
            onChanged();
          } else {
            apiBuilder_.remove(index);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder getApiBuilder(
            int index) {
          return getApiFieldBuilder().getBuilder(index);
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.ApiOrBuilder getApiOrBuilder(
            int index) {
          if (apiBuilder_ == null) {
            return api_.get(index);  } else {
            return apiBuilder_.getMessageOrBuilder(index);
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.ApiOrBuilder>
             getApiOrBuilderList() {
          if (apiBuilder_ != null) {
            return apiBuilder_.getMessageOrBuilderList();
          } else {
            return java.util.Collections.unmodifiableList(api_);
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder addApiBuilder() {
          return getApiFieldBuilder().addBuilder(
              PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.getDefaultInstance());
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder addApiBuilder(
            int index) {
          return getApiFieldBuilder().addBuilder(
              index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.getDefaultInstance());
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Api api = 10;</code>
         */
        public java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder>
             getApiBuilderList() {
          return getApiFieldBuilder().getBuilderList();
        }
        private com.google.protobuf.RepeatedFieldBuilderV3<
            PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder, PrivilegeOuterClass.GetUserPrivilegeTreeReply.ApiOrBuilder>
            getApiFieldBuilder() {
          if (apiBuilder_ == null) {
            apiBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Api.Builder, PrivilegeOuterClass.GetUserPrivilegeTreeReply.ApiOrBuilder>(
                    api_,
                    ((bitField0_ & 0x00000001) != 0),
                    getParentForChildren(),
                    isClean());
            api_ = null;
          }
          return apiBuilder_;
        }

        private java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege> children_ =
          java.util.Collections.emptyList();
        private void ensureChildrenIsMutable() {
          if (!((bitField0_ & 0x00000002) != 0)) {
            children_ = new java.util.ArrayList<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege>(children_);
            bitField0_ |= 0x00000002;
           }
        }

        private com.google.protobuf.RepeatedFieldBuilderV3<
            PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder, PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder> childrenBuilder_;

        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege> getChildrenList() {
          if (childrenBuilder_ == null) {
            return java.util.Collections.unmodifiableList(children_);
          } else {
            return childrenBuilder_.getMessageList();
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public int getChildrenCount() {
          if (childrenBuilder_ == null) {
            return children_.size();
          } else {
            return childrenBuilder_.getCount();
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege getChildren(int index) {
          if (childrenBuilder_ == null) {
            return children_.get(index);
          } else {
            return childrenBuilder_.getMessage(index);
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public Builder setChildren(
            int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege value) {
          if (childrenBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureChildrenIsMutable();
            children_.set(index, value);
            onChanged();
          } else {
            childrenBuilder_.setMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public Builder setChildren(
            int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder builderForValue) {
          if (childrenBuilder_ == null) {
            ensureChildrenIsMutable();
            children_.set(index, builderForValue.build());
            onChanged();
          } else {
            childrenBuilder_.setMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public Builder addChildren(PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege value) {
          if (childrenBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureChildrenIsMutable();
            children_.add(value);
            onChanged();
          } else {
            childrenBuilder_.addMessage(value);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public Builder addChildren(
            int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege value) {
          if (childrenBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensureChildrenIsMutable();
            children_.add(index, value);
            onChanged();
          } else {
            childrenBuilder_.addMessage(index, value);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public Builder addChildren(
            PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder builderForValue) {
          if (childrenBuilder_ == null) {
            ensureChildrenIsMutable();
            children_.add(builderForValue.build());
            onChanged();
          } else {
            childrenBuilder_.addMessage(builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public Builder addChildren(
            int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder builderForValue) {
          if (childrenBuilder_ == null) {
            ensureChildrenIsMutable();
            children_.add(index, builderForValue.build());
            onChanged();
          } else {
            childrenBuilder_.addMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public Builder addAllChildren(
            java.lang.Iterable<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege> values) {
          if (childrenBuilder_ == null) {
            ensureChildrenIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, children_);
            onChanged();
          } else {
            childrenBuilder_.addAllMessages(values);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public Builder clearChildren() {
          if (childrenBuilder_ == null) {
            children_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000002);
            onChanged();
          } else {
            childrenBuilder_.clear();
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public Builder removeChildren(int index) {
          if (childrenBuilder_ == null) {
            ensureChildrenIsMutable();
            children_.remove(index);
            onChanged();
          } else {
            childrenBuilder_.remove(index);
          }
          return this;
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder getChildrenBuilder(
            int index) {
          return getChildrenFieldBuilder().getBuilder(index);
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder getChildrenOrBuilder(
            int index) {
          if (childrenBuilder_ == null) {
            return children_.get(index);  } else {
            return childrenBuilder_.getMessageOrBuilder(index);
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder>
             getChildrenOrBuilderList() {
          if (childrenBuilder_ != null) {
            return childrenBuilder_.getMessageOrBuilderList();
          } else {
            return java.util.Collections.unmodifiableList(children_);
          }
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder addChildrenBuilder() {
          return getChildrenFieldBuilder().addBuilder(
              PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.getDefaultInstance());
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder addChildrenBuilder(
            int index) {
          return getChildrenFieldBuilder().addBuilder(
              index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.getDefaultInstance());
        }
        /**
         * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege children = 11;</code>
         */
        public java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder>
             getChildrenBuilderList() {
          return getChildrenFieldBuilder().getBuilderList();
        }
        private com.google.protobuf.RepeatedFieldBuilderV3<
            PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder, PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder>
            getChildrenFieldBuilder() {
          if (childrenBuilder_ == null) {
            childrenBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder, PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder>(
                    children_,
                    ((bitField0_ & 0x00000002) != 0),
                    getParentForChildren(),
                    isClean());
            children_ = null;
          }
          return childrenBuilder_;
        }
        @java.lang.Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @java.lang.Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:protos.GetUserPrivilegeTreeReply.Privilege)
      }

      // @@protoc_insertion_point(class_scope:protos.GetUserPrivilegeTreeReply.Privilege)
      private static final PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege();
      }

      public static PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Privilege>
          PARSER = new com.google.protobuf.AbstractParser<Privilege>() {
        @java.lang.Override
        public Privilege parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return new Privilege(input, extensionRegistry);
        }
      };

      public static com.google.protobuf.Parser<Privilege> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Privilege> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int PRIVILEGE_FIELD_NUMBER = 1;
    private java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege> privilege_;
    /**
     * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
     */
    @java.lang.Override
    public java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege> getPrivilegeList() {
      return privilege_;
    }
    /**
     * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder>
        getPrivilegeOrBuilderList() {
      return privilege_;
    }
    /**
     * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
     */
    @java.lang.Override
    public int getPrivilegeCount() {
      return privilege_.size();
    }
    /**
     * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
     */
    @java.lang.Override
    public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege getPrivilege(int index) {
      return privilege_.get(index);
    }
    /**
     * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
     */
    @java.lang.Override
    public PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder getPrivilegeOrBuilder(
        int index) {
      return privilege_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < privilege_.size(); i++) {
        output.writeMessage(1, privilege_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < privilege_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, privilege_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PrivilegeOuterClass.GetUserPrivilegeTreeReply)) {
        return super.equals(obj);
      }
      PrivilegeOuterClass.GetUserPrivilegeTreeReply other = (PrivilegeOuterClass.GetUserPrivilegeTreeReply) obj;

      if (!getPrivilegeList()
          .equals(other.getPrivilegeList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPrivilegeCount() > 0) {
        hash = (37 * hash) + PRIVILEGE_FIELD_NUMBER;
        hash = (53 * hash) + getPrivilegeList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PrivilegeOuterClass.GetUserPrivilegeTreeReply prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protos.GetUserPrivilegeTreeReply}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:protos.GetUserPrivilegeTreeReply)
        PrivilegeOuterClass.GetUserPrivilegeTreeReplyOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PrivilegeOuterClass.GetUserPrivilegeTreeReply.class, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Builder.class);
      }

      // Construct using PrivilegeOuterClass.GetUserPrivilegeTreeReply.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPrivilegeFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (privilegeBuilder_ == null) {
          privilege_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          privilegeBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return PrivilegeOuterClass.internal_static_protos_GetUserPrivilegeTreeReply_descriptor;
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply getDefaultInstanceForType() {
        return PrivilegeOuterClass.GetUserPrivilegeTreeReply.getDefaultInstance();
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply build() {
        PrivilegeOuterClass.GetUserPrivilegeTreeReply result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply buildPartial() {
        PrivilegeOuterClass.GetUserPrivilegeTreeReply result = new PrivilegeOuterClass.GetUserPrivilegeTreeReply(this);
        int from_bitField0_ = bitField0_;
        if (privilegeBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            privilege_ = java.util.Collections.unmodifiableList(privilege_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.privilege_ = privilege_;
        } else {
          result.privilege_ = privilegeBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PrivilegeOuterClass.GetUserPrivilegeTreeReply) {
          return mergeFrom((PrivilegeOuterClass.GetUserPrivilegeTreeReply)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PrivilegeOuterClass.GetUserPrivilegeTreeReply other) {
        if (other == PrivilegeOuterClass.GetUserPrivilegeTreeReply.getDefaultInstance()) return this;
        if (privilegeBuilder_ == null) {
          if (!other.privilege_.isEmpty()) {
            if (privilege_.isEmpty()) {
              privilege_ = other.privilege_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePrivilegeIsMutable();
              privilege_.addAll(other.privilege_);
            }
            onChanged();
          }
        } else {
          if (!other.privilege_.isEmpty()) {
            if (privilegeBuilder_.isEmpty()) {
              privilegeBuilder_.dispose();
              privilegeBuilder_ = null;
              privilege_ = other.privilege_;
              bitField0_ = (bitField0_ & ~0x00000001);
              privilegeBuilder_ =
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPrivilegeFieldBuilder() : null;
            } else {
              privilegeBuilder_.addAllMessages(other.privilege_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        PrivilegeOuterClass.GetUserPrivilegeTreeReply parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (PrivilegeOuterClass.GetUserPrivilegeTreeReply) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege> privilege_ =
        java.util.Collections.emptyList();
      private void ensurePrivilegeIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          privilege_ = new java.util.ArrayList<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege>(privilege_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder, PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder> privilegeBuilder_;

      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege> getPrivilegeList() {
        if (privilegeBuilder_ == null) {
          return java.util.Collections.unmodifiableList(privilege_);
        } else {
          return privilegeBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public int getPrivilegeCount() {
        if (privilegeBuilder_ == null) {
          return privilege_.size();
        } else {
          return privilegeBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege getPrivilege(int index) {
        if (privilegeBuilder_ == null) {
          return privilege_.get(index);
        } else {
          return privilegeBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public Builder setPrivilege(
          int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege value) {
        if (privilegeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegeIsMutable();
          privilege_.set(index, value);
          onChanged();
        } else {
          privilegeBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public Builder setPrivilege(
          int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder builderForValue) {
        if (privilegeBuilder_ == null) {
          ensurePrivilegeIsMutable();
          privilege_.set(index, builderForValue.build());
          onChanged();
        } else {
          privilegeBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public Builder addPrivilege(PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege value) {
        if (privilegeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegeIsMutable();
          privilege_.add(value);
          onChanged();
        } else {
          privilegeBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public Builder addPrivilege(
          int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege value) {
        if (privilegeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePrivilegeIsMutable();
          privilege_.add(index, value);
          onChanged();
        } else {
          privilegeBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public Builder addPrivilege(
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder builderForValue) {
        if (privilegeBuilder_ == null) {
          ensurePrivilegeIsMutable();
          privilege_.add(builderForValue.build());
          onChanged();
        } else {
          privilegeBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public Builder addPrivilege(
          int index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder builderForValue) {
        if (privilegeBuilder_ == null) {
          ensurePrivilegeIsMutable();
          privilege_.add(index, builderForValue.build());
          onChanged();
        } else {
          privilegeBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public Builder addAllPrivilege(
          java.lang.Iterable<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege> values) {
        if (privilegeBuilder_ == null) {
          ensurePrivilegeIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, privilege_);
          onChanged();
        } else {
          privilegeBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public Builder clearPrivilege() {
        if (privilegeBuilder_ == null) {
          privilege_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          privilegeBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public Builder removePrivilege(int index) {
        if (privilegeBuilder_ == null) {
          ensurePrivilegeIsMutable();
          privilege_.remove(index);
          onChanged();
        } else {
          privilegeBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder getPrivilegeBuilder(
          int index) {
        return getPrivilegeFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder getPrivilegeOrBuilder(
          int index) {
        if (privilegeBuilder_ == null) {
          return privilege_.get(index);  } else {
          return privilegeBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public java.util.List<? extends PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder>
           getPrivilegeOrBuilderList() {
        if (privilegeBuilder_ != null) {
          return privilegeBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(privilege_);
        }
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder addPrivilegeBuilder() {
        return getPrivilegeFieldBuilder().addBuilder(
            PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.getDefaultInstance());
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder addPrivilegeBuilder(
          int index) {
        return getPrivilegeFieldBuilder().addBuilder(
            index, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.getDefaultInstance());
      }
      /**
       * <code>repeated .protos.GetUserPrivilegeTreeReply.Privilege privilege = 1;</code>
       */
      public java.util.List<PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder>
           getPrivilegeBuilderList() {
        return getPrivilegeFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder, PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder>
          getPrivilegeFieldBuilder() {
        if (privilegeBuilder_ == null) {
          privilegeBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege, PrivilegeOuterClass.GetUserPrivilegeTreeReply.Privilege.Builder, PrivilegeOuterClass.GetUserPrivilegeTreeReply.PrivilegeOrBuilder>(
                  privilege_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          privilege_ = null;
        }
        return privilegeBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:protos.GetUserPrivilegeTreeReply)
    }

    // @@protoc_insertion_point(class_scope:protos.GetUserPrivilegeTreeReply)
    private static final PrivilegeOuterClass.GetUserPrivilegeTreeReply DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new PrivilegeOuterClass.GetUserPrivilegeTreeReply();
    }

    public static PrivilegeOuterClass.GetUserPrivilegeTreeReply getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GetUserPrivilegeTreeReply>
        PARSER = new com.google.protobuf.AbstractParser<GetUserPrivilegeTreeReply>() {
      @java.lang.Override
      public GetUserPrivilegeTreeReply parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GetUserPrivilegeTreeReply(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<GetUserPrivilegeTreeReply> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GetUserPrivilegeTreeReply> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public PrivilegeOuterClass.GetUserPrivilegeTreeReply getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protos_GetUserPrivilegeAllParam_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protos_GetUserPrivilegeAllParam_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protos_GetUserPrivilegeAllReply_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protos_GetUserPrivilegeAllReply_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protos_GetUserPrivilegeAllReply_Api_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protos_GetUserPrivilegeAllReply_Api_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protos_GetUserPrivilegeAllReply_Privilege_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protos_GetUserPrivilegeAllReply_Privilege_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protos_GetUserPrivilegeTreeParam_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protos_GetUserPrivilegeTreeParam_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protos_GetUserPrivilegeTreeReply_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protos_GetUserPrivilegeTreeReply_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protos_GetUserPrivilegeTreeReply_Api_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protos_GetUserPrivilegeTreeReply_Api_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protos_GetUserPrivilegeTreeReply_Privilege_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protos_GetUserPrivilegeTreeReply_Privilege_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\"src/main/resources/privilege.proto\022\006pr" +
      "otos\";\n\030GetUserPrivilegeAllParam\022\016\n\006doma" +
      "in\030\001 \001(\t\022\017\n\007siteKey\030\002 \001(\t\"\366\002\n\030GetUserPri" +
      "vilegeAllReply\022=\n\tprivilege\030\001 \003(\0132*.prot" +
      "os.GetUserPrivilegeAllReply.Privilege\032A\n" +
      "\003Api\022\017\n\007api_key\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\013\n\003u" +
      "rl\030\003 \001(\t\022\016\n\006method\030\004 \001(\t\032\327\001\n\tPrivilege\022\025" +
      "\n\rprivilege_key\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\022\n\np" +
      "arent_key\030\003 \001(\t\022\020\n\010category\030\004 \001(\t\022\r\n\005sha" +
      "pe\030\005 \001(\t\022\r\n\005point\030\006 \001(\t\022\014\n\004type\030\007 \001(\t\022\014\n" +
      "\004icon\030\010 \001(\t\022\022\n\nsort_value\030\t \001(\005\0221\n\003api\030\n" +
      " \003(\0132$.protos.GetUserPrivilegeAllReply.A" +
      "pi\"<\n\031GetUserPrivilegeTreeParam\022\016\n\006domai" +
      "n\030\001 \001(\t\022\017\n\007siteKey\030\002 \001(\t\"\270\003\n\031GetUserPriv" +
      "ilegeTreeReply\022>\n\tprivilege\030\001 \003(\0132+.prot" +
      "os.GetUserPrivilegeTreeReply.Privilege\032A" +
      "\n\003Api\022\017\n\007api_key\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\013\n\003" +
      "url\030\003 \001(\t\022\016\n\006method\030\004 \001(\t\032\227\002\n\tPrivilege\022" +
      "\025\n\rprivilege_key\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\022\n\n" +
      "parent_key\030\003 \001(\t\022\020\n\010category\030\004 \001(\t\022\r\n\005sh" +
      "ape\030\005 \001(\t\022\r\n\005point\030\006 \001(\t\022\014\n\004type\030\007 \001(\t\022\014" +
      "\n\004icon\030\010 \001(\t\022\022\n\nsort_value\030\t \001(\005\0222\n\003api\030" +
      "\n \003(\0132%.protos.GetUserPrivilegeTreeReply" +
      ".Api\022=\n\010children\030\013 \003(\0132+.protos.GetUserP" +
      "rivilegeTreeReply.Privilege2\310\001\n\tPrivileg" +
      "e\022[\n\023GetUserPrivilegeAll\022 .protos.GetUse" +
      "rPrivilegeAllParam\032 .protos.GetUserPrivi" +
      "legeAllReply\"\000\022^\n\024GetUserPrivilegeTree\022!" +
      ".protos.GetUserPrivilegeTreeParam\032!.prot" +
      "os.GetUserPrivilegeTreeReply\"\000B \n\006protos" +
      "Z\026api/grpc/protos;protosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_protos_GetUserPrivilegeAllParam_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_protos_GetUserPrivilegeAllParam_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protos_GetUserPrivilegeAllParam_descriptor,
        new java.lang.String[] { "Domain", "SiteKey", });
    internal_static_protos_GetUserPrivilegeAllReply_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_protos_GetUserPrivilegeAllReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protos_GetUserPrivilegeAllReply_descriptor,
        new java.lang.String[] { "Privilege", });
    internal_static_protos_GetUserPrivilegeAllReply_Api_descriptor =
      internal_static_protos_GetUserPrivilegeAllReply_descriptor.getNestedTypes().get(0);
    internal_static_protos_GetUserPrivilegeAllReply_Api_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protos_GetUserPrivilegeAllReply_Api_descriptor,
        new java.lang.String[] { "ApiKey", "Name", "Url", "Method", });
    internal_static_protos_GetUserPrivilegeAllReply_Privilege_descriptor =
      internal_static_protos_GetUserPrivilegeAllReply_descriptor.getNestedTypes().get(1);
    internal_static_protos_GetUserPrivilegeAllReply_Privilege_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protos_GetUserPrivilegeAllReply_Privilege_descriptor,
        new java.lang.String[] { "PrivilegeKey", "Name", "ParentKey", "Category", "Shape", "Point", "Type", "Icon", "SortValue", "Api", });
    internal_static_protos_GetUserPrivilegeTreeParam_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_protos_GetUserPrivilegeTreeParam_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protos_GetUserPrivilegeTreeParam_descriptor,
        new java.lang.String[] { "Domain", "SiteKey", });
    internal_static_protos_GetUserPrivilegeTreeReply_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_protos_GetUserPrivilegeTreeReply_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protos_GetUserPrivilegeTreeReply_descriptor,
        new java.lang.String[] { "Privilege", });
    internal_static_protos_GetUserPrivilegeTreeReply_Api_descriptor =
      internal_static_protos_GetUserPrivilegeTreeReply_descriptor.getNestedTypes().get(0);
    internal_static_protos_GetUserPrivilegeTreeReply_Api_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protos_GetUserPrivilegeTreeReply_Api_descriptor,
        new java.lang.String[] { "ApiKey", "Name", "Url", "Method", });
    internal_static_protos_GetUserPrivilegeTreeReply_Privilege_descriptor =
      internal_static_protos_GetUserPrivilegeTreeReply_descriptor.getNestedTypes().get(1);
    internal_static_protos_GetUserPrivilegeTreeReply_Privilege_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protos_GetUserPrivilegeTreeReply_Privilege_descriptor,
        new java.lang.String[] { "PrivilegeKey", "Name", "ParentKey", "Category", "Shape", "Point", "Type", "Icon", "SortValue", "Api", "Children", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
