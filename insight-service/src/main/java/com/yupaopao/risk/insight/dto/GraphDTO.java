package com.yupaopao.risk.insight.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc GraphDTO
 * @date 2020/6/1
 */
@Data
public class GraphDTO {

    private List<Node> nodes = new ArrayList<>();

    private List<Edge> edges = new ArrayList<>();

    public GraphDTO addNode(Node node) {
        if (nodes.contains(node)) {
            return this;
        }
        nodes.add(node);
        return this;
    }

    public GraphDTO addEdge(Edge edge) {
        if (edges.contains(edge)) {
            return this;
        }
        edges.add(edge);
        return this;
    }

    @Data
    public static class Node {
        private String id;
        private String label;
        private String type;
        private String properties;
        private String badge;
        private boolean black;
        private boolean freeze;
    }

    @Data
    public static class Edge {
        private String id;
        private String label;
        private String source;
        private String target;
        private String properties;
    }
}
