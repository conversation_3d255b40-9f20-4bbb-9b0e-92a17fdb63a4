package com.yupaopao.risk.insight.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.yupaopao.risk.insight.common.property.connection.GDBProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.GDBUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.tinkerpop.gremlin.driver.Client;
import org.apache.tinkerpop.gremlin.driver.Cluster;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-28 19:06
 *
 ***/

@Slf4j
@Service
public class GdbConfig {
    private GDBProperties gdbProperties = GDBProperties.getProperties(PropertyType.GDB);

    @ApolloConfig("middleware.gdb.risk")
    private Config apolloConfigForGdb;

    private Client client;

    @PostConstruct
    public void init() {
        createClient();
        apolloConfigForGdb.addChangeListener(elem -> {
            gdbProperties = GDBProperties.getProperties(PropertyType.GDB);
            createClient();
        });
    }

    private Client createClient() {
        try {
            Cluster cluster = GDBUtil.getCluster(gdbProperties);
            client = GDBUtil.initClient(cluster);
        } catch (Exception e) {
            log.error("create clickhouse connection error");
        }
        return null;
    }

    public Client getClient() {
        if (client == null) {
            createClient();
        }
        return client;
    }
}
