package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-29 17:12
 *
 ***/

@Getter
@Setter
@Table(name = "t_cep_pattern")
public class CepPattern implements Serializable {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;


    @Column(name = "create_by")
    private String createBy;

    @Column(name = "update_by")
    private String updateBy;

    private String name;

    private String remarks;

    private String content;

    private String status;

    private String groupByColumn;

    @Column(name="result_handler_json")
    private String resultHandler;

    private String dataSource;
}
