package com.yupaopao.risk.insight.job;

import com.yupaopao.aries.client.JobExecutionContext;
import com.yupaopao.aries.client.JobListener;
import com.yupaopao.framework.spring.boot.aries.annotation.AriesCronJobListener;
import com.yupaopao.risk.insight.service.FlinkJobSchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/****
 * zengxiangcai
 * 2021/5/26 3:49 下午
 ***/


@Slf4j
@AriesCronJobListener
public class SynchroTrapJobListener implements JobListener {

    @Autowired
    private FlinkJobSchedulerService flinkJobSchedulerService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        String actionType = jobExecutionContext.getParameter();
        String jarName = "synchroTrap-prepare.jar";
        log.info("action type: {}", actionType);
        Map<String, String> argMap = new HashMap<>();
        argMap.put("actionType", actionType);

        try {
            flinkJobSchedulerService.run(jarName, argMap);
        } catch (Exception e) {
            log.error("failed to run jarName= " + jarName, e);
        }


    }

}
