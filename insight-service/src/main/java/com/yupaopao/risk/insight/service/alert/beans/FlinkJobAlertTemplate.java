package com.yupaopao.risk.insight.service.alert.beans;

import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.text.StrSubstitutor;

import java.util.*;
import java.util.stream.Collectors;

/****
 * zengxiangcai
 * 2022/11/9 17:13
 ***/

//
//告警名称：流任务卡死
//        任务名称：factor cal/aduit-metric
//        当前指标值：
//               N分钟内checkpoint次数为0: (N=10, value =0)
//        时间：2022-11-09 12:00:01
//@zengxiangcai
//
//告警名称：任务输出量波动较大
//任务名称：factor cal/aduit-metric
//当前指标值：
//        过去1h输出量环比大于M: (M,N,value)
//        过去N分钟输出量>M: (M,N,value)
//
//时间：2022-11-09 12:00:01
//任务详情：https://risk-flink.yupaopao.com/#/job/6b283c7942b9defe390d497d08321305/overview
//
//@zengxiangcai


@Getter
@Setter
public class FlinkJobAlertTemplate {
    private String ruleName; //告警名称
    private String jobName; //任务名称
    private String jobId; //flink job id
    private Map<String, String> currentMaticValues;
    private String alertUsers;
    private String alertTime;

    private String metricJob;

    public String getContent() {
        String template = "告警名称：${alertName}\n" +
                "任务名称：${flinkJobName}\n" +
                "当前指标值：\n" +
                "${currentMetrics}\n" +
                "时间：${currentTime}\n" +
                "任务详情：${flinkWebUrl}/${jobUrl}\n" +
                "${cronJobDetail}" +
                "\n" +
                "${alertUsers}";

        List<String> metricList = new ArrayList<>();
        currentMaticValues.forEach((k, v) -> {
            metricList.add(k + ": " + v);
        });
        Map<String, Object> params = new HashMap<>();
        params.put("alertName", ruleName);
        params.put("flinkJobName", jobName);
        String valuesWhitespace = "        ";
        params.put("currentMetrics", metricList.stream().collect(Collectors.joining("\n" + valuesWhitespace, valuesWhitespace, "")));
        params.put("currentTime", alertTime);
        String jobUrl = "";
        if (StringUtils.isNotEmpty(jobId)) {
            jobUrl = "#/job/" + jobId + "/overview";
        }
        params.put("jobUrl", jobUrl);

        params.put("alertUsers", Arrays.stream(alertUsers.split("#")).collect(Collectors.joining(";@", "@", "")));
        String content = new StrSubstitutor(params).replace(template);
        return content;
    }

    public static void main(String[] args) {
        FlinkJobAlertTemplate t = new FlinkJobAlertTemplate();
        t.setRuleName("flink任务测试");
        t.setJobId("6b283c7942b9defe390d497d08321305");
        t.setJobName("factor cal");
        t.setAlertUsers("zengxiangcai#xiaming");
        t.setAlertTime(InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));
        Map<String, String> vv = new HashMap<>();
        vv.put("过去1h输出量环比大于M", "(10,20,150%)");
        vv.put("过去1h输出量环比大于M2", "(10,34,150%)");
        t.setCurrentMaticValues(vv);
        System.err.println(t.getContent());
    }
}
