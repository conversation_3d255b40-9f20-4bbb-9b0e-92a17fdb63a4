package com.yupaopao.risk.insight.service.bloodline;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yupaopao.risk.insight.beans.BloodLineRelateVO;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import com.yupaopao.risk.insight.enums.BloodTypeEnum;
import com.yupaopao.risk.insight.service.RuleService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class AsyncStraToTagFetcher implements BloodLineFetcher {

    @Resource
    private RuleService ruleService;

    @Override
    public BloodTypeEnum getSrcType() {
        return BloodTypeEnum.ASYNC_STRA;
    }

    @Override
    public BloodTypeEnum getDstType() {
        return BloodTypeEnum.TAG;
    }

    @Override
    public List<BloodLineRelateVO> fetch() {
        List<BloodLineRelateVO> result = new ArrayList<>();
        List<JSONObject> tagRelateAsyncStraList = ruleService.queryTagRelateAsyncStra();

        if (tagRelateAsyncStraList == null || tagRelateAsyncStraList.isEmpty()) {
            return result;
        }
        Map<Integer, String> pidTagCodeMap = ruleService.queryTagPid();

        tagRelateAsyncStraList.forEach(tagRelateAsyncStra -> {

            Integer pid = tagRelateAsyncStra.getInteger("pids");
            if (pid == null) {
                return;
            }
            String srcName = tagRelateAsyncStra.getString("srcName");
            Integer srcRelateId = tagRelateAsyncStra.getInteger("srcRelateId");

            String tagCode = pidTagCodeMap.get(pid);
            // 指定code的标签打标惩罚包处理
            if (tagCode != null) {
                BloodLineRelateVO bloodLineRelateVO = new BloodLineRelateVO();

                bloodLineRelateVO.setSrcName(srcName);
                bloodLineRelateVO.setSrcRelateId(srcRelateId);
                bloodLineRelateVO.setSrcType(getSrcType());

                bloodLineRelateVO.setDstName(tagCode);
                TagInfoBO tagInfoBO = TagCacheSupport.getTagInfoByCode(tagCode);
                if (tagInfoBO == null) {
                    return;
                }
                bloodLineRelateVO.setDstRelateId(tagInfoBO.getId());
                bloodLineRelateVO.setDstType(getDstType());
                result.add(bloodLineRelateVO);
            } else {
                String pidAttr = tagRelateAsyncStra.getString("pidAttrs");
                if (pidAttr == null) {
                    return;
                }
                String customTagCode = JSONPath.read(pidAttr, "$.extMap.portraitCode", String.class);
                // 自定义标签打标惩罚包处理
                if (customTagCode == null) {
                    return;
                }
                TagInfoBO tagInfoBO = TagCacheSupport.getTagInfoByCode(customTagCode);
                if (tagInfoBO == null) {
                    return;
                }
                BloodLineRelateVO bloodLineRelateVO = new BloodLineRelateVO();

                bloodLineRelateVO.setSrcName(srcName);
                bloodLineRelateVO.setSrcRelateId(srcRelateId);
                bloodLineRelateVO.setSrcType(getSrcType());

                bloodLineRelateVO.setDstName(customTagCode);
                bloodLineRelateVO.setDstRelateId(tagInfoBO.getId());
                bloodLineRelateVO.setDstType(getDstType());
                result.add(bloodLineRelateVO);
            }

        });
        return result;
    }
}
