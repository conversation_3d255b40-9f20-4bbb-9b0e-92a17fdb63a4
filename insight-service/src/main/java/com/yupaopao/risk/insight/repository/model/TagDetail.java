package com.yupaopao.risk.insight.repository.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc Tag
 * @date 2020/4/29
 */
@Getter
@Setter
@Table(name ="t_tag_detail")
public class TagDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "tag_id")
    private Integer tagId;

    @Column(name = "data_content")
    private String dataContent;

    @Column(name = "value_type")
    private String valueType;
//
//    @Column(name = "group_key")
//    private String groupKey;
//
//    @Column(name = "agg_key")
//    private String aggKey;
//
//    @Column(name = "function")
//    private String function;
//
//    @Column(name = "time_span")
//    private Long timeSpan;
//
//    @Column(name = "condition")
//    private String condition;

    @Column(name = "remarks")
    private String remarks;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_by")
    private String createBy;

    @Override public boolean equals(Object o) {
        if (this == o) {
            return true;

        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TagDetail tagDetail = (TagDetail) o;
        return Objects.equals(id, tagDetail.id)
            && Objects.equals(tagId, tagDetail.tagId)
            && JSONObject.parseObject(dataContent).equals(JSONObject.parseObject(tagDetail.dataContent))
            && Objects.equals(valueType, tagDetail.valueType)
            ;
    }
}
