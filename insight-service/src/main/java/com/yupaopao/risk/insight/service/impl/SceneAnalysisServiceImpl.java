package com.yupaopao.risk.insight.service.impl;

import ch.qos.logback.core.joran.util.StringToObjectConverter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.google.common.collect.Maps;
import com.yupaopao.risk.common.model.Event;
import com.yupaopao.risk.insight.beans.*;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import com.yupaopao.risk.insight.repository.risk.dao.RiskRepository;
import com.yupaopao.risk.insight.service.ClickHouseService;
import com.yupaopao.risk.insight.service.PortraitTagService;
import com.yupaopao.risk.insight.service.RiskOverviewService;
import com.yupaopao.risk.insight.service.SceneAnalysisService;
import com.yupaopao.risk.insight.service.beans.BatchPortraitParam;
import com.yupaopao.risk.insight.service.beans.PortraitTagQueryVO;
import com.yupaopao.risk.insight.service.beans.QueryTag;
import com.yupaopao.risk.insight.service.beans.RiskOverviewResult;
import com.yupaopao.risk.insight.util.AnalysisUtils;
import com.yupaopao.risk.insight.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class SceneAnalysisServiceImpl implements SceneAnalysisService {

    @Autowired
    private RiskOverviewService riskOverviewService;

    @Autowired
    private ClickHouseService clickHouseService;

    @ApolloConfig
    private Config config;

    @Autowired
    private RiskRepository riskRepository;


    private Map<String, String> complainMap = Maps.newHashMap();

    private Map<String, String> eventMap = Maps.newHashMap();

    private static final String ACTIVE_SQL = "select userId, eventCode %s, count() as count from risk_hit_log where " +
            " userId in (%s)  and (%s) and  createdAt between '%s' and '%s' group by userId,eventCode %s order by " +
            "count" +
            "() " +
            "desc  limit 5 by userId";

    private static final String ASSIGN_ORDER_CREATE_SQL = "select userId, data_biggieUserId as biggieUid, count() as count from risk_hit_log where eventCode='assign-order-create' and userId in (%s) and createdAt between '%s' and '%s' group by userId,data_biggieUserId order by userId desc";

    private static final String REAL_TIME_ORDER_ACCEPT_PRE = "select userId,data_type as type,count() as count,formatDateTime(createdAt, '%Y-%m-%d') as createTime from risk_hit_log where eventCode='realtime-order-accept-pre' ";

    private static final String REAL_TIME_ORDER_ACCEPT_PRE_WHERE = "and userId in (%s) and createdAt between '%s' and '%s' group by userId,type,createTime order by createTime asc";

    private static final String COMPLAIN_SQL = "select data_attachment_toUid as uid ,data_attachment_reasonCode as code,count(1) as count from audit_mx_spot_complain where\n" +
            "     data_attachment_toUid in (%s)\n" +
            "and   data_business = 'COMPLAIN' and createdAt between '%s' and '%s'\n" +
            "group by data_attachment_toUid,data_attachment_reasonCode order by count(1) desc;";

    private static final String ACTIVE_COUNT = "select userId, count() as count from risk_hit_log where userId in (%s) and createdAt between '%s' and '%s' group by userId";

    private static final String ASSIGN_ORDER_BIGGIE_SQL = "select userId,data_biggieUserId as biggieUid,count() as count from risk_hit_log where eventCode='assign-order-create'\n" +
            "and data_biggieUserId in (%s) and createdAt between '%s' and '%s' group by userId,data_biggieUserId order by biggieUid desc";

    public void flushEven() {
        List<Event> events = riskRepository.queryEvents();
        for (Event event : events) {
            eventMap.put(event.getCode(), event.getName());
        }
    }

    @PostConstruct
    public void init() {
        String codeMap = config.getProperty("complain.reason.code", "");
        complainMap = JSONObject.parseObject(codeMap, Map.class);
        config.addChangeListener(event -> {
            if (event.isChanged("complain.reason.code")) {
                String changedActions = config.getProperty("complain.reason.code", "");
                complainMap = JSONObject.parseObject(changedActions, Map.class);
            }
        });

    }

    @Override
    public Map<String, Set<PieVO>> batchComplainReasonTop(List<Long> uidList, String time, String queryEndTime) {
        String endTime = queryEndTime + " 23:59:59";
        Date queryEnd = InsightDateUtils.getDateFromString(queryEndTime, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        String startTime = DateUtil.getBeforeDay(queryEnd, AnalysisUtils.getQueryDate(time),
                DateUtil.YYYY_MM_DD_00_00_00);

        String formatSql = String.format(COMPLAIN_SQL, StringUtils.join(uidList, ","), startTime, endTime);
        List<Map<String, String>> list = clickHouseService.executeQuery(formatSql);
        Map<String, Set<PieVO>> result = Maps.newHashMap();
        for (Map<String, String> map : list) {
            String uid = map.get("uid") + "";
            if (!complainMap.containsKey(map.get("code"))) {
                log.warn("举报code 没有配置:{}", map);
                continue;
            }
            if (result.containsKey(uid)) {
                Set<PieVO> pieVOS = result.get(uid);
                pieVOS.add(new PieVO(complainMap.get(map.get("code")), Integer.valueOf(map.get("count") + "")));
            } else {
                Set<PieVO> pieVOS = new HashSet<>();
                pieVOS.add(new PieVO(complainMap.get(map.get("code")), Integer.valueOf(map.get("count") + "")));
                result.put(uid, pieVOS);
            }
        }

        return result;
    }

    @Override
    public Map<String, List<PieVO>> batchActiveTop(List<Long> uidList, String time, String riskType,
                                                   String queryEndTime) {
        String endTime = queryEndTime + " 23:59:59";
        Date queryEnd = InsightDateUtils.getDateFromString(queryEndTime, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        String startTime = DateUtil.getBeforeDay(queryEnd, AnalysisUtils.getQueryDate(time),
                DateUtil.YYYY_MM_DD_00_00_00);


        String externalGroupColumn = " ";
        String riskTypeCondition = "1=1";
        if ("base".equalsIgnoreCase(riskType)) {
            riskTypeCondition = "data_Business!='riskPayment'";
        } else if ("payment".equalsIgnoreCase(riskType)) {
            riskTypeCondition = "data_Business='riskPayment'";
            externalGroupColumn = ",data_uniqTradeType ";
        }
        String querySql = String.format(ACTIVE_SQL, externalGroupColumn, getSql(uidList), riskTypeCondition,
                startTime, endTime, externalGroupColumn);
        List<Map<String, String>> list = clickHouseService.executeQuery(querySql);

        Map<String, List<PieVO>> result = Maps.newHashMap();

        for (Map<String, String> map : list) {
            String userId = (String) map.get("userId");
            Object eventCode = map.get("eventCode");
            if (!eventMap.containsKey(eventCode)) {
                Event query = new Event();
                query.setCode(eventCode + "");
                List<Event> select = riskRepository.selectEventsBy(query);
                log.info("查询到新加事件:{}", select);
                for (Event event : select) {
                    eventMap.put(event.getCode(), event.getName());
                }
                if (!eventMap.containsKey(eventCode)) {
                    log.warn("不存在该事件 没有配置:{}", map);
                    continue;
                }
            }
            Object externalValue = map.get("data_uniqTradeType");
            String pieType = eventMap.get(eventCode);
            if (externalValue != null) {
                pieType += externalValue.toString();
            }

            if (result.containsKey(userId)) {
                result.get(userId).add(new PieVO(pieType, Integer.valueOf(map.get("count") + "")));
            } else {
                List<PieVO> pieVOS = new ArrayList<>();
                pieVOS.add(new PieVO(pieType, Integer.valueOf(map.get("count") + "")));
                result.put(userId, pieVOS);
            }
        }
        return result;
    }

    @Autowired
    private PortraitTagService portraitTagService;

    @Override
    public Map<String, PortraitRow> batchPortraitAnalysis(List uidList, String time, Boolean sensitiveData) {
        Map<String, PortraitRow> response = new HashMap<>();
        for (Object uid : uidList) {
            String commonDevice = "";
            String commonIp = "";
            PortraitTagQueryVO portraitTagQueryVO = new PortraitTagQueryVO();
            portraitTagQueryVO.setDimension("userId");
            portraitTagQueryVO.setValue(uid + "");
            List<QueryTag> list = portraitTagService.getHbaseTags(portraitTagQueryVO);
            Map<String, List<QueryTag>> map = new HashMap<>();
            for (QueryTag queryTag : list) {
                if ("账号7天全事件关联设备次数map".equals(queryTag.getTagName())) {
                    String value = queryTag.getValue();
                    JSONArray jsonArray = JSONArray.parseArray(value);
                    for (Object o : jsonArray) {

                    }

                }
                if ("账号7天全事件关联城市次数map".equals(queryTag.getTagName())) {

                }
                if (queryTag.getValue().contains("{") || queryTag.getValue().contains("[")) {
                    //不需要聚合明细数据
                    continue;
                }
                if (map.containsKey(queryTag.getTypeName())) {
                    map.get(queryTag.getTypeName()).add(queryTag);
                } else {
                    List<QueryTag> subList = new ArrayList<>();
                    subList.add(queryTag);
                    map.put(queryTag.getTypeName(), subList);
                }
            }
            PortraitRow portraitRow = new PortraitRow();
            portraitRow.setUserPortraitList(map);
            response.put(uid + "", portraitRow);
        }
        return response;
    }

    @Override
    public Map<String, Map<String, Integer>> biggieAssignOrder(List<Long> uidList, String time, String queryEndTime) {
        String endTime = queryEndTime + " 23:59:59";
        Date queryEnd = InsightDateUtils.getDateFromString(queryEndTime, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        String startTime = DateUtil.getBeforeDay(queryEnd, AnalysisUtils.getQueryDate(time),
                DateUtil.YYYY_MM_DD_00_00_00);
        List<Map<String, String>> list = clickHouseService.executeQuery(String.format(ASSIGN_ORDER_BIGGIE_SQL,
                getSql(uidList), startTime, endTime));

        Map<String, Map<String, Integer>> result = Maps.newHashMap();
        for (Map<String, String> map : list) {
            Object userId = map.get("userId");
            Object biggieUid = map.get("biggieUid");
            Object count = map.get("count");
            if (result.containsKey(biggieUid + "")) {
                Map<String, Integer> record = result.get(biggieUid + "");
                record.put("userCount", record.get("userCount") + 1);
                record.put("total", record.get("total") + Integer.valueOf(count + ""));
                result.put(biggieUid + "", record);
            } else {
                Map<String, Integer> record = Maps.newHashMap();
                record.put("userCount", 1);
                record.put("total", Integer.valueOf(count + ""));
                result.put(biggieUid + "", record);
            }
        }
        return result;
    }

    @Override
    public Map<String, Object> batchUserRiskOverview(List<String> uidList, String time, String queryEndTime) {
        Map<String, RiskOverviewResult> resultMap = riskOverviewService.batchUserRiskOverview(uidList, time, queryEndTime);
        Map<String, Object> returnMap = new HashMap<>();
        resultMap.forEach((k, v) -> {
            returnMap.put(k, v);
        });
        return returnMap;
    }

    private Map<String, Integer> batchUserIdActiveCount(List<Long> uidList, String time, String queryEndTime) {
        String endTime = queryEndTime + " 23:59:59";
        Date queryEnd = InsightDateUtils.getDateFromString(queryEndTime, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        String startTime = DateUtil.getBeforeDay(queryEnd, AnalysisUtils.getQueryDate(time),
                DateUtil.YYYY_MM_DD_00_00_00);

        List<Map<String, String>> list = clickHouseService.executeQuery(String.format(ACTIVE_COUNT, getSql(uidList),
                startTime, endTime));
        Map<String, Integer> result = Maps.newHashMap();
        for (Map<String, String> map : list) {
            Object userId = map.get("userId");
            Object count = map.get("count");
            result.put(userId + "", Integer.valueOf(count + ""));
        }
        return result;
    }

    @Override
    public Map<String, AssignOrderRow> batchAssignOrder(List uidList, String time, String queryEndTime) {
        String endTime = queryEndTime + " 23:59:59";
        Date queryEnd = InsightDateUtils.getDateFromString(queryEndTime, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        String startTime = DateUtil.getBeforeDay(queryEnd, AnalysisUtils.getQueryDate(time),
                DateUtil.YYYY_MM_DD_00_00_00);

        List<Map<String, String>> list = clickHouseService.executeQuery(String.format(ASSIGN_ORDER_CREATE_SQL,
                getSql(uidList), startTime, endTime));

        Map<String, Object> countMap = batchUserIdActiveCount(uidList, time, queryEndTime);

        Map<String, Map<String, Integer>> biggieAssignOrder = biggieAssignOrder(uidList, time, queryEndTime);

        Map<String, AssignOrderRow> result = Maps.newHashMap();
        for (Map<String, String> map : list) {
            String userId = (String) map.get("userId");
            PieVO pieVO = new PieVO(map.get("biggieUid") + "", Integer.valueOf(map.get("count") + ""));
            if (result.containsKey(userId)) {
                AssignOrderRow assignOrderRow = result.get(userId);
                assignOrderRow.getPieVOList().add(pieVO);
                assignOrderRow.setCount(assignOrderRow.getCount() + pieVO.getValue());
                if (assignOrderRow.getTopGod().getValue() < pieVO.getValue()) {
                    assignOrderRow.setTopGod(pieVO);
                }
                result.put(userId, assignOrderRow);
            } else {
                AssignOrderRow assignOrderRow = new AssignOrderRow();
                List<PieVO> pieVOS = new ArrayList<>();
                pieVOS.add(pieVO);
                assignOrderRow.setCount(pieVO.getValue());
                assignOrderRow.setPieVOList(pieVOS);
                assignOrderRow.setTopGod(pieVO);
                result.put(userId, assignOrderRow);
            }

        }
        result.forEach((k, v) -> {

            if (biggieAssignOrder.containsKey(k)) {
                Map<String, Integer> biggie = biggieAssignOrder.get(k);
                v.setCustomers(biggie.get("userCount"));
                v.setTotalOrders(biggie.get("total"));
            }
            if (countMap.containsKey(k)) {

                Integer o = (Integer) countMap.get(k);
                BigDecimal totalCount = BigDecimal.valueOf(o);

                BigDecimal count = BigDecimal.valueOf(v.getCount());
                BigDecimal divide = count.divide(totalCount, BigDecimal.ROUND_HALF_UP, BigDecimal.ROUND_HALF_DOWN);

                BigDecimal multiply = divide.multiply(BigDecimal.valueOf(100));
                v.setRatio(multiply.doubleValue() + "%");

            }
            List<PieVO> pieVOList = v.getPieVOList();
            v.setGodCount(pieVOList.size());
            log.info("batchAssignOrder pieVOList:{},{}", pieVOList.size(), pieVOList);
            if (CollectionUtils.isNotEmpty(pieVOList) && pieVOList.size() > 10) {
                Set<PieVO> objects = new TreeSet<>();
                objects.addAll(pieVOList);
                int i = 0;
                List<PieVO> newList = new ArrayList<>();
                PieVO otherPie = new PieVO("其他", 0);
                for (PieVO pieVO : objects) {
                    if (i < 9) {
                        newList.add(pieVO);
                        log.info("batchAssignOrder newList i:{},{},{}", i, pieVO, newList.size());
                    } else {
                        otherPie.setValue(otherPie.getValue() + pieVO.getValue());
                        log.info("batchAssignOrder otherPie:{}, pieVO{}", otherPie, pieVO);
                    }
                    i++;
                }
                log.info("batchAssignOrder newList:{},{}", newList.size(), newList);
                newList.add(otherPie);
                v.setPieVOList(newList);
            }

        });

        return result;
    }

    private String getSql(List uidList) {
        if (CollectionUtils.isEmpty(uidList)) {
            return StringUtils.EMPTY;
        }
        StringBuilder sb = new StringBuilder();
        for (Object aLong : uidList) {
            sb.append("'").append(aLong).append("'").append(",");
        }
        StringBuilder stringBuilder = sb.deleteCharAt(sb.length() - 1);
        return stringBuilder.toString();
    }

    @Override
    public Map<String, RealTimeOrderAcceptRow> batchRealTimeOrderAccept(List uidList, String time,
                                                                        String queryEndTime) {
        String endTime = queryEndTime + " 23:59:59";
        Date queryEnd = InsightDateUtils.getDateFromString(queryEndTime, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        String startTime = DateUtil.getBeforeDay(queryEnd, AnalysisUtils.getQueryDate(time),
                DateUtil.YYYY_MM_DD_00_00_00);

        String format = String.format(REAL_TIME_ORDER_ACCEPT_PRE_WHERE, getSql(uidList), startTime, endTime);
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append(REAL_TIME_ORDER_ACCEPT_PRE).append(format);
        List<Map<String, String>> list = clickHouseService.executeQuery(sqlBuilder.toString());
        //TODO 占比没统计
        Map<String, RealTimeOrderAcceptRow> result = Maps.newHashMap();
        for (Map<String, String> map : list) {
            String userId = (String) map.get("userId");
            String type = (String) map.get("type");
            if ("1".equals(type)) {
                type = "文字";
            } else {
                type = "语音";
            }
            String date = (String) map.get("createTime");
            Integer count = Integer.valueOf(map.get("count") + "");
            RealTimeOrderPie realTimeOrderPie = new RealTimeOrderPie();
            realTimeOrderPie.setName(type);
            realTimeOrderPie.setDate(date);
            realTimeOrderPie.setCount(count);
            if (result.containsKey(userId)) {
                RealTimeOrderAcceptRow realTimeOrderAcceptRow = result.get(userId);
                realTimeOrderAcceptRow.setCount(realTimeOrderAcceptRow.getCount() + count);
                realTimeOrderAcceptRow.getRealTimeOrderPies().add(realTimeOrderPie);
                if (realTimeOrderAcceptRow.getRealTimeOrderTypePie().containsKey(type)) {
                    realTimeOrderAcceptRow.getRealTimeOrderTypePie().put(type, realTimeOrderAcceptRow.getRealTimeOrderTypePie().get(type) + count);
                } else {
                    realTimeOrderAcceptRow.getRealTimeOrderTypePie().put(type, count);
                }

            } else {
                RealTimeOrderAcceptRow realTimeOrderAcceptRow = new RealTimeOrderAcceptRow();

                List<RealTimeOrderPie> realTimeOrderPieList = new ArrayList<>();

                realTimeOrderPieList.add(realTimeOrderPie);
                realTimeOrderAcceptRow.setRealTimeOrderPies(realTimeOrderPieList);

                Map<String, Integer> realTypeMap = Maps.newHashMap();
                realTypeMap.put(type, count);
                realTimeOrderAcceptRow.setRealTimeOrderTypePie(realTypeMap);

                realTimeOrderAcceptRow.setCount(count);

                result.put(userId, realTimeOrderAcceptRow);
            }

        }
        Map<String, Object> countMap = batchUserIdActiveCount(uidList, time, queryEndTime);

        result.forEach((k, v) -> {
            if (countMap.containsKey(k)) {
                Integer o = (Integer) countMap.get(k);

                BigDecimal totalCount = BigDecimal.valueOf(o - v.getCount());

                v.setActiveCount(totalCount.intValue());
                BigDecimal count = BigDecimal.valueOf(v.getCount());
                BigDecimal divide = count.divide(totalCount, BigDecimal.ROUND_HALF_UP, BigDecimal.ROUND_HALF_DOWN);

                BigDecimal multiply = divide.multiply(BigDecimal.valueOf(100));
                v.setRatio(multiply.doubleValue() + "%");
            }
        });

        return result;
    }
}
