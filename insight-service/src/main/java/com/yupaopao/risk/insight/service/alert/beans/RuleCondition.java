package com.yupaopao.risk.insight.service.alert.beans;

import com.dianping.cat.util.StringUtils;
import lombok.Getter;
import lombok.Setter;

/****
 * zengxiangcai
 * 2022/11/9 17:10
 ***/


@Getter
@Setter
public class RuleCondition {
    private String type;
    private String paramN;
    private String paramM;


    public String getThresholdDesc() {
        StringBuffer buffer = new StringBuffer();
        if (StringUtils.isNotEmpty(paramN)) {
            buffer.append("N: " + paramN);
        }
        if (StringUtils.isNotEmpty(paramM)) {
            buffer.append(",M: " + paramM);
        }
        String result = buffer.toString();
        if (result.startsWith(",")) {
            result = result.substring(1);
        }
        return result;
    }

    public boolean equals(RuleCondition other) {
        if (other == null) {
            return false;
        }
        boolean typeEqual =
                (type == null && other.getType() == null) || (type != null && type.equals(other.getType()));

        boolean paramNEqual =
                (paramN == null && other.getParamN() == null) || (paramN != null && paramN.equals(other.getParamN()));

        boolean paramMEqual =
                (paramM == null && other.getParamM() == null) || (paramM != null && paramM.equals(other.getParamM()));
        return typeEqual && paramNEqual && paramMEqual;


    }
}
