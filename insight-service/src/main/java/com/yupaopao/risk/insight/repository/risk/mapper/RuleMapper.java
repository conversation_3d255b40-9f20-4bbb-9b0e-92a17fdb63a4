package com.yupaopao.risk.insight.repository.risk.mapper;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.common.model.AtomRule;
import com.yupaopao.risk.common.model.Event;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface RuleMapper extends Mapper<AtomRule> {

    @Select("select t.name as dstName, t.id as dstRelateId, t.risk_const as srcName\n" +
            "from risk_rule_atom t,\n" +
            "(SELECT DISTINCT _function\n" +
            "    FROM risk_attribute\n" +
            "    WHERE LOWER(_function) LIKE '%portrait%') as f\n" +
            "where t.status = 'ENABLE'\n" +
            "and t.risk_const like CONCAT('%', f._function, '%')")
    List<JSONObject> querySyncStraRelateTag();

    @Select("select t.name as dstName, t.id as dstRelateId, t.risk_const as srcName\n" +
            "from risk_patrol_rule t,\n" +
            "     (SELECT DISTINCT _function\n" +
            "      FROM risk_attribute\n" +
            "      WHERE LOWER(_function) LIKE '%portrait%') as f\n" +
            "where t.state = 1\n" +
            "  and t.risk_const like CONCAT('%', f._function, '%')")
    List<JSONObject> queryAsyncStraRelateTag();


    @Select("select distinct name as attr from risk_attribute\n" +
            " where LOWER(_function) like '%portrait%'")
    List<String> queryPortraitRelateAttr();


    @Select("select id as srcRelateId, name as srcName, punish_package_ids as pids, punish_package_attr as pidAttrs\n" +
            "from risk_rule_atom\n" +
            "where status = 'ENABLE'\n" +
            "and punish_package_ids != ''")
    List<JSONObject> queryTagRelateSyncStra();



    @Select("select id as srcRelateId, name as srcName, punish_package_id as pids, parameters as pidAttrs\n" +
            "from risk_patrol_rule\n" +
            "where state = 1\n" +
            "  and punish_package_id != ''")
    List<JSONObject> queryTagRelateAsyncStra();


    @Select("select pkg_id as pid, ext_conf as tagCode\n" +
            "from t_punish_ability\n" +
            "where code like 'MARK_PORTRAIT%'\n" +
            "and code not like 'MARK_PORTRAIT_%_CUSTOM'")
    List<JSONObject> queryTagPid();

    @Select("select pkg_id as pid\n" +
            "from t_punish_ability\n" +
            "where code like 'MARK_PORTRAIT_%_CUSTOM'")
    List<Integer> queryCustomTagPid();
}
