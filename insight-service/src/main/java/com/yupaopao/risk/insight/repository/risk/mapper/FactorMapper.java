package com.yupaopao.risk.insight.repository.risk.mapper;

import com.yupaopao.risk.common.model.Factor;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface FactorMapper extends Mapper<Factor> {
    @Select("select id from risk_factor where group_key = #{group_key} ")
    List<Long> queryFactorIdByGroupKey(@Param("group_key") String groupKey);
}
