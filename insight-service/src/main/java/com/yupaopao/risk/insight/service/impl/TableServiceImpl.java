package com.yupaopao.risk.insight.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.yupaopao.risk.insight.beans.DBTableInfo;
import com.yupaopao.risk.insight.repository.mapper.TableDetailMapper;
import com.yupaopao.risk.insight.repository.mapper.TableMapper;
import com.yupaopao.risk.insight.repository.model.TableDetail;
import com.yupaopao.risk.insight.repository.model.TableInfo;
import com.yupaopao.risk.insight.service.ClickHouseService;
import com.yupaopao.risk.insight.service.TableService;
import com.yupaopao.risk.insight.service.beans.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-03 13:19
 * 数仓管理service
 ***/

@Service
@Slf4j
public class TableServiceImpl implements TableService {

    @Autowired
    private TableMapper tableMapper;

    @Autowired
    private TableDetailMapper tableDetailMapper;

    @Autowired
    private ClickHouseService clickHouseService;

    @Override
    public boolean existsTable(String tableName) {
        TableInfo tableInfo = new TableInfo();
        tableInfo.setName(tableName);
        return !CollectionUtils.isEmpty(tableMapper.select(tableInfo));
    }

    @Override
    public Optional<DBTableInfo> getTableInfo(String tableName) {

        TableInfo table = getLatestTableByName(tableName);
        if (table == null) {
            return Optional.empty();
        }
        DBTableInfo dbTableInfo = new DBTableInfo();
        dbTableInfo.setTableName(table.getName());
        dbTableInfo.setTableId(table.getId());
        dbTableInfo.setTableType(table.getType());
        //get columns
        TableDetail searchDetail = new TableDetail();
        searchDetail.setTableId(table.getId());
        List<TableDetail> tableDetailList = tableDetailMapper.select(searchDetail);
        if (CollectionUtils.isNotEmpty(tableDetailList)) {
            List<DBTableInfo.TableColumn> columnList = tableDetailList.stream().map(elem -> {
                DBTableInfo.TableColumn column = new DBTableInfo.TableColumn(elem.getName(), elem.getType(), elem.getId());
                return column;
            }).collect(Collectors.toList());
            dbTableInfo.setTableColumns(columnList);
        }

        return Optional.of(dbTableInfo);
    }

    @Override
    public Integer getIdForUserDefinedTable(String tableName) {
        TableInfo table = getLatestTableByName(tableName);
        if (table == null) {
            throw new IllegalArgumentException(String.format("table[%] does not exist", tableName));
        }
        return table.getId();
    }


    private TableInfo getLatestTableByName(String tableName) {
        TableInfo searchTable = new TableInfo();
        searchTable.setName(tableName);
        List<TableInfo> tableList = tableMapper.select(searchTable);
        if (CollectionUtils.isEmpty(tableList)) {
            return null;
        }
        return tableList.stream().max(Comparator.comparing(TableInfo::getId)).get();
    }


//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public TableInfo uploadTable(String sql, DBTableType tableType, String createBy) {
//        DBTableInfo dbTableInfo = ParserSelector.select(ParserEnum.JSQL_PARSER).parseCreate(sql);
//        dbTableInfo.setTableType(tableType.name());
//        //insert into db
//        TableInfo insertTable = new TableInfo();
//        insertTable.setName(dbTableInfo.getTableName());
//        insertTable.setType(dbTableInfo.getTableType());
//        insertTable.setCreateTime(new Date());
//        insertTable.setUpdateTime(new Date());
//        insertTable.setCreateBy(StringUtils.isEmpty(createBy) ? "admin" : createBy);
//        checkTableName(insertTable);
//        int created = tableMapper.insertSelective(insertTable);
//        if (created != 1) {
//            //create table error
//            log.warn("create table error,created=" + created);
//            throw new IllegalArgumentException("create table error");
//        }
//        List<TableDetail> tableDetailList = dbTableInfo.getTableColumns().stream().map(elem -> {
//            TableDetail detail = new TableDetail();
//            detail.setName(elem.getColumnName());
//            detail.setType(elem.getType());
//            detail.setCreateTime(new Date());
//            detail.setUpdateTime(new Date());
//            detail.setTableId(insertTable.getId());
//            return detail;
//        }).collect(Collectors.toList());
//        checkTableDetail(tableDetailList);
//        int rowCreated = tableDetailMapper.insertList(tableDetailList);
//        return insertTable;
//    }

    @Override
    public PageResult<TableInfo> getTables(TableInfo tableInfo, Integer pageSize, Integer pageNumber) {
        String orderBy = " create_time desc ";
        PageHelper.startPage(pageNumber, pageSize, orderBy);

        Example search = new Example(TableInfo.class);
        Example.Criteria criteria = search.createCriteria();
        if (tableInfo.getId() != null) {
            criteria.andEqualTo("id", tableInfo.getId());
        }
        if (StringUtils.isNotBlank(tableInfo.getName())) {
            criteria.andCondition("upper(name) like '" + "%" + tableInfo.getName().toUpperCase() + "%'");
        }
        if (StringUtils.isNotBlank(tableInfo.getType())) {
            criteria.andEqualTo("type", tableInfo.getType());
        }
        List<TableInfo> list = tableMapper.selectByExample(search);
        PageInfo page = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setDataList(list);
        return pageResult;
    }

    @Override
    public List<String> queryTables(String tableName) {
//        PageHelper.startPage(10, 0);
//        Example example = new Example(TableInfo.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (tableInfo.getType() != null) {
//            criteria.andEqualTo("type", tableInfo.getType());
//        }
//        if (StringUtils.isNotBlank(tableInfo.getName())) {
//            criteria.andCondition("name like '" + "%" + tableInfo.getName() + "%'");
//        }
        return tableMapper.queryTables(tableName);
    }

    @Override
    public PageResult<TableDetail> getTableDetails(TableDetail tableDetail, Integer pageSize, Integer pageNumber) {
        //对risk_hit_log字段比较多，可以考虑根据remarks的长度,column的长度来排序，有remark多数是常用的字段
        String orderBy = " if(remarks='',0,1) desc,length(name) asc";
        PageHelper.startPage(pageNumber, pageSize);

        Example search = new Example(TableDetail.class);
        Example.Criteria criteria = search.createCriteria();
        if (StringUtils.isNotBlank(tableDetail.getType())) {
            criteria.andEqualTo("type", tableDetail.getType());
        }
        if (StringUtils.isNotBlank(tableDetail.getName())) {
            criteria.andCondition("upper(name) like '" + "%" + tableDetail.getName().toUpperCase() + "%'");
//            criteria.andLike("name", "%"+tableDetail.getName()+"%");
        }
        if (StringUtils.isNotBlank(tableDetail.getRemarks())) {
            criteria.andLike("remarks", "%" + tableDetail.getRemarks() + "%");
        }
        if (tableDetail.getTableId() != null) {
            criteria.andEqualTo("tableId", tableDetail.getTableId());
        }
        if (tableDetail.getId() != null) {
            criteria.andEqualTo("id", tableDetail.getId());
        }
        List<TableDetail> list = tableDetailMapper.selectByExample(search);
        PageInfo page = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setCurrentPage(page.getPageNum());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setDataList(list);
        return pageResult;
    }

    @Override public Set<String> getTableAndDetail() {
        Set<String> data = new HashSet<>();
        HashMap<Integer, String> tableNameMap = new HashMap<>();

        List<TableInfo> tableInfos = tableMapper.selectAll();
        tableInfos.forEach(p -> {
            data.add(p.getName());
            tableNameMap.put(p.getId(), p.getName());
        });

        List<TableDetail> tableDetails = tableDetailMapper.selectAll();
        tableDetails.stream().filter(p -> tableNameMap.containsKey(p.getTableId())).forEach(p -> {
            data.add(tableNameMap.get(p.getTableId()) + "." + p.getName());
        });
        return data;
    }

    @Override
    public List<TableInfo> getTables() {
        return tableMapper.selectAll();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TableInfo uploadTable(DBTableInfo dbTableInfo) {
        //insert into db
        TableInfo insertTable = new TableInfo();
        insertTable.setName(dbTableInfo.getTableName());
        insertTable.setType(dbTableInfo.getTableType());
        insertTable.setRemarks(dbTableInfo.getRemarks());
        insertTable.setCreateBy(dbTableInfo.getCreatedBy());
        checkTableName(insertTable);
        int created = tableMapper.insertSelective(insertTable);
        if (created != 1) {
            //create table error
            log.warn("create table error,created=" + created);
            throw new IllegalArgumentException("create table error");
        }
        List<TableDetail> tableDetailList = dbTableInfo.getTableColumns().stream().map(elem -> {
            TableDetail detail = new TableDetail();
            detail.setName(elem.getColumnName());
            detail.setType(elem.getType());
            detail.setCreateBy(dbTableInfo.getCreatedBy());
            detail.setTableId(insertTable.getId());
            detail.setRemarks("");
            return detail;
        }).collect(Collectors.toList());
        checkTableDetail(tableDetailList);
        tableDetailMapper.insertList(tableDetailList);
        return insertTable;
    }

    @Override
    public TableInfo upsertTable(TableInfo tableInfo) {
        if (tableInfo.getId() == null) {
            tableMapper.insertSelective(tableInfo);
        } else {
            tableMapper.updateByPrimaryKeySelective(tableInfo);
        }
        return tableInfo;
    }

    @Override
    public TableDetail upsertTableDetail(TableDetail tableDetail) {
        checkTableDetail(Arrays.asList(tableDetail));
        if (tableDetail.getId() == null) {
            tableDetailMapper.insertSelective(tableDetail);
        } else {
            tableDetailMapper.updateByPrimaryKeySelective(tableDetail);
        }
        return tableDetail;
    }

    @Override
    public boolean saveTableDetails(List<TableDetail> tableDetails) {
        return tableDetailMapper.insertList(tableDetails) > 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTable(Integer tableId) throws Exception{
        //删除表字段明细
        Example deleteDetail = new Example(TableDetail.class);
        deleteDetail.createCriteria().andEqualTo("tableId",tableId);
        tableDetailMapper.deleteByExample(deleteDetail);
        //删除表记录

        TableInfo table = new TableInfo();
        table.setId(tableId);
        TableInfo dbTable =tableMapper.selectByPrimaryKey(table);
        tableMapper.deleteByPrimaryKey(tableId);
        if(dbTable!=null && "TEMP".equalsIgnoreCase(dbTable.getType())){
            //删除ck
            clickHouseService.dropDistributedTable(dbTable.getName());
        }
        return true;
    }

    @Override
    public boolean deleteTableDetail(Integer detailId) {
        tableDetailMapper.deleteByPrimaryKey(detailId);
        return true;
    }

    /***
     * 表名不能以数字开头
     * @param tableInfo
     * @return
     */
    private boolean checkTableName(TableInfo tableInfo) {
        String tableName = tableInfo.getName();
        if (StringUtils.isEmpty(tableName)) {
            throw new IllegalArgumentException("table name is empty");
        }
        if (!isValidName(tableName)) {
            throw new IllegalArgumentException("column name is invalid, first letter must be _ or alphabet");
        }
        return true;
    }

    /**
     * 字段不能以数字开头
     *
     * @param details
     * @return
     */
    private boolean checkTableDetail(List<TableDetail> details) {
        for (TableDetail detail : details) {
            if (!isValidName(detail.getName())) {
                throw new IllegalArgumentException("column name is invalid, first letter must be _ or alphabet");
            }
        }
        return true;
    }

    private boolean isValidName(String name) {
        return NAME_PATTERN.matcher(name).find();
    }

    private static final Pattern NAME_PATTERN = Pattern.compile("(^_([a-zA-Z0-9|\\-]_?)*$)|(^[a-zA-Z](_?[a-zA-Z0-9|\\-])*_?$)");


}
