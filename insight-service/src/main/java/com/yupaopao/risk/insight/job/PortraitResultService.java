package com.yupaopao.risk.insight.job;

import com.yupaopao.aries.client.JobExecutionContext;
import com.yupaopao.aries.client.JobListener;
import com.yupaopao.framework.spring.boot.aries.annotation.AriesCronJobListener;
import com.yupaopao.framework.spring.boot.redis.RedisService;
import com.yupaopao.framework.spring.boot.redis.annotation.RedisAutowired;
import com.yupaopao.risk.insight.InsightException;
import com.yupaopao.risk.insight.common.meta.JobParams;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.enums.ErrorMessage;
import com.yupaopao.risk.insight.flink.beans.RemoteSQLResponse;
import com.yupaopao.risk.insight.flink.tools.FlinkRestClient;
import com.yupaopao.risk.insight.repository.model.JobInfo;
import com.yupaopao.risk.insight.service.JobService;
import com.yupaopao.risk.insight.service.beans.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.ArrayList;
import java.util.List;
@Slf4j
@Deprecated
@AriesCronJobListener
public class PortraitResultService implements JobListener {

    @Value("${portrait.device.agg.days}")
    private Integer aggDay;
    @Autowired
    private FlinkRestClient flinkRestClient;

    @Autowired
    private JobService jobService;

    @RedisAutowired("middleware.redis.risk")
    private RedisService redisService;


    @Override
    public String getName() {
        return PortraitResultService.class.getName();
    }

    @Override
    public void execute(JobExecutionContext jobExecutionContext) {

        JobParams jobParams = new JobParams();
        List<JobParams.TableQueryDatePeriod> periods = new ArrayList<>();
        JobParams.TableQueryDatePeriod datePeriod = new JobParams.TableQueryDatePeriod();
        datePeriod.setBeginDate(InsightDateUtils.getAfterDayStr(-aggDay));
        datePeriod.setEndDate(InsightDateUtils.getCurrentDayStr());
        periods.add(datePeriod);
        jobParams.setDatePeriodList(periods);
        JobInfo jobInfo = new JobInfo();
        jobInfo.setJobName("system_portrait_device_agg_job");
        jobInfo.setStatus("COMPLETED");
        jobInfo.setCreateTime(InsightDateUtils.getCurrentDateZero());

        Object jobState = redisService.get("system_portrait_device_agg_job");
        if (null != jobState && Boolean.valueOf(jobState+"")){
            log.info("今日汇聚已完成");
            return;
        }

        PageResult<JobInfo> pagedJobList = jobService.getPagedJobList(jobInfo, 1, 1);
        if (CollectionUtils.isEmpty(pagedJobList.getDataList())){
            log.info("收集任务未完成");
            return;
        }

        RemoteSQLResponse response = flinkRestClient.runSql(jobParams, "risk-insight-portrait-active-device.jar");
        if (!response.isSuccess()) {
            throw new InsightException(ErrorMessage.SQL_EXECUTE_ERROR.getCode(), response.getMessage());
        }

        if (response.isSuccess()) {
            redisService.set("system_portrait_device_agg_job", true);
        }
    }
}
