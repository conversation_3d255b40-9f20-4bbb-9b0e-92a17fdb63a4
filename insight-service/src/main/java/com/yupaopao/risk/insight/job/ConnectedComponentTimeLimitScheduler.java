package com.yupaopao.risk.insight.job;

import com.yupaopao.aries.client.JobExecutionContext;
import com.yupaopao.aries.client.JobListener;
import com.yupaopao.framework.spring.boot.aries.annotation.AriesCronJobListener;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.service.FlinkJobSchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.Map;

/****
 * zengxiangcai
 * 2021/4/16 1:55 下午
 ***/

@Slf4j
@AriesCronJobListener
public class ConnectedComponentTimeLimitScheduler implements JobListener {

    @Autowired
    private FlinkJobSchedulerService flinkJobSchedulerService;

    @Value("${cc.hourLimit:4}")
    private String ccHourLimit;
    @Value("${cc.dayLimit:1}")
    private String ccDayLimit;
    @Value("${cc.needRunDaily:true}")
    private String needRunDaily;
    @Value("${cc.needRunHourly:true}")
    private String needRunHourly;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        String jarName = "connected-detection-time-limit.jar";
        String runTime = getRunTime();
        Map<String, String> argMap = new HashMap<>();
        argMap.put("hourLimit", ccHourLimit);
        argMap.put("runTime", runTime);

        try {
            if (runTime.endsWith("00:00:00") && "true".equals(needRunDaily.trim())) {
                argMap.put("dayLimit", ccDayLimit);
                flinkJobSchedulerService.run(jarName, argMap);
                argMap.remove("dayLimit");
            }
            if ("true".equals(needRunHourly)) {
                flinkJobSchedulerService.run(jarName, argMap);
            }
        } catch (Exception e) {
            log.error("run " + jarName + " error: ", e);
        }
    }

    private String getRunTime() {
        String currentTime = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH);
        return currentTime + ":00:00";
    }

}
