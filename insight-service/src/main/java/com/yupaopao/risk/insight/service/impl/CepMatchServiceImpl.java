package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.repository.model.CepPattern;
import com.yupaopao.risk.insight.repository.model.CepResult;
import com.yupaopao.risk.insight.service.CepMatchResultHandler;
import com.yupaopao.risk.insight.service.CepMatchService;
import com.yupaopao.risk.insight.service.CepService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-17 19:27
 *
 ***/

@Service
public class CepMatchServiceImpl implements CepMatchService {

    @Autowired
    private List<CepMatchResultHandler> handlers;

    @Autowired
    private CepService cepService;

    @Override
    public boolean process(CepResult result, Object subBatchId) {
        Integer patternId = result.getPatternId();
        CepPattern pattern = cepService.getPattern(patternId);
        if (pattern == null) {
            return false;
        }
        if (StringUtils.isEmpty(pattern.getResultHandler()) || "\"[]\"".equals(pattern.getResultHandler()) || "[]".equals(pattern.getResultHandler())) {
            return true;
        }
        JSONObject jsonObj = JSON.parseObject(pattern.getResultHandler());
        //兼容未配置惩罚包参数
        Object rel;
        String type;
        String event = jsonObj.getString("event");
        if (!StringUtils.isEmpty(event)) {
             rel = result;
            type = pattern.getResultHandler();
        }else {
            rel = subBatchId;
            type = jsonObj.getString("handlerType");
        }

        //处理所有后续流程
        handlers.stream().filter(elem -> elem.filter(type, rel))
                .forEach(handler -> handler.doHandle(result, pattern));
        return true;
    }
}
