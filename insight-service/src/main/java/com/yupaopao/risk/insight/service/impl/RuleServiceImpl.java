package com.yupaopao.risk.insight.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yupaopao.risk.insight.repository.risk.dao.RiskRepository;
import com.yupaopao.risk.insight.service.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Avalon on 2023/12/27 19:20
 **/
@Slf4j
@Service
public class RuleServiceImpl implements RuleService {


    @Resource
    private RiskRepository riskRepository;

    @Override
    public List<JSONObject> querySyncStraRelateTag() {
        return riskRepository.querySyncStraRelateTag();
    }

    @Override
    public List<JSONObject> queryAsyncStraRelateTag() {
        return riskRepository.queryAsyncStraRelateTag();
    }

    @Override
    public List<String> queryPortraitRelateAttr() {
        return riskRepository.queryPortraitRelateAttr();
    }

    @Override
    public List<JSONObject> queryTagRelateSyncStra() {
        return riskRepository.queryTagRelateSyncStra();
    }

    @Override
    public List<JSONObject> queryTagRelateAsyncStra() {
        return riskRepository.queryTagRelateAsyncStra();
    }

    @Override
    public Map<Integer, String> queryTagPid() {
        Map<Integer, String> pidTagCodeMap = new HashMap<>();
        List<JSONObject> tagPidList = riskRepository.queryTagPid();
        if (!CollectionUtils.isEmpty(tagPidList)) {
            tagPidList.forEach(tagPid -> {
                Integer pid = tagPid.getInteger("pid");
                String tagCode = JSONPath.read(tagPid.getString("tagCode"), "$.code", String.class);
                if (pid == null || tagCode == null) {
                    return;
                }
                pidTagCodeMap.put(pid, tagCode);
            });
        }
        return pidTagCodeMap;
    }

    @Override
    public List<Integer> queryCustomTagPid() {
        return riskRepository.queryCustomTagPid();
    }
}
