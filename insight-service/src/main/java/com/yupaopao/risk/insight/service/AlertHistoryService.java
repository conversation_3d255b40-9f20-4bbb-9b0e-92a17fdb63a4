package com.yupaopao.risk.insight.service;

import com.yupaopao.risk.insight.repository.model.AlertHistory;
import com.yupaopao.risk.insight.service.beans.AlertHistoryParam;
import com.yupaopao.risk.insight.service.beans.PageResult;


/****
 * zengxiangcai
 * 2022/11/15 14:17
 ***/
public interface AlertHistoryService {

    boolean addAlertHistory(AlertHistory alertHistory);

    PageResult<AlertHistory> getAlertHistory(AlertHistoryParam queryParams);

    Integer sendCountPerDay(Long ruleId);
}
