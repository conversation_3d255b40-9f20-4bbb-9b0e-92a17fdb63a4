package com.yupaopao.risk.insight.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.session.data.redis.RedisOperationsSessionRepository;

@Configuration
@Slf4j
public class CusterHttpSessionListener {
    @Value("${sessionTimeOut:18000}")
    private Integer sessionTimeOut;
    @Autowired
    private RedisOperationsSessionRepository sessionRepository;

    @EventListener
    public void onApplicationEvent(ApplicationEvent event) {
        if (event instanceof ContextRefreshedEvent) {
            log.info("设置默认session失效时间 {}", sessionTimeOut);
            sessionRepository.setDefaultMaxInactiveInterval(sessionTimeOut);
        }
    }


}
