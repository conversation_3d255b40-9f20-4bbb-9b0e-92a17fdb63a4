package com.yupaopao.risk.insight.service.beans;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;
import java.util.Set;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-11-21 15:34
 *
 ***/

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SqlResult {
    private List<Map<String,String>> rows;
    private String nextStartKey;
    private Boolean end;
    //列的可变的，有些列可能没数据，一次统计下所有列名称
    private List<String> fieldNames;


    private String status;

    private Integer totalCount;

}
