package com.yupaopao.risk.insight.enums;

public enum ExpTypeEnum {

    PEERAGE(0, "爵位"),
    VIP(1, "VIP"),
    YUER_LIVE_USER(2, "鱼耳直播用户"),
    YUER_LIVE(3, "鱼耳直播主播"),
    GOD(4, "大神"),
    AUDIO_SHOW(5, "配音秀"),
    NEW_VIP(6, "新VIP");

    ExpTypeEnum(Integer type, String name){
        this.type = type;
        this.name = name;
    }
    private Integer type;
    private String name;

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static ExpTypeEnum getVip(Integer type){
        for (ExpTypeEnum value : values()) {
            if (value.type==type) {
                return value;
            }
        }
        return null;
    }
}
