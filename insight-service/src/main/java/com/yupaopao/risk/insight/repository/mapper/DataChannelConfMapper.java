package com.yupaopao.risk.insight.repository.mapper;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.beans.TagDataChannelParam;
import com.yupaopao.risk.insight.repository.model.DataChannelConfVO;
import com.yupaopao.risk.insight.repository.model.TagInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface DataChannelConfMapper extends Mapper<DataChannelConfVO> {




    @Select("<script>"
            + "select id,code,type,name,remark,extend_conf as extendConf,update_time as updateTime, update_by as updateBy from t_data_channel_conf WHERE 1=1\n"
            + "<if test='param.code!=null'>"
            + " and code = #{param.code}"
            + "</if>"
            + "<if test='param.name!=null'>"
            + " and name = #{param.name}"
            + "</if>"
            + "ORDER BY update_time desc"
            + "</script>")
    List<DataChannelConfVO> query(@Param("param") TagDataChannelParam param);
}
