package com.yupaopao.risk.insight.repository.mapper;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.repository.model.TUserGroupQuotaConfVO;
import com.yupaopao.risk.insight.repository.model.TagInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface TUserGroupQuotaConfVOMapper extends Mapper<TUserGroupQuotaConfVO> {


    @Select("<script>"
            + "select code,name,map_url as mapUrl from t_user_group_quota_conf where 1=1 "
            + "<if test='quotaList!=null'>"
            + " and code in "
            + "<foreach item=\"item\" collection=\"quotaList\" separator=\",\" open=\"(\" close=\")\" index=\"\">  #{item, jdbcType=VARCHRT}   \n" +
            "        </foreach>"
            + "</if>"
            + " order by code desc"
            + "</script>")
    List<JSONObject> queryMapUrlByQuota(@Param("quotaList") List quotaList);


    @Select("<script>"
            + "select id,code,name,map_url as mapUrl,create_time as createTime,update_time as updateTime," +
            " update_by as updateBy, sql_text as sqlText, field_map as fieldMap" +
            " from t_user_group_quota_conf where 1=1 "
            + "<if test='code!=null'>"
            + " and code LIKE CONCAT('%',CONCAT(#{code},'%')) "
            + "</if>"
            + "<if test='name!=null'>"
            + " and name LIKE CONCAT('%',CONCAT(#{name},'%')) "
            + "</if>"
            + "order by update_time desc "
            + "</script>")
    List<TUserGroupQuotaConfVO> queryQuotaList(@Param("code") String code, @Param("name") String name);

}