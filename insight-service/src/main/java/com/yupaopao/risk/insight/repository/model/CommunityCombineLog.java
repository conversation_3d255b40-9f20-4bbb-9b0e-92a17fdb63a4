package com.yupaopao.risk.insight.repository.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Table;
import java.util.Date;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-08-20 16:06
 *
 ***/

@Getter
@Setter
@Table(name ="t_community_combine_log")
public class CommunityCombineLog {
    private Long id;
    private String name;
    private String communityLabel;
    private Integer vertexCount;
    private String runDay;
    private String tags;
    private String remark;
    private String originalLabel;

    private Date createTime;
    private Date updateTime;
    private String createBy;
    private String updateBy;
}
