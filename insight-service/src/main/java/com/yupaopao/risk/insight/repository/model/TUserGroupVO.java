package com.yupaopao.risk.insight.repository.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Table(name = "t_user_group_conf")
public class TUserGroupVO implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 用户群名称
     */
    private String name;

    /**
     * 用户群code
     */
    private String code;

    /**
     * 创建者
     */
    @Column(name = "create_by")
    private String createBy;

    /**
     * 更新者
     */
    @Column(name = "update_by")
    private String updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 用户群数量
     */
    @Column(name = "group_num")
    private Integer groupNum;

    /**
     * 状态:0 下线,1 上线
     */
    private Integer status;

    /**
     * sql
     */
    @Column(name = "to_sql")
    private String toSql;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取用户群名称
     *
     * @return name - 用户群名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置用户群名称
     *
     * @param name 用户群名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取用户群code
     *
     * @return code - 用户群code
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置用户群code
     *
     * @param code 用户群code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取创建者
     *
     * @return create_by - 创建者
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     * 设置创建者
     *
     * @param createBy 创建者
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 获取更新者
     *
     * @return update_by - 更新者
     */
    public String getUpdateBy() {
        return updateBy;
    }

    /**
     * 设置更新者
     *
     * @param updateBy 更新者
     */
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    /**
     * 获取备注
     *
     * @return remark - 备注
     */
    public String getRemark() {
        return remark;
    }

    /**
     * 设置备注
     *
     * @param remark 备注
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 获取用户群数量
     *
     * @return group_num - 用户群数量
     */
    public Integer getGroupNum() {
        return groupNum;
    }

    /**
     * 设置用户群数量
     *
     * @param groupNum 用户群数量
     */
    public void setGroupNum(Integer groupNum) {
        this.groupNum = groupNum;
    }

    /**
     * 获取状态:0 下线,1 上线
     *
     * @return status - 状态:0 下线,1 上线
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态:0 下线,1 上线
     *
     * @param status 状态:0 下线,1 上线
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取sql
     *
     * @return to_sql - sql
     */
    public String getToSql() {
        return toSql;
    }

    /**
     * 设置sql
     *
     * @param toSql sql
     */
    public void setToSql(String toSql) {
        this.toSql = toSql;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", name=").append(name);
        sb.append(", code=").append(code);
        sb.append(", createBy=").append(createBy);
        sb.append(", updateBy=").append(updateBy);
        sb.append(", remark=").append(remark);
        sb.append(", groupNum=").append(groupNum);
        sb.append(", status=").append(status);
        sb.append(", toSql=").append(toSql);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}