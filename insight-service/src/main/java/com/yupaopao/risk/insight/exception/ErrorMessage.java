package com.yupaopao.risk.insight.exception;

/**
 * 异常错误信息
 *
 * <AUTHOR>
 * @date 2018-08-20
 */
public enum ErrorMessage {

    SYSTEM_MISSING_PARAM("1100", "缺少入参"),
    SYSTEM_ERROR("9999", "系统查询异常");



    ErrorMessage(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private String code;
    private String msg;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
