package com.yupaopao.risk.insight.service.alert;

import com.yupaopao.risk.insight.repository.model.AlertConditionType;
import com.yupaopao.risk.insight.service.AlertConditionTypeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/****
 * zengxiangcai
 * 2022/11/9 17:44
 ***/

@Service
public class ConditionTypeManager {


    @Resource
    private AlertConditionTypeService alertConditionTypeService;

    public String getPromQL(String typeId) {
        AlertConditionType dbType = alertConditionTypeService.getAlertConditionType(typeId);
        return dbType == null ? null : dbType.getPromQL();
    }

    public String getConditionTypeDesc(String typeId) {
        AlertConditionType dbType = alertConditionTypeService.getAlertConditionType(typeId);
        return dbType == null ? null : dbType.getConditionDesc();
    }


}
