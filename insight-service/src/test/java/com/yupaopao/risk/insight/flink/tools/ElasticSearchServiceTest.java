package com.yupaopao.risk.insight.flink.tools;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.service.beans.AggregationParam;
import com.yupaopao.risk.insight.service.beans.ChartData;
import com.yupaopao.risk.insight.service.beans.Series;
import com.yupaopao.risk.insight.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.HttpHost;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.histogram.DateHistogramAggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.NumericMetricsAggregation;
import org.elasticsearch.search.aggregations.metrics.sum.SumAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.joda.time.DateTimeZone;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.util.*;

/**
 * Created by Avalon on 2020/2/28 17:06
 */
@Slf4j
public class ElasticSearchServiceTest {

    public static final String DATE_FORMAT = "yyyy-MM-dd\'T\'HH:mm:ss+08:00";

    private RestHighLevelClient client;

    @Before
    public void init() {
        String hosts = "test-es-new.yupaopao.com:80";
        List<HttpHost> httpHostList = new ArrayList<>();
        String[] addr = hosts.split(",");
        for (String add : addr) {
            String[] pair = add.split(":");
            httpHostList.add(new HttpHost(pair[0], Integer.valueOf(pair[1]), "http"));
        }
        RestClientBuilder builder = RestClient.builder(httpHostList.toArray(new HttpHost[httpHostList.size()]));

        client = new RestHighLevelClient(builder);
    }

    @Test
    public void termsTest() {
        // Query
        Date startDate = new Date();
        Date endDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(endDate);
        calendar.add(Calendar.HOUR_OF_DAY, -9);
        startDate = calendar.getTime();


        AggregationParam.AggregationParamBuilder builder1 = AggregationParam.builder();
        builder1.startDate(startDate)
            .endDate(endDate)
            .indexName("bi_demo")
            .aggField("levelCount")
            .dateField("createdAt")
            .dimensionField("riskLevel");

        AggregationParam param = builder1.build();

        // DateHistogram
        DateHistogramAggregationBuilder agg = new DateHistogramAggregationBuilder("date");
//        agg.field(param.getDateField()).interval(1000 * 30).timeZone(TimeZone.getDefault().toZoneId());

        // Query
        BoolQueryBuilder query = QueryBuilders.boolQuery();
        if (param.getStartDate() != null || param.getStartDate() != null) {
            RangeQueryBuilder builder = QueryBuilders.rangeQuery(param.getDateField());
            builder.timeZone("Asia/Shanghai");
            long now = System.currentTimeMillis();
            if (param.getStartDate() != null && param.getStartDate().getTime() <= now) {
                builder.gte(DateFormatUtils.format(param.getStartDate(), DATE_FORMAT));
            }
            if (param.getEndDate() != null && param.getEndDate().getTime() <= now) {
                builder.lte(DateFormatUtils.format(param.getEndDate(), DATE_FORMAT));
            }
            query.filter(builder);
        }

        // Group By
        TermsAggregationBuilder aggregation = AggregationBuilders.terms(param.getDimensionField()).field(param.getDimensionField()).minDocCount(1);

        // Sum
        SumAggregationBuilder subSumAgg = AggregationBuilders.sum(param.getAggField()).field(param.getAggField());
        aggregation.subAggregation(subSumAgg);

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        agg.subAggregation(aggregation);
        searchSourceBuilder.aggregation(agg);
        searchSourceBuilder.size(0);
        searchSourceBuilder.query(query);
        SearchRequest searchRequest = new SearchRequest(param.getIndexName());
        searchRequest.source(searchSourceBuilder);
        try {
            SearchResponse response = client.search(searchRequest);
            Map<String, Object> result = parseAggregations(response.getAggregations());
            System.out.println(JSONObject.toJSONString(parseResult(result, param)));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void fieldsTest() throws IOException {
        List<String> fields = new ArrayList<>();
        try {
            Request request = new Request.Builder()
                    .url("http:" + "test-es-new.yupaopao.com" + "/" + "bi_demo1" + "/_mappings")
                    .get().build();
            Response response = new OkHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            fields = CommonUtil.read(responseStr, "$."+"bi_demo"+".mappings.V1.properties.keySet()", List.class);
            System.out.println(fields);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private Map<String, Object> parseAggregations(Aggregations aggregations) {
        Map<String, Object> data = new HashMap<>();
        if (aggregations != null) {
            List<Aggregation> list = aggregations.asList();
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(item -> {
                    if (item instanceof NumericMetricsAggregation.SingleValue) {
                        NumericMetricsAggregation.SingleValue value = (NumericMetricsAggregation.SingleValue) item;
                        data.put(item.getName(), value.getValueAsString());
                    } else {
                        data.put(item.getName(), parseAggregations(item));
                    }
                });
            }
        }
        return data;
    }

    // 聚合结果解析
    public Map<String, Object> parseAggregations(Aggregation aggregation) {
        Map<String, Object> data = new HashMap<>();
        List<? extends MultiBucketsAggregation.Bucket> buckets = new ArrayList<>();
        if (aggregation instanceof Terms) {
            Terms terms = (Terms) aggregation;
            buckets = terms.getBuckets();
        } else if (aggregation instanceof Histogram) {
            Histogram histogram = (Histogram) aggregation;
            buckets = histogram.getBuckets();
        }
        buckets.forEach(bucket -> {
            if (bucket.getAggregations() != null) {
                Aggregations aggregations = bucket.getAggregations();
                data.put(bucket.getKeyAsString(), parseAggregations(aggregations));
            } else {
                data.put(bucket.getKeyAsString(), bucket.getDocCount());
            }
        });
        return data;
    }

    private ChartData parseResult(Map<String, Object> result, AggregationParam param) {
        ChartData chartData = new ChartData();
        if (MapUtils.isEmpty(result)) {
            return chartData;
        }

        final Map<String, List<String>> tmpMap = new HashMap<>();

        final List<Series> series = new ArrayList<>();
        final Set<String> legend = new HashSet<>();
        final List<String> axis = new ArrayList<>();
        result = (Map<String, Object>)result.get("date");
        result.forEach((k, v) -> {
            axis.add(k);
            legend.addAll(CommonUtil.read(v, "$."+ param.getDimensionField() + ".keySet()", Set.class));
        });

        result.forEach((k, v) -> {
            legend.forEach(l -> {
                List<String> keyList = tmpMap.get(l);
                if (CollectionUtils.isEmpty(keyList)) {
                    keyList = new ArrayList<>();
                    tmpMap.put(l, keyList);
                }
                Map<String, Object> tmp = CommonUtil.read(v, "$."+ param.getDimensionField(), Map.class);
                Map<String, Object> tmp2 = CommonUtil.read(tmp, "$." + l, Map.class);
                if (MapUtils.isEmpty(tmp2)) {
                    keyList.add("0.0");
                } else {
                    keyList.add(CommonUtil.read(tmp2, "$." + param.getAggField()));
                }
            });
        });
        tmpMap.forEach((k, v) -> {
            series.add(new Series(k, v));
        });
        chartData.setAxis(axis);
        chartData.setLegend(legend);
        chartData.setSeries(series);
        return chartData;
    }

//
//
//    private List<Object> parseAggregations(Aggregations aggregations) {
//        List<Object> result = new ArrayList<>();
//        if (aggregations != null) {
//            List<Aggregation> list = aggregations.asList();
//            if (CollectionUtils.isNotEmpty(list)) {
//                for (Aggregation item : list) {
//                    if (item instanceof NumericMetricsAggregation.SingleValue) {
//                        NumericMetricsAggregation.SingleValue value = (NumericMetricsAggregation.SingleValue) item;
//                        result.add(value.getValueAsString());
//                    } else if (item instanceof ParsedDateHistogram) {
//                        result.add(parseAggregations(item));
//                    } else if (item instanceof ParsedStringTerms) {
//                        result.add(parseAggregations(item));
//                    }
//                }
////                list.forEach(item -> {
////
////                });
//            }
//        }
//        return result;
//    }
//
//    // 聚合结果解析
//    public Map<String, Object> parseAggregations(Aggregation aggregation) {
//        Map<String, Object> data = new HashMap<>();
//        List<? extends MultiBucketsAggregation.Bucket> buckets = new ArrayList<>();
//        if (aggregation instanceof Terms) {
//            Terms terms = (Terms) aggregation;
//            buckets = terms.getBuckets();
//        } else if (aggregation instanceof Histogram) {
//            Histogram histogram = (Histogram) aggregation;
//            buckets = histogram.getBuckets();
//        }
//        buckets.forEach(bucket -> {
//            if (bucket.getAggregations() != null) {
//                Aggregations aggregations = bucket.getAggregations();
//                data.put(bucket.getKeyAsString(), parseAggregations(aggregations));
//            }
//        });
//        return data;
//    }
}
