package com.yupaopao.risk.insight.flink.tools;


//import com.alicloud.openservices.tablestore.SyncClient;
//import com.alicloud.openservices.tablestore.model.*;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsClientFactory;


public class DebugTools {
    public static void main(String[] args) {
        try {
//            String s = "117.136.62.71,138.0,2019-11-09T06:43:41+08:00,false,yuer,117.136.62.71," +
//                    "2019092001092089e600842568c39a72dbcdddaed7c2e801e2c2f82ebca93d,chat-room-enter,2950,000023f5d9a44cbbb4cbbd3b3c4cd213,dcf3ec4f0969d4a8b03777fdb9ae007a,218766946542309376,成都市,2470.0,中国,移动,四川省,西南,PASS,true,1.523506677E12,0.0,true,6.0,10.0,1.833902922800017E15,dcf3ec4f0969d4a8b03777fdb9ae007a,2.0,2.0,0.0,0.0,0.0,2019092001092089e600842568c39a72dbcdddaed7c2e801e2c2f82ebca93d,chat-room-enter,,PASS,PASS,yuer,chat-room-enter,PASS,PASS,true,000023f5d9a44cbbb4cbbd3b3c4cd213,000023f5d9a44cbbb4cbbd3b3c4cd213,dcf3ec4f0969d4a8b03777fdb9ae007a\n";
//            System.err.println(s.length());
//           // LocalEnvDemo.runDebug();

//            updatePrimaryKey();

            // test();

            //createTable();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    ////
////
////
////
////
////
////
//    public static void test() {
//        String endPoint = "http://vpcbixin-global-test-ots.cn-hangzhou.vpc.ots.aliyuncs.com";
//        String accessKeyId = "LTAIMe0Hnfw0sr3l";
//        String accessKeySecret = "******************************";
//        String instanceName = "global-test-ots";
//        String tsTableName = "risk_hit_log";
//        SyncClient client = new SyncClient(endPoint,
//                accessKeyId,
//                accessKeySecret,
//                instanceName);
//
//        ComputeSplitsBySizeRequest csbsr = new ComputeSplitsBySizeRequest();
//        csbsr.setTableName(tsTableName);
//        csbsr.setSplitSizeInByte(2, 1024 * 1024);
//        ComputeSplitsBySizeResponse resp = client.computeSplitsBySize(csbsr);
//        System.err.println(resp);
//        System.err.println(resp.jsonize());
//        List<Split> splits = resp.getSplits();
//        System.err.println(splits);
//    }


//    public static void updatePrimaryKey() {
//        String endPoint = "http://vpcbixin-global-test-ots.cn-hangzhou.vpc.ots.aliyuncs.com";
//        String accessKeyId = "LTAIMe0Hnfw0sr3l";
//        String accessKeySecret = "******************************";
//        String instanceName = "global-test-ots";
//        String tsTableName = TsConstants.TABLE_RISK_HIT_LOG;
//        TsProperties config = new TsProperties()
//                .withEndPoint(endPoint)
//                .withAccessKeyId(accessKeyId)
//                .withAccessKeySecret(accessKeySecret)
//                .withInstanceName(instanceName);
//
//        TsClientFactory provider = new TsClientFactory(config);
//
//
//
//        PrimaryKeyBuilder builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_DATE_PARTITION,
//                PrimaryKeyValue.fromString("20191223_0875"));
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_TRACE_ID,
//                PrimaryKeyValue.fromString("b04eee9c7f7344b6808b1b81dab77551"));
//        PrimaryKey pk = builder.build();
//
//        //read write delete
//        SyncClient client = provider.getClient();
//        SingleRowQueryCriteria criteria = new SingleRowQueryCriteria(tsTableName, pk);
//        // 设置读取最新版本
//        criteria.setMaxVersions(3);
//        GetRowResponse getRowResponse = client.getRow(new GetRowRequest(criteria));
//
//        Row row = getRowResponse.getRow();
//
//        PrimaryKeyColumn partitionId = row.getPrimaryKey().getPrimaryKeyColumn(0);
//        PrimaryKeyColumn trace = row.getPrimaryKey().getPrimaryKeyColumn(1);
//
//        builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        String partitionValue = partitionId.getValue().asString();
//
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_DATE_PARTITION,
//                PrimaryKeyValue.fromString(partitionValue.split("_")[1] + "_"+partitionValue.split("_")[0]));
//        builder.addPrimaryKeyColumn(trace);
//
//        //write data
//        RowPutChange rowPutChange = new RowPutChange(tsTableName, builder.build());
//        for (Column oldCol : row.getColumns()) {
//            rowPutChange.addColumn(new Column(oldCol.getName(),oldCol.getValue()));
//        }
//
//
//        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
//        batchWriteRowRequest.addRowChange(rowPutChange);
//        batchWriteRowRequest.addRowChange(rowPutChange);
//        BatchWriteRowResponse repsone = client.batchWriteRow(batchWriteRowRequest);
//        System.err.println(repsone);
//        // RowDeleteChange rowDeleteChange = new RowDeleteChange(tsTableName, pk);
//
//    }


    ////
////
//    public static void readAll() {
//                String endPoint = "http://vpcbixin-ts-risk.cn-hangzhou.vpc.ots.aliyuncs.com";
//        String accessKeyId = "LTAI4FinBzEvCAMKTELRygd4";
//        String accessKeySecret = "******************************";
//        String instanceName = "ts-risk";
//        String tsTableName = "risk_hit_log";
//
////        String endPoint = "http://vpcbixin-global-test-ots.cn-hangzhou.vpc.ots.aliyuncs.com";
////        String accessKeyId = "LTAIMe0Hnfw0sr3l";
////        String accessKeySecret = "******************************";
////        String instanceName = "global-test-ots";
////        String tsTableName = TsConstants.TABLE_RISK_USER_DEFINE_DATA;
//        TsProperties config = new TsProperties()
//                .withEndPoint(endPoint)
//                .withAccessKeyId(accessKeyId)
//                .withAccessKeySecret(accessKeySecret)
//                .withInstanceName(instanceName);
//
//        TsClientFactory provider = new TsClientFactory(config);
//
//
//        PrimaryKeyBuilder builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_DATE_PARTITION,
//                PrimaryKeyValue.fromString("20191225_0542"));
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_TRACE_ID,
//                PrimaryKeyValue.INF_MIN);
//        PrimaryKey start = builder.build();
//
//        builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_DATE_PARTITION,
//                PrimaryKeyValue.fromString("20191225_0542"));
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_TRACE_ID,
//                PrimaryKeyValue.INF_MAX);
//
//        PrimaryKey end = builder.build();
//
////        PrimaryKey start = buildRiskUserDefinedDataStartPk("49");
////        PrimaryKey end = buildRiskUserDefinedDataEndPk("49");
//
//        while (!Thread.interrupted()) {
//            //范围读tablestore
//
//            long beginTime = System.currentTimeMillis();
//            GetRangeResponse res = getRange(provider.getClient(), start, end);
//            List<Row> temp = res.getRows();
//            System.err.println("read count: {}" + (temp != null ? temp.size() : 0) + ", cost: " +
//                    (System.currentTimeMillis() - beginTime));
//
//            //读取结果放入queue中
//            try {
//                if (CollectionUtils.isEmpty(temp)) {
//                    System.err.println(("empty row, nextKey: {}" + res.getNextStartPrimaryKey()));
//                    return;
//                } else {
//                    // System.err.println(temp);
//                   // return;
//                }
//
//            } catch (Exception e) {
//                e.printStackTrace();
//                System.err.println("put range response batchFetchedList to queue error");
//            }
//            //任然有数据继续读， getRows可能只返回部分
//            if (res.getNextStartPrimaryKey() != null) {
//                start = res.getNextStartPrimaryKey();
//            }
//        }
//
//
//    }
//
//
//    private static GetRangeResponse getRange(SyncClient client, PrimaryKey lowerBound, PrimaryKey upperBound) {
//        GetRangeRequest grr = new GetRangeRequest();
//        RangeRowQueryCriteria rec = new RangeRowQueryCriteria(TsConstants.TABLE_RISK_HIT_LOG);
//        rec.setDirection(Direction.FORWARD);
//        rec.setInclusiveStartPrimaryKey(lowerBound);
//        rec.setExclusiveEndPrimaryKey(upperBound);
//        rec.setMaxVersions(1);
//
//        grr.setRangeRowQueryCriteria(rec);
//        return client.getRange(grr);
//    }

//    public static void createTable() {
//        String endPoint = "http://vpcbixin-ts-risk.cn-hangzhou.vpc.ots.aliyuncs.com";
//        String accessKeyId = "LTAI4FinBzEvCAMKTELRygd4";
//        String accessKeySecret = "******************************";
//        String instanceName = "ts-risk";
//        String tsTableName = "risk_hit_log";
//        TableMeta tableMeta = new TableMeta(tsTableName);
//        tableMeta.addPrimaryKeyColumn(new PrimaryKeySchema("traceId", PrimaryKeyType.STRING)); // 为主表添加主键列。
//        int timeToLive = -1; // 数据的过期时间，单位秒, -1代表永不过期，例如设置过期时间为一年, 即为 365 * 24 * 3600。
//        int maxVersions = 1; // 保存的最大版本数，设置为3即代表每列上最多保存3个最新的版本。
//        TableOptions tableOptions = new TableOptions(timeToLive, maxVersions);
//        CreateTableRequest request = new CreateTableRequest(tableMeta, tableOptions);
//        //request.setReservedThroughput(new ReservedThroughput(new CapacityUnit(0, 0))); // 设置读写预留值，容量型实例只能设置为0，高性能实例可以设置为非零值。
//
//        SyncClient client = new SyncClient(endPoint,
//                accessKeyId,
//                accessKeySecret,
//                instanceName);
//
//        CreateTableResponse resp = client.createTable(request);
//        System.err.println(resp);

//    }
}
