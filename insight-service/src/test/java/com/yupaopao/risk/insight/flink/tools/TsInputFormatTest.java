package com.yupaopao.risk.insight.flink.tools;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/***
 * inputFormat 必须能够序列化
 */
@Slf4j
public class TsInputFormatTest  {

    public static void main(String[] args) throws Exception {
        TsInputFormatTest testFormat = new TsInputFormatTest();
        TestSplit[] splits = TestSplit.generate();
        for(TestSplit split: splits){
            testFormat.open(split);
            try {
                while (!testFormat.reachedEnd()) {
                    System.err.println(testFormat.nextRecord(null));
                }
            }finally {
                testFormat.close();
            }
        }
        System.err.println("finished.....");
    }

    //每次getRange的结果放到queue中
    BlockingQueue<LinkedList<String>> batchFetchedList = new LinkedBlockingQueue<>();
    //从queue中提取出来的当期rowList
    LinkedList<String> currentProcessList = new LinkedList<>();
    //读取该InputFormat负责的splits的线程列表
    private List<TsInputSplitOpenThread> threadList = new ArrayList<>();

    private final static Long splitSizeUnitInBytes = 1024L * 1024L;

    private final static Long unitsPerSplit = 400L;

    transient Map<String,TsInputSplitOpenThread> splitThreadName = new ConcurrentHashMap<>();


    public TsInputFormatTest() {

    }


    @Getter
    @Setter
    public static class TestSplit {
        private Integer lowerBound;
        private Integer upperBound;

        private int splitNumber;

        public static TestSplit[] generate() {
            TestSplit[] splits = new TestSplit[10];
            for (int i = 0; i < 10; i++) {
                TestSplit split = new TestSplit();
                split.setLowerBound(i * 30000);
                Random random = new Random();
                int randomInt = random.nextInt(3787);
                split.setUpperBound(i * 30000 + 20000 + randomInt);
                split.setSplitNumber(i);
                splits[i] = split;
            }
            return splits;
        }


        public int getSplitNumber() {
            return splitNumber;
        }

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this, ToStringStyle.JSON_STYLE);
        }
    }





    /**
     * 开始读取某一个分片，分配资源
     *
     * @param tableStoreInputSplit
     * @throws IOException
     */

    public void open(TestSplit tableStoreInputSplit)  {
        try {
            System.err.println("start split: {}"+tableStoreInputSplit.getSplitNumber());
            TsInputSplitOpenThread isot = new TsInputSplitOpenThread(tableStoreInputSplit,
                    batchFetchedList);
            threadList.add(isot);
            splitThreadName.put(isot.getName(),isot);
            System.err.println("create thread: {}, task: {}"+isot.getName()+tableStoreInputSplit.getSplitNumber());
            isot.start();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /***
     * 判断某一个split中是否还有数据需要读取,
     * 是否还有线程未消耗完,以及是否还有数据未读取完
     * @return
     * @throws IOException
     */

    public boolean reachedEnd() throws IOException {
        List<String> needRemoveList= new ArrayList<>();
        for(Map.Entry<String,TsInputSplitOpenThread> entry: splitThreadName.entrySet()){
            if(entry.getValue()==null){
                log.info("thread is null,{}",entry.getKey());
            }
            if(entry.getValue().exit){
                log.info(entry.getValue().getName()+" thread exit");
                needRemoveList.add(entry.getValue().getName());
            }
        }
        TsInputSplitOpenThread firstThread = threadList.size()>0?threadList.get(0): null;
        boolean hasRemoved = threadList.stream().filter(elem->elem.exit).findFirst().isPresent();
        if(hasRemoved){
            System.err.println("hasRemoved threadList:  {},{}"+ threadList.size());
            threadList.stream().forEach(elem->log.info("has removed, thread:{},exit: {} ",elem.getName(),elem.exit));
        }
        threadList.removeIf(t -> t.exit);

        //remove
        needRemoveList.stream().forEach(elem->splitThreadName.remove(elem));

        boolean reachEnd =
                CollectionUtils.isEmpty(threadList) && CollectionUtils.isEmpty(batchFetchedList)
                && CollectionUtils.isEmpty(currentProcessList);
       // threadList.size() == 0 && batchFetchedList.size() == 0 && currentProcessList.size() == 0;

        return reachEnd;
    }

    /**
     * 读取queue中的下一行数据
     *
     * @param reuse
     * @return
     * @throws IOException
     */

    public String nextRecord(String reuse) throws IOException {
        try {
            while (CollectionUtils.isEmpty(currentProcessList)) {
                if(batchFetchedList.size()==0){
                    System.err.println("start fetch: "+ batchFetchedList.size());
                }

                try{
                    currentProcessList = batchFetchedList.poll(10, TimeUnit.MILLISECONDS);
                }catch (Exception e){
                    e.printStackTrace(); //可能线程已经读取完
                    return null;
                }
                if (CollectionUtils.isNotEmpty(currentProcessList)) {
                    break;
                }else if(hasExitThread()){
                    //read thead finished, exit to check the split task
                    return null;
                }
            }
           return currentProcessList.poll();
        } catch (Exception e) {
            log.info("read next row error", e);
            return null;
        }

    }

   private boolean hasExitThread(){
        return threadList.stream().filter(elem->elem.exit).findFirst().isPresent();
   }


    public void close() throws IOException {
        log.info("start close...");
        for (TsInputSplitOpenThread t : threadList) {
            if(t.exit){
                splitThreadName.remove(t.getName());
            }
            t.interrupt();
        }
        for (TsInputSplitOpenThread t : threadList) {
            try {
                t.join();
            } catch (InterruptedException e) {
                log.info("close error", e);
            }
        }
    }


    class TsInputSplitOpenThread extends Thread {
        private final TestSplit split;
        private BlockingQueue<LinkedList<String>> rows;
        //private String[] columns;
        private volatile boolean exit = false;

        public TsInputSplitOpenThread(TestSplit split,
                                      BlockingQueue<LinkedList<String>> rows) {
            this.split = split;
            this.rows = rows;
        }

        @Override
        public void run() {
            Integer start = split.getLowerBound();
            while (!Thread.interrupted()) {
                //范围读tablestore
                ResponseRange res = ResponseRange.generate(start,split.getUpperBound());
                List<String> temp = res.getRows();
                log.info("read count: {}", temp != null ? temp.size() : 0);

                //读取结果放入queue中
                try {
                    if(CollectionUtils.isEmpty(temp)){
                        log.info("empty row, nextKey: {}",res.nextKey);
                    }else{
                        rows.put(new LinkedList<>(temp));
                    }

                } catch (InterruptedException e) {
                    log.error("put range response batchFetchedList to queue error", e);
                }
                //任然有数据继续读， getRows可能只返回部分
                if (res.getNextKey() != null) {
                    start = res.getNextKey();
                } else {
                    log.info("thread: {} exit flag set",this.getName());
                    exit = true;
                    break;
                }
            }
            System.err.println("read finished");
        }
    }

    @Getter
    @Setter
    public static class ResponseRange{
        private List<String> rows;
        private Integer nextKey;

        public static ResponseRange generate(Integer startKey,Integer endKey){
            try {
                Thread.sleep(10+new Random().nextInt(10));
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            ResponseRange resp = new ResponseRange();
            int random = 500+new Random().nextInt(600);
            List<String> data = new ArrayList<>();
            int currentValue = startKey;
            for(int i=0;i<random;i++){
                currentValue = startKey+i;
                if(currentValue>=endKey){
                    break;
                }
                data.add(String.valueOf(currentValue));
            }

            if(CollectionUtils.isEmpty(data)){
                resp.setNextKey(null);
            }
            if(currentValue>=endKey){
                resp.setNextKey(null);
            }else{
                resp.setNextKey(currentValue+1);
            }
            resp.setRows(data);
            return resp;

        }
    }


}
