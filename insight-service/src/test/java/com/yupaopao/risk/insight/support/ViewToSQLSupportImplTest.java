package com.yupaopao.risk.insight.support;

import com.yupaopao.risk.insight.support.bean.ConditionItem;
import junit.framework.TestCase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
public class ViewToSQLSupportImplTest extends TestCase {

/*    @Autowired
    private ViewToSQLSupport viewToSQLSupport;*/

    @Test
    public void testParseViewToSql() {
/*        SQLView sqlView = new SQLView();
        sqlView.setDistinct("distinct");
        List<QueryField> queryFieldList = new ArrayList<>();
        QueryField queryField = new QueryField();
        queryField.setFieldCode("eventCode");
        queryField.setBelongTable("risk_hit_log");
        queryFieldList.add(queryField);
        sqlView.setQueryFields(queryFieldList);
        sqlView.setJoinTable("risk_hit_log");
        sqlView.setBeJoinTable("risk_hit_content");
        sqlView.setOnField("risk_hit_log.data_TraceId");
        sqlView.setBeOnField("risk_hit_content.data_TraceId");
        sqlView.setConditionList(getItemList());
        Map<String, String> tabMap = Maps.newHashMap();
        tabMap.put("risk_hit_log", "a");
        sqlView.setTableAlisList(tabMap);
        ViewToSQLSupport viewToSQLSupport = new ViewToSQLSupportImpl();
        String sql = viewToSQLSupport.parseViewToSql(sqlView);
        System.out.println(sql);*/
    }

    private List<ConditionItem> getItemList(){
        List<ConditionItem> itemList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            ConditionItem conditionItem = new ConditionItem();

            conditionItem.setCode("risk_hit_log.create"+i);
            conditionItem.setBelongTable("risk_hit_log");
            conditionItem.setOperator("=");
            conditionItem.setConcatenation("and");
            conditionItem.setFieldType("String");
            List list=new ArrayList();
            list.add(i);
            conditionItem.setCompareValue(list);
            itemList.add(conditionItem);
        }
        ConditionItem conditionItem = new ConditionItem();
        conditionItem.setConcatenation("and");
        conditionItem.setCode("risk_hit_log.eventCode");
        conditionItem.setBelongTable("risk_hit_log");
        conditionItem.setOperator("in");
        conditionItem.setFieldType("String");
        List list=new ArrayList();
        list.add("im-message");
        list.add("im-message2");
        conditionItem.setCompareValue(list);
        itemList.add(conditionItem);
        return itemList;
    }
}