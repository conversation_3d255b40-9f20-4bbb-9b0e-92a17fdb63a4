package com.yupaopao.risk.insight.flink.tools;

import com.yupaopao.risk.insight.beans.DBTableInfo;
import com.yupaopao.risk.insight.flink.beans.LocalSQLResponse;
import com.yupaopao.risk.insight.flink.beans.LocalSqlParams;
import com.yupaopao.risk.insight.common.meta.JobParams;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-03 13:00
 *
 ***/
public class LocalEnvSqlStarterTest {

    @Test
    public void localTest(){
        //given sql
        String sql ="select a.*,d.auditStatus from risk_hit_log a,risk_diff d where a.traceid=d.traceid and a" +
                ".createAt between '2019-01-01' and  '2020-01-01'";

        //given related table info
        List<DBTableInfo> tableInfos = new ArrayList<>();
        DBTableInfo hitLog = new DBTableInfo();
        hitLog.setTableName("risk_hit_log");
        hitLog.setTableType("custom");
        hitLog.setTableColumns(Arrays.asList(
                new DBTableInfo.TableColumn("traceid","STRING"),
                new DBTableInfo.TableColumn("eventcode","STRING"),
                new DBTableInfo.TableColumn("createAt","TIMESTAMP")
                )
        );

        tableInfos.add(hitLog);
        DBTableInfo hitDiff = new DBTableInfo();
        hitDiff.setTableName("risk_diff");
        hitDiff.setTableType("custom");
        hitDiff.setTableColumns(Arrays.asList(
                new DBTableInfo.TableColumn("traceid","STRING"),
                new DBTableInfo.TableColumn("auditStatus","STRING")
                )
        );

        tableInfos.add(hitDiff);
        LocalSqlParams localSqlParams = new LocalSqlParams();
        localSqlParams.setSql(sql);
        LocalSqlParams.LocalTableInfo t1 = new LocalSqlParams.LocalTableInfo(hitLog,
                Arrays.asList(Arrays.asList("trace1","im",
                        "2019-12-11 12:01:01")),Arrays.asList("traceid","eventcode","createAt"));
        LocalSqlParams.LocalTableInfo t2 = new LocalSqlParams.LocalTableInfo(hitDiff,
                Arrays.asList(Arrays.asList("trace1","reject")),Arrays.asList("traceid","auditStatus"));
        localSqlParams.setTables(Arrays.asList(t1,t2));
//        LocalSQLResponse response = new LocalEnvSqlStarter().run(localSqlParams);
//        Assert.assertTrue(!response.isFailed());
    }
    @Test
    public void validateValidSQL(){
        //given sql
       String sql = "-- SQL\n" +
               "-- ********************************************************************--\n" +
               "-- Author: xiaming\n" +
               "-- CreateTime: 2019-12-20 10:30:11\n" +
               "-- Comment: 请输入业务注释信息\n" +
               "-- ********************************************************************--\n" +
               "select\n" +
               "  userId\n" +
               "from\n" +
               "  risk_hit_log\n" +
               "where\n" +
               "  createdAt BETWEEN '2019-12-16 00:00:00'\n" +
               "  AND '2019-12-17 00:00:00'\n" +
               "  AND eventCode = 'im-message'\n" +
               "order by\n" +
               "  createdAt\n" +
               "limit\n" +
               "  10";

        //given related table info
        JobParams jobParams = new JobParams();
        List<JobParams.FlinkTableInfo> tableInfos = new ArrayList<>();
        JobParams.FlinkTableInfo t1 = new JobParams.FlinkTableInfo();
        t1.setStrColumns("userId#STRING,createdAt#TIMESTAMP(3),eventCode#STRING");
        t1.setTableId("1");
        t1.setTableName("risk_hit_log");
        t1.setTableType("SYSTEM");
        tableInfos.add(t1);
        jobParams.setTableList(tableInfos);
        jobParams.setSql(sql);
//
//        boolean validateResult = LocalEnvSqlStarter.validateSQL(jobParams);
//        Assert.assertTrue(validateResult);
    }

//    @Test(expected = Exception.class)
//    public void validateInvalidSQL(){
//        //given sql
//        String sql ="select a.*,d.auditStatus from risk_hit_log a,risk_diff_log d where a.traceid=d.traceid";
//
//        //given related table info
//        List<DBTableInfo> tableInfos = new ArrayList<>();
//        DBTableInfo hitLog = new DBTableInfo();
//        hitLog.setTableName("risk_hit_log");
//        hitLog.setTableType("custom");
//        hitLog.setTableColumns(Arrays.asList(
//                new DBTableInfo.TableColumn("traceid","STRING"),
//                new DBTableInfo.TableColumn("eventcode","STRING")
//                )
//        );
//
//        tableInfos.add(hitLog);
//        DBTableInfo hitDiff = new DBTableInfo();
//        hitDiff.setTableName("risk_diff");
//        hitDiff.setTableType("custom");
//        hitDiff.setTableColumns(Arrays.asList(
//                new DBTableInfo.TableColumn("traceid","STRING"),
//                new DBTableInfo.TableColumn("auditstatus","STRING")
//                )
//        );
//
//        tableInfos.add(hitDiff);
//        boolean validateResult = LocalEnvSqlStarter.validateSQL(sql,tableInfos);
//    }
}
