package com.yupaopao.risk.insight.flink.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.ReadContext;
import com.yupaopao.risk.insight.api.RiskPortraitService;
import com.yupaopao.risk.insight.bean.PortraitResult;
import com.yupaopao.risk.insight.bean.PortraitTag;
import com.yupaopao.risk.insight.common.meta.JobParams;
import com.yupaopao.risk.insight.config.FlinkConfig;
import com.yupaopao.risk.insight.config.JarConfiguration;
import com.yupaopao.risk.insight.flink.beans.RemoteSQLResponse;
import com.yupaopao.risk.insight.util.CommonUtil;
import jdk.nashorn.internal.ir.annotations.Reference;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class FlinkRestClientTest {

    @Autowired
    private OkHttpClient okHttpClient;
    @Autowired
    private FlinkConfig flinkConfig;
    @Autowired
    private JarConfiguration jarConfiguration;

    public static final String DEFAULT_RETURN_JOB_STATUS = "FAILED";

//    private String templateSqlJarId;
//
//    @PostConstruct
//    public void init() {
//        templateSqlJarId = getJarId(flinkConfig.getTemplateSqlFile());
//        ConfigService.getAppConfig().addChangeListener(event -> {
//            if (event.isChanged("flink.cluster.templateSqlFile")) {
//                ConfigChange change = event.getChange("flink.cluster.templateSqlFile");
//                templateSqlJarId = getJarId(change.getNewValue());
//            }
//        });
//    }

    @Reference
    private RiskPortraitService riskPortraitService;

    public void testMark() {
        PortraitTag portraitTag = new PortraitTag();
        portraitTag.setKey("193220948115900145");
        portraitTag.setCode("orderGray");
        PortraitResult result = riskPortraitService.mark(portraitTag);
        if (result.getSuccess()) {
            System.out.println("打标成功");
        }
    }

    public void testUnMark() {
        PortraitTag portraitTag = new PortraitTag();
        portraitTag.setKey("193220948115900145");
        portraitTag.setCode("orderGray");
        PortraitResult result = riskPortraitService.unMark(portraitTag);
        if (result.getSuccess()) {
            System.out.println("取消打标");
        }
    }

    public String upload(String filePath) {


        File jarFile = new File(filePath);

        String fileName = jarFile.getName();
        log.info("the upload jar file is: {}", fileName);
        //create("application/x-java-archive", "");
        RequestBody body = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("jarfile", fileName, RequestBody.create(MediaType.parse("application/x-java-archive"), jarFile)
                )
                .build();
        Request request = new Request.Builder()
                .url(flinkConfig.getWebUrl() + "/jars/upload")
                .post(body)
                .build();
        try {
            Response response = getLongCallHttpClient().newCall(request).execute();
            String jsonResp = response.body().string();
            log.info("jar {} upload response: {}", fileName, jsonResp);

            JSONObject json = JSONObject.parseObject(jsonResp);
            String jobName = json.getString("filename");
            File uploadedJarFile = new File(jobName);
            jobName = uploadedJarFile.getName();

            log.info("uploaded file: {}", jobName);
            return jobName;
        } catch (Exception e) {
            log.info("uploaded failed, file: " + fileName, e);
        }
        return null;
    }


    public String run(String file) {
        Map<String, Object> jsonParams = new HashMap<>();
        jsonParams.put("entryClass", jarConfiguration.getMainclass());
        jsonParams.put("parallelism", null);
        jsonParams.put("programArgs", null);
        jsonParams.put("savepointPath", null);
        jsonParams.put("allowNonRestoredState", null);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(jsonParams));

        Request request = new Request.Builder()
                .url(flinkConfig.getWebUrl() + "/jars/" + file + "/run?entry-class=" + jarConfiguration.getMainclass())
                .post(body).build();
        try {
            Response response = getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("the run result is: {}", responseStr);
            JSONObject json = JSONObject.parseObject(responseStr);
            String jobId = json.getString("jobid");
            return jobId;
        } catch (Exception e) {
            log.error("run job error, jobName: " + file, e);
        }
        return null;
    }


    public RemoteSQLResponse runSql(JobParams params) {
        try {
            params.setBucketPerSplit(flinkConfig.getBucketSplitThread());
            Map<String, Object> jsonParams = new HashMap<>();
            jsonParams.put("entryClass", "com.yupaopao.risk.insight.flink.job.sql.SqlMainJob");
            jsonParams.put("parallelism", flinkConfig.getSqlParallelism());
            jsonParams.put("programArgs", URLEncoder.encode(JSON.toJSONString(params), "UTF-8"));
            jsonParams.put("savepointPath", null);
            jsonParams.put("allowNonRestoredState", null);
            String sendParam = JSON.toJSONString(jsonParams);
            log.info("sql run param: {}", sendParam);
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), sendParam);
            String templateJarId = getJarId(flinkConfig.getTemplateSqlFile());
            Request request = new Request.Builder()
                    .url(flinkConfig.getWebUrl() + "/jars/" + templateJarId + "/run")
                    .post(body).build();

            Response response = this.getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("the run sql response is: {}, randomJobId: {}", responseStr, params.getRandomJobId());
            JSONObject json = JSONObject.parseObject(responseStr);
            String jobId = json.getString("jobid");
            if (StringUtils.isEmpty(jobId)) {
                return RemoteSQLResponse.buildFailResponse(jobId, params.getRandomJobId(), responseStr);
            } else {
                return RemoteSQLResponse.buildSuccessResponse(jobId, params.getRandomJobId(), "job executing");
            }

        } catch (Exception e) {
            log.error("run sql error, params:  " + params.toMap(), e);
            return RemoteSQLResponse.buildFailResponse(null, params.getRandomJobId(), e.getMessage());
        }
    }


    /***
     * only
     * @param jobId
     * @return
     */
    public String getJobStatus(String jobId) {
        Request request = new Request.Builder()
                .url(flinkConfig.getWebUrl() + "/jobs/" + jobId + "/execution-result")
                .get().build();
        Response response = null;
        try {
            response = okHttpClient.newCall(request).execute();
            if (response == null) {
                return DEFAULT_RETURN_JOB_STATUS;
            }
            String jsonResponse = response.body().string();
            log.info("the job {} execution response is {}", jobId, jsonResponse);

            JSONObject json = JSONObject.parseObject(jsonResponse);
            ReadContext ctx = JsonPath.parse(json);
            String jobStatus = ctx.read("$.status.id");
            return jobStatus;
        } catch (Exception e) {
            log.error("get job execution result error, jobId= " + jobId, e);
            return DEFAULT_RETURN_JOB_STATUS;
        }

    }


    public boolean abortJob(String jobId) {
        Request request = new Request.Builder()
                .url("https://flink.yupaopao.com" + "/jobs/" + jobId + "?mode=cancel")
                .patch(RequestBody.create(MediaType.parse("application/json"), "{}")).build();
        Response response = null;
        try {
            response = getLongCallHttpClient().newCall(request).execute();
            log.info("cancel response: {}", response);
            return response != null;
        } catch (Exception e) {
            log.error("abort job occurs error, jobId= " + jobId, e);
            return false;
        }
    }

    public String getJarId(String jarName) {
        String webUrl = "https://flink.yupaopao.com";
        Request request = new Request.Builder()
                .url(webUrl + "/jars")
                .get().build();
        Response response = null;
        try {
            response = getLongCallHttpClient().newCall(request).execute();
            JSONObject json = JSONObject.parseObject(response.body().string());
            return CommonUtil.read(json, "$.files[name = '" + jarName + "'][0].id");
        } catch (Exception e) {
            log.error("get jar id occurs fail: jarName:{}, ", jarName, e);
            throw new IllegalArgumentException("cannot find the template sql jar");
        }
    }


    /***
     * noexception {"root-exception":null,"timestamp":null,"all-exceptions":[],"truncated":false}
     * @param jobId
     * @return
     */
    public String getExceptionInfo(String jobId) {
        Request request = new Request.Builder()
                .url(flinkConfig.getWebUrl() + "/jobs/" + jobId + "/exceptions")
                .get().build();
        try {
            Response response = okHttpClient.newCall(request).execute();
            String responseStr = response.body().string();
            JSONObject jsonObj = JSONObject.parseObject(responseStr);
            boolean noRootException = jsonObj.get("root-exception") == null;
            boolean noAllException = jsonObj.get("all-exceptions") == null;
            if (!noAllException && jsonObj.get("all-exceptions") instanceof List) {
                JSONArray errorArr = jsonObj.getJSONArray("all-exceptions");
                if (errorArr == null || errorArr.isEmpty()) {
                    noAllException = true;
                }
            }
            if (noRootException && noAllException) {
                //无异常信息
                return "no exception";
            }
            return responseStr;
        } catch (IOException e) {
            log.error("get job exception error: ", e);
            return e.getMessage();
        }
    }


    /***
     * fllink 任务提交耗时较长，才用长超时的链接
     * @return
     */
    private OkHttpClient getLongCallHttpClient() {

//        return okHttpClient.newBuilder()
//                .connectTimeout(10, TimeUnit.SECONDS) //连接超时
//                .readTimeout(120, TimeUnit.SECONDS) //读取超时
//                .writeTimeout(120, TimeUnit.SECONDS) //写超时
//                .build();


        return new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS) //连接超时
                .readTimeout(120, TimeUnit.SECONDS) //读取超时
                .writeTimeout(120, TimeUnit.SECONDS) //写超时
                .build();

    }


    /***
     * 自动升级流程：
     * 调用savepoint with cancel 记录返回的savepoint trigger
     * 查询savepoint的具体位置
     * 调用run with savepoint
     * @param jobId
     * @throws Exception
     */
    public String cancelJobWithSavepoint(String jobId) {
        try {
            String webUrl = "https://flink-risk-cron-native.yupaopao.com/";

            Map<String, Object> sendParam = new HashMap<>();
            //sendParam.put("target-directory", "/flink/upgrade-release");
            sendParam.put("cancel-job", true);
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(sendParam));
            Request request = new Request.Builder()
                    .url(webUrl + "/jobs/" + jobId + "/savepoints")
                    .post(body).build();
            Response response = getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("cancelJobWithSavepoint response: {}", responseStr);
            if (StringUtils.isEmpty(responseStr)) {
                throw new RuntimeException("cancel job with empty response");
            }
            JSONObject jsonObj = JSONObject.parseObject(responseStr);
            String savepointTriggerId = jsonObj.getString("request-id");
            if (StringUtils.isEmpty(savepointTriggerId)) {
                throw new RuntimeException("cancel job without savepoint trigger, response: " + responseStr);
            }
            return savepointTriggerId;
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e1) {
            log.error("cancelJobWithSavepoint failed, jobId= " + jobId, e1);
            throw new RuntimeException("cancelJobWithSavepoint: " + e1.getMessage());
        }
    }

    /**
     * 根据triggerId获取某个job的savepoint具体位置
     *
     * @param jobId
     * @param savepointTriggerId
     * @return
     */
    public String getSavepointLocation(String jobId, String savepointTriggerId) {
        try {
            String webUrl = "http://flink-risk-native.yupaopao.com/";
            Request request = new Request.Builder()
                    .url(webUrl + "/jobs/" + jobId + "/savepoints/" + savepointTriggerId)
                    .get().build();
            Response response = getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("get savepoint location response: {}", responseStr);
            if (StringUtils.isEmpty(responseStr)) {
                throw new RuntimeException("cancel job with empty response");
            }
            JSONObject jsonObj = JSONObject.parseObject(responseStr);
            String savepointStatus = (String) JSONPath.read(responseStr, "$.status.id");
            if ("COMPLETED".equalsIgnoreCase(savepointStatus)) {
                String savepointLocation = (String) JSONPath.eval(responseStr, "$.operation.location");
                if (StringUtils.isEmpty(savepointLocation)) {
                    throw new RuntimeException("cannot get savepoint location, please check");
                }
                return savepointLocation;
            }
            return "";
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e1) {
            log.error("getSavepointLocation failed: jobId=" + jobId + ",savepointTrigger=" + savepointTriggerId, e1);
            throw new RuntimeException("getSavepointLocation failed: " + e1.getMessage());
        }
    }

    /**
     * 通过savepoint 重新运行job
     *
     * @param newJarFileName
     * @param savepointLocation
     * @return
     */
    public String runWithSavepoint(String newJarFileName, String savepointLocation) {
        try {
            String webUrl = "https://test-flink.yupaopao.com";
            Map<String, Object> jsonParams = new HashMap<>();
            jsonParams.put("entryClass", null);
            jsonParams.put("parallelism", null);
            jsonParams.put("programArgs", null);
            jsonParams.put("savepointPath", savepointLocation);
            jsonParams.put("allowNonRestoredState", true);
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(jsonParams));
            String jarId = getJarId(newJarFileName);
            if (StringUtils.isEmpty(jarId)) {
                throw new RuntimeException("getJarId error");
            }
            Request request = new Request.Builder()
                    .url(webUrl + "/jars/" + jarId + "/run")
                    .post(body).build();
            Response response = getLongCallHttpClient().newCall(request).execute();
            String responseStr = response.body().string();
            log.info("runWithSavePoint response: {}", responseStr);
            JSONObject json = JSONObject.parseObject(responseStr);
            String jobId = json.getString("jobid");
            if (StringUtils.isEmpty(jobId)) {
                throw new RuntimeException("runWithSavepoint failed: response: " + responseStr);
            }
            return jobId;
        } catch (RuntimeException e1) {
            throw e1;
        } catch (Exception e) {
            log.error("runWithSavepoint: jarFileName=" + newJarFileName + ", savepointLocation=" + savepointLocation, e);
            throw new RuntimeException("runWithSavepoint: " + e.getMessage());
        }
    }

    public static void main(String[] args) throws Exception {
String json ="%7B%22partition%22%3A%2220231110%22%2C%22hiveQuerySql%22%3A%22select+id%2C%28case+when+kv%5B%27deviceRegisterCnt1d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceRegisterCnt1d%27%5D%5Cn+end%5Cn+%29+as+deviceRegisterCnt1d%2C%5Cn%28case+when+kv%5B%27deviceRegisterSuccessCnt1d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceRegisterSuccessCnt1d%27%5D%5Cn+end%5Cn+%29+as+deviceRegisterSuccessCnt1d%2C%5Cn%28case+when+kv%5B%27deviceRegisterFailedCnt1d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceRegisterFailedCnt1d%27%5D%5Cn+end%5Cn+%29+as+deviceRegisterFailedCnt1d%2C%5Cn%28case+when+kv%5B%27deviceLoginFailedCnt1d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceLoginFailedCnt1d%27%5D%5Cn+end%5Cn+%29+as+deviceLoginFailedCnt1d%2C%5Cn%28case+when+kv%5B%27deviceLoginSuccessCnt1d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceLoginSuccessCnt1d%27%5D%5Cn+end%5Cn+%29+as+deviceLoginSuccessCnt1d%2C%5Cn%28case+when+kv%5B%27deviceLoginCnt1d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceLoginCnt1d%27%5D%5Cn+end%5Cn+%29+as+deviceLoginCnt1d%2C%5Cn%28case+when+kv%5B%27deviceRegisterCnt7d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceRegisterCnt7d%27%5D%5Cn+end%5Cn+%29+as+deviceRegisterCnt7d%2C%5Cn%28case+when+kv%5B%27deviceRegisterSuccessCnt7d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceRegisterSuccessCnt7d%27%5D%5Cn+end%5Cn+%29+as+deviceRegisterSuccessCnt7d%2C%5Cn%28case+when+kv%5B%27deviceRegisterFailedCnt7d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceRegisterFailedCnt7d%27%5D%5Cn+end%5Cn+%29+as+deviceRegisterFailedCnt7d%2C%5Cn%28case+when+kv%5B%27deviceLoginFailedCnt7d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceLoginFailedCnt7d%27%5D%5Cn+end%5Cn+%29+as+deviceLoginFailedCnt7d%2C%5Cn%28case+when+kv%5B%27deviceLoginSuccessCnt7d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceLoginSuccessCnt7d%27%5D%5Cn+end%5Cn+%29+as+deviceLoginSuccessCnt7d%2C%5Cn%28case+when+kv%5B%27deviceLoginCnt7d%27%5D+is+null+then+%270%27%5Cn+else+kv%5B%27deviceLoginCnt7d%27%5D%5Cn+end%5Cn+%29+as+deviceLoginCnt7d+from+%28%5Cn+select+id%2C%5Cn+str_to_map%28CONCAT_WS%28%27%26%27%2CCOLLECT_SET%28concat%28fname%2C%5C%22%3A%5C%22%2Cfvalue%29%29%29+%2C%5C%22%26%5C%22%2C%5C%22%3A%5C%22%29+as+kv%5Cn+from+risk_control.ods_risk_feature_df+where+dt+%3D+%2720231110%27+and+id%21%3D%270%27+%5Cn+and+idtype+%3D+1%5Cn+group+by+id%5Cn%29+ttt%22%2C%22outTable%22%3A%22ods_risk_device_feature_agg_df%22%2C%22ckQuerySql%22%3A%22select+id%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceRegisterCnt1h%27%29+as+deviceRegisterCnt1h%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceRegisterSuccessCnt1h%27%29+as+deviceRegisterSuccessCnt1h%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceRegisterFailedCnt1h%27%29+as+deviceRegisterFailedCnt1h%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceLoginFailedCnt1h%27%29+as+deviceLoginFailedCnt1h%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceLoginSuccessCnt1h%27%29+as+deviceLoginSuccessCnt1h%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceLoginCnt1h%27%29+as+deviceLoginCnt1h%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceRegisterCnt10min%27%29+as+deviceRegisterCnt10min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceRegisterSuccessCnt10min%27%29+as+deviceRegisterSuccessCnt10min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceRegisterFailedCnt10min%27%29+as+deviceRegisterFailedCnt10min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceLoginFailedCnt10min%27%29+as+deviceLoginFailedCnt10min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceLoginSuccessCnt10min%27%29+as+deviceLoginSuccessCnt10min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceLoginCnt10min%27%29+as+deviceLoginCnt10min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceRegisterCnt30min%27%29+as+deviceRegisterCnt30min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceRegisterSuccessCnt30min%27%29+as+deviceRegisterSuccessCnt30min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceRegisterFailedCnt30min%27%29+as+deviceRegisterFailedCnt30min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceLoginFailedCnt30min%27%29+as+deviceLoginFailedCnt30min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceLoginSuccessCnt30min%27%29+as+deviceLoginSuccessCnt30min%2C%5CnsumIf%28toInt64OrZero%28fvalue%29%2Cfname%3D%27deviceLoginCnt30min%27%29+as+deviceLoginCnt30min+from+ods_risk_feature_df+where+dt+%3D%272023-11-10%27+and+idtype+%3D+1+%5Cngroup+by+id+order+by+id%3B%22%7D";
testRun(json);
    }




    public static String testRun(String args) {
        String jarId = "b1d140ae-385e-4653-86da-5aefd234b8b4_statistic-feature-agg.jar";
        Map<String, Object> jsonParams = new HashMap<>();
        jsonParams.put("parallelism", null);
        jsonParams.put("programArgs", args);
        jsonParams.put("savepointPath", null);
        jsonParams.put("allowNonRestoredState", null);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(jsonParams));

        Request request = new Request.Builder()
                .url("https://flink-risk-cron-native.yupaopao.com/jars/" + jarId + "/run")
                .post(body).build();
        try {
            Response response = client.newCall(request).execute();
            String responseStr = response.body().string();
            log.info("the run result is: {}", responseStr);
            JSONObject json = JSONObject.parseObject(responseStr);
            return json.getString("jobid");
        } catch (Exception e) {
            log.error("run job error: ", e);
            e.printStackTrace();
        }
        return null;
    }

    private static OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS) //连接超时
            .readTimeout(120, TimeUnit.SECONDS) //读取超时
            .writeTimeout(120, TimeUnit.SECONDS) //写超时
            .build();


}
