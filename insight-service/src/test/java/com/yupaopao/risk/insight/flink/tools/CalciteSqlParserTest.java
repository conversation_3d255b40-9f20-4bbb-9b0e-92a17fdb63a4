//package com.yupaopao.risk.insight.flink.tools;
//
//import com.yupaopao.risk.insight.flink.meta.JobParams;
//import com.yupaopao.risk.insight.flink.parser.InsightSqlParser;
//import com.yupaopao.risk.insight.flink.parser.ParserEnum;
//import com.yupaopao.risk.insight.flink.parser.ParserSelector;
//import com.yupaopao.risk.insight.flink.parser.impl.CalciteIdentifierFinder;
//import org.apache.calcite.config.Lex;
//import org.apache.calcite.sql.SqlNode;
//import org.apache.calcite.sql.parser.SqlParseException;
//import org.apache.calcite.sql.parser.SqlParser;
//import org.junit.Assert;
//import org.junit.Test;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//
///****
// *
// * @Author: zengxiangcai
// * @Date: 2019-12-11 17:36
// *
// ***/
//public class CalciteSqlParserTest {
//
//    @Test
//    public void testTablePeriod(){
//        InsightSqlParser parser = ParserSelector.select(ParserEnum.CALCITE);
//
//        String sql = "select level,count(1) from risk_hit_log where createdAt between '2019-12-01' and '2019-12-20'";
//        List<JobParams.TableQueryDatePeriod> periods = parser.getTableQueryDatePeriod(sql);
//        Assert.assertEquals(1L,periods.size());
//    }
//
//
//    @Test
//    public void parseColumn(){
//        SqlParser.ConfigBuilder configBuilder = SqlParser.configBuilder();
//        configBuilder.setCaseSensitive(true);
//        configBuilder.setLex(Lex.MYSQL);
//        SqlParser.Config config = configBuilder.build();
//        String sql = "select a.traceId,a.eventCode,a.level,b.userName,b.post,c.mobile from risk_hit_log a join " +
//                "zxc_data b on a.traceId = b.traceId join zxc_data2 c on b.userName = c.userName where a.createdAt between '2019-12-16' and '2019-12-17'" ;
//
//        sql  = "select count(1) as count_time_test1 from risk_hit_log where createdAt BETWEEN '2019-12-16 00:00:00' AND '2019-12-17 00:00:00' and eventCode = 'user-login'";
//        sql = "-- SQL\n" +
//                "-- ********************************************************************--\n" +
//                "-- Author: xiaming\n" +
//                "-- CreateTime: 2019-12-20 10:30:11\n" +
//                "-- Comment: 请输入业务注释信息\n" +
//                "-- ********************************************************************--\n" +
//                "select\n" +
//                "  userId\n" +
//                "from\n" +
//                "  risk_hit_log\n" +
//                "where\n" +
//                "  createdAt BETWEEN '2019-12-16 00:00:00'\n" +
//                "  AND '2019-12-17 00:00:00'\n" +
//                "  AND eventCode = 'im-message'\n" +
//                "order by\n" +
//                "  createdAt\n" +
//                "limit\n" +
//                "  10";
//
//
//        sql  = "select\n" +
//                "  data_UserId,\n" +
//                "  count(data_TraceId) as cishu\n" +
//                "from\n" +
//                "  risk_hit_log\n" +
//                "where\n" +
//                "  createdAt BETWEEN '2019-12-23 00:00:00'\n" +
//                "  AND '2019-12-24 00:00:00'\n" +
//                "  and eventCode in ('chat-room')\n" +
//                "  and data_userData_vipLevel < 2\n" +
//                "  and data_userData_god <> 'true'\n" +
//                "  and auth <> 'true'\n" +
//                "  and data_toUserData_nobilityLevel < 2\n" +
//                "  and data_userData_createdAt>'2019-12-22 12:00:00'\n" +
//                "group by\n" +
//                "  data_UserId\n" +
//                "order by\n" +
//                "  cishu desc\n" +
//                "limit\n" +
//                "  20";
//        SqlParser parser = SqlParser.create(sql, config);
//        try {
//            SqlNode sqlNode = parser.parseQuery();
//            CalciteIdentifierFinder.extractDataPeriod(sqlNode,"createdAt");
//            Map<String, Set<String>> tableColumnMap = CalciteIdentifierFinder.extractTableColumnsInSQL(sqlNode);
//            tableColumnMap.entrySet().stream().filter(elem->elem.getValue().contains("*")).forEach(elem-> System.err.println("table "+elem.getValue()+" contains *"));
//            System.err.println(tableColumnMap);
//            System.err.println("********************");
//
////            sql = "select level,count(id) from risk_hit_log a where createdAt between '2019-12-01' and " +
////                    "'2019-12-20' group by level" ;
////            parser = SqlParser.create(sql, config);
////            sqlNode =  parser.parseQuery();
////
////            System.err.println(CalciteIdentifierFinder.extractTableColumnsInSQL(sqlNode));
//
//        } catch (SqlParseException e) {
//
//            throw new IllegalArgumentException("parse sql error: " + e.getMessage());
//        }
//    }
//}
