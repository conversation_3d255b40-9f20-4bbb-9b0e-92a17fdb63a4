<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.yupaopao.risk.insight</groupId>
        <artifactId>risk-insight</artifactId>
        <version>1.0.4</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yupaopao.risk.insight</groupId>
    <artifactId>insight-service</artifactId>
    <version>1.0.4</version>
    <packaging>jar</packaging>

    <properties>
        <log4j-over-slf4j.version>1.7.15</log4j-over-slf4j.version>
        <scala.binary.version>2.11</scala.binary.version>
        <groovy-all.version>2.4.16</groovy-all.version>
        <velocity.version>2.1</velocity.version>
        <risk-shoot-api.version>1.12.15</risk-shoot-api.version>
        <operation-common-sdk.version>1.3.7</operation-common-sdk.version>

        <punish-api.version>1.16.26</punish-api.version>
        <arthur-api.version>1.0.9-SNAPSHOT</arthur-api.version>
        <insight-common.version>2.0-SNAPSHOT</insight-common.version>
        <ali-hbase.client.version>2.0.6</ali-hbase.client.version>
        <json-flattener.version>0.8.0</json-flattener.version>
        <gremlin-driver.version>3.4.6</gremlin-driver.version>

    </properties>

    <dependencies>
        <!-- New Auth -->
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-netty-shaded</artifactId>
            <version>1.37.0</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
            <version>1.37.0</version>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-stub</artifactId>
            <version>1.37.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>3.7.1</version>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>cat-mybatis-monitor</artifactId>
        </dependency>
        <!-- kafka -->
        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>kafka-high-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.yupaopao.framework</groupId>
                    <artifactId>cat-mybatis-monitor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>redis-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>sso-spring-boot-starter</artifactId>
            <version>1.0.5-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>cat-web-monitor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.live</groupId>
            <artifactId>yuer-live-api</artifactId>
            <version>0.2.7.4</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!-- WebSocket support -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- dubbo -->
        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <!-- db -->
        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <!-- mybatis -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>${velocity.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy-all.version}</version>
        </dependency>


       <!--    cep pattern  校验，ck类型用到flink相关 -->
<!--        <dependency>-->
<!--            <groupId>org.apache.flink</groupId>-->
<!--            <artifactId>flink-java</artifactId>-->
<!--            <version>${flink.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.flink</groupId>-->
<!--            <artifactId>flink-streaming-java_${scala.binary.version}</artifactId>-->
<!--            <version>${flink.version}</version>-->
<!--        </dependency>-->

<!--        &lt;!&ndash;  table sql api  &ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>org.apache.flink</groupId>-->
<!--            <artifactId>flink-table-api-java-bridge_${scala.binary.version}</artifactId>-->
<!--            <version>${flink.version}</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.apache.flink</groupId>-->
<!--            <artifactId>flink-table-planner_${scala.binary.version}</artifactId>-->
<!--            <version>${flink.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <version>${flink.version}</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.apache.flink</groupId>-->
<!--            <artifactId>flink-streaming-scala_${scala.binary.version}</artifactId>-->
<!--            <version>${flink.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-cep_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <!--  flink compile class lib end -->
        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>aries-spring-boot-starter</artifactId>
            <version>${aries-spring-boot-starter.version}</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.operation</groupId>
            <artifactId>operation-common-sdk</artifactId>
            <version>${operation-common-sdk.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <!-- Add the log4j -> sfl4j (-> logback) bridge into the classpath
         Hadoop is logging to log4j! -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
            <version>${log4j-over-slf4j.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.risk</groupId>
            <artifactId>shoot-api</artifactId>
            <version>${risk-shoot-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yupaopao.framework</groupId>
                    <artifactId>cat-mybatis-monitor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.risk</groupId>
            <artifactId>data-api</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform-common</groupId>
            <artifactId>platform-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.7</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.risk.insight</groupId>
            <artifactId>insight-api</artifactId>
            <version>1.0.4</version>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.risk</groupId>
            <artifactId>punish-api</artifactId>
            <version>${punish-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>passport-api</artifactId>
        </dependency>


        <dependency>
            <groupId>com.yupaopao.bixin</groupId>
            <artifactId>bixin-biggie-api</artifactId>
            <version>1.3.3</version>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>user-growth-api</artifactId>
            <!--            <version>0.1.1-SNAPSHOT</version>-->
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>passport-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>user-api</artifactId>
            <version>cube-1.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>user-auth-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.yupaopao.risk.insight</groupId>-->
<!--            <artifactId>insight-common</artifactId>-->
<!--            <version>1.0.1-release</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.yupaopao.risk.insight</groupId>
            <artifactId>insight-common_${flink.version}</artifactId>
            <version>${insight-common.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.arthur</groupId>
            <artifactId>arthur-api</artifactId>
            <version>${arthur-api.version}</version>
        </dependency>


        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>authentication-api</artifactId>
            <version>1.0.11</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-core</artifactId>
            <version>0.36.4-public</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.10.2</version>
        </dependency>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <!-- ali hbase client  -->
        <dependency>
            <groupId>com.aliyun.hbase</groupId>
            <artifactId>alihbase-client</artifactId>
            <version>${ali-hbase.client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-cli</groupId>
                    <artifactId>commons-cli</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.wnameless.json</groupId>
            <artifactId>json-flattener</artifactId>
            <version>${json-flattener.version}</version>
        </dependency>
        <!--   es  -->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>${elasticsearch.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.tinkerpop</groupId>
            <artifactId>gremlin-driver</artifactId>
            <version>${gremlin-driver.version}</version>
            <exclusions>
                <!-- commons-collections 在insight-flink-common包已经包含，避免冲突需要排除 -->
                <exclusion>
                    <groupId>commons-configuration</groupId>
                    <artifactId>commons-configuration</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-json</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>flink-log-connector</artifactId>
            <version>0.1.16</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.yupaopao.platform</groupId>
            <artifactId>member-app-api</artifactId>
            <version>1.0.5</version>
        </dependency>

        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-runtime</artifactId>
            <version>4.13.1</version>
        </dependency>

    </dependencies>


    <build>
        <plugins>
            <plugin>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <configurationFile>${basedir}/src/main/resources/generator.xml
                    </configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>tk.mybatis</groupId>
                        <artifactId>mapper</artifactId>
                        <version>${mapper.version}</version>
                    </dependency>
                </dependencies>
            </plugin>

        </plugins>
    </build>
</project>
