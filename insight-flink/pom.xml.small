<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>


    <groupId>com.yupaopao.risk.insight</groupId>
    <artifactId>insight-flink</artifactId>
    <version>1.0.2</version>

    <packaging>jar</packaging>

    <!--    <name>Insight Flink Job</name>-->

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <flink.version>1.13.3</flink.version>
        <java.version>1.8</java.version>
        <scala.binary.version>2.11</scala.binary.version>
        <tablestore.sdk.version>5.3.0</tablestore.sdk.version>
        <lombok.version>1.18.10</lombok.version>
        <json-flattener.version>0.8.0</json-flattener.version>
        <insight-flink-common.version>3.0.0</insight-flink-common.version>
        <log4j-over-slf4j.version>1.7.15</log4j-over-slf4j.version>
        <cat-client-flink.version>1.0.1</cat-client-flink.version>
        <logback-core.version>1.2.11</logback-core.version>
        <logback-classic.version>1.2.11</logback-classic.version>
        <groovy-all.version>2.4.16</groovy-all.version>
        <async.client.version>2.10.4</async.client.version>
        <fastjson.version>1.2.83</fastjson.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <okhttp.version>3.8.1</okhttp.version>
        <!--        <clickhouse-jdbc.version>0.2</clickhouse-jdbc.version>-->
        <geoip2.version>2.13.1</geoip2.version>
        <gremlin-driver.version>3.4.6</gremlin-driver.version>
        <netty-all.version>4.1.31.Final</netty-all.version>
        <insight-common.version>2.0-SNAPSHOT</insight-common.version>
    </properties>


    <dependencies>

        <dependency>
            <groupId>com.yupaopao.platform-common</groupId>
            <artifactId>platform-common</artifactId>
            <version>1.4.16</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.10</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>5.0.12.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>5.0.12.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>${groovy-all.version}</version>
            <scope>provided</scope>
        </dependency>


        <!-- Apache Flink dependencies -->
        <!-- flink 核心的包在flink 集群中已经包含，避免冲突的话尽量scope 设置为provided -->
        <!-- These dependencies are provided, because they should not be packaged into the JAR file. -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-java</artifactId>
            <version>${flink.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-java_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>

        <!--  table sql api  -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-api-java-bridge_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
            <scope>provided</scope>
        </dependency>

        <!--   rocksdb   -->
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-statebackend-rocksdb_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-planner_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-table-common</artifactId>
            <version>${flink.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-streaming-scala_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
            <scope>provided</scope>
        </dependency>


        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>


        <!-- Add connector dependencies here. They must be in the default scope (compile). -->

        <!-- Example:

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-kafka-0.10_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>
        -->

        <dependency>
            <groupId>com.yupaopao.risk.insight</groupId>
            <artifactId>insight-flink-common_${flink.version}</artifactId>
            <version>${insight-flink-common.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client-flink</artifactId>
            <version>${cat-client-flink.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Add logging framework, to produce console output when running in the IDE. -->
        <!-- These dependencies are excluded from the application JAR by default. -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback-core.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback-classic.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Add the log4j -> sfl4j (-> logback) bridge into the classpath
         Hadoop is logging to log4j! -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
            <version>${log4j-over-slf4j.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
            <version>${fastjson.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-connector-elasticsearch6_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>


        <dependency>
            <groupId>com.maxmind.geoip2</groupId>
            <artifactId>geoip2</artifactId>
            <version>${geoip2.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-gelly_${scala.binary.version}</artifactId>
            <version>${flink.version}</version>
        </dependency>

        <!--  alink  -->
        <dependency>
            <groupId>com.alibaba.alink</groupId>
            <artifactId>alink_core_flink-1.13_2.11-shadedv2-1.5.0</artifactId>
            <version>1.5.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-jdbc_2.11</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.derby</groupId>
                    <artifactId>derby</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.jayway.jsonpath</groupId>
                    <artifactId>json-path</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.risk.insight</groupId>
            <artifactId>insight-common_${flink.version}</artifactId>
            <version>${insight-common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-core</artifactId>
            <version>0.36.4-public</version>
            <exclusions>
                <exclusion>
                    <artifactId>protobuf-java</artifactId>
                    <groupId>com.google.protobuf</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.dtstack.flinkx</groupId>
            <artifactId>flinkx-core</artifactId>
            <version>1.7-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>flink-log-connector</artifactId>
            <version>0.1.16</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-collections</groupId>
                    <artifactId>commons-collections</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- oss -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.10.2</version>
        </dependency>

        <dependency>
            <groupId>com.yupaopao.framework</groupId>
            <artifactId>logging-spring-boot-starter</artifactId>
            <version>1.8.18</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.ojalgo</groupId>
            <artifactId>ojalgo</artifactId>
            <version>49.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.github.jelmerk</groupId>
            <artifactId>hnswlib-core</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>2.7.19</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>4.0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.101tec</groupId>-->
        <!--            <artifactId>zkclient</artifactId>-->
        <!--            <version>0.11</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.slf4j</groupId>-->
        <!--                    <artifactId>*</artifactId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <groupId>log4j</groupId>-->
        <!--                    <artifactId>log4j</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>io.searchbox</groupId>
            <artifactId>jest</artifactId>
            <version>6.3.1</version>
        </dependency>

        <!-- test -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-core</artifactId>
            <version>2.0.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.23.0</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.2</version>
            <scope>test</scope>
        </dependency>


    </dependencies>

    <build>
        <plugins>

            <!-- Java Compiler -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>

            <!-- We use the maven-shade plugin to create a fat jar that contains all necessary dependencies. -->
            <!-- Change the value of <mainClass>...</mainClass> if your program entry point changes. -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                </configuration>
                <executions>
                    <!-- Run shade goal on package phase -->
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <id>risk-hit-log-sync</id>
                        <configuration>
                            <finalName>risk-hit-log-sync</finalName>
                            <artifactSet>
                                <excludes>
                                    <exclude>org.apache.flink:force-shading</exclude>
                                    <exclude>com.google.code.findbugs:jsr305</exclude>
                                    <exclude>org.slf4j:*</exclude>
                                    <exclude>log4j:*</exclude>
                                    <exclude>ch.qos.logback:*</exclude>
                                    <exclude>net.logstash.logback:logstash-logback-encoder</exclude>
                                    <exclude>org.apache.logging.log4j:log4j-api</exclude>
                                    <exclude>org.apache.logging.log4j:log4j-to-slf4j</exclude>
                                    <exclude>org.apache.httpcomponents:httpclient</exclude>
                                    <exclude>org.apache.httpcomponents:httpcore</exclude>
                                    <exclude>org.apache.httpcomponents:httpasyncclient</exclude>
                                    <exclude>org.apache.httpcomponents:httpcore-nio</exclude>
                                    <exclude>commons-codec:commons-codec</exclude>
                                    <exclude>commons-logging:commons-logging</exclude>
                                    <exclude>org.yaml:snakeyaml</exclude>
                                    <exclude>joda-time:joda-time</exclude>
                                    <exclude>net.sf.jopt-simple:jopt-simple</exclude>
                                    <exclude>com.carrotsearch:hppc</exclude>
                                    <exclude>com.tdunning:t-digest</exclude>
                                    <exclude>org.hdrhistogram:HdrHistogram</exclude>
                                    <exclude>com.fasterxml.jackson.dataformat:*</exclude>
                                    <exclude>com.fasterxml.jackson.core:jackson-databind</exclude>
                                    <exclude>com.fasterxml.jackson.core:jackson-core</exclude>
                                    <exclude>mysql:*</exclude>
                                    <exclude>org.elasticsearch.client:*</exclude>
                                    <exclude>org.elasticsearch:*</exclude>
                                    <exclude>org.elasticsearch.plugin:*</exclude>
                                    <exclude>org.apache.lucene:*</exclude>
                                    <exclude>org.apache.commons.collections:*</exclude>
                                    <exclude>org.scalanlp:*</exclude>
                                    <exclude>jfree:*</exclude>
                                    <exclude>com.alibaba.alink:*</exclude>
                                    <exclude>org.spire-math:*</exclude>
                                    <exclude>com.github.rwl:*</exclude>
                                    <exclude>org.scala-lang:*</exclude>
                                    <exclude>commons-collections:commons-collections</exclude>
                                </excludes>
                            </artifactSet>
                            <filters>
                                <filter>
                                    <!-- Do not copy the signatures in the META-INF folder.
                                    Otherwise, this might cause SecurityExceptions when using the JAR. -->
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>com.yupaopao.risk.insight.flink.job.etl.RiskHitLogSync</mainClass>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <id>44.1</id>
                        <configuration>
                            <finalName>risk-user-data</finalName>
                            <artifactSet>
                                <excludes>
                                    <exclude>org.apache.flink:force-shading</exclude>
                                    <exclude>com.google.code.findbugs:jsr305</exclude>
                                    <exclude>org.slf4j:*</exclude>
                                    <exclude>log4j:*</exclude>
                                    <exclude>ch.qos.logback:*</exclude>
                                    <exclude>net.logstash.logback:logstash-logback-encoder</exclude>
                                    <exclude>org.apache.logging.log4j:log4j-api</exclude>
                                    <exclude>org.apache.logging.log4j:log4j-to-slf4j</exclude>
                                    <exclude>org.apache.httpcomponents:httpclient</exclude>
                                    <exclude>org.apache.httpcomponents:httpcore</exclude>
                                    <exclude>org.apache.httpcomponents:httpasyncclient</exclude>
                                    <exclude>org.apache.httpcomponents:httpcore-nio</exclude>
                                    <exclude>commons-codec:commons-codec</exclude>
                                    <exclude>commons-logging:commons-logging</exclude>
                                    <exclude>org.yaml:snakeyaml</exclude>
                                    <exclude>joda-time:joda-time</exclude>
                                    <exclude>net.sf.jopt-simple:jopt-simple</exclude>
                                    <exclude>com.carrotsearch:hppc</exclude>
                                    <exclude>com.tdunning:t-digest</exclude>
                                    <exclude>org.hdrhistogram:HdrHistogram</exclude>
                                    <exclude>com.fasterxml.jackson.dataformat:*</exclude>
                                    <exclude>com.fasterxml.jackson.core:jackson-databind</exclude>
                                    <exclude>com.fasterxml.jackson.core:jackson-core</exclude>
                                    <exclude>mysql:*</exclude>
                                    <exclude>org.elasticsearch.client:*</exclude>
                                    <exclude>org.elasticsearch:*</exclude>
                                    <exclude>org.elasticsearch.plugin:*</exclude>
                                    <exclude>org.apache.lucene:*</exclude>
                                    <exclude>org.apache.httpcomponents:*</exclude>
                                    <exclude>org.apache.commons.collections:*</exclude>
                                    <exclude>org.scalanlp:*</exclude>
                                    <exclude>jfree:*</exclude>
                                    <exclude>com.alibaba.alink:*</exclude>
                                    <exclude>org.spire-math:*</exclude>
                                    <exclude>com.github.rwl:*</exclude>
                                    <exclude>org.scala-lang:*</exclude>
                                    <exclude>com.dtstack.flinkx:*</exclude>
                                    <exclude>com.aliyun.oss:aliyun-sdk-oss</exclude>
                                    <exclude>com.aliyun:aliyun-java-sdk-core</exclude>
                                    <exclude>com.aliyun:aliyun-java-sdk-ram</exclude>
                                    <exclude>com.aliyun:aliyun-java-sdk-sts</exclude>
                                    <exclude>com.aliyun:aliyun-java-sdk-ecs</exclude>
                                    <exclude>com.aliyun:aliyun-java-sdk-kms</exclude>
                                </excludes>
                            </artifactSet>
                            <filters>
                                <filter>
                                    <!-- Do not copy the signatures in the META-INF folder.
                                    Otherwise, this might cause SecurityExceptions when using the JAR. -->
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>com.yupaopao.risk.insight.flink.job.etl.UserDataETL
                                    </mainClass>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <id>18</id>
                        <configuration>
                            <finalName>audit-task-performance</finalName>
                            <artifactSet>
                                <excludes>
                                    <exclude>org.apache.flink:force-shading</exclude>
                                    <exclude>com.google.code.findbugs:jsr305</exclude>
                                    <exclude>org.slf4j:*</exclude>
                                    <exclude>log4j:*</exclude>
                                    <exclude>ch.qos.logback:*</exclude>
                                    <exclude>net.logstash.logback:logstash-logback-encoder</exclude>
                                    <exclude>org.apache.logging.log4j:log4j-api</exclude>
                                    <exclude>org.apache.logging.log4j:log4j-to-slf4j</exclude>
                                    <exclude>org.apache.httpcomponents:httpclient</exclude>
                                    <exclude>org.apache.httpcomponents:httpcore</exclude>
                                    <exclude>org.apache.httpcomponents:httpasyncclient</exclude>
                                    <exclude>org.apache.httpcomponents:httpcore-nio</exclude>
                                    <exclude>commons-codec:commons-codec</exclude>
                                    <exclude>commons-logging:commons-logging</exclude>
                                    <exclude>org.yaml:snakeyaml</exclude>
                                    <exclude>joda-time:joda-time</exclude>
                                    <exclude>net.sf.jopt-simple:jopt-simple</exclude>
                                    <exclude>com.carrotsearch:hppc</exclude>
                                    <exclude>com.tdunning:t-digest</exclude>
                                    <exclude>org.hdrhistogram:HdrHistogram</exclude>
                                    <exclude>com.fasterxml.jackson.dataformat:*</exclude>
                                    <exclude>com.fasterxml.jackson.core:jackson-databind</exclude>
                                    <exclude>com.fasterxml.jackson.core:jackson-core</exclude>
                                    <exclude>mysql:*</exclude>
                                    <exclude>org.apache.commons.collections:*</exclude>
                                    <exclude>org.scalanlp:*</exclude>
                                    <exclude>jfree:*</exclude>
                                    <exclude>com.alibaba.alink:*</exclude>
                                    <exclude>org.spire-math:*</exclude>
                                    <exclude>com.github.rwl:*</exclude>
                                    <exclude>org.scala-lang:*</exclude>
                                    <exclude>com.dtstack.flinkx:*</exclude>
                                </excludes>
                            </artifactSet>
                            <filters>
                                <filter>
                                    <!-- Do not copy the signatures in the META-INF folder.
                                    Otherwise, this might cause SecurityExceptions when using the JAR. -->
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <transformers>
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>com.yupaopao.risk.insight.flink.job.audit.TaskPerformanceJob</mainClass>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>

                </executions>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>

                <!-- This improves the out-of-the-box experience in Eclipse by resolving some warnings. -->
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.apache.maven.plugins</groupId>
                                        <artifactId>maven-shade-plugin</artifactId>
                                        <versionRange>[3.0.0,)</versionRange>
                                        <goals>
                                            <goal>shade</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                    </action>
                                </pluginExecution>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.apache.maven.plugins</groupId>
                                        <artifactId>maven-compiler-plugin</artifactId>
                                        <versionRange>[3.1,)</versionRange>
                                        <goals>
                                            <goal>testCompile</goal>
                                            <goal>compile</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!-- This profile helps to make things run out of the box in IntelliJ -->
    <!-- Its adds Flink's core classes to the runtime class path. -->
    <!-- Otherwise they are missing in IntelliJ, because the dependency is 'provided' -->
    <profiles>
        <profile>
            <id>add-dependencies-for-IDEA</id>

            <activation>
                <property>
                    <name>idea.version</name>
                </property>
            </activation>

            <dependencies>
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-java</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-streaming-java_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>

                <!--   rocksdb   -->
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-statebackend-rocksdb_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-table-api-java-bridge_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-streaming-scala_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-table-common</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-table-planner_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>com.dianping.cat</groupId>
                    <artifactId>cat-client-flink</artifactId>
                    <version>1.0.1</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                    <version>1.2.3</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                    <version>1.2.3</version>
                    <scope>compile</scope>
                </dependency>

                <!-- Add the log4j -> sfl4j (-> logback) bridge into the classpath
                 Hadoop is logging to log4j! -->
                <dependency>
                    <groupId>org.slf4j</groupId>
                    <artifactId>log4j-over-slf4j</artifactId>
                    <version>1.7.15</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>net.logstash.logback</groupId>
                    <artifactId>logstash-logback-encoder</artifactId>
                    <version>4.11</version>
                    <scope>compile</scope>
                </dependency>

                <dependency>
                    <groupId>com.yupaopao.risk.insight</groupId>
                    <artifactId>insight-flink-common_${flink.version}</artifactId>
                    <version>${insight-flink-common.version}</version>
                    <scope>compile</scope>
                </dependency>

                <dependency>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                    <version>${fastjson.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-all</artifactId>
                    <version>${groovy-all.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-clients_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.flink</groupId>
                    <artifactId>flink-metrics-prometheus_${scala.binary.version}</artifactId>
                    <version>${flink.version}</version>
                    <scope>compile</scope>
                </dependency>
                <dependency>
                    <groupId>com.aliyun.oss</groupId>
                    <artifactId>aliyun-sdk-oss</artifactId>
                    <version>3.10.2</version>
                    <scope>compile</scope>
                </dependency>
                <!--  alink  -->
                <dependency>
                    <groupId>com.alibaba.alink</groupId>
                    <artifactId>alink_core_flink-1.13_2.11-shadedv2-1.5.0</artifactId>
                    <version>1.5.0</version>
                    <exclusions>
                        <exclusion>
                            <groupId>org.apache.flink</groupId>
                            <artifactId>flink-jdbc_2.11</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.httpcomponents</groupId>
                            <artifactId>httpclient</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.derby</groupId>
                            <artifactId>derby</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>mysql</groupId>
                            <artifactId>mysql-connector-java</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>com.jayway.jsonpath</groupId>
                            <artifactId>json-path</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>com.google.code.gson</groupId>
                            <artifactId>gson</artifactId>
                        </exclusion>
                    </exclusions>
                    <scope>compile</scope>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

</project>
