package com.yupaopao.risk.insight.flink.job;

import com.yupaopao.risk.insight.flink.windows.FactorCaches;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import com.yupaopao.risk.insight.flink.windows.triggers.FactorSlidingTimeTrigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.runtime.operators.windowing.WindowOperator;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/****
 * zengxiangcai
 * 2023/2/8 12:16
 ***/

@RunWith(PowerMockRunner.class)
@PrepareForTest(FactorCaches.class)
public class FactorSlidingTimeTriggerTest {


    FactorSlidingTimeTrigger trigger = new FactorSlidingTimeTrigger(10);


    /***
     * 滑动窗口数据到来(windowType =1)
     */
    @Test
    public void testSlidingOnElement() throws Exception {

        Long time = 1675839414857L; //2023-02-08 14:56:54
        //构建参数
        FactorCalDetail detail = FactorTestParamsConstants.buildSlidingWindowData(time);

        TimeWindow curWindow = new TimeWindow(1675838820000L, 1675839420000L);
        TimeWindow futureWindow = new TimeWindow(1675839360000L, 1675839960000L);


        //mock
        WindowOperator.Context ctx = PowerMockito.mock(WindowOperator.Context.class);
        PowerMockito.doNothing().when(ctx).registerProcessingTimeTimer(Mockito.anyLong());
        PowerMockito.when(ctx.getCurrentProcessingTime()).thenReturn(time);


        TriggerResult result = trigger.onElement(detail, time, curWindow, ctx);
        Assert.assertTrue("current window fire", result.equals(TriggerResult.FIRE));

        TriggerResult result1 = trigger.onElement(detail, time, futureWindow, ctx);
        Assert.assertTrue("current window is continue", result1.equals(TriggerResult.CONTINUE));

    }


    /****
     * 滚动窗口数据到来(windowType=2)
     */

    @Test
    public void testTumblingOnElement() throws Exception {
        //构建参数
        Long time = 1675839414857L; //2023-02-08 14:56:54
        FactorCalDetail detail = FactorTestParamsConstants.buildTumblingWindowData(time);
        //mock
        WindowOperator.Context ctx = PowerMockito.mock(WindowOperator.Context.class);
        PowerMockito.doNothing().when(ctx).registerProcessingTimeTimer(Mockito.anyLong());
        PowerMockito.when(ctx.getCurrentProcessingTime()).thenReturn(time);

        TimeWindow curWindow = new TimeWindow(1675839000000L, 1675839600000L);
        TriggerResult result = trigger.onElement(detail, time, curWindow, ctx);
        Assert.assertTrue("current window fire", result.equals(TriggerResult.FIRE));

    }

    /***
     * 调用了私有方法如何mock
     */

    @Test
    public void testSlidingOnProcessingTime() throws Exception {
        Long time = 1675839414857L; //2023-02-08 14:56:54
        //构建参数
        FactorCalDetail detail = FactorTestParamsConstants.buildSlidingWindowData(time);

        FactorTestParamsConstants.initMockFactorCaches();
        PowerMockito.when(FactorCaches.getWindowType(Mockito.anyString())).thenReturn(1);
        TimeWindow curWindow = new TimeWindow(1675838820000L, 1675839420000L);
        WindowOperator.Context ctx = PowerMockito.mock(WindowOperator.Context.class);
        PowerMockito.doNothing().when(ctx).registerProcessingTimeTimer(Mockito.anyLong());
        PowerMockito.when(ctx.getCurrentProcessingTime()).thenReturn(time);
        PowerMockito.when(ctx.toString()).thenReturn("Context{" + "key=" + detail.getGroupKey() + ", window=" + curWindow + '}');

        TriggerResult result = trigger.onProcessingTime(time, curWindow, ctx);
        Assert.assertTrue("windowType 1 processTime windowEnd: fire", result.equals(TriggerResult.FIRE));

    }


    @Test
    public void testTumblingOnProcessingTime() throws Exception {
        Long time = 1675839414857L; //2023-02-08 14:56:54
        //构建参数
        FactorCalDetail detail = FactorTestParamsConstants.buildTumblingWindowData(time);

        PowerMockito.spy(FactorCaches.class);
        PowerMockito.when(FactorCaches.class, "getWindowType", Mockito.anyString()).thenReturn(2);
        TimeWindow curWindow = new TimeWindow(1675839000000L, 1675839600000L);
        WindowOperator.Context ctx = PowerMockito.mock(WindowOperator.Context.class);
        PowerMockito.doNothing().when(ctx).registerProcessingTimeTimer(Mockito.anyLong());
        PowerMockito.when(ctx.getCurrentProcessingTime()).thenReturn(time);
        PowerMockito.when(ctx.toString()).thenReturn("Context{" + "key=" + detail.getGroupKey() + ", window=" + curWindow + '}');

        TriggerResult result = trigger.onProcessingTime(time, curWindow, ctx);
        Assert.assertTrue("windowType 1 processTime windowEnd: fire", result.equals(TriggerResult.CONTINUE));

    }


}
