package com.yupaopao.risk.insight.flink.job;

import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.connector.redis.sink.RedisCustomizeSink;
import com.yupaopao.risk.insight.flink.windows.FactorCaches;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.internal.impl.PowerMockJUnit44RunnerDelegateImpl;
import org.powermock.reflect.Whitebox;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.util.concurrent.ConcurrentHashMap;

/****
 * zengxiangcai
 * 2023/2/8 12:16
 ***/


@RunWith(PowerMockRunner.class)
@PrepareForTest(FactorCaches.class)
@SuppressStaticInitializationFor("com.yupaopao.risk.insight.flink.windows.FactorCaches")
public class RedisCustomizeSinkTest {

    RedisCustomizeSink sink = new RedisCustomizeSink(RedisProperties.getProperties(PropertyType.REDIS));

    @Test
    public void testGetExpireTime() throws Exception {
        //
        String factorId = "1";
        long timespan = 10L;

        FactorTestParamsConstants.initMockFactorCaches();
        PowerMockito.when(FactorCaches.getWindowType(factorId)).thenReturn(1);

        Object res = Whitebox.invokeMethod(sink, "getExpireTime", factorId, timespan);

        Assert.assertTrue(res.equals(63L)); // 63 seconds

    }
}
