package com.yupaopao.risk.insight.flink.job;

import com.yupaopao.risk.insight.flink.windows.FactorCaches;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;

import java.sql.Connection;
import java.util.concurrent.ConcurrentHashMap;

/****
 * zengxiangcai
 * 2023/2/8 16:24
 ***/
public class FactorTestParamsConstants {

    public static FactorCalDetail buildSlidingWindowData(Long time) {
        //构建参数
        /***
         * 该时间期望的窗口：
         * 2023-02-08 14:56:00
         * 2023-02-08 15:06:00
         * #####
         * 2023-02-08 14:55:00
         * 2023-02-08 15:05:00
         * #####
         * 2023-02-08 14:54:00
         * 2023-02-08 15:04:00
         * #####
         * 2023-02-08 14:53:00
         * 2023-02-08 15:03:00
         * #####
         * 2023-02-08 14:52:00
         * 2023-02-08 15:02:00
         * #####
         * 2023-02-08 14:51:00
         * 2023-02-08 15:01:00
         * #####
         * 2023-02-08 14:50:00
         * 2023-02-08 15:00:00
         * #####
         * 2023-02-08 14:49:00
         * 2023-02-08 14:59:00
         * #####
         * 2023-02-08 14:48:00
         * 2023-02-08 14:58:00
         * #####
         * 2023-02-08 14:47:00
         * 2023-02-08 14:57:00
         * #####
         */
        FactorCalDetail detail = new FactorCalDetail();
        detail.setWindowType(1);
        detail.setGroupKey("FACTOR-DATA#1#190100000000000001");
        detail.setTimeSpan(10L);  //10min, per sliding 1min
        detail.setPurge(false);
        detail.setTimeStamp(time);
        detail.setRequestTime(time);
        detail.setFunction("COUNT");
        return detail;
    }

    public static FactorCalDetail buildTumblingWindowData(Long time) {

        FactorCalDetail detail = new FactorCalDetail();
        detail.setWindowType(2);
        detail.setGroupKey("FACTOR-DATA#1#190100000000000001");
        detail.setTimeSpan(10L);  //10min, per sliding 1min
        detail.setPurge(false);
        detail.setTimeStamp(time);
        detail.setRequestTime(time);
        detail.setFunction("COUNT");
        return detail;
    }

    public static void initMockFactorCaches(){
        Connection conn = PowerMockito.mock(Connection.class);
        PowerMockito.mockStatic(FactorCaches.class);
        //init static variables
        //Whitebox.setInternalState(ClassName.class,fieldName,fieldValue)
        Whitebox.setInternalState(FactorCaches.class,"FACTOR_WINDOW_TYPE_MAP", new ConcurrentHashMap<String, Integer>());
        Whitebox.setInternalState(FactorCaches.class,"FACTOR_FUNCTION_MAP", new ConcurrentHashMap<String, Integer>());
        Whitebox.setInternalState(FactorCaches.class,"dbConn", conn);
    }
}
