package com.yupaopao.risk.insight.flink.cep;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.common.typeutils.base.ListSerializer;
import org.apache.flink.api.common.typeutils.base.StringSerializer;
import org.apache.flink.cep.PatternStream;
import org.apache.flink.cep.*;
import org.apache.flink.cep.functions.PatternProcessFunction;
import org.apache.flink.cep.nfa.aftermatch.AfterMatchSkipStrategy;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.AssignerWithPeriodicWatermarks;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.streaming.api.watermark.Watermark;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.junit.Test;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

@Slf4j
public class PatternCaseTest {

    @Test
    public void test01() throws Exception {

        Pattern<String,String> p1 = Pattern.<String>begin("emoji0", AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<String>(){
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("userEmoji");
            }
        }).times(2).greedy();

        Pattern<String,String> p2 = Pattern.<String>begin("emoji0", AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<String>(){
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("userEmoji");
            }
        }).times(2);

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
//        DataStream<String> inDs = env.fromElements("userEmoji0", "imMessage0","userEmoji1","register0","userEmoji2","register2","userEmoji3","liveRoomEnter1", "imMessage1","im1");
        DataStream<String> inDs = env.fromElements("userEmoji0", "userEmoji1","userEmoji2","userEmoji3","userEmoji4","userEmoji5","userEmoji6","userEmoji7","liveRoomEnter1","im1");
        matchPattern(inDs,p1);
        matchPattern(inDs,p2);

        env.execute("test02");

    }

    private void matchPattern(DataStream<String> inDs,Pattern<String,String> p){
        PatternStream<String> matchStream = CEP.pattern(inDs, p);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }

                System.err.println("************##################************");
            }
        });
    }

    @Test
    public void testConsecutive() throws Exception {

//        Works in conjunction with oneOrMore() and times() and imposes strict contiguity between the matching events,
//        i.e. any non-matching element breaks the match (as in next()).
//        If not applied a relaxed contiguity (as in followedBy()) is used.

        Pattern<String,String> p1 = Pattern.<String>begin("start").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.equals("c");
            }
        }).followedBy("middle").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("a");
            }
        }).oneOrMore().consecutive().followedBy("end1").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.equals("b");
            }
        });

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        DataStream<String> inDs = env.fromElements("c","d","a1","a2","a3","d","a4","b");
        PatternStream<String> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });

        env.execute("testConsecutive");

    }

    @Test
    public void testAllowCombinations() throws Exception {

//        Works in conjunction with oneOrMore() and times() and imposes strict contiguity between the matching events,
//        i.e. any non-matching element breaks the match (as in next()).
//        If not applied a relaxed contiguity (as in followedBy()) is used.

        Pattern<String,String> p1 = Pattern.<String>begin("start").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.equals("c");
            }
        }).followedBy("middle").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("a");
            }
        }).oneOrMore().allowCombinations().followedBy("end1").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.equals("b");
            }
        });

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        DataStream<String> inDs = env.fromElements("c","d","a1","a2","a3","d","a4","b");
        PatternStream<String> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });

        env.execute("testAllowCombinations");

    }

    @Test
    public void testSkipPastLastEvent() throws Exception {

//        Works in conjunction with oneOrMore() and times() and imposes strict contiguity between the matching events,
//        i.e. any non-matching element breaks the match (as in next()).
//        If not applied a relaxed contiguity (as in followedBy()) is used.

        Pattern<String,String> p1 = Pattern.<String>begin("start", AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.equals("c");
            }
        }).followedBy("middle").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("a");
            }
        }).oneOrMore().consecutive().followedBy("end1").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.equals("b");
            }
        });

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        DataStream<String> inDs = env.fromElements("c","d","a1","a2","a3","d","a4","b");
        PatternStream<String> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });

        env.execute("testSkipPastLastEvent");

    }

    @Test
    public void testContiguityWithinLoopingPatterns() throws Exception {

        Pattern<String,String> p1 = Pattern.<String>begin("start").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.equals("a");
            }
        }).followedByAny("middle").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("b");
            }
        }).oneOrMore().followedBy("end1").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.equals("c");
            }
        });

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        DataStream<String> inDs = env.fromElements("a","b1","d1","b2","d2","b3","c");
        PatternStream<String> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });

        env.execute("testContiguityWithinLoopingPatterns");

    }

    @Test
    public void test02() throws Exception {

//        Pattern<String,String> p1 = Pattern.<String>begin("start").where(new SimpleCondition<String>() {
//            @Override
//            public boolean filter(String value) throws Exception {
//                return value.equals("a");
//            }
//        }).next("middle").where(new SimpleCondition<String>() {
//            @Override
//            public boolean filter(String value) throws Exception {
//                return value.startsWith("b");
//            }
//        });
//
////        result:
////        {}

        Pattern<String,String> p1 = Pattern.<String>begin("start").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.equals("a");
            }
        }).followedBy("middle").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("b");
            }
        });

//        result:
//        pattern: start, value: [a]
//        pattern: middle, value: [b1]


//        Pattern<String,String> p1 = Pattern.<String>begin("start").where(new SimpleCondition<String>() {
//            @Override
//            public boolean filter(String value) throws Exception {
//                return value.equals("a");
//            }
//        }).followedByAny("middle").where(new SimpleCondition<String>() {
//            @Override
//            public boolean filter(String value) throws Exception {
//                return value.startsWith("b");
//            }
//        });

//        result:
//        pattern: start, value: [a]
//        pattern: middle, value: [b1]
//        pattern: start, value: [a]
//        pattern: middle, value: [b2]

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        DataStream<String> inDs = env.fromElements("a","c","b1","b2");
        PatternStream<String> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });

        env.execute("testSkipPastLastEvent");

    }

    @Test
    public void test03() throws Exception {

        Pattern<String,String> p1 = Pattern.<String>begin("start").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.equals("a");
            }
        }).followedBy("middle").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("b");
            }
        }).oneOrMore().consecutive().within(Time.milliseconds(3));

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
//        System.err.println(PatternCaseTest.class.getResource("/").getPath()+"EventStream.txt");
//        String path = PatternCaseTest.class.getResource("/").getPath()+"EventStream.txt";
        String path = "file:///Users/<USER>/yupaopao/risk-insight/insight-flink/src/test/java/com/yupaopao/risk/insight/flink/cep/EventStream.txt";
//        DataStream<String> inDs = env.readTextFile(path);
        DataStream<String> inDs = env.fromElements("a","b1","b2","c");
        PatternStream<String> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("PatternProcessFunction pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });

        matchStream.select(new PatternSelectFunction<String, Object>() {
            @Override
            public Object select(Map<String, List<String>> pattern) throws Exception {
                for (Map.Entry<String, List<String>> entry : pattern.entrySet()) {
                    System.err.println("PatternSelectFunction pattern: " + entry.getKey() + ", value: " + entry.getValue());
                }

                return null;
            }
        });

        matchStream.flatSelect(new PatternFlatSelectFunction<String, Object>() {
            @Override
            public void flatSelect(Map<String, List<String>> pattern, Collector<Object> out) throws Exception {
                for (Map.Entry<String, List<String>> entry : pattern.entrySet()) {
                    System.err.println("PatternFlatSelectFunction pattern: " + entry.getKey() + ", value: " + entry.getValue());
                }
            }
        });

//        final OutputTag<L> timedOutPartialMatchesTag,
//        final PatternFlatTimeoutFunction<T, L> patternFlatTimeoutFunction,
//        final TypeInformation<R> outTypeInfo,
//        final PatternFlatSelectFunction<T, R> patternFlatSelectFunction

        OutputTag<String> timeOutPartialMatchesTag = new OutputTag<String>("timeOutPartialMatchesTag"){
            private static final long serialVersionUID = 6248021963350975088L;
        };

        PatternFlatTimeoutFunction<String,String> patternFlatTimeoutFunction = new PatternFlatTimeoutFunction<String,String>(){

            @Override
            public void timeout(Map<String, List<String>> pattern, long timeoutTimestamp, Collector<String> out) throws Exception {
                for (Map.Entry<String, List<String>> entry : pattern.entrySet()) {
                    System.err.println("PatternFlatTimeoutFunction pattern: " + entry.getKey() + ", value: " + entry.getValue());
                }
            }
        };

        TypeInformation<String> outTypeInfo = new TypeInformation<String>() {
            @Override
            public boolean isBasicType() {
                return false;
            }

            @Override
            public boolean isTupleType() {
                return false;
            }

            @Override
            public int getArity() {
                return 0;
            }

            @Override
            public int getTotalFields() {
                return 0;
            }

            @Override
            public Class<String> getTypeClass() {
                return null;
            }

            @Override
            public boolean isKeyType() {
                return false;
            }

            @Override
            public TypeSerializer<String> createSerializer(ExecutionConfig executionConfig) {
                return null;
            }

            @Override
            public String toString() {
                return null;
            }

            @Override
            public boolean equals(Object o) {
                return false;
            }

            @Override
            public int hashCode() {
                return 0;
            }

            @Override
            public boolean canEqual(Object o) {
                return false;
            }
        };

        PatternFlatSelectFunction<String,String> patternFlatSelectFunction = new PatternFlatSelectFunction<String,String>(){

            @Override
            public void flatSelect(Map<String, List<String>> pattern, Collector<String> out) throws Exception {
                for (Map.Entry<String, List<String>> entry : pattern.entrySet()) {
                    System.err.println("PatternFlatSelectFunction pattern: " + entry.getKey() + ", value: " + entry.getValue());
                }
            }
        };

        SingleOutputStreamOperator singleOutputStreamOperator = matchStream.flatSelect(timeOutPartialMatchesTag,patternFlatTimeoutFunction,outTypeInfo,patternFlatSelectFunction);

        env.execute("testSkipPastLastEvent");

    }

   private List<MyEvent> createEventList(){
       List<MyEvent> events = Lists.newArrayList();

       events.add(new MyEvent(1614592135000L,"a"));
       events.add(new MyEvent(1614592135010L,"c"));
       events.add(new MyEvent(1614592135020L,"b1"));
       events.add(new MyEvent(1614592135030L,"b2"));
       events.add(new MyEvent(1614592135040L,"b3"));
       events.add(new MyEvent(1614592135050L,"b4"));
       events.add(new MyEvent(1614592135060L,"b5"));

       return events;
   }

    private List<MyRule> createRuleList(){
        List<MyRule> rules = Lists.newArrayList();

        rules.add(new MyRule(1614592135000L,1, 10));
        rules.add(new MyRule(1614592135010L,2,20));
        rules.add(new MyRule(1614592135020L,3,30));
        rules.add(new MyRule(1614592135030L,4,40));
        rules.add(new MyRule(1614592135040L,5,50));
        rules.add(new MyRule(1614592135050L,6,60));
        rules.add(new MyRule(1614592135060L,7,70));

        return rules;
    }

    @Test
    public void test04() throws Exception {

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        List<MyEvent> myEvents = createEventList();
        DataStream<MyEvent> inDos1 = env.fromCollection(myEvents);
//                .fromElements("a","c","b1","b2","b3","b4","b5");
        DataStream<MyEvent> riskEventStream = inDos1.map(new MapFunction<MyEvent,MyEvent>(){
            @Override
            public MyEvent map(MyEvent event) throws Exception {
                System.err.println("event: "+event.content);
                return event.content.startsWith("b")?event:null;
            }
        }).setParallelism(1)
                .filter(element->{
                    System.err.println("element:"+JSONObject.toJSONString(element));
                    return element!=null&&element.content.startsWith("b");
                }).setParallelism(1).assignTimestampsAndWatermarks(new MyTimestampsAndWatermarks());

//        System.err.println("riskEventStream:"+ );

        List<MyRule> myRules = createRuleList();
//        DataStream<Integer> inDos2 = env.fromElements(1,2,3,4,5,6,7);
        DataStream<MyRule> ruleDataStream = env.fromCollection(myRules);
        DataStream<List<MyRule>> riskRuleStream = ruleDataStream.map(new MapFunction<MyRule, List<MyRule>>(){
            @Override
            public List<MyRule> map(MyRule value) throws Exception {

                System.err.println("riskRules="+JSONObject.toJSONString(value));

                List<MyRule> lists = Lists.newArrayList();
                lists.add(value);

                return lists;
            }
        });

        StringSerializer keySerializer = StringSerializer.INSTANCE;

        TypeSerializer<MyRule> elemSerializer = TypeInformation.of(MyRule.class).createSerializer(env.getConfig());
        ListSerializer<MyRule> valueSerializer = new ListSerializer<>(elemSerializer);

        MapStateDescriptor<String, List<MyRule>> ruleListState = new MapStateDescriptor<String,
                List<MyRule>>(
                "ruleListState",
                keySerializer,
                valueSerializer
        );

        BroadcastStream<List<MyRule>> broadcastStream =  riskRuleStream.broadcast(ruleListState);

        //connect non-broadcast stream to broadcastStream
        SingleOutputStreamOperator<MyEvent> outputStream =
                riskEventStream.connect(broadcastStream).process(new BroadcastProcessFunction<MyEvent, List<MyRule>, MyEvent>(){

                    @Override
                    public void processElement(MyEvent value, ReadOnlyContext ctx, Collector<MyEvent> out) throws Exception {
                        System.err.println("processElement value="+JSONObject.toJSONString(value)+", latestRules="+JSONObject.toJSONString(ctx.getBroadcastState(ruleListState).get("allRules"))+
                                ",timestamp="+ctx.timestamp()
                                +", currentWatermark="+ctx.currentWatermark()+",currentProcessingTime="+ctx.currentProcessingTime());

                        //最新的规则
                        List<MyRule> latestRules = ctx.getBroadcastState(ruleListState).get("allRules");
                        if (CollectionUtils.isEmpty(latestRules)) {
                            System.err.println("latest rules is empty , maybe not initialized...");
                            return;
                        }

                        System.err.println("value="+value);
//                       collector.collect(riskEvent);
                    }

                    @Override
                    public void processBroadcastElement(List<MyRule> cepRules, Context ctx, Collector<MyEvent> out) throws Exception {
                        System.err.println("processBroadcastElement="+JSONObject.toJSONString(cepRules));
                        ctx.getBroadcastState(ruleListState).put("allRules",cepRules);
                        System.err.println("processBroadcastElement allRules="+JSONObject.toJSONString(ctx.getBroadcastState(ruleListState).get("allRules")));
                    }
                });

        env.execute("test04");

    }

    class MyTimestampsAndWatermarks implements AssignerWithPeriodicWatermarks<MyEvent> {

        private long currentMaxTimestamp;

        @Nullable
        @Override
        public Watermark getCurrentWatermark() {

            long maxOutOfOrderness = 3500;
            return new Watermark(currentMaxTimestamp- maxOutOfOrderness);
        }

        @Override
        public long extractTimestamp(MyEvent element, long previousElementTimestamp) {
            long timeStamp = element.getTimeStamp();
            currentMaxTimestamp = Math.max(timeStamp,currentMaxTimestamp);
            return timeStamp;
        }
    }

    @Setter
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    class MyEvent{

        private long timeStamp;
        private String content;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class MyRule{

        private long timeStamp;
        private Integer id;
        private Integer quantity;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    class RiskEvent{

        private String eventCode;
        private String level;
        private String reason; // "1","2","3"
    }

    private List<RiskEvent> createRiskEventList(){
        List<RiskEvent> riskEvents = Lists.newArrayList();

        RiskEvent riskEvent01 = new RiskEvent("userRegister","PASS","");
        RiskEvent riskEvent02 = new RiskEvent("userRegister","REJECT","1");
        RiskEvent riskEvent021 = new RiskEvent("imMessage","REJECT","1");
        RiskEvent riskEvent03 = new RiskEvent("userRegister","REJECT","2");
        RiskEvent riskEvent031 = new RiskEvent("imMessage","REJECT","1");
        RiskEvent riskEvent04 = new RiskEvent("userRegister","REJECT","3");
        RiskEvent riskEvent041 = new RiskEvent("imMessage","REJECT","1");
        RiskEvent riskEvent05 = new RiskEvent("userRegister","PASS","");

        riskEvents.add(riskEvent01);
        riskEvents.add(riskEvent02);
        riskEvents.add(riskEvent021);
        riskEvents.add(riskEvent03);
        riskEvents.add(riskEvent031);
        riskEvents.add(riskEvent04);
        riskEvents.add(riskEvent041);
        riskEvents.add(riskEvent05);

        return riskEvents;
    }

    private List<RiskEvent> createRiskEventList01(){
        List<RiskEvent> riskEvents = Lists.newArrayList();

        RiskEvent riskEvent01 = new RiskEvent("userRegister","PASS","");
        RiskEvent riskEvent02 = new RiskEvent("userRegister","REJECT","1");
        RiskEvent riskEvent03 = new RiskEvent("userRegister","REJECT","2");
        RiskEvent riskEvent031 = new RiskEvent("imMessage","REJECT","1");
        RiskEvent riskEvent04 = new RiskEvent("userRegister","REJECT","3");
        RiskEvent riskEvent041 = new RiskEvent("imMessage","REJECT","1");
        RiskEvent riskEvent05 = new RiskEvent("userRegister","PASS","");

        riskEvents.add(riskEvent01);
        riskEvents.add(riskEvent02);
        riskEvents.add(riskEvent03);
        riskEvents.add(riskEvent031);
        riskEvents.add(riskEvent04);
        riskEvents.add(riskEvent041);
        riskEvents.add(riskEvent05);

        return riskEvents;
    }

    @Test
    public void testFencingMobile() throws Exception {

        Pattern<RiskEvent,RiskEvent> p1 = Pattern.<RiskEvent>begin("start",AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<RiskEvent>() {
            @Override
            public boolean filter(RiskEvent riskEvent) throws Exception {
                return "userRegister".equalsIgnoreCase(riskEvent.eventCode)&&"REJECT".equalsIgnoreCase(riskEvent.level)&&"1".equalsIgnoreCase(riskEvent.reason);
            }
        }).or(new SimpleCondition<RiskEvent>() {
            @Override
            public boolean filter(RiskEvent riskEvent) throws Exception {
                return "userRegister".equalsIgnoreCase(riskEvent.eventCode)&&"REJECT".equalsIgnoreCase(riskEvent.level)&&"2".equalsIgnoreCase(riskEvent.reason);
            }
        }).or(new SimpleCondition<RiskEvent>() {
            @Override
            public boolean filter(RiskEvent riskEvent) throws Exception {
                return "userRegister".equalsIgnoreCase(riskEvent.eventCode)&&"REJECT".equalsIgnoreCase(riskEvent.level)&&"3".equalsIgnoreCase(riskEvent.reason);
            }
        }).oneOrMore().followedBy("end").where(new SimpleCondition<RiskEvent>() {
            @Override
            public boolean filter(RiskEvent riskEvent) throws Exception {
                return "userRegister".equalsIgnoreCase(riskEvent.eventCode)&&"PASS".equalsIgnoreCase(riskEvent.level);
            }
        }).within(Time.seconds(10));


        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);

//        DataStream<RiskEvent> inDs = env.fromCollection(createRiskEventList());
        DataStream<RiskEvent> inDs = env.fromCollection(createRiskEventList01());
        PatternStream<RiskEvent> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<RiskEvent, RiskEvent>() {
            @Override
            public void processMatch(Map<String, List<RiskEvent>> map, Context context, Collector<RiskEvent> collector) throws Exception {
                for (Map.Entry<String, List<RiskEvent>> entry : map.entrySet()) {
                    System.err.println("PatternProcessFunction pattern: " + entry.getKey() + ", value: " + JSONObject.toJSONString(entry.getValue()));
                }
            }
        });

        env.execute("testFencingMobile");


    }


    private List<RiskEvent> createRiskEventList02(){
        List<RiskEvent> riskEvents = Lists.newArrayList();

        RiskEvent riskEvent01 = new RiskEvent("im-message0","PASS","");
        RiskEvent riskEvent02 = new RiskEvent("chat-room-enter0","REJECT","1");
        RiskEvent riskEvent03 = new RiskEvent("userRegister","REJECT","2");

        RiskEvent riskEvent032 = new RiskEvent("im-message1","PASS","");
        RiskEvent riskEvent031 = new RiskEvent("imMessage","REJECT","1");
        RiskEvent riskEvent033 = new RiskEvent("chat-room-enter1","REJECT","1");
        RiskEvent riskEvent04 = new RiskEvent("userRegister","REJECT","3");

        RiskEvent riskEvent042 = new RiskEvent("im-message2","PASS","");
        RiskEvent riskEvent041 = new RiskEvent("imMessage","REJECT","1");
        RiskEvent riskEvent044 = new RiskEvent("chat-room-enter2","REJECT","1");
        RiskEvent riskEvent043 = new RiskEvent("chat-room-enter3","REJECT","1");
        RiskEvent riskEvent05 = new RiskEvent("userRegister","PASS","");

        riskEvents.add(riskEvent01);
        riskEvents.add(riskEvent02);
        riskEvents.add(riskEvent03);

        riskEvents.add(riskEvent032);
        riskEvents.add(riskEvent031);
        riskEvents.add(riskEvent033);
        riskEvents.add(riskEvent04);

        riskEvents.add(riskEvent042);
        riskEvents.add(riskEvent041);
        riskEvents.add(riskEvent044);
        riskEvents.add(riskEvent043);
        riskEvents.add(riskEvent05);

        return riskEvents;
    }


    @Test
    public void test011() throws Exception {

        Pattern<RiskEvent,RiskEvent> p1 = Pattern.<RiskEvent>begin("start",AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<RiskEvent>() {
            @Override
            public boolean filter(RiskEvent riskEvent) throws Exception {
                return riskEvent.eventCode.startsWith("im-message");
            }
        }).followedBy("middle").where(new SimpleCondition<RiskEvent>() {
            @Override
            public boolean filter(RiskEvent riskEvent) throws Exception {
                return riskEvent.eventCode.startsWith("chat-room-enter");
            }
        }).within(Time.seconds(100));


        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);

//        DataStream<RiskEvent> inDs = env.fromCollection(createRiskEventList());
        DataStream<RiskEvent> inDs = env.fromCollection(createRiskEventList02());
        PatternStream<RiskEvent> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<RiskEvent, RiskEvent>() {
            @Override
            public void processMatch(Map<String, List<RiskEvent>> map, Context context, Collector<RiskEvent> collector) throws Exception {
                for (Map.Entry<String, List<RiskEvent>> entry : map.entrySet()) {
                    System.err.println("PatternProcessFunction pattern: " + entry.getKey() + ", value: " + JSONObject.toJSONString(entry.getValue()));
                }
            }
        });

        env.execute("test011");


    }
}
