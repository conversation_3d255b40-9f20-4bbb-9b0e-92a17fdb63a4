package com.yupaopao.risk.insight.flink.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.connector.mysql.model.Factor;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import com.yupaopao.risk.insight.flink.windows.process.FactorBroadcastProcess;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.util.Collector;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/****
 * zengxiangcai
 * 2023/2/3 17:09
 ***/

@RunWith(PowerMockRunner.class)
@PrepareForTest({LoggerFactory.class})
public class FactorBroadcastProcessTest {


    @InjectMocks
    FactorBroadcastProcess process;



    private Map<Long, Boolean> checkConditionExpectedResultMap = new ConcurrentHashMap<>();
    private Map<Long, Boolean> checkConditionExpectedResultMap2 = new ConcurrentHashMap<>();

    @BeforeClass
    public static void initLog(){
        Logger mk = PowerMockito.mock(Logger.class);
        PowerMockito.mockStatic(LoggerFactory.class);
        PowerMockito.when(LoggerFactory.getLogger(Mockito.anyString())).
                thenReturn(mk);
        PowerMockito.when(LoggerFactory.getLogger(Mockito.any(Class.class))).
                thenReturn(mk);
    }

    /***
     * 累计因子前提条件检测
     * 目前是一个数据过来了N个累计因子循环遍历，防止改方法改变后多线程问题，或者多累计因子前提条件没有生效
     *
     * 1、1个线程内多个表达式，结果符合要求
     * 2、多个线程内1个表达式，结果符合要求
     *
     *
     */
    @Test
    public void testCheckConditionSingleThread() {

        for (Tuple2<Factor, JSONObject> f : buildTestParams()) {
            boolean result = process.checkCondition(f.f1, f.f0);
            Assert.assertTrue(result == checkConditionExpectedResultMap.get(f.f0.getId()));
        }

    }

    /***
     * 多线程主要是防止多个线程渠道取到的对象是混乱的导致结果不如预期
     */
    @Test
    public void testCheckConditionMultiThread() throws Exception {
        List<Boolean> t1Result = new ArrayList<>();
        List<Boolean> t2Result = new ArrayList<>();
        CountDownLatch c = new CountDownLatch(2);
        Thread t1 = new Thread() {
            @Override
            public void run() {
                for (int i = 0; i < 10000; i++) {
                    for (Tuple2<Factor, JSONObject> f : buildTestParams()) {
                        boolean result = process.checkCondition(f.f1, f.f0);
                        t1Result.add(result == checkConditionExpectedResultMap.get(f.f0.getId()));
                    }
                }
                c.countDown();
            }
        };

        Thread t2 = new Thread() {
            @Override
            public void run() {
                for (int i = 0; i < 100000; i++) {
                    for (Tuple2<Factor, JSONObject> f : buildTestParams2()) {
                        boolean result = process.checkCondition(f.f1, f.f0);
                        t2Result.add(result == checkConditionExpectedResultMap2.get(f.f0.getId()));
                    }
                }
                c.countDown();
            }
        };

        t1.start();
        t2.start();

        c.await(3, TimeUnit.MINUTES);


        for (boolean b : t1Result) {
            Assert.assertTrue(b);
        }
        for (boolean b : t2Result) {
            Assert.assertTrue(b);
        }

    }


    /***
     * 从入参解析出事件事件
     */
    @Test
    public void testGetEventTime() {
        Long curr = System.currentTimeMillis();
        JSONObject data = new JSONObject();
        data.put("riskRequestTime", 1L);
        data.put("kafkaMsgTime", 2L);

        Long result1 = process.getEventTime(data);

        Assert.assertTrue(1 == result1);

        data.put("riskRequestTime", null);

        Long result2 = process.getEventTime(data);

        Assert.assertTrue(2 == result2);

        data.put("kafkaMsgTime", null);

        Long result3 = process.getEventTime(data);
        Assert.assertTrue(result3 > curr);

    }

    /***
     * 构建正常累计数据
     */
    @Test
    public void testAssembleNormalElement() throws Exception {
        String jsonParam1 = "{\n" +
                "\"level\":\"PASS\",\n" +
                "\"toUid\":\"19000000000000001\",\n" +
                "\"UserId\":\"19000000000000000\",\n" +
                "\"DeviceId\":\"device111\",\n" +
                "\"Event\":\"chat-room-reward\"\n" +
                "}";
        String jsonParam2 = "{\n" +
                "\"level\":\"PASS\",\n" +
                "\"toUid\":\"19000000000000001\",\n" +
                "\"UserId\":\"192960999575200100\",\n" +
                "\"DeviceId\":\"device111\",\n" +
                "\"Event\":\"im-message\"\n" +
                "}";
        String jsonParam3 = "{\n" +
                "\"level\":\"PASS\",\n" +
                "\"toUid\":\"19000000000000001\",\n" +
                "\"UserId\":\"19000000000000000\",\n" +
                "\"DeviceId\":\"device111\",\n" +
                "\"Event\":\"user-follow\"\n" +
                "}";
        JSONObject obj1 = new JSONObject();
        obj1.put("data", JSON.parseObject(jsonParam1));
        JSONObject obj2 = new JSONObject();
        obj2.put("data", JSON.parseObject(jsonParam2));
        JSONObject obj3 = new JSONObject();
        obj3.put("data", JSON.parseObject(jsonParam3));

        List<JSONObject> inputs = Arrays.asList(obj1, obj2, obj3);
        inputs.stream().forEach(elem -> elem.put("level", elem.getJSONObject("data").getString("level")));
        List<Factor> configFactors = getCfgFactors();

        JedisPool jedisPool = PowerMockito.mock(JedisPool.class);
        Jedis jedis = PowerMockito.mock(Jedis.class);
        PowerMockito.when(jedisPool.getResource()).thenReturn(jedis);
        PowerMockito.when(jedis.get(Mockito.anyString())).thenReturn("");

        TestCollector testCollector = new TestCollector();
        for (JSONObject data : inputs) {
            process.assembleNormalFactorCal(data, configFactors, testCollector);
        }

        for (FactorCalDetail factorCalDetail : testCollector.getResult()) {
            if (factorCalDetail.getGroupKey().contains("FACTOR-DATA#3342")) {
                //无resetTime，有windowType
                Assert.assertTrue("expect function COUNT", factorCalDetail.getFunction().equals("COUNT"));
                Assert.assertTrue("expect timespan 1440", factorCalDetail.getTimeSpan().equals(1440L));
                Assert.assertTrue("expect group key FACTOR-DATA#3342#19000000000000000", factorCalDetail.getGroupKey().equals("FACTOR-DATA#3342#19000000000000000"));
                Assert.assertFalse("expect not purge", factorCalDetail.isPurge());
            } else if (factorCalDetail.getGroupKey().contains("FACTOR-DATA#3344")) {
                //无resetTime，有windowType
                Assert.assertTrue("expect function COUNT", factorCalDetail.getFunction().equals("COUNT"));
                Assert.assertTrue("expect timespan 60", factorCalDetail.getTimeSpan().equals(60L));
                Assert.assertTrue("expect group key FACTOR-DATA#3344#192960999575200100",
                        factorCalDetail.getGroupKey().equals("FACTOR-DATA#3344#192960999575200100"));
                Assert.assertFalse("expect not purge", factorCalDetail.isPurge());
            } else if (factorCalDetail.getGroupKey().contains("FACTOR-DATA#105")) {
                //有reset有windowType
                Assert.assertTrue("expect function COUNT_DISTINCT", factorCalDetail.getFunction().equals("COUNT_DISTINCT"));
                Assert.assertTrue("expect timespan 60", factorCalDetail.getTimeSpan().equals(60L));
                Assert.assertTrue("expect group key 1675760100206FACTOR-DATA#105#19000000000000000", factorCalDetail.getGroupKey().equals("1675760100206FACTOR-DATA#105#19000000000000000"));
                Assert.assertFalse("expect not purge", factorCalDetail.isPurge());
            } else {
                Assert.assertTrue("error group key data", false);
            }
        }


    }


    /***
     * 构建因子重置事件数据
     */
    @Test
    public void testAssembleResetElement() {
        //with windowType && resetTime
        //with windowType no resetTime
        //no windowType && with resetTime
        //no windowType && resetTime

        String jsonParam = "{\n" +
                "    \"factor\":[\n" +
                "        {\n" +
                "            \"factorId\":105\n" +
                "        },\n" +
                "        {\n" +
                "            \"factorId\":3342\n" +
                "        },\n" +
                "        {\n" +
                "            \"factorId\":3344\n" +
                "        }\n" +
                "    ],\n" +
                "    \"params\":{\n" +
                "        \"UserId\":\"190000000000000\",\n" +
                "        \"DeviceId\":\"dev11111\"\n" +
                "    }\n" +
                "}";

        List<Factor> configFactors = getCfgFactors();

        JSONObject data = JSON.parseObject(jsonParam);

        TestCollector testCollector = new TestCollector();
        process.assembleResetFactorCal(data, configFactors, testCollector);
        for (FactorCalDetail factorCalDetail : testCollector.getResult()) {
            if (factorCalDetail.getGroupKey().contains("FACTOR-DATA#3342")) {
                //无resetTime，有windowType
                Assert.assertTrue("expect function COUNT", factorCalDetail.getFunction().equals("COUNT"));
                Assert.assertTrue("expect timespan 1440", factorCalDetail.getTimeSpan().equals(1440L));
                Assert.assertTrue("expect group key FACTOR-DATA#3342#190000000000000", factorCalDetail.getGroupKey().equals("FACTOR-DATA#3342#190000000000000"));
                Assert.assertTrue("expect purge", factorCalDetail.isPurge());
            } else if (factorCalDetail.getGroupKey().contains("FACTOR-DATA#3344")) {
                //无resetTime，有windowType
                Assert.assertTrue("expect function COUNT", factorCalDetail.getFunction().equals("COUNT"));
                Assert.assertTrue("expect timespan 60", factorCalDetail.getTimeSpan().equals(60L));
                Assert.assertTrue("expect group key", factorCalDetail.getGroupKey().equals("FACTOR-DATA#3344" +
                        "#190000000000000"));
                Assert.assertTrue("expect purge", factorCalDetail.isPurge());
            } else if (factorCalDetail.getGroupKey().contains("FACTOR-DATA#105")) {
                //有reset有windowType
                Assert.assertTrue("expect function COUNT_DISTINCT", factorCalDetail.getFunction().equals("COUNT_DISTINCT"));
                Assert.assertTrue("expect timespan 60", factorCalDetail.getTimeSpan().equals(60L));
                Assert.assertTrue("expect group key 1675760100206FACTOR-DATA#105#190000000000000", factorCalDetail.getGroupKey().equals("1675760100206FACTOR-DATA" +
                        "#105#190000000000000"));
                Assert.assertTrue("expect purge", factorCalDetail.isPurge());
            } else {
                Assert.assertTrue("error group key data", false);
            }
        }

    }

    public static class TestCollector implements Collector<FactorCalDetail> {
        List<FactorCalDetail> result = new ArrayList<>();

        @Override
        public void collect(FactorCalDetail record) {
            result.add(record);
        }

        @Override
        public void close() {

        }

        public List<FactorCalDetail> getResult() {
            return result;
        }
    }


    private List<Factor> getCfgFactors() {
        List<Factor> configFactors = new ArrayList<>();
        Factor factor1 = new Factor();
        factor1.setId(3342);
        factor1.setGroupKey("UserId" + "##" + 0);
        factor1.setAggKey("");
        factor1.setFunction("COUNT" + "##" + 1);
        factor1.setTimeSpan(1440);
        factor1.setCondition("#Event=='chat-room-reward'");
        configFactors.add(factor1);

        Factor factor2 = new Factor();
        factor2.setId(3344);
        factor2.setGroupKey("UserId" + "##" + 0);
        factor2.setAggKey("");
        factor2.setFunction("COUNT" + "##" + 1);
        factor2.setTimeSpan(60);
        factor2.setCondition("#Event=='im-message' && #UserId=='192960999575200100'");
        configFactors.add(factor2);

        Factor factor3 = new Factor();
        factor3.setId(105);
        factor3.setGroupKey("UserId" + "##" + 1675760100206L); // groupKey + resetTime
        factor3.setAggKey("toUid");
        factor3.setFunction("COUNT_DISTINCT" + "##" + 1); // function+windowType
        factor3.setTimeSpan(60);
        factor3.setCondition("#Event=='user-follow' &&  (#RiskLevel == 'PASS' || #RiskLevel == 'REVIEW')");
        configFactors.add(factor3);
        return configFactors;
    }

    private List<Tuple2<Factor, JSONObject>> buildTestParams() {
        List<Tuple2<Factor, JSONObject>> factorParams = new ArrayList<>();

        JSONObject params1 = new JSONObject();
        params1.put("eventCode", "user-login");
        Factor factor1 = new Factor();
        factor1.setCondition("#eventCode == 'user-login'");
        factor1.setId(1);
        checkConditionExpectedResultMap.put(1L, true);


        JSONObject params2 = new JSONObject();
        params2.put("eventCode", "user-login");
        Factor factor2 = new Factor();
        factor2.setCondition("#eventCode == 'user-login-post'");
        factor2.setId(2);
        checkConditionExpectedResultMap.put(2L, false);

        JSONObject params3 = new JSONObject();
        params3.put("eventCode", "user-register");
        Factor factor3 = new Factor();
        factor3.setCondition("#eventCode == 'user-register'");
        factor3.setId(3);
        checkConditionExpectedResultMap.put(3L, true);


        JSONObject params4 = new JSONObject();
        params4.put("eventCode", "user-register-post");
        Factor factor4 = new Factor();
        factor4.setCondition("#eventCode == 'user-register'");
        factor4.setId(4);
        checkConditionExpectedResultMap.put(4L, false);

        factorParams.add(Tuple2.of(factor1, params1));

        factorParams.add(Tuple2.of(factor2, params2));
        factorParams.add(Tuple2.of(factor3, params3));
        factorParams.add(Tuple2.of(factor4, params4));

        return factorParams;
    }


    private List<Tuple2<Factor, JSONObject>> buildTestParams2() {
        List<Tuple2<Factor, JSONObject>> factorParams = new ArrayList<>();

        JSONObject params1 = new JSONObject();
        params1.put("eventCode", "user-login-post");
        Factor factor1 = new Factor();
        factor1.setCondition("#eventCode == 'user-login'");
        factor1.setId(1);
        checkConditionExpectedResultMap2.put(1L, false);  ///////////


        JSONObject params2 = new JSONObject();
        params2.put("eventCode", "user-login-post");
        Factor factor2 = new Factor();
        factor2.setCondition("#eventCode == 'user-login-post'");
        factor2.setId(2);
        checkConditionExpectedResultMap2.put(2L, true);

        JSONObject params3 = new JSONObject();
        params3.put("eventCode", "user-register-post");
        Factor factor3 = new Factor();
        factor3.setCondition("#eventCode == 'user-register'");
        factor3.setId(3);
        checkConditionExpectedResultMap2.put(3L, false);


        JSONObject params4 = new JSONObject();
        params4.put("eventCode", "user-register");
        Factor factor4 = new Factor();
        factor4.setCondition("#eventCode == 'user-register'");
        factor4.setId(4);
        checkConditionExpectedResultMap2.put(4L, true);

        factorParams.add(Tuple2.of(factor1, params1));

        factorParams.add(Tuple2.of(factor2, params2));
        factorParams.add(Tuple2.of(factor3, params3));
        factorParams.add(Tuple2.of(factor4, params4));

        return factorParams;
    }
}
