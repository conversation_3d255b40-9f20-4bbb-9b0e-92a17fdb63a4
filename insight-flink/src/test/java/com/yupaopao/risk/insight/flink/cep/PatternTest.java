package com.yupaopao.risk.insight.flink.cep;

import com.google.common.collect.Maps;
import com.yupaopao.risk.insight.common.cep.beans.RiskEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.flink.cep.CEP;
import org.apache.flink.cep.PatternStream;
import org.apache.flink.cep.functions.PatternProcessFunction;
import org.apache.flink.cep.nfa.NFA;
import org.apache.flink.cep.nfa.aftermatch.AfterMatchSkipStrategy;
import org.apache.flink.cep.nfa.compiler.NFACompiler;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.util.Collector;
import org.junit.Test;

import java.util.List;
import java.util.Map;

public class PatternTest {

    private RiskEvent[] createRiskEventSeq(){
        RiskEvent[] result = new RiskEvent[36];

        for(int i = 0;i< 36;i++){
            RiskEvent riskEvent01 = new RiskEvent();

            Map<String, Object> data01 = Maps.newHashMap();
            data01.put("eventCode","live-room-enter");
            data01.put("data_userData_auth",0);
            data01.put("data_userData_vipLevel",1);
            data01.put("data_userData_nobilityLevel",1);
            data01.put("reason","黑名单");

            riskEvent01.setData(data01);

            result[i]=riskEvent01;

            RiskEvent riskEvent02 = new RiskEvent();
            Map<String, Object> data02 = Maps.newHashMap();
            data02.put("eventCode","im-message");
            riskEvent02.setData(data02);

            i++;
            result[i]=riskEvent02;

        }

        return result;

    }

    @Test
    public void test01() throws Exception {

//        Pattern.<RiskEvent>begin("live-room-enter0", AfterMatchSkipStrategy.skipPastLastEvent())
//                .where(eventCode=='live-room-enter' && data_userData_auth!=null && data_userData_auth==0
//                        && data_userData_vipLevel!=null && data_userData_vipLevel<2 && data_userData_nobilityLevel!= null
//                        && data_userData_nobilityLevel<2 && reason!=null && !reason.contains('白名单'))
//                .followedBy("im-message0")
//                .where(eventCode =='im-message')
//                .followedBy(
//                        Pattern.<RiskEvent>begin("live-room-enter1", AfterMatchSkipStrategy.skipPastLastEvent())
//                                .where(eventCode=='live-room-enter' && data_userData_auth!=null && data_userData_auth==0
//                                        && data_userData_vipLevel!=null && data_userData_vipLevel<2 && data_userData_nobilityLevel!= null
//                                        && data_userData_nobilityLevel<2 && reason!=null && !reason.contains('白名单'))
//                                .followedBy("im-message1")
//                                .where(eventCode =='im-message')
//                )
//                .within(Time.minutes(10)).times(6)

        Pattern<RiskEvent,RiskEvent> group = Pattern.<RiskEvent>begin("live-room-enter1",AfterMatchSkipStrategy.skipPastLastEvent())
                .where(new SimpleCondition<RiskEvent>() {

                    @Override
                    public boolean filter(RiskEvent riskEvent) throws Exception {
                        String eventCode = riskEvent.getStringValue("eventCode");
                        Integer auth = MapUtils.getInteger(riskEvent.getData(),"data_userData_auth");
                        Integer vipLevel = MapUtils.getInteger(riskEvent.getData(),"data_userData_vipLevel");
                        Integer nobilityLevel = MapUtils.getInteger(riskEvent.getData(),"data_userData_nobilityLevel");
                        String reason = riskEvent.getStringValue("reason");

                        return "live-room-enter".equals(eventCode) && auth != null && auth == 0 && vipLevel != null && vipLevel < 2 && nobilityLevel != null
                                && nobilityLevel < 2 && !"白名单".equals(reason);
                    }
                }).followedBy("im-message1")
                .where(new SimpleCondition<RiskEvent>() {
                    @Override
                    public boolean filter(RiskEvent riskEvent) throws Exception {
                        String eventCode = riskEvent.getStringValue("eventCode");
                        return "im-mesage".equals(eventCode);
                    }
                });


        Pattern<RiskEvent, ?> p1 =  Pattern.<RiskEvent>begin("live-room-enter0", AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<RiskEvent>(){

                    @Override
                    public boolean filter(RiskEvent riskEvent) throws Exception {
                        String eventCode = riskEvent.getStringValue("eventCode");
                        Integer auth = MapUtils.getInteger(riskEvent.getData(),"data_userData_auth");
                        Integer vipLevel = MapUtils.getInteger(riskEvent.getData(),"data_userData_vipLevel");
                        Integer nobilityLevel = MapUtils.getInteger(riskEvent.getData(),"data_userData_nobilityLevel");
                        String reason = riskEvent.getStringValue("reason");

                        return "live-room-enter".equals(eventCode) && auth != null && auth == 0 && vipLevel != null && vipLevel < 2 && nobilityLevel != null
                                && nobilityLevel < 2 && !"白名单".equals(reason);
                    }
                }).followedBy("im-message0")
                .where(new SimpleCondition<RiskEvent>() {
                    @Override
                    public boolean filter(RiskEvent riskEvent) throws Exception {
                        String eventCode = riskEvent.getStringValue("eventCode");

                        return "im-message".equals(eventCode);
                    }
                })
                .followedBy(group).within(Time.minutes(10)).times(6);

        final NFACompiler.NFAFactory<RiskEvent> newNfaFactory = NFACompiler.compileFactory(p1, true);
        NFA<RiskEvent> nfa = newNfaFactory.createNFA();

        System.err.println(nfa.getStates());

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        DataStream<RiskEvent> inDs = env.fromElements(this.createRiskEventSeq());

        PatternStream<RiskEvent> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<RiskEvent,RiskEvent>(){

            @Override
            public void processMatch(Map<String, List<RiskEvent>> match, Context ctx, Collector<RiskEvent> out) throws Exception {
                for (Map.Entry<String, List<RiskEvent>> entry : match.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                }
            }
        });

        env.execute("testPattern");

    }

    // 1 策略名- "IM高频骚扰-低等级用户"
    @Test
    public void test02() throws Exception {

        Pattern<String,String> group = Pattern.<String>begin("liveRoomEnter1",AfterMatchSkipStrategy.skipPastLastEvent())
                .where(new SimpleCondition<String>() {
                    @Override
                    public boolean filter(String value) throws Exception {

                        return value.startsWith("liveRoomEnter");
                    }
                }).followedBy("imMessage1")
                .where(new SimpleCondition<String>() {
                    @Override
                    public boolean filter(String value) throws Exception {
                        return value.startsWith("imMessage");
                    }
                });


        Pattern<String, ?> p1 =  Pattern.<String>begin("liveRoomEnter0", AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<String>(){
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("liveRoomEnter");
            }
        }).followedBy("imMessage0").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {

                return value.startsWith("imMessage");
            }
        })
        .followedBy(group).within(Time.minutes(10)).times(3);

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
//        DataStream<String> inDs = env.fromElements("liveRoomEnter0", "imMessage0","register0","register3","liveRoomEnter1", "imMessage1","im1","liveRoomEnter2", "imMessage2","im2","liveRoomEnter3", "imMessage3","im3");
        DataStream<String> inDs = env.fromElements("liveRoomEnter0","register3", "imMessage0","register0","register0","liveRoomEnter1", "register3","imMessage1","im1","liveRoomEnter2","register3", "imMessage2","im2","liveRoomEnter3", "register3","imMessage3","im3","liveRoomEnter4", "register4","imMessage4");
//        DataStream<String> inDs = env.fromElements("liveRoomEnter0","register3", "imMessage0","register0","register0","liveRoomEnter1", "register3","imMessage1","im1","liveRoomEnter2","register3", "imMessage2","im2","liveRoomEnter3", "register3","imMessage3","im3","liveRoomEnter4", "register4","imMessage4"
//                ,"im4","liveRoomEnter5", "register5","imMessage5","im5","liveRoomEnter6", "register6","imMessage7","im6","liveRoomEnter7", "register7","imMessage8");
        PatternStream<String> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });
        env.execute("test02");

    }

    //2  策略名 - "自定义表情骚扰-小白号连续8次"
    @Test
    public void test03() throws Exception {

//        Pattern<String, ?> p1 =  Pattern.<String>begin("emoji0", AfterMatchSkipStrategy.skipPastLastEvent())
        Pattern<String,String> p1 = Pattern.<String>begin("emoji0",AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<String>(){
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("userEmoji");
            }
        }).next("emoji1").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("userEmoji");
            }
        }).next("emoji2").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("userEmoji");
            }
        }).next("emoji3").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("userEmoji");
            }
        }).within(Time.seconds(2));

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
//        DataStream<String> inDs = env.fromElements("userEmoji0", "imMessage0","userEmoji1","register0","userEmoji2","register2","userEmoji3","liveRoomEnter1", "imMessage1","im1");
        DataStream<String> inDs = env.fromElements("userEmoji0", "userEmoji1","userEmoji2","userEmoji3","userEmoji4","userEmoji5","userEmoji6","userEmoji7","liveRoomEnter1","im1");
        PatternStream<String> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });
        env.execute("test02");

    }

    // 3 策略名 - "异常设备emoji骚扰-v2"
    @Test
    public void test04() throws Exception {

        Pattern<String, ?> p1 =  Pattern.<String>begin("emoji0", AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<String>(){
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("userEmoji");
            }
        }).next("emoji1").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("userEmoji");
            }
        }).next("emoji2").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("userEmoji");
            }
        }).next("emoji3").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("userEmoji");
            }
        }).within(Time.seconds(2));

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        DataStream<String> inDs = env.fromElements("liveRoomEnter0", "imMessage0","register0","register2","liveRoomEnter1", "imMessage1","","im1");
        PatternStream<String> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });
        env.execute("test02");

    }

    // 4 策略名 - "刷单用户打击"
    @Test
    public void test05() throws Exception {

        Pattern<String, ?> p1 =  Pattern.<String>begin("search", AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<String>(){
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("search");
            }
        }).followedBy("assignOrderCreate").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("assignOrderCreate");
            }
        }).followedBy("playOrderFinish").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("playOrderFinish");
            }
        }).within(Time.seconds(2));

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
//        DataStream<String> inDs = env.fromElements("search", "imMessage0","assignOrderCreate","register2","playOrderFinish", "imMessage1","","im1");
        DataStream<String> inDs = env.fromElements("search", "assignOrderCreate","playOrderFinish","register2","playOrderFinish", "imMessage1","","im1");
        PatternStream<String> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });
        env.execute("test02");

    }




}
