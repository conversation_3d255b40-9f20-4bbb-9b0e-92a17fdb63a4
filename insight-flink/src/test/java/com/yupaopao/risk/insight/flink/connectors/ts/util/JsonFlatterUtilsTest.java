package com.yupaopao.risk.insight.flink.connectors.ts.util;

import com.alibaba.fastjson.JSONObject;
import com.github.wnameless.json.flattener.FlattenMode;
import com.github.wnameless.json.flattener.JsonFlattener;
import com.github.wnameless.json.unflattener.JsonUnflattener;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.junit.Test;

import java.util.Date;
import java.util.Map;

public class JsonFlatterUtilsTest {

    @Test
    public void testJson(){



        JSONObject obj = new JSONObject();
        obj.put("msgTimestamp",new Date().getTime());
        obj.put("createdAt", DateFormatUtils.format(new Date(),"yyyy-MM-dd HH:mm:ss"));
        System.err.println(obj.toJSONString());
        Map<String, Object> flattenMap = JsonFlatterUtils.toMap(obj.toJSONString(), FlattenMode.KEEP_ARRAYS);
        System.err.println(flattenMap);
    }

    @Test
    public void testToMap(){
        String json = "{\n" +
                "\t\"costTime\": 125,\n" +
                "\t\"riskAction\": {\n" +
                "\t\t\"async\": false,\n" +
                "\t\t\"businessCode\": \"common\",\n" +
                "\t\t\"clientIp\": \"**************\",\n" +
                "\t\t\"data\": {\n" +
                "\t\t\t\"msgType\": \"TEXT\",\n" +
                "\t\t\t\"DeviceId\": \"20191030012440b43b509985ff7cdfce87a6f219c2422901d47342a35e64bd\",\n" +
                "\t\t\t\"imNormalTextCheck\": {\n" +
                "\t\t\t\t\"finger\": \"*****************\",\n" +
                "\t\t\t\t\"tagList\": [],\n" +
                "\t\t\t\t\"riskLevel\": \"PASS\",\n" +
                "\t\t\t\t\"wordList\": [],\n" +
                "\t\t\t\t\"sceneList\": []\n" +
                "\t\t\t},\n" +
                "\t\t\t\"fromAccount\": \"46e778d36cb13c3ff7dc2205caea3b9c\",\n" +
                "\t\t\t\"fromClientType\": \"IOS\",\n" +
                "\t\t\t\"fromDeviceId\": \"0A156C1C-C36A-448A-8D06-9AE38F4A5033\",\n" +
                "\t\t\t\"imTextCheck\": {\n" +
                "\t\t\t\t\"finger\": \"*****************\",\n" +
                "\t\t\t\t\"tagList\": [\"淫秽色情\"],\n" +
                "\t\t\t\t\"reason\": \"接线下,线下\",\n" +
                "\t\t\t\t\"riskLevel\": \"REJECT\",\n" +
                "\t\t\t\t\"wordList\": [\"接线下\", \"线下\"],\n" +
                "\t\t\t\t\"sceneList\": [\"public\"]\n" +
                "\t\t\t},\n" +
                "\t\t\t\"body\": \"啊嘞，我不接线下哇，哥哥[可怜]\",\n" +
                "\t\t\t\"ClientIp\": \"**************\",\n" +
                "\t\t\t\"userIdComplainCount24Hours\": 0.0,\n" +
                "\t\t\t\"userBlackEventCount30Days\": 1.0,\n" +
                "\t\t\t\"fromClientPort\": \"49501\",\n" +
                "\t\t\t\"imUserIdBlackDetect\": false,\n" +
                "\t\t\t\"userEventCount30Days\": 0.0,\n" +
                "\t\t\t\"userMessageReceiverCount60m\": 5.0,\n" +
                "\t\t\t\"Business\": \"common\",\n" +
                "\t\t\t\"fromNick\": \"暮春拾柒\",\n" +
                "\t\t\t\"app\": \"bixin\",\n" +
                "\t\t\t\"userMessageSenderCount60m\": 3.0,\n" +
                "\t\t\t\"Timeout\": \"2950\",\n" +
                "\t\t\t\"userAdvertMessageReceiverCount24h\": 0.0,\n" +
                "\t\t\t\"userMessageSendCount\": 18.0,\n" +
                "\t\t\t\"cashBlackDetect\": false,\n" +
                "\t\t\t\"targetUserId\": \"cd1c3952ed044a7282c54e828d6ce8bd\",\n" +
                "\t\t\t\"eventType\": \"1\",\n" +
                "\t\t\t\"userAttractMessageReceiverCount24h\": 0.0,\n" +
                "\t\t\t\"msgidClient\": \"7db2a85f-4e1b-4c9b-b040-60f9cc490f58\",\n" +
                "\t\t\t\"clientIpDetail\": {\n" +
                "\t\t\t\t\"country\": \"中国\",\n" +
                "\t\t\t\t\"riskLevel\": \"PASS\",\n" +
                "\t\t\t\t\"province\": \"江苏省\",\n" +
                "\t\t\t\t\"city\": \"南京市\",\n" +
                "\t\t\t\t\"isp\": \"电信\",\n" +
                "\t\t\t\t\"cityId\": 1015,\n" +
                "\t\t\t\t\"region\": \"华东\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"msgTimestamp\": \"1573699295037\",\n" +
                "\t\t\t\"UserId\": \"b589170632f14d459698146e4680ae4a\",\n" +
                "\t\t\t\"userAudioMessageReceiverCount60m\": 0.0,\n" +
                "\t\t\t\"Event\": \"im-message\",\n" +
                "\t\t\t\"TraceId\": \"e10f7a653eac4d4b8ceba2806a7a9c43\",\n" +
                "\t\t\t\"to\": \"5e831f994daf80d27be8a4193944fa03\"\n" +
                "\t\t},\n" +
                "\t\t\"deviceId\": \"20191030012440b43b509985ff7cdfce87a6f219c2422901d47342a35e64bd\",\n" +
                "\t\t\"eventCode\": \"im-message\",\n" +
                "\t\t\"timeout\": 2950,\n" +
                "\t\t\"traceId\": \"e10f7a653eac4d4b8ceba2806a7a9c43\",\n" +
                "\t\t\"userId\": \"b589170632f14d459698146e4680ae4a\"\n" +
                "\t},\n" +
                "\t\"riskResult\": {\n" +
                "\t\t\"businessCode\": \"common\",\n" +
                "\t\t\"eventCode\": \"im-message\",\n" +
                "\t\t\"level\": \"REJECT\",\n" +
                "\t\t\"reason\": \"IM文本违规\",\n" +
                "\t\t\"reply\": \"\",\n" +
                "\t\t\"rule\": 334,\n" +
                "\t\t\"success\": true,\n" +
                "\t\t\"traceId\": \"e10f7a653eac4d4b8ceba2806a7a9c43\"\n" +
                "\t}\n" +
                "}";

        String flatterJson =  new JsonFlattener(json).withFlattenMode(FlattenMode.KEEP_ARRAYS).withSeparator('_').flatten();

        System.err.println("flatterJson: "+flatterJson);

        String unflatterJson = new JsonUnflattener(flatterJson).withFlattenMode(FlattenMode.KEEP_ARRAYS).withSeparator('_').unflatten();

        System.err.println(unflatterJson);

    }

    @Test
    public void testNeteaseComplexJson(){
        String json = "\n" +
                "{\n" +
                "\t\"costTime\": 529,\n" +
                "\t\"riskAction\": {\n" +
                "\t\t\"async\": false,\n" +
                "\t\t\"businessCode\": \"common\",\n" +
                "\t\t\"clientIp\": \"***************\",\n" +
                "\t\t\"data\": {\n" +
                "\t\t\t\"msgType\": \"PICTURE\",\n" +
                "\t\t\t\"userData\": {\n" +
                "\t\t\t\t\"createdAt\": 1572856909000,\n" +
                "\t\t\t\t\"uid\": 193081001274218506,\n" +
                "\t\t\t\t\"vipLevel\": 0,\n" +
                "\t\t\t\t\"gender\": 1,\n" +
                "\t\t\t\t\"auth\": true,\n" +
                "\t\t\t\t\"qqUnionId\": \"643666E03A330AD68BB9CBD8B2110FCF\",\n" +
                "\t\t\t\t\"godLevel\": 3,\n" +
                "\t\t\t\t\"userId\": \"d23a7cf956544bfdbc3ab63137daef2e\",\n" +
                "\t\t\t\t\"god\": true,\n" +
                "\t\t\t\t\"value\": 0,\n" +
                "\t\t\t\t\"nobilityLevel\": 0\n" +
                "\t\t\t},\n" +
                "\t\t\t\"DeviceId\": \"201911041640394289a0747278c1d23121435c42a0f750012f286b3ccde2e8\",\n" +
                "\t\t\t\"fromAccount\": \"f52883f5a70fd680847936ff9a6b97fa\",\n" +
                "\t\t\t\"fromClientType\": \"AOS\",\n" +
                "\t\t\t\"fromDeviceId\": \"1ccf6e23-d48c-491b-ba24-42f1e11394a4\",\n" +
                "\t\t\t\"ClientIp\": \"***************\",\n" +
                "\t\t\t\"userBlackEventCount30Days\": 3.0,\n" +
                "\t\t\t\"userIdComplainCount24Hours\": 0.0,\n" +
                "\t\t\t\"fromClientPort\": \"14139\",\n" +
                "\t\t\t\"imUserIdBlackDetect\": false,\n" +
                "\t\t\t\"userEventCount30Days\": 0.0,\n" +
                "\t\t\t\"attach\": {\n" +
                "\t\t\t\t\"ext\": \"jpg\",\n" +
                "\t\t\t\t\"force_upload\": false,\n" +
                "\t\t\t\t\"size\": 117972,\n" +
                "\t\t\t\t\"w\": 720,\n" +
                "\t\t\t\t\"name\": \"Screenshot_20191114_104802.jpg\",\n" +
                "\t\t\t\t\"h\": 1520,\n" +
                "\t\t\t\t\"sen\": \"nim_default_im\",\n" +
                "\t\t\t\t\"url\": \"https://nim-nosdn.netease.im/MTAyMjYwOA==/bmltYV8xNDc4NTIyMDU1N18xNTczMDUyMDc0MTcxX2U1NmJiNDk2LWFmNWYtNDc5OC05MzUyLTM4MzNjYWIwOWIyNw==\",\n" +
                "\t\t\t\t\"md5\": \"78d64b184cac4c8abbc44c69cdb69c13\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"userMessageReceiverCount60m\": 1.0,\n" +
                "\t\t\t\"Business\": \"common\",\n" +
                "\t\t\t\"fromNick\": \"玫瑰吖.♡\",\n" +
                "\t\t\t\"app\": \"bixin\",\n" +
                "\t\t\t\"images\": \"https://nim-nosdn.netease.im/MTAyMjYwOA==/bmltYV8xNDc4NTIyMDU1N18xNTczMDUyMDc0MTcxX2U1NmJiNDk2LWFmNWYtNDc5OC05MzUyLTM4MzNjYWIwOWIyNw==\",\n" +
                "\t\t\t\"userVipLevel\": 0,\n" +
                "\t\t\t\"userMessageSenderCount60m\": 1.0,\n" +
                "\t\t\t\"Timeout\": \"2950\",\n" +
                "\t\t\t\"userAdvertMessageReceiverCount24h\": 0.0,\n" +
                "\t\t\t\"userMessageSendCount\": 6.0,\n" +
                "\t\t\t\"cashBlackDetect\": false,\n" +
                "\t\t\t\"targetUserId\": \"3a5203a02df24a9a873be2145dbc2313\",\n" +
                "\t\t\t\"eventType\": \"1\",\n" +
                "\t\t\t\"userAttractMessageReceiverCount24h\": 0.0,\n" +
                "\t\t\t\"msgidClient\": \"41b273d3e41e492995b581522699a496\",\n" +
                "\t\t\t\"clientIpDetail\": {\n" +
                "\t\t\t\t\"country\": \"中国\",\n" +
                "\t\t\t\t\"riskLevel\": \"PASS\",\n" +
                "\t\t\t\t\"province\": \"湖南省\",\n" +
                "\t\t\t\t\"city\": \"0\",\n" +
                "\t\t\t\t\"isp\": \"移动\",\n" +
                "\t\t\t\t\"cityId\": 2002,\n" +
                "\t\t\t\t\"region\": \"华中\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"msgTimestamp\": \"1573699688617\",\n" +
                "\t\t\t\"UserId\": \"d23a7cf956544bfdbc3ab63137daef2e\",\n" +
                "\t\t\t\"userAudioMessageReceiverCount60m\": 0.0,\n" +
                "\t\t\t\"Event\": \"im-message\",\n" +
                "\t\t\t\"TraceId\": \"1c1a42d9711f401d90f868cc28479da9\",\n" +
                "\t\t\t\"to\": \"72379e27dff68da0bc02538d849a2be6\",\n" +
                "\t\t\t\"neteaseImageCheck\": {\n" +
                "\t\t\t\t\"tagList\": [],\n" +
                "\t\t\t\t\"reason\": \"\",\n" +
                "\t\t\t\t\"riskLevel\": \"REVIEW\",\n" +
                "\t\t\t\t\"ocrText\": \"10:482个人资料设置MqQQ:2546900VIPTA还未开通任何特权服务Mq的空间加好友,\",\n" +
                "\t\t\t\t\"detail\": {\n" +
                "\t\t\t\t\t\"result\": [{\n" +
                "\t\t\t\t\t\t\t\"name\": \"0\",\n" +
                "\t\t\t\t\t\t\t\"details\": {\n" +
                "\t\t\t\t\t\t\t\t\"flagNames\": [],\n" +
                "\t\t\t\t\t\t\t\t\"logoNames\": [],\n" +
                "\t\t\t\t\t\t\t\t\"ocrText\": [\"10:482个人资料设置MqQQ:2546900VIPTA还未开通任何特权服务Mq的空间加好友\"],\n" +
                "\t\t\t\t\t\t\t\t\"faceNames\": []\n" +
                "\t\t\t\t\t\t\t},\n" +
                "\t\t\t\t\t\t\t\"taskId\": \"5055a1c70c7744bba30f49b64f530179\",\n" +
                "\t\t\t\t\t\t\t\"labels\": [{\n" +
                "\t\t\t\t\t\t\t\t\t\"level\": 1,\n" +
                "\t\t\t\t\t\t\t\t\t\"rate\": 0.5,\n" +
                "\t\t\t\t\t\t\t\t\t\"label\": 200\n" +
                "\t\t\t\t\t\t\t\t}, {\n" +
                "\t\t\t\t\t\t\t\t\t\"level\": 0,\n" +
                "\t\t\t\t\t\t\t\t\t\"rate\": 0.0,\n" +
                "\t\t\t\t\t\t\t\t\t\"label\": 100\n" +
                "\t\t\t\t\t\t\t\t}, {\n" +
                "\t\t\t\t\t\t\t\t\t\"level\": 0,\n" +
                "\t\t\t\t\t\t\t\t\t\"rate\": 0.0,\n" +
                "\t\t\t\t\t\t\t\t\t\"label\": 110\n" +
                "\t\t\t\t\t\t\t\t}, {\n" +
                "\t\t\t\t\t\t\t\t\t\"level\": 0,\n" +
                "\t\t\t\t\t\t\t\t\t\"rate\": 0.0,\n" +
                "\t\t\t\t\t\t\t\t\t\"label\": 210\n" +
                "\t\t\t\t\t\t\t\t}, {\n" +
                "\t\t\t\t\t\t\t\t\t\"level\": 0,\n" +
                "\t\t\t\t\t\t\t\t\t\"rate\": 0.0,\n" +
                "\t\t\t\t\t\t\t\t\t\"label\": 400\n" +
                "\t\t\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t\t\t]\n" +
                "\t\t\t\t\t\t}\n" +
                "\t\t\t\t\t]\n" +
                "\t\t\t\t}\n" +
                "\t\t\t},\n" +
                "\t\t\t\"md5\": \"78d64b184cac4c8abbc44c69cdb69c13\"\n" +
                "\t\t},\n" +
                "\t\t\"deviceId\": \"201911041640394289a0747278c1d23121435c42a0f750012f286b3ccde2e8\",\n" +
                "\t\t\"eventCode\": \"im-message\",\n" +
                "\t\t\"timeout\": 2950,\n" +
                "\t\t\"traceId\": \"1c1a42d9711f401d90f868cc28479da9\",\n" +
                "\t\t\"userId\": \"d23a7cf956544bfdbc3ab63137daef2e\"\n" +
                "\t},\n" +
                "\t\"riskResult\": {\n" +
                "\t\t\"businessCode\": \"common\",\n" +
                "\t\t\"eventCode\": \"im-message\",\n" +
                "\t\t\"level\": \"REVIEW\",\n" +
                "\t\t\"reason\": \"网易图片审核\",\n" +
                "\t\t\"reply\": \"\",\n" +
                "\t\t\"rule\": 288,\n" +
                "\t\t\"success\": true,\n" +
                "\t\t\"traceId\": \"1c1a42d9711f401d90f868cc28479da9\"\n" +
                "\t}\n" +
                "}\n" +
                "\n";

        Map<String,Object> responseMap = JsonFlatterUtils.toMap(json, FlattenMode.KEEP_ARRAYS);
        System.err.println(responseMap);
    }
}
