package com.yupaopao.risk.insight.flink.job;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/****
 * zengxiangcai
 * 2023/2/8 17:51
 ***/

@RunWith(Suite.class)
@Suite.SuiteClasses({
        FactorBroadcastProcessTest.class,
        FactorSlidingTimeTriggerTest.class,
        FactorSlidingWindowAssignerTest.class,
        RedisCustomizeSinkTest.class
})
public class FactorClassTests {

    public static void main(String[] args) {
    }
}
