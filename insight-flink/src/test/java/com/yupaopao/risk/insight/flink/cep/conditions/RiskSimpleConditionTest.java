//package com.yupaopao.risk.insight.flink.cep.conditions;
//
//import groovy.lang.Binding;
//import groovy.lang.GroovyClassLoader;
//import groovy.lang.Script;
//import org.junit.Test;
//
//import java.util.HashMap;
//import java.util.Map;
//
///****
// *
// * @Author: zengxiangcai
// * @Date: 2020-03-04 2:56
// *
// ***/
//public class RiskSimpleConditionTest {
//
//
//    @Test
//    public static void testAviator() {
////        try {
////            Map<String, Object> data = new HashMap<>();
////            data.put("eventCode", "im");
////            String testCondition = "eventCode=='im'";
////
////            Boolean result = (Boolean) AviatorEvaluator.exec(testCondition, data);
////            System.err.println(result);
////        } catch (Exception e) {
////            e.printStackTrace();
////        }
//    }
//
//    @Test(expected = Exception.class)
//    public static void testGroovyScript() throws Exception {
//
//        Map<String, Object> data = new HashMap<>();
//        data.put("eventCode", "im");
//        GroovyClassLoader groovyLoader = new GroovyClassLoader();
//        Class<Script> groovyClass = (Class<Script>) groovyLoader.parseClass("abcdskald");
//        groovy.lang.Script script = groovyClass.newInstance();
//        Binding binding = new Binding(data);
//        script.setBinding(binding);
//        Object result = script.run();
//
//    }
//
//}
