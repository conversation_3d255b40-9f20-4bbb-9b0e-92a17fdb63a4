package com.yupaopao.risk.insight.flink.cep;

import com.yupaopao.risk.insight.common.cep.beans.RiskEvent;
import org.apache.flink.cep.CEP;
import org.apache.flink.cep.PatternStream;
import org.apache.flink.cep.functions.PatternProcessFunction;
import org.apache.flink.cep.nfa.aftermatch.AfterMatchSkipStrategy;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.IterativeCondition;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.Collector;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-05 16:26
 *
 ***/
public class CepJobTest {


    public static void main(String[] args) throws Exception {


//        Pattern<String, String> p = Pattern.<String>begin("register").where(new SimpleCondition<String>() {
//            @Override
//            public boolean filter(String value) throws Exception {
//                return value.startsWith("register");
//            }
//        }).next("im").where(new SimpleCondition<String>() {
//            @Override
//            public boolean filter(String value) throws Exception {
//                return value.startsWith("im");
//            }
//        }).times(1, 2);

        Pattern<String, String> p = Pattern.<String>begin("shelf").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("shelf");
            }
        }).notNext("register").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("register");
            }
        }).followedBy("exit").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("exit");
            }
        });

//        Pattern<String, String> p = Pattern.<String>begin("shelf").where(new SimpleCondition<String>() {
//            @Override
//            public boolean filter(String value) throws Exception {
//                return value.startsWith("shelf");
//            }
//        }).followedByAny("register").where(new SimpleCondition<String>() {
//            @Override
//            public boolean filter(String value) throws Exception {
//                return value.startsWith("register");
//            }
//        });


        //价格大于10后命中，后续 持续在之前的均值之上保留，大于15卖出

        Pattern<Integer, Integer> p1 = Pattern.<Integer>begin("largeThan10").where(new SimpleCondition<Integer>() {
            @Override
            public boolean filter(Integer value) throws Exception {
                return value > 10;
            }
        }).next("largeAvg").where(new IterativeCondition<Integer>() {
            @Override
            public boolean filter(Integer value, Context<Integer> ctx) throws Exception {
                Iterable<Integer> matchedList = ctx.getEventsForPattern("largeAvg");
                int sum = 0;
                int count = 0;
                for (Integer v : matchedList) {
                    sum += v;
                    count += 1;
                }
                if (count == 0) {
                    return value > 10;
                }
                int avg = sum / count;
                return value > avg;
            }
        }).oneOrMore().followedBy("large15").where(new SimpleCondition<Integer>() {
            @Override
            public boolean filter(Integer value) throws Exception {
                return value > 15;
            }
        });

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
//        DataStream<Integer> inDs = env.fromElements(11,12,13,14,13,16,17);
        DataStream<Integer> inDs = env.fromElements(11, 13, 16);
        PatternStream<Integer> matchStream = CEP.pattern(inDs, p1);
        matchStream.process(new PatternProcessFunction<Integer, Integer>() {
            @Override
            public void processMatch(Map<String, List<Integer>> map, Context context, Collector<Integer> collector) throws Exception {
                for (Map.Entry<String, List<Integer>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
//                    collector.collect(entry.getKey());
                }
            }
        });
//        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
//        env.setParallelism(1);
//        DataStream<String> inDs = env.fromElements("shelf","exit");
//        PatternStream<String> matchStream = CEP.pattern(inDs, p);
//        matchStream.process(new PatternProcessFunction<String, String>() {
//            @Override
//            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
//                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
//                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
//                    collector.collect(entry.getKey());
//                }
//            }
//        });
        env.execute("test");
    }


    @Test
    public void test1() throws Exception {
//        Pattern<String, String> p = Pattern.<String>begin("register").where(new SimpleCondition<String>() {
//            @Override
//            public boolean filter(String value) throws Exception {
//                return value.startsWith("register");
//            }
//        }).followedBy("im").where(new SimpleCondition<String>() {
//            @Override
//            public boolean filter(String value) throws Exception {
//                return value.startsWith("im");
//            }
//        });

        Pattern<String,String> p1 = Pattern.<String>begin("search0").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("search");
            }
        }).next("immessage0").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("im");
            }
        });

        Pattern<String,String> p = p1.followedBy(Pattern.<String>begin("search-g").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("search");
            }
        }).next("immessage-g").where(new SimpleCondition<String>() {
            @Override
            public boolean filter(String value) throws Exception {
                return value.startsWith("im");
            }
        })).times(2);


        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        DataStream<String> inDs = env.fromElements("search", "im1","search2","im2","search3","im3","search4",
                "im4");
        PatternStream<String> matchStream = CEP.pattern(inDs, p);
        matchStream.process(new PatternProcessFunction<String, String>() {
            @Override
            public void processMatch(Map<String, List<String>> map, Context context, Collector<String> collector) throws Exception {
                for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                    System.err.println("pattern: " + entry.getKey() + ", value: " + entry.getValue());
                    collector.collect(entry.getKey());
                }
            }
        });
        env.execute("test");
    }
}
