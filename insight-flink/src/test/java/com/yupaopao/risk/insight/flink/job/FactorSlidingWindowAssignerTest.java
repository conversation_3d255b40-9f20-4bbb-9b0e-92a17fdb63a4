package com.yupaopao.risk.insight.flink.job;

import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import com.yupaopao.risk.insight.flink.windows.assigner.FactorSlidingWindowAssigner;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.streaming.api.windowing.assigners.WindowAssigner;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/****
 * zengxiangcai
 * 2023/2/8 12:12
 ***/

@RunWith(PowerMockRunner.class)
public class FactorSlidingWindowAssignerTest {

    @InjectMocks
    FactorSlidingWindowAssigner assigner;


    @Test
    public void testAssignWindows() {
        Long time = 1675839414857L; //2023-02-08 14:56:54

        FactorCalDetail elem = FactorTestParamsConstants.buildSlidingWindowData(time);

        WindowAssigner.WindowAssignerContext ctx = PowerMockito.mock(WindowAssigner.WindowAssignerContext.class);

        PowerMockito.when(ctx.getCurrentProcessingTime()).thenReturn(time);

        Collection<TimeWindow> collections =
                assigner.assignWindows(elem, time, ctx).stream().sorted(Comparator.comparing(TimeWindow::getStart)).collect(Collectors.toList());
        Assert.assertTrue("window count 10: ", collections.size() == 10);
        List<TimeWindow> expectedList = Arrays.asList(
                new TimeWindow(1675839360000L, 1675839960000L),
                new TimeWindow(1675839300000L, 1675839900000L),
                new TimeWindow(1675839240000L, 1675839840000L),
                new TimeWindow(1675839180000L, 1675839780000L),
                new TimeWindow(1675839120000L, 1675839720000L),
                new TimeWindow(1675839060000L, 1675839660000L),
                new TimeWindow(1675839000000L, 1675839600000L),
                new TimeWindow(1675838940000L, 1675839540000L),
                new TimeWindow(1675838880000L, 1675839480000L),
                new TimeWindow(1675838820000L, 1675839420000L)
        ).stream().sorted(Comparator.comparing(TimeWindow::getStart)).collect(Collectors.toList());

        Assert.assertTrue("expected window: ", collections.toString().equals(expectedList.toString()));


        FactorCalDetail tumblingElem = FactorTestParamsConstants.buildTumblingWindowData(time);

        Collection<TimeWindow> tumblingResultList =
                assigner.assignWindows(tumblingElem, time, ctx).stream().sorted(Comparator.comparing(TimeWindow::getStart)).collect(Collectors.toList());

        List<TimeWindow> tumblingExpectedList =
                Arrays.asList(new TimeWindow(1675839000000L, 1675839600000L)).stream().sorted(Comparator.comparing(TimeWindow::getStart)).collect(Collectors.toList());

        Assert.assertTrue("tumbling windows count: 1", tumblingResultList.size() == 1);
        Assert.assertTrue("expected tumbling window: ", tumblingExpectedList.toString().equals(tumblingResultList.toString()));

    }
}
