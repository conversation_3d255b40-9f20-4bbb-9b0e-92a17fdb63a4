package com.yupaopao.risk.insight.flink.cep;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yupaopao.risk.insight.common.cep.beans.CepRule;
import com.yupaopao.risk.insight.common.cep.beans.RiskEvent;
import com.yupaopao.risk.insight.common.cep.utils.PatternTools;
import com.yupaopao.risk.insight.common.cep.utils.RuleUtils;
import org.apache.flink.cep.nfa.NFA;
import org.apache.flink.cep.nfa.aftermatch.AfterMatchSkipStrategy;
import org.apache.flink.cep.nfa.compiler.NFACompiler;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.junit.Test;

import java.util.Map;


/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-14 19:20
 *
 ***/
public class CepRuleTest {


    public void createPattern() {
        String input = "";
        String ruleContent = createRuleContent(input);
        CepRule rule = new CepRule();
        rule.setContent(ruleContent);
        System.err.println(RuleUtils.transToPattern(rule));
    }

    private String createRuleContent(String text) {

        String patternText = PatternTools.transInputPattern(text);
        return patternText;
    }


    @Test
    public void test() {
        Pattern<RiskEvent, ?> p1 =
                Pattern.<RiskEvent>begin("search", AfterMatchSkipStrategy.skipPastLastEvent()).where(new SimpleCondition<RiskEvent>() {
                    @Override
                    public boolean filter(RiskEvent value) throws Exception {
                        return value.getStringValue("eventCode") == "search";
                    }
                })
                        .next("im").where(new SimpleCondition<RiskEvent>() {
                    @Override
                    public boolean filter(RiskEvent value) throws Exception {
                        return value.getStringValue("eventCode") == "im";
                    }
                });


        Pattern<RiskEvent, ?> g =
                Pattern.begin(p1, AfterMatchSkipStrategy.skipPastLastEvent()).times(20).within(Time.minutes(20));

        final NFACompiler.NFAFactory<RiskEvent> newNfaFactory =
                (NFACompiler.NFAFactory<RiskEvent>) NFACompiler.compileFactory(g, true);
        NFA<RiskEvent> nfa = newNfaFactory.createNFA();

        System.err.println(nfa.getStates());
    }


    public static void main(String[] args) {
        Map<String, Integer> objectObjectHashMap=Maps.newHashMap();
        try {
            Map map = JSONObject.parseObject("{\"sql\":\"必填\"}", Map.class);

            objectObjectHashMap.putAll(map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
