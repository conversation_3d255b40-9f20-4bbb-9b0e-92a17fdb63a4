//package com.yupaopao.risk.insight.flink.cep;
//
//import org.apache.flink.api.common.functions.ReduceFunction;
//import org.apache.flink.api.java.DataSet;
//import org.apache.flink.api.java.ExecutionEnvironment;
//import org.apache.flink.api.java.io.PrintingOutputFormat;
//import org.apache.flink.api.java.tuple.Tuple2;
//
//
//import java.util.Arrays;
//import java.util.Iterator;
//import java.util.TreeSet;
//
///****
// *
// * @Author: zengxiangcai
// * @Date: 2020-04-01 0:18
// *
// ***/
//public class Test {
//    public static void main(String[] args) throws Exception {
//        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
//        DataSet<Tuple2<String, TreeSet<String>>> testStream = env.fromElements(
//                new Tuple2<String, TreeSet<String>>("u1", new TreeSet<>(Arrays.asList("tag1", "tag2"))),
//                new Tuple2<String, TreeSet<String>>(
//                        "u2", new TreeSet<>(Arrays.asList("tag12", "tag22"))),
//                new Tuple2<String, TreeSet<String>>(
//                        "u3", new TreeSet<>(Arrays.asList("tag1", "tag2"))),
//                new Tuple2<String, TreeSet<String>>(
//                        "u1", new TreeSet<>(Arrays.asList("tag1", "tag2"))),
//                new Tuple2<String, TreeSet<String>>(
//                        "u3", new TreeSet<>(Arrays.asList("tag1", "tag3"))),
//                new Tuple2<String, TreeSet<String>>(
//                        "u3", new TreeSet<>(Arrays.asList("tag1", "tag31","tag31","tag32","tag33"))),
//                new Tuple2<String, TreeSet<String>>(
//                        "u3", new TreeSet<>(Arrays.asList("tag1", "tag31","tag31","tag32","tag33")))
//                );
//        DataSet<Tuple2<String, TreeSet<String>>> ds = testStream.groupBy(0).reduce(new ReduceFunction<Tuple2<String, TreeSet<String>>>() {
//            @Override
//            public Tuple2<String, TreeSet<String>> reduce(Tuple2<String, TreeSet<String>> value1, Tuple2<String,
//                    TreeSet<String>> value2) throws Exception {
//                System.err.println(value1);
//                System.err.println(value2);
//                System.err.println("***********");
//                Tuple2 res = new Tuple2<>();
//                res.f0 = value1.getField(0);
//
//                res.f1 = value2.f1.size()>value1.f1.size()?addAll(value2.f1,value1.f1):addAll(value1.f1,value2.f1);
//                System.err.println("result: "+res);
//                return res;
//            }
//        });
//        ds.output(new PrintingOutputFormat());
//        System.err.println(env.getExecutionPlan());
//
//        env.execute("ssss");
//
//    }
////
////    private static TreeSet addAll(TreeSet<String> value1, TreeSet<String> value2){
////        Iterator<String> it = value2.iterator();
////        while(it.hasNext()){
////            String obj = it.next();
////            value1.add(obj);
////        }
////    }
//}
