package com.yupaopao.risk.insight.flink.apollo;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import org.junit.Test;

import java.util.Set;

public class ApolloTest {
    /***
     * DefaultServerProvider 设置apollo的环境
     *
     */

    @Test
    public void getApolloPropertyNamesTest() {
        //System.setProperty("config.server.env", "test");
        Config config = ConfigService.getConfig("middleware.tablestore");
        Set<String> jsonProperties = config.getPropertyNames();
        System.err.println(jsonProperties);

    }
}
