关于窗口
==
```html
在业务开发过程总进程会考虑到最近几秒钟、几分钟、几小时发生的什么事情的汇总。
一般的都是当前时间-N做统计.
大数据流计算中也会提供窗口的含义，
滚动窗口：窗口大小固定，窗口间没有重叠
滑动窗口：窗口大小固定，窗口可能有重叠，也可能是窗口抽样
会话窗口：窗口大小不定，一般窗口不会重叠
```


CEP
===
- 历史
```html
 complex event processing 包含一些列的概念和技术，源于20世纪90年代。
主要用来处理实时事件以及从事件流中提取信息。
cep的目的是在实时场景中尽可能快的识别有意义的事件(可能是机遇也可能是威胁)
event 可以来自于不同的源头：销售信息、订单信息、社交媒体信息、股票信息等等

```

- 包含的内容
  - event filtering
  - event pattern matching
  - causal and timing analysis
  - hierarchical abstraction of event
  - construction of complex event
  - specification of event hierarchies

- what is complex event
```html
   复杂事件是普通时间的简单聚合、组合、抽象
```
- 模式匹配
- pattern
  指时间出现一次多次，0次，循环等
- condition
  指示某个pattern接收的event必须满足的条件
  ```java
    SimpleCondition 会对event 的properties进行过滤
    IterativeCondition 可以获取pattern的上线文满足相应的需求
  ```


Metric
===

- Counters
对某些事情计数，long类型数据

- Gauge

- Histogram 处理long 类型数据的分布

- Meter 


entrypoint
==
```html
StandaloneSessionClusterEntrypoint 
初始化相关服务：
commonRpcService
ioExecutor
haServices
blobServer
heartbeatServices
metricRegistry
archivedExecutionGraphStore
```

JobManager
==
- DefaultDispatcherResourceManagerComponentFactory
- ResouceManager
- Dispatcher

CliFrontend
==
- ClusterClient.getJobGraph
- StreamingJobGraphGenerator.createJobGraph
- JobGraphGenerator.compileJobGraph


graph
==
-----
基于hbase存储图结构：

-----
