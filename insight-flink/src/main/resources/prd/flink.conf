jobmanager.rpc.address: flink-jobmanager
taskmanager.numberOfTaskSlots: 4
blob.server.port: 6124
jobmanager.rpc.port: 6123
taskmanager.rpc.port: 6122
jobmanager.heap.size: 7168m
taskmanager.heap.size: 7168m
high-availability: zookeeper
high-availability.cluster-id: cluster-risk-flink
high-availability.storageDir: /flink/ha
high-availability.zookeeper.quorum: prod-zk.yupaopao.com:2181
high-availability.jobmanager.port: 6123
high-availability.zookeeper.path.root: /flink/risk-insight
high-availability.zookeeper.path.checkpoints: /flink/zk-checkpoints
high-availability.zookeeper.path.jobgraphs: /flink/zk-jobgraphs
high-availability.zookeeper.client.connection-timeout: 120000
state.backend: filesystem
state.checkpoints.dir: file:///flink/checkpoints
state.savepoints.dir: file:///flink/savepoints
state.checkpoints.num-retained: 2
jobmanager.execution.failover-strategy: region
jobmanager.archive.fs.dir: file:///flink/archive/history
web.upload.dir: /flink/webUpload
env.java.opts: -Dspring.profiles.active=prod -XX:ActiveProcessorCount=4
jobstore.expiration-time: 1296000
metrics.reporter.prom.class: org.apache.flink.metrics.prometheus.PrometheusReporter
metrics.reporter.prom.port: 9249
metrics.system-resource: true