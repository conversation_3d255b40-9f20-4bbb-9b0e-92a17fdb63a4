<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>-->

    <property name="logger.dir" value="/data/logs"/>

    <property name="applicationName" value="risk-insight-flink"/>

    <!-- 配置日志格式, 可自定义，必须保留下列字段 -->
    <property name="ALL_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%p][%thread] [tid:%X{framework_trace_id:-}] %logger{30} - %msg%xEx%n"/>

    <!--控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${ALL_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 日志文件, 默认INFO级别, 可接入logging-starter动态修改日志级别 -->
    <appender name="ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logger.dir}/${applicationName}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${logger.dir}/${applicationName}/info-%d{yyyy-MM-dd-HH}-%i.log.gz</FileNamePattern>
            <MaxHistory>48</MaxHistory>
            <maxFileSize>100MB</maxFileSize>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
            <TotalSizeCap>80GB</TotalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${ALL_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 日志文件，只打印WARN日志 -->
    <appender name="WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logger.dir}/${applicationName}/warn.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logger.dir}/${applicationName}/warn-%d{yyyy-MM-dd-HH}.log.gz</FileNamePattern>
            <MaxHistory>72</MaxHistory>
            <TotalSizeCap>10GB</TotalSizeCap>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${ALL_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 日志文件，只打印ERROR日志 -->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logger.dir}/${applicationName}/error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${logger.dir}/${applicationName}/error-%d{yyyy-MM-dd}.log.gz</FileNamePattern>
            <MaxHistory>30</MaxHistory>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
            <TotalSizeCap>2GB</TotalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${ALL_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 日志文件，异步打印日志 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <includeCallerData>true</includeCallerData>
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <appender-ref ref="ALL"/>
    </appender>

    <!-- ELK收集日志, 可在kibana、pangu上查看 -->
    <appender name="LOG_STASH" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${logger.dir}/logstash/${applicationName}.json</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${logger.dir}/logstash/${applicationName}-%d{yyyy-MM-dd-HH}-%i.json.gz</fileNamePattern>
            <MaxHistory>2</MaxHistory>
            <maxFileSize>100MB</maxFileSize>
            <CleanHistoryOnStart>true</CleanHistoryOnStart>
            <TotalSizeCap>10GB</TotalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>Asia/Shanghai</timeZone>
                </timestamp>
                <pattern>
                    <!-- 日志格式, JSON格式, 可添加自定义字段, 下列字段为必需字段 -->
                    <pattern>
                        {
                        "createTime": "%d{yyyy-MM-dd HH:mm:ss.SSS}",
                        "severity": "%level",
                        "service": "${applicationName}",
                        "trace": "%X{framework_trace_id:-}",
                        "span": "%X{framework_span_id:-}",
                        "parent": "%X{framework_parent_span_id:-}",
                        "pid": "${PID:-}",
                        "thread": "%thread",
                        "class": "%logger{30}",
                        "rest": "%message",
                        "exception": "%exception"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <appender name="CAT" class="com.dianping.cat.logback.CatLogbackAppender"/>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="CAT"/>
            <appender-ref ref="LOG_STASH"/>
            <appender-ref ref="ASYNC"/>
            <appender-ref ref="WARN"/>
            <appender-ref ref="ERROR"/>
        </root>
    </springProfile>

    <springProfile name="uat">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="CAT"/>
            <appender-ref ref="LOG_STASH"/>
            <appender-ref ref="ASYNC"/>
            <appender-ref ref="WARN"/>
            <appender-ref ref="ERROR"/>
        </root>
    </springProfile>

    <springProfile name="k8s">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="CAT"/>
            <appender-ref ref="LOG_STASH"/>
            <appender-ref ref="ASYNC"/>
            <appender-ref ref="WARN"/>
            <appender-ref ref="ERROR"/>
        </root>
    </springProfile>
    <springProfile name="test">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="CAT"/>
            <appender-ref ref="LOG_STASH"/>
            <appender-ref ref="ASYNC"/>
            <appender-ref ref="WARN"/>
            <appender-ref ref="ERROR"/>
        </root>
    </springProfile>

    <springProfile name="local">
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
