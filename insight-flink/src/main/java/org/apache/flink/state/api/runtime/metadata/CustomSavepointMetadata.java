package org.apache.flink.state.api.runtime.metadata;

import org.apache.flink.runtime.checkpoint.MasterState;
import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.jobgraph.OperatorID;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/****
 * zengxiangcai
 * 2023/1/11 15:20
 ***/
public class CustomSavepointMetadata extends SavepointMetadata {

    private final Map<OperatorID, CustomOperatorStateSpec> operatorStateIndex;


    public CustomSavepointMetadata(int maxParallelism, Collection<MasterState> masterStates, Collection<OperatorState> initialStates) {
        super(maxParallelism, masterStates, initialStates);
        this.operatorStateIndex = new HashMap<>(initialStates.size());
        initialStates.forEach(
                existingState ->
                        operatorStateIndex.put(
                                existingState.getOperatorID(),
                                CustomOperatorStateSpec.existing(existingState)));
    }


    /****
     *  父类需要传入uid，然后根据uid生成OperatorID，但是我们之前的代码都没有uid，所以只能遍历查到一致的uid，然后分析相关的数据operatorId由lowerPart####upperPart组成
     * @param operatorId: lowerPart###upperPart
     * @return
     * @throws IOException
     */
    public OperatorState getOperatorState(String operatorId) throws IOException {
//    OperatorID operatorID = OperatorIDGenerator.fromUid(uid);
        String operatorSplit[] = operatorId.split("###");
        String upper = operatorSplit[0];
        String lower = operatorSplit[1];
        OperatorID operatorID = new OperatorID(Long.valueOf(lower), Long.valueOf(upper));
        CustomOperatorStateSpec operatorState = operatorStateIndex.get(operatorID);
        if (operatorState == null || operatorState.isNewStateTransformation()) {
            throw new IOException("Savepoint does not contain state with operator uid " + operatorId);
        }
        return operatorState.asExistingState();
    }

    public static void main(String[] args) {
        String operatorId = "29fccc29470a913fa7eba3e671468783";
        OperatorID operatorID = new OperatorID(operatorId.getBytes(StandardCharsets.UTF_8));
        System.err.println(operatorID);

    }

}
