package org.apache.flink.state.api;

import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.runtime.state.StateBackend;
import org.apache.flink.state.api.runtime.SavepointLoader;
import org.apache.flink.state.api.runtime.metadata.CustomSavepointMetadata;

import java.io.IOException;
import java.util.Comparator;

/****
 * zengxiangcai
 * 2023/1/11 15:05
 * @see org.apache.flink.state.api.Savepoint
 *
 * copy savepoint 代码，主要是flink-state-processor_api中代码很多是package范围的访问权限
 ***/
public class CustomSavepoint {

    public CustomSavepoint() {

    }


    /**
     * Loads an existing savepoint. Useful if you want to query, modify, or extend the state of an
     * existing application.
     *
     * @param env          The execution environment used to transform the savepoint.
     * @param path         The path to an existing savepoint on disk.
     * @param stateBackend The state backend of the savepoint.
     */
    public static CustomExistingSavepoint load(
            ExecutionEnvironment env, String path, StateBackend stateBackend) throws IOException {
        CheckpointMetadata metadata = SavepointLoader.loadSavepointMetadata(path);

        int maxParallelism =
                metadata.getOperatorStates().stream()
                        .map(OperatorState::getMaxParallelism)
                        .max(Comparator.naturalOrder())
                        .orElseThrow(
                                () ->
                                        new RuntimeException(
                                                "Savepoint must contain at least one operator state."));

//        SavepointMetadata savepointMetadata =
//                new SavepointMetadata(
//                        maxParallelism, metadata.getMasterStates(), metadata.getOperatorStates());
        CustomSavepointMetadata savepointMetadata =
                new CustomSavepointMetadata(
                        maxParallelism, metadata.getMasterStates(), metadata.getOperatorStates());
        return new CustomExistingSavepoint(env, savepointMetadata, stateBackend);
    }
}
