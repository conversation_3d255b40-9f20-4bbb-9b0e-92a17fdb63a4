package org.apache.flink.state.api.runtime.metadata;

/****
 * zengxiangcai
 * 2023/1/12 14:47
 * copy from {@link org.apache.flink.state.api.runtime.metadata.OperatorStateSpec}
 *
 ***/



import org.apache.flink.annotation.Internal;
import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.jobgraph.OperatorID;
import org.apache.flink.state.api.runtime.BootstrapTransformationWithID;
import org.apache.flink.util.Preconditions;

import javax.annotation.Nullable;

/**
 * This class specifies an operator state maintained by {@link SavepointMetadata}. An operator state
 * is either represented as an existing {@link OperatorState}, or a {@link
 * org.apache.flink.state.api.BootstrapTransformation} that will be used to create it.
 */
public class CustomOperatorStateSpec {

  private final OperatorID id;

  @Nullable private final OperatorState existingState;

  @Nullable private final BootstrapTransformationWithID<?> newOperatorStateTransformation;

  public static CustomOperatorStateSpec existing(OperatorState existingState) {
    return new CustomOperatorStateSpec(Preconditions.checkNotNull(existingState));
  }

  public static CustomOperatorStateSpec newWithTransformation(
          BootstrapTransformationWithID<?> transformation) {
    return new CustomOperatorStateSpec(Preconditions.checkNotNull(transformation));
  }

  private CustomOperatorStateSpec(OperatorState existingState) {
    this.id = existingState.getOperatorID();
    this.existingState = existingState;
    this.newOperatorStateTransformation = null;
  }

  private CustomOperatorStateSpec(BootstrapTransformationWithID<?> transformation) {
    this.id = transformation.getOperatorID();
    this.newOperatorStateTransformation = transformation;
    this.existingState = null;
  }

  boolean isExistingState() {
    return existingState != null;
  }

  boolean isNewStateTransformation() {
    return !isExistingState();
  }

  OperatorState asExistingState() {
    Preconditions.checkState(
            isExistingState(), "OperatorState %s is not an existing state.", id);
    return existingState;
  }

  @SuppressWarnings("unchecked")
  <T> BootstrapTransformationWithID<T> asNewStateTransformation() {
    Preconditions.checkState(
            isNewStateTransformation(),
            "OperatorState %s is not a new state defined with BootstrapTransformation",
            id);
    return (BootstrapTransformationWithID<T>) newOperatorStateTransformation;
  }
}

