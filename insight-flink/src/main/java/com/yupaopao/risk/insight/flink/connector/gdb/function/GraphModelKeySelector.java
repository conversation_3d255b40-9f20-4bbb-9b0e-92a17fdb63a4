package com.yupaopao.risk.insight.flink.connector.gdb.function;

import com.yupaopao.risk.insight.flink.bean.graph.UserGraphModel;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.functions.KeySelector;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-01 19:09
 *
 ***/
public class GraphModelKeySelector implements KeySelector<UserGraphModel, String> {
    @Override
    public String getKey(UserGraphModel value) throws Exception {
        if (StringUtils.isNotEmpty(value.getUserId())) {
            return value.getUserId();
        } else if (StringUtils.isNotEmpty(value.getDeviceId())) {
            return value.getDeviceId();
        } else if (StringUtils.isNotEmpty(value.getIp())) {
            return value.getIp();
        }
        return "default";
    }
}
