package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.connector.mysql.model.Factor;
import com.yupaopao.risk.insight.flink.constants.FactorConstants;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class CommonFactorBroadcastProcess extends BroadcastProcessFunction<String, List<Factor>, FactorCalDetail> {

    private static Logger LOGGER = LoggerFactory.getLogger(CommonFactorBroadcastProcess.class);

    public final static MapStateDescriptor<Void, List<Factor>> COMMON_CONFIG_DESCRIPTOR = new MapStateDescriptor<>("common-actors", Types.VOID,
        TypeInformation.of(new TypeHint<List<Factor>>() {}));

    @Override public void processElement(String value, ReadOnlyContext ctx, Collector<FactorCalDetail> out) throws Exception {
        List<Factor> factors = ctx.getBroadcastState(COMMON_CONFIG_DESCRIPTOR).get(null);
        if (null == factors || factors.size() <= 0) {
            return;
        }
        JSONObject param = JSONObject.parseObject(value);
        if (param.containsKey("action")) {
            assembleFactorCal(param, factors, out);
        } else {
            JSONObject data = param.containsKey("data") ? param.getJSONObject("data") : param;
            if (null == data || data.size() == 0) {
                return;
            }
            factors.forEach(factor -> {
                String groupKeyAndTime = factor.getGroupKey();
                String[] split = groupKeyAndTime.split("##");
                String[] functionSplit = factor.getFunction().split("##");

                if (checkCondition(data, factor)) {
                    String aggValue = getAggValue(factor, data, split[0]);
                    String groupKey = getGroupKey(factor, data, split[0]);
                    if ((StringUtils.isNotEmpty(aggValue) || "COUNT".equals(functionSplit[0])) && StringUtils.isNotEmpty(groupKey) && !Objects.isNull(data.getLong("timestamp"))) {
                        FactorCalDetail result = new FactorCalDetail();
                        result.setGroupKey(String.format(FactorConstants.FACTOR_DATA_PREFIX, factor.getId(), groupKey));
                        result.setData(aggValue);
                        result.setFunction(functionSplit[0]);
                        result.setTimeSpan(factor.getTimeSpan());
                        result.setTimeStamp(data.getLong("timestamp"));
                        out.collect(result);
                    }
                }
            });
        }
    }

    @Override public void processBroadcastElement(List<Factor> value, Context ctx, Collector<FactorCalDetail> out) throws Exception {
        ctx.getBroadcastState(COMMON_CONFIG_DESCRIPTOR).put(null, value);
    }

    private boolean checkCondition(JSONObject param, Factor factor) {
        if (StringUtils.isEmpty(factor.getCondition())) {
            return true;
        }

        try {
            Expression expression = new SpelExpressionParser().parseExpression(factor.getCondition());
            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setVariables(param);
            Object result = expression.getValue(context);
            LOGGER.debug("前置表达式执行结果:{} - {} - {}", result, factor.getCondition(), param);
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else {
                return result != null;
            }
        } catch (Throwable e) {
            LOGGER.error("执行前置表达式出错:" + factor.getId() + "\n" + factor.getCondition()  + "\n" + param, e);
        }
        return false;
    }

    private String getGroupKey(Factor factor, JSONObject data, String group){
        List<String> groupValues = new LinkedList<>();
        for (String groupKey : group.split(",")) {
            String groupValue;

            if (groupKey.contains(".")) {
                Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                groupValue = dataMap.getOrDefault(groupKey, "").toString();
            } else {
                groupValue = data.getString(groupKey);
            }
            // 缺失任意groupKeyValue则放弃
            if (StringUtils.isBlank(groupValue)) {
                return "";
            }
            groupValues.add(groupValue);
        }
        return String.join("$", groupValues);
    }


    private String getAggValue(Factor factor, JSONObject data, String group){
        if(StringUtils.isEmpty(factor.getAggKey())){
            String[] groupKeys = group.split(",");
            return data.getString(groupKeys[groupKeys.length - 1]);
        } else {
            if (factor.getAggKey().contains(".")) {
                Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                return dataMap.getOrDefault(factor.getAggKey(), "").toString();
            } else {
                return data.getString(factor.getAggKey());
            }
        }
    }

    private void assembleFactorCal(JSONObject data, List<Factor> factors, Collector<FactorCalDetail> out) {
        try {
            JSONObject params = data.getJSONObject("params");
            JSONArray purgeFactor = data.getJSONArray("factor");
            for (int i = 0; i < purgeFactor.size(); i++) {
                JSONObject jsonObject = purgeFactor.getJSONObject(i);
                long factorId = jsonObject.getLong("factorId");
                factors.forEach(factor -> {
                    String groupKeyAndTime = factor.getGroupKey();
                    String[] split = groupKeyAndTime.split("##");

                    if (factor.getId() == factorId) {
                        String groupKey = getGroupKey(factor, params, split[0]);
                        if (StringUtils.isEmpty(groupKey)) {
                            LOGGER.error("累计因子 {} 清除失败，请检查参数: {}",factorId, data.toJSONString());
                            return;
                        }
                        FactorCalDetail result = new FactorCalDetail();
                        result.setPurge(true);
                        result.setFunction(factor.getFunction());
                        result.setTimeSpan(factor.getTimeSpan());
                        result.setGroupKey(String.format(FactorConstants.FACTOR_DATA_PREFIX, factor.getId(), groupKey));
                        out.collect(result);
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.error("清除累计因子异常：", e);
        }
    }
}
