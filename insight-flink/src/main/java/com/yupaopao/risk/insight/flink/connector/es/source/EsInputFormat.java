package com.yupaopao.risk.insight.flink.connector.es.source;

import com.google.common.collect.Lists;
import com.yupaopao.risk.insight.flink.connector.ts.util.EsUtil;
import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.io.DefaultInputSplitAssigner;
import org.apache.flink.api.common.io.RichInputFormat;
import org.apache.flink.api.common.io.statistics.BaseStatistics;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.GenericInputSplit;
import org.apache.flink.core.io.InputSplit;
import org.apache.flink.core.io.InputSplitAssigner;
import org.elasticsearch.action.search.*;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.slice.SliceBuilder;

import java.io.IOException;
import java.io.Serializable;
import java.util.Iterator;
import java.util.List;

/**
 * Created by Avalon on 2020/1/8 15:00
 */
@Slf4j
@Getter
@Setter
public class EsInputFormat extends RichInputFormat<String, InputSplit> implements Serializable {

    protected EsProperties esProperties;

    protected int batchSize = 10;

    protected long keepAlive = 30;

    private transient RestHighLevelClient client;

    private Iterator<String> iterator;

    private transient SearchRequest searchRequest;

    private transient Scroll scroll;

    private String scrollId;

    private LongCounter readCounter = new LongCounter();

    public EsInputFormat(EsProperties esProperties) {
        this.esProperties = esProperties;
        if (esProperties.getBatchSize() > 0) {
            this.batchSize = esProperties.getBatchSize();
        }
        log.info("es properties: {}", esProperties);
    }

    @Override
    public InputSplit[] createInputSplits(int splitNum) throws IOException {
        InputSplit[] splits = new InputSplit[splitNum];
        for (int i = 0; i < splitNum; i++) {
            splits[i] = new GenericInputSplit(i,splitNum);
        }

        return splits;
    }

    @Override
    public boolean reachedEnd() throws IOException {
        if(iterator != null && iterator.hasNext()) {
            return false;
        } else {
            return searchScroll();
        }
    }

    @Override
    public String nextRecord(String reuse) throws IOException {
        return iterator.next();
    }

    @Override
    public void close() throws IOException {
        if(client != null) {
            clearScroll();

            client.close();
            client = null;
        }
    }

    private boolean searchScroll() throws IOException{
        SearchHit[] searchHits;
        if(scrollId == null){
            SearchResponse searchResponse = client.search(searchRequest);
            scrollId = searchResponse.getScrollId();
            searchHits = searchResponse.getHits().getHits();
        } else {
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(scroll);
            SearchResponse searchResponse = client.searchScroll(scrollRequest);
            scrollId = searchResponse.getScrollId();
            searchHits = searchResponse.getHits().getHits();
        }

        List<String> resultList = Lists.newArrayList();
        for(SearchHit searchHit : searchHits) {
            resultList.add(searchHit.getSourceAsString());
        }
        readCounter.add(resultList.size());
        iterator = resultList.iterator();
        return !iterator.hasNext();
    }


    private void clearScroll() throws IOException{
        if(scrollId == null){
            return;
        }

        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        ClearScrollResponse clearScrollResponse = client.clearScroll(clearScrollRequest);
        boolean succeeded = clearScrollResponse.isSucceeded();
        log.info("Clear scroll response:{}", succeeded);
    }

    @Override
    public InputSplitAssigner getInputSplitAssigner(InputSplit[] inputSplits) {
        return new DefaultInputSplitAssigner(inputSplits);
    }

    @Override
    public void open(InputSplit split) throws IOException {
        GenericInputSplit genericInputSplit = (GenericInputSplit)split;

        client = EsUtil.getClient(esProperties);
        scroll = new Scroll(TimeValue.timeValueMinutes(keepAlive));

        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(batchSize);

        if(StringUtils.isNotEmpty(esProperties.getQuery())){
            searchSourceBuilder.query(QueryBuilders.wrapperQuery(esProperties.getQuery()));
        }

        if(StringUtils.isNotEmpty(esProperties.getSort())){
            searchSourceBuilder.sort(esProperties.getSort());
        }

        if(genericInputSplit.getTotalNumberOfSplits() > 1){
            searchSourceBuilder.slice(new SliceBuilder(genericInputSplit.getSplitNumber(), genericInputSplit.getTotalNumberOfSplits()));
        }

        searchRequest = new SearchRequest(esProperties.getIndex());
        searchRequest.types(esProperties.getType());
        searchRequest.scroll(scroll);
        searchRequest.source(searchSourceBuilder);
        getRuntimeContext().addAccumulator("esReader", readCounter);
    }

    @Override
    public BaseStatistics getStatistics(BaseStatistics baseStatistics) throws IOException {
        return null;
    }

    @Override
    public void configure(Configuration configuration) {

    }
}
