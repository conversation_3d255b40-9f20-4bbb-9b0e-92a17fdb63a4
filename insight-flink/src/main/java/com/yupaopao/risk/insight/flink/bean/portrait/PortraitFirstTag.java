package com.yupaopao.risk.insight.flink.bean.portrait;

import lombok.Data;

import java.io.Serializable;
import java.util.TreeSet;

@Data
public class PortraitFirstTag implements Comparable, Serializable {

    private String code;
    private String name;
    private String createAt;
    private Double score;
    private String updateTime;
    private Integer riskLevel;
    private Integer freeDays = 0;
    private Integer riskTotal = 0;
    private String source;
    private TreeSet<PortraitSubTag> subtag = new TreeSet<>();

    @Override
    public int compareTo(Object o) {
        // 排序优先级: riskLevel>scope>riskTotal>name
        PortraitFirstTag other = (PortraitFirstTag) o;
        if (this.riskLevel.intValue()!=other.getRiskLevel().intValue()){
            return this.riskLevel.compareTo(other.getRiskTotal()) * -1;
        }else if (this.riskTotal.intValue() != other.getRiskTotal()){
            return this.riskTotal.compareTo(other.riskTotal) * -1;
        }else {
            return this.name.compareTo(other.getName()) * -1;
        }
    }

}
