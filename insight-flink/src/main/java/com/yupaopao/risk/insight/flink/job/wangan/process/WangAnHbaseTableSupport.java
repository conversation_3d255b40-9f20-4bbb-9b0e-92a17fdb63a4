package com.yupaopao.risk.insight.flink.job.wangan.process;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class WangAnHbaseTableSupport {

    private Map<String, HTable> cachceTable = new HashMap();

    public void putRow(String rowKey, String tableName, JSONObject jsonObject){
        try {
            Put put = setColumn(rowKey, jsonObject);
            if (Objects.nonNull(put)){
                getTable(tableName).put(put);
            }
        } catch (Exception e) {
            createTable(tableName);
            putRow(rowKey, tableName, jsonObject);
            log.error("wangan put hbase error:{},{}", tableName, jsonObject, e);
        }
    }

    private Put setColumn(String rowKey,JSONObject jsonObject) throws Exception{
        try {
            Put put = new Put(Bytes.toBytes(rowKey));
            Map map = jsonObject.toJavaObject(Map.class);
            map.forEach((k, v)->{
                put.addColumn(Bytes.toBytes("info"),Bytes.toBytes(k+""), Bytes.toBytes(v+""));
            });
            return put;
        } catch (Exception e) {
            throw e;
        }
    }

    public Map<String, Object> getRow(String tableName, String rowKey, List<String> columns) throws Exception {
        try {
            HTable hTable = getTable(tableName);
            if (Objects.isNull(hTable)|| StringUtils.isEmpty(rowKey)){
                log.warn("表或主键不存在:{},{}", tableName,rowKey);
                return new HashMap<>();
            }
            return HBaseUtil.getRow(rowKey, hTable, columns);
        } catch (Exception e) {
            log.error("hbase 查询错误:{}",rowKey, e);
           throw new Exception("hbase 查询错误:tableName="+tableName+",rowKey="+rowKey+",e="+e.getMessage());

        }
    }



    private HTable getTable(String tableName)throws Exception{
        HTable hTable = cachceTable.get(tableName);
        if (Objects.isNull(hTable)) {
            connectTable(tableName);
            hTable = cachceTable.get(tableName);
        }
        return hTable;
    }

    private synchronized void connectTable(String tableName)throws Exception{
        try {
            if (cachceTable.containsKey(tableName)){
                return;
            }
            Connection connection = HBaseUtil.createConnection(HBaseProperties.getProperties(PropertyType.HBASE));
            HTable table =  (HTable) connection.getTable(TableName.valueOf(HBaseConstants.NAMESPACE, tableName.replace(".","_")));
            if (Objects.nonNull(table)){
                cachceTable.put(tableName, table);
            }
        } catch (IOException e) {
           throw e;
        }
    }

    private void createTable(String tableName){
        log.info("开始创建表:{}", tableName);
        try {
            HBaseUtil.createTable(tableName.replace(".","_"),
                    HBaseUtil.createConnection(HBaseProperties.getProperties(PropertyType.HBASE)));
            HTable table = HBaseUtil.getTable(tableName.replace(".","_"),
                    HBaseUtil.createConnection(HBaseProperties.getProperties(PropertyType.HBASE)));
            cachceTable.put(tableName, table);
        } catch (IOException ex) {
            log.error("建表失败:{}", tableName, ex);
        }
    }


    public void close(){
        cachceTable.forEach((k, v)->{
            try {
                v.close();
            } catch (IOException e) {
               log.error("关闭hbase表错误:{}", k, e);
            }
        });
    }




}
