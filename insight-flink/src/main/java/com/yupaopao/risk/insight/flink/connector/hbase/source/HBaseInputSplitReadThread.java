package com.yupaopao.risk.insight.flink.connector.hbase.source;

import com.alibaba.lindorm.client.core.utils.Bytes;
import com.google.common.util.concurrent.RateLimiter;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.ResultScanner;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.filter.FilterList;

import java.io.IOException;
import java.util.concurrent.LinkedBlockingQueue;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-01-07 16:23
 *
 ***/

@Slf4j
@Getter
public class HBaseInputSplitReadThread extends Thread {

    private final HBaseSplit.BucketSplit split;
    private LinkedBlockingQueue<Result> queueRows;
    private final String otsTableName;
    private String tableId;
    private String tableType;
    private String[] columns;
    private HTable hTable;
    public volatile boolean exit = false;
    protected transient Scan scan = null;
    private long startTime = 0;

    //current read
    protected byte[] currentRow;
    /**
     * HBase iterator wrapper.
     */
    protected ResultScanner resultScanner = null;
    protected long scannedRows = 0;

    public transient RateLimiter hbaseReadLimiter;

    private FilterList filterList;

    public void setExitFlag(boolean exit){
        this.exit = exit;
    }

    public boolean isExit(){
        return this.exit;
    }

    public HBaseInputSplitReadThread(HBaseSplit.BucketSplit split,
                                     HTable hTable,
                                     LinkedBlockingQueue<Result> queueRows,
                                     String otsTableName,
                                     String tableId,
                                     String tableType,
                                     String[] columns,
                                     String threadName,
                                     FilterList... filters) {
        super(threadName);
        if (hTable == null) {
            throw new RuntimeException("htable cannot be null");
        }
        this.hTable = hTable;
        this.split = split;
        this.queueRows = queueRows;
        this.columns = columns;
        this.tableId = tableId;
        this.tableType = tableType;
        this.otsTableName = otsTableName;
        if (StringUtils.isNotEmpty(split.getStartKey())) {
            currentRow = Bytes.toBytes(split.getStartKey());
        } else {
            currentRow = HConstants.EMPTY_START_ROW;
        }

        log.info("fetch rows for table: {}, tableId: {}, threadName: {}", this.otsTableName, tableId,
                this.getName());
        startTime = System.currentTimeMillis();

        if (filters != null && filters.length > 0 && filters[0] != null && CollectionUtils.isNotEmpty(filters[0].getFilters())) {
            filterList = new FilterList(filters[0].getFilters());
        }

    }

    public Scan initScan() {
        Scan initScan = new Scan();
        initScan.setCaching(500);
        //        initScan.setCacheBlocks(false);
        if (filterList != null && CollectionUtils.isNotEmpty(filterList.getFilters())) {
            initScan.setFilter(filterList);
        }
        return initScan;
    }

    public void sleepPerRow(){

    }

    public void initRate(){

    }

    @Override
    public void run() {
        initRate();
        this.scan = initScan();
        if (StringUtils.isNotEmpty(split.getStartKey())) {
            scan.withStartRow(Bytes.toBytes(split.getStartKey()));
        }
        if (StringUtils.isNotEmpty(split.getEndKey())) {
            scan.withStopRow(Bytes.toBytes(split.getEndKey()));
        }

        //目前表都只有一个family : info
        byte[] family = Bytes.toBytes(HBaseConstants.COLUMN_FAMILY);
        if (columns != null) {
            for (String column : columns) {
                scan.addColumn(family, Bytes.toBytes(column));
            }
        }


        //读取hbase
        createResultScanner();
        if (resultScanner == null) {
            log.warn("create scanner error");
        }
        try {
            while (!isExit()) {
                try {
                    Result res = resultScanner.next();
                    if (res != null) {
                        scannedRows++;
                        currentRow = res.getRow();
                        sleepPerRow();
                        queueRows.put(res);
                        if (scannedRows % 1000000 == 0) {
                            log.info("current read rows: {}", scannedRows);
                        }
                    } else {
                        break;
                    }
                } catch (Exception e) {
                    log.warn("read data error in loop, scannedRows: {}",scannedRows);
                    scan.withStartRow(currentRow);
                    createResultScanner();
                    Result res = resultScanner.next();
                    if (res != null) {
                        scannedRows++;
                        currentRow = res.getRow();
                        sleepPerRow();
                        queueRows.put(res);
                        if (scannedRows % 1000000 == 0) {
                            log.info("current read rows: {}", scannedRows);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("read data error: ", e);
        } finally {
            setExitFlag(true);
            log.info("total row read: {}, cost: {} ms", scannedRows, (System.currentTimeMillis() - startTime));
            try {
                cleanThread();
            } catch (IOException e) {
                log.warn("close thread error: ", e);
            }
        }
    }


    private void createResultScanner() {
        try {
            resultScanner = hTable.getScanner(scan);
        } catch (IOException e) {
            log.error("create result scanner error ", e);
        }
    }

    private void cleanThread() throws IOException {
        log.info("thread finished split (scanned {} rows)", scannedRows);
        currentRow = null;
        try {
            if (resultScanner != null) {
                resultScanner.close();
            }
            if (hTable != null) {
                hTable.close();
            }
        } finally {
            resultScanner = null;
        }
    }

}
