//package com.yupaopao.risk.insight.flink.connector.ts.util;
//
//import com.alicloud.openservices.tablestore.ClientConfiguration;
//import com.alicloud.openservices.tablestore.SyncClient;
//import com.alicloud.openservices.tablestore.model.AlwaysRetryStrategy;
//import com.yupaopao.risk.insight.flink.property.connection.TsProperties;
//import lombok.extern.slf4j.Slf4j;
//
//import java.io.Serializable;
//
//@Slf4j
//public class TsClientFactory implements Serializable {
//
//    private TsProperties tsProperties;
//
//    private SyncClient client;
//
//    public TsClientFactory(TsProperties tsProperties) {
//        this.tsProperties = tsProperties;
//    }
//
//    public void setTsProperties(TsProperties tsProperties){
//        this.tsProperties = tsProperties;
//    }
//
//    public SyncClient getClient() {
//        if (client == null) {
//            synchronized (this) {
//                if (client == null) {
//                    ClientConfiguration clientConfiguration = new ClientConfiguration();
//                    // 设置建立连接的超时时间。
//                    clientConfiguration.setConnectionTimeoutInMillisecond(5000);
//                    // 设置socket超时时间。
//                    clientConfiguration.setSocketTimeoutInMillisecond(5000);
//                    // 设置重试策略，若不设置，采用默认的重试策略。
//                    clientConfiguration.setRetryStrategy(new AlwaysRetryStrategy(1, 2000));
//                    client = new SyncClient(tsProperties.getEndPoint(),
//                            tsProperties.getAccessKeyId(),
//                            tsProperties.getAccessKeySecret(),
//                            tsProperties.getInstanceName(), clientConfiguration, null);
//                    log.info("created ts client...");
//                }
//            }
//
//        }
//        return client;
//    }
//
//    public void close() {
//        if (client != null) {
//            String parentThread = "";
//            String currentThread = "";
//            if (Thread.currentThread().getThreadGroup().getParent() != null) {
//                parentThread = Thread.currentThread().getThreadGroup().getParent().getName();
//                currentThread = Thread.currentThread().getName();
//            }
//            log.info("start close client...cur: {}, parent: {}", currentThread, parentThread);
//            client.shutdown();
//        }
//    }
//
//
//}
