package com.yupaopao.risk.insight.flink.job.etl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.lindorm.client.core.utils.Bytes;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitFirstTag;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitSubTag;
import com.yupaopao.risk.insight.flink.connector.hbase.sink.HBaseBaseOutputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseSplit;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.flink.job.portrait.process.PortraitTagMergeSupport;
import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.meta.JobParams;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.ReduceFunction;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;
import org.apache.flink.types.Row;
import org.apache.flink.util.CollectionUtil;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.Put;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.TreeSet;

import static com.yupaopao.risk.insight.common.constants.HBaseConstants.COLUMN_FAMILY;

@Slf4j
public class PortraitResult {

    public static void main(String[] args) throws Exception {

        JobParams jobParams = new JobParams();
        List<JobParams.TableQueryDatePeriod> periods = new ArrayList<>();
        JobParams.TableQueryDatePeriod datePeriod = new JobParams.TableQueryDatePeriod();
        datePeriod.setBeginDate(InsightDateUtils.getAfterDayStr(-Integer.valueOf(PortraitApolloProperties.getConfigByKey("portrait.history.tag.agg.days", "7"))));
        datePeriod.setEndDate(InsightDateUtils.getAfterDayStr(1));
        periods.add(datePeriod);
        jobParams.setDatePeriodList(periods);


        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        HBaseProperties hBProperties = HBaseProperties.getProperties(PropertyType.HBASE);

        TsTableInfo tagInfo = new TsTableInfo();
        JobParams.TableQueryDatePeriod tableQueryDatePeriod = new JobParams.TableQueryDatePeriod();
        tableQueryDatePeriod.setBeginDate(jobParams.getDatePeriodList().get(0).getBeginDate());
        tableQueryDatePeriod.setEndDate(jobParams.getDatePeriodList().get(0).getEndDate());
        tableQueryDatePeriod.setTableName("portrait_tag_etl");
        tagInfo.setDatePeriod(tableQueryDatePeriod);
        tagInfo.setTableName("portrait_tag_etl");
        DataType[] dataTypes = new DataType[3];
        dataTypes[0] = DataTypes.STRING();
        dataTypes[1] = DataTypes.STRING();
        dataTypes[2] = DataTypes.STRING();
        HBaseBaseOutputFormat hBaseBaseOutputFormat = new HBaseBaseOutputFormat("risk_portrait_result", hBProperties) {
            @Override
            public void writeRecord(Object record) throws IOException {
                try {
                    Tuple3<Long, String, TreeSet<PortraitFirstTag>> value = (Tuple3<Long, String, TreeSet<PortraitFirstTag>>) record;
                    if (Objects.nonNull(value.f2) && !value.f2.isEmpty()) {
                        Put put = new Put(Bytes.toBytes(value.f0 + ""));
                        byte[] columnFamily = Bytes.toBytes(COLUMN_FAMILY);
                        put.addColumn(columnFamily, Bytes.toBytes("uid"), Bytes.toBytes(value.f0 + ""));
                        put.addColumn(columnFamily, Bytes.toBytes("riskTag"), Bytes.toBytes(JSONObject.toJSONString(value.f2)));
                        this.getMutator().mutate(put);
                        if (canFlush()) {
                            flush();
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    System.out.println("write record error: ,record: " + record);
//                    log.error("write record error: ,record: " + record, e);
                }
            }
        };
        FlinkMetaInfo flinkMetaInfo = new FlinkMetaInfo(new String[]{"userId", "date", "riskTags"}, dataTypes);
        HBaseInputFormat hBaseInputFormat = new HBaseInputFormat(tagInfo, hBProperties, flinkMetaInfo) {
            @Override
            public HBaseSplit[] createInputSplits(int minNumSplits) throws IOException {

                List<String> dateList = InsightDateUtils.getDateList(tableQueryDatePeriod.getBeginDate(), tableQueryDatePeriod.getEndDate(), InsightDateUtils.DATE_FORMAT_yyyyMMdd);
                HBaseSplit[] hBaseSplits = new HBaseSplit[dateList.size()];
                for (int i = 0; i < dateList.size(); i++) {
                    List<HBaseSplit.BucketSplit> list = new ArrayList();
                    HBaseSplit.BucketSplit bucketSplit = new HBaseSplit.BucketSplit();
                    bucketSplit.setStartKey(dateList.get(i) + "_!");
                    bucketSplit.setEndKey(dateList.get(i) + "_~");
                    list.add(bucketSplit);
                    HBaseSplit hBaseSplit = new HBaseSplit(0, JSONObject.toJSONString(list));
                    hBaseSplits[i] = hBaseSplit;
                }
                return hBaseSplits;
            }
        };
        env.createInput(hBaseInputFormat).flatMap(new FlatMapFunction<Row, Tuple3<Long, String, TreeSet<PortraitFirstTag>>>() {
            @Override
            public void flatMap(Row value, Collector<Tuple3<Long, String, TreeSet<PortraitFirstTag>>> out) throws Exception {
                try {
                    if (value.getArity() > 0) {
                        Object uid = value.getField(0);
                        //不处理 实时画像 (uid_current)的数据
                        if (StringUtils.isNumeric(uid + "")) {
                            Object timestamp = value.getField(1);
                            Object field2 = value.getField(2);
                            List<JSONObject> list = JSONObject.parseObject(field2 + "", List.class);
                            TreeSet<PortraitFirstTag> portraitFirstTags = new TreeSet<>();
                            if (!CollectionUtil.isNullOrEmpty(list)) {
                                for (JSONObject jsonObject : list) {
                                    JSONArray twoTagBean1 = jsonObject.getJSONArray("subtag");
                                    List<JSONObject> list1 = JSONObject.parseObject(JSONObject.toJSONString(twoTagBean1), List.class);
                                    TreeSet<PortraitSubTag> portraitTwoTags = new TreeSet<>();
                                    if (!CollectionUtil.isNullOrEmpty(list1)) {
                                        for (JSONObject o : list1) {
                                            PortraitSubTag portraitTwoTag = JSONObject.parseObject(JSONObject.toJSONString(o), PortraitSubTag.class);
                                            portraitTwoTags.add(portraitTwoTag);
                                        }
                                    }
                                    PortraitFirstTag portraitFirstTag = JSONObject.parseObject(JSONObject.toJSONString(jsonObject), PortraitFirstTag.class);
                                    portraitFirstTag.setSubtag(portraitTwoTags);
                                    portraitFirstTags.add(portraitFirstTag);
                                }
                            }
                            if (Objects.nonNull(timestamp) && Objects.nonNull(portraitFirstTags) && !portraitFirstTags.isEmpty()){
                                out.collect(new Tuple3(Long.valueOf(uid + ""), timestamp, portraitFirstTags));
                            }else {
//                                log.warn("无效数据:{}", value);
                            }

                        }
                    }

                } catch (Exception e) {
                    log.warn("flatMap:{}",value,e);
                }
            }
        }).groupBy(0).reduce(new ReduceFunction<Tuple3<Long, String, TreeSet<PortraitFirstTag>>>() {
            @Override
            public Tuple3<Long, String, TreeSet<PortraitFirstTag>> reduce(Tuple3<Long, String, TreeSet<PortraitFirstTag>> value1, Tuple3<Long, String, TreeSet<PortraitFirstTag>> value2) throws Exception {
                Tuple3<Long, String, TreeSet<PortraitFirstTag>> mergeResult = new Tuple3<>();
                if (StringUtils.isEmpty(value1.f1) || Objects.isNull(value1.f2)) {
                    return value2;
                }
                if (StringUtils.isEmpty(value2.f1) || Objects.isNull(value2.f2)) {
                    return value1;
                }
                mergeResult.f0 = value1.f0;
                if (InsightDateUtils.getDateFromString(value1.f1, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd).before(InsightDateUtils.getDateFromString(value2.f1, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd))) {
                    mergeResult.f1 = value1.f1;
                    TreeSet<PortraitFirstTag> newTags = value2.f2;
                    mergeResult.f2 = PortraitTagMergeSupport.mergeTag(value1, value2);
                } else {
                    TreeSet<PortraitFirstTag> newTags = value1.f2;
                    mergeResult.f2 = PortraitTagMergeSupport.mergeTag(value2, value1);
                    mergeResult.f1 = value2.f1;
                }
                return mergeResult;
            }
        }).output(hBaseBaseOutputFormat);
        env.execute("portrait");
    }
}
