package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.connector.scheduler.IntervalFlushSchedulerTools;
import com.yupaopao.risk.insight.flink.connector.scheduler.IntervalFlushTask;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.configuration.Configuration;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.BufferedMutator;
import org.apache.hadoop.hbase.client.BufferedMutatorParams;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.RetriesExhaustedWithDetailsException;

import java.io.IOException;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.atomic.LongAdder;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-01-10 11:27
 *
 ***/

@Slf4j
@Getter
public abstract class HBaseBaseOutputFormat<IT> extends RichOutputFormat<IT>
        implements BufferedMutator.ExceptionListener, IntervalFlushTask {

    private HBaseProperties hBaseProperties;
    private String tableName;
    private transient Connection conn;
    private transient BufferedMutator mutator;
    private final long bufferFlushMaxSizeInBytes = 64 * 1024 * 1024;
    private final long bufferFlushMaxMutations = 2048; //最大缓存条数
    private transient LongAdder numPendingRequests;

    private boolean canIntervalFlush = false;

    private LongCounter  writerCounter = new LongCounter();

    //定时刷新数据
    private transient ScheduledThreadPoolExecutor scheduler;

    public HBaseBaseOutputFormat(String tableName, HBaseProperties hBaseProperties) {
        this.tableName = tableName;
        this.hBaseProperties = hBaseProperties;
        hBaseProperties.setWriteBuffer(String.valueOf(bufferFlushMaxSizeInBytes));
    }

    @Override
    public void configure(Configuration parameters) {

    }

    /***
     * open connection and create mutator
     * @param taskNumber
     * @param numTasks
     * @throws IOException
     */
    @Override
    public void open(int taskNumber, int numTasks) throws IOException {
        createConnection();
        BufferedMutatorParams params = new BufferedMutatorParams(TableName.valueOf(HBaseConstants.NAMESPACE, this.tableName))
                .listener(this)
//                .writeBufferSize(bufferFlushMaxSizeInBytes)
                ;
        this.mutator = conn.getBufferedMutator(params);
        this.numPendingRequests = new LongAdder();
        numPendingRequests.reset();

        if (hBaseProperties.isNeedFlushInterval() && hBaseProperties.getFlushIntervalInMillis() != null && hBaseProperties.getFlushIntervalInMillis() > 0) {
            this.scheduler =
                    IntervalFlushSchedulerTools.createScheduler(taskNumber + "-write-hbase-interval-" + tableName,
                            hBaseProperties.getFlushIntervalInMillis(), this);
        }
        String counterName = "hbseWriteCounter-" + taskNumber;
        if (getRuntimeContext().getAccumulator(counterName) == null) {
            getRuntimeContext().addAccumulator(counterName, writerCounter);
        }
    }


    @Override
    public void close() throws IOException {
        flush();
        HBaseUtil.closeResource(mutator);
        HBaseUtil.closeResource(conn);
        IntervalFlushSchedulerTools.closeScheduler(scheduler);
    }

    public boolean canFlush() {
        numPendingRequests.increment();
        return bufferFlushMaxMutations > 0 &&  numPendingRequests.longValue()>= bufferFlushMaxMutations;
    }

    public void flush() throws IOException {
        //有scheduler定时器更新，多线程需要注意相关字段设置
        long beforeFlushReq = numPendingRequests.longValue();
        mutator.flush();
        numPendingRequests.add(beforeFlushReq * -1);
        this.getWriterCounter().add(beforeFlushReq);
    }


    private Connection createConnection() throws IOException {
        if (conn != null) {
            return conn;
        }
        conn = HBaseUtil.createConnection(hBaseProperties);
        return conn;
    }


    //batch write exception
    @Override
    public void onException(RetriesExhaustedWithDetailsException e, BufferedMutator bufferedMutator) throws RetriesExhaustedWithDetailsException {
        log.warn("write to hbase error: table: " + tableName, e);
        for (int i = 0; i < e.getNumExceptions(); i++) {
            log.info("insert failed: {} ", e.getRow(i));
        }
    }

    @Override
    public void run() {
        try {
            flush();
        } catch (IOException e) {
            log.error("flush with scheduler interval error: ", e);
        }
    }


}
