package com.yupaopao.risk.insight.flink.connector.hbase.source;

import com.alibaba.lindorm.client.core.utils.Bytes;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.java.typeutils.TypeExtractor;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.source.InputFormatSourceFunction;

import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc HBaseStreamSource
 * @date 2020/7/22
 */
@Slf4j
public class HBaseStreamSource<T> extends InputFormatSourceFunction<T> implements CheckpointedFunction {
    private static final long serialVersionUID = -248098283258340500L;
    private ListState<String> checkpointState;
    private BaseHBaseSingleInputFormat<T> inputFormat;

    public HBaseStreamSource(BaseHBaseSingleInputFormat<T> inputFormat) {
        super(inputFormat, TypeExtractor.getInputFormatTypes(inputFormat));
        this.inputFormat = inputFormat;
    }

    @Override public void snapshotState(FunctionSnapshotContext context) throws Exception {
        if (null != inputFormat.currentRow) {
            String row = Bytes.toString(inputFormat.currentRow);
            log.info("save row: {}", row);
            checkpointState.clear();
            checkpointState.add(row);
        } else {
            checkpointState.add("");
        }
        LongCounter hbaseReadRecords = getRuntimeContext().getLongCounter("hbaseReadRecords");
        if (hbaseReadRecords != null) {
            checkpointState.add(hbaseReadRecords.getLocalValue().toString());
        } else {
            checkpointState.add("0");
        }
    }

    @Override public void initializeState(FunctionInitializationContext context) throws Exception {
        ListStateDescriptor<String> hbaseRow = new ListStateDescriptor<>("hbaseRow", String.class);
        checkpointState = context.getOperatorStateStore().getListState(hbaseRow);

        if (context.isRestored()) {
            if (!Objects.isNull(checkpointState)) {
                List<String> state = Lists.newArrayList(checkpointState.get().iterator());
                if (StringUtils.isNotBlank(state.get(0))) {
                    inputFormat.setCurrentRow(Bytes.toBytes(state.get(0)));
                }
                Long count = Long.parseLong(state.get(1));
                LongCounter hbaseReadRecords = getRuntimeContext().getLongCounter("hbaseReadRecords");
                if (null != hbaseReadRecords) {
                    hbaseReadRecords.add(count);
                } else {
                    getRuntimeContext().addAccumulator("hbaseReadRecords", new LongCounter(count));
                }
            }
        }
    }
}
