/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.yupaopao.risk.insight.flink.job.etl.common.connect.kafkanew.core.serialization;

import com.dtstack.flinkx.decoder.IDecode;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.kafkanew.core.decoder.JsonDecoder;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.kafkanew.core.decoder.TextDecoder;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.Charset;
import java.util.Date;
import java.util.Map;

public class RowDeserializationSchema extends DynamicKafkaDeserializationSchema {

    protected static final Logger LOG = LoggerFactory.getLogger(RowDeserializationSchema.class);
    private static final long serialVersionUID = 2846279667136852272L;

    private final String encoding;

    private IDecode codec;

    private final String codecStr;

    /** kafka conf */
    public RowDeserializationSchema(String codec, String encoding) {
        super(1, null, null, null, null,
            false, null, null, false);
        this.codecStr = codec;
        this.encoding = encoding;
    }

    @Override public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<Map<String, Object>> collector) throws Exception {
        try {
            long timestamp = record.timestamp();
            beforeDeserialize(record);
            Map<String, Object> data = this.codec.decode(new String(record.value(), Charset.forName(encoding)));
            if (data.containsKey("source")) {
                data.put("data_source", record.topic());
            } else {
                data.put("source", record.topic());
            }
            if(!data.containsKey("createdAt")){
                String createdAt = InsightDateUtils.getDateStr(new Date(timestamp), InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
                data.put("createdAt", createdAt);
            }
            getRuntimeContext().getLongCounter(record.topic() + "_reader_count").add(1);
            collector.collect(data);
        } catch (Exception e) {
            // todo kafka 比较特殊这里直接对接脏数据即可
            dirtyDataCounter(record, e);
        }
    }

    @Override
    public void open(DeserializationSchema.InitializationContext context) {
        beforeOpen();
        if ("json".equals(codecStr)) {
            this.codec = new JsonDecoder();
        } else if ("text".equals(codecStr)) {
            this.codec = new TextDecoder();
        } else {
            throw new IllegalArgumentException("not support this type codec: " + codecStr);
        }
        LOG.info("[{}] open successfully, \ninputSplit = {}",
                this.getClass().getSimpleName(),
                "see other log");
    }
}
