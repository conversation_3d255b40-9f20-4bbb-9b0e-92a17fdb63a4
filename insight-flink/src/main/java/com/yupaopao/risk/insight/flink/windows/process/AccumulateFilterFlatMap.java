package com.yupaopao.risk.insight.flink.windows.process;

import com.yupaopao.risk.insight.common.beans.aggregate.AccumulateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;

import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc AccumulateFilterFlatMap
 * @date 2020/11/24
 */
@Slf4j
public class AccumulateFilterFlatMap implements FlatMapFunction<AccumulateResult, AccumulateResult> {
    private long lastFlashTime = 0L;
    private final Long SPAN = 60 * 1000L;
    private Map<String, AccumulateResult> cache = new HashMap<>();

    @Override public void flatMap(AccumulateResult value, Collector<AccumulateResult> out) throws Exception {
        long now = System.currentTimeMillis();
        if (value.getThresholds() != -1 && value.getResult() > value.getThresholds()) {
            cache.put(value.getKey(), value);
        }
        if (now - lastFlashTime > SPAN) {
            lastFlashTime = now;
            cache.values().forEach(item -> {
                String[] split = item.getKey().split("#");
                item.setKey(split[2]);
                out.collect(item);
            });
            log.info("发送异常流量信息条数：{}", cache.size());
            cache.clear();
        }
    }
}
