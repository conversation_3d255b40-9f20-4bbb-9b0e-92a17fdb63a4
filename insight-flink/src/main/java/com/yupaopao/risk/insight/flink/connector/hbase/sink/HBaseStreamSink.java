package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

import java.util.concurrent.ScheduledThreadPoolExecutor;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-01-06 19:33
 *
 ***/
@Slf4j
public class HBaseStreamSink extends RichSinkFunction<String> implements CheckpointedFunction {

    final CommonHBaseOutputFormat outputFormat;



    public HBaseStreamSink(CommonHBaseOutputFormat format) {
        this.outputFormat = format;
    }

    @Override
    public void snapshotState(FunctionSnapshotContext functionSnapshotContext) throws Exception {
        outputFormat.flush();
    }

    @Override
    public void initializeState(FunctionInitializationContext functionInitializationContext) throws Exception {

    }


    @Override
    public void invoke(String value, Context context) throws Exception {
        outputFormat.writeRecord(value);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        RuntimeContext ctx = getRuntimeContext();
        outputFormat.setRuntimeContext(ctx);
        outputFormat.open(ctx.getIndexOfThisSubtask(), ctx.getNumberOfParallelSubtasks());
    }

    @Override
    public void close() throws Exception {
        outputFormat.close();
    }
}
