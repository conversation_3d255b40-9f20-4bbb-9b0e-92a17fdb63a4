package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.flink.windows.AggTagDetail;
import org.apache.flink.api.common.functions.AggregateFunction;

public class TagCountFunction implements AggregateFunction<AggTagDetail, TagAggregateResult, TagAggregateResult> {

    @Override public TagAggregateResult createAccumulator() {
        TagAggregateResult result = new TagAggregateResult();
        result.setCount(0.0);
        return result;
    }

    @Override public TagAggregateResult add(AggTagDetail value, TagAggregateResult accumulator) {
        accumulator.setId(value.getId());
        accumulator.setGroupKey(value.getGroupKey());
        accumulator.setCount(accumulator.getCount() + 1);
        accumulator.setType(value.getType());
        Cat.logMetricForCount("tag.count");
        return accumulator;
    }

    @Override public TagAggregateResult getResult(TagAggregateResult accumulator) {
        return accumulator;
    }

    @Override public TagAggregateResult merge(TagAggregateResult a, TagAggregateResult b) {
        return a;
    }
}
