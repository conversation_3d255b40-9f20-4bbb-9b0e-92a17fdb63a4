/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yupaopao.risk.insight.flink.job.etl.common.options;

import com.yupaopao.risk.insight.flink.utils.MapUtil;
import scala.collection.mutable.HashMap;

import java.util.List;
import java.util.Map;

public class MapperConfig extends AbstractConfig {

    private static final long serialVersionUID = -2278532550336483480L;
    public static String KEY_WRITER_NAME = "name";
    public static final String KEY_COLUMN_LIST = "column";
    public static final String KEY_GROOVY = "groovy";
    public static final String KEY_IDEMPOTENT = "idempotentKey";
    public static final String KEY_CACHE = "cacheTime";
    public static final String KEY_DUBBO = "dubbo";

    private Map<String, Object> parameter;

    public MapperConfig(Map<String, Object> map) throws Exception {
        super(map);
        parameter = map;
    }

    public String getName() {
        return getStringVal(KEY_WRITER_NAME);
    }

    public void setName(String name) {
        setStringVal(KEY_WRITER_NAME, name);
    }

    public Map<String, Object> getParameter() {
        return parameter;
    }

    public void setParameter(Map<String, Object> parameter) {
        this.parameter = parameter;
    }

}
