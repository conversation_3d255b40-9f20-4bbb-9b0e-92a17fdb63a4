package com.yupaopao.risk.insight.flink.job.processor.graph.historyData;

import com.alibaba.fastjson.JSON;
import com.alibaba.lindorm.client.core.utils.Bytes;
import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseFullTableInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseInputSplitReadThread;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseSplit;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.filter.BinaryComparator;
import org.apache.hadoop.hbase.filter.Filter;
import org.apache.hadoop.hbase.filter.FilterList;
import org.apache.hadoop.hbase.filter.SingleColumnValueFilter;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;

import static com.yupaopao.risk.insight.common.constants.HBaseConstants.HBASE_FAMILY_KEY;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-23 20:25
 *
 ***/

@Slf4j
public class VertexPropertyEmptyProcessor implements FlinkJobBuilder.MainProcessor {

    @Override
    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
        LinkedBlockingQueue<Result> cache = new LinkedBlockingQueue<>(1024 * 10);

        HBaseProperties hBaseProperties = HBaseProperties.getProperties(PropertyType.HBASE);
        ClickHouseProperties clickHouseProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);

        ClickHouseStreamSink ckSink = new ClickHouseStreamSink(clickHouseProperties, "gdb_vertex_prop_empty", 60000,
                50000
                , false);

        TsTableInfo tsInTable = new TsTableInfo()
                .withTableName("g_vertex")
                .withBucketPerSplit(4)
                .withColumnNameType("label#String,p_createdAt#Long,p_cityId#String")
                .withTableType("SYSTEM");


        env.createInput(new HBaseFullTableInputFormat(tsInTable, hBaseProperties, cache) {

            @Override
            public String parseHBaseResult(Result rowResult) {
                getReaderCounter().add(1);
                if (rowResult == null) {
                    return "{}";
                }
                try {

                    String label = getHBaseString(rowResult, "label");
                    if ("user".equals(label)) {
                        if(getHBaseLong(rowResult, "p_createdAt")==null){
                            Map<String,String> data = new HashMap<>();
                            data.put("id",Bytes.toString(rowResult.getRow()));
                            data.put("label","user");
                            data.put("createdAt", InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));
                            Cat.logMetricForCount("user.prop.empty");
                            return JSON.toJSONString(data);
                        }

                    } else if ("device".equals(label)) {
//                        vertexInfo = new VertexInfo(vertexId, label, null);
                    } else if ("ip".equals(label)) {
                         if(getHBaseLong(rowResult, "p_cityId")==null){
                             Map<String,String> data = new HashMap<>();
                             data.put("id",Bytes.toString(rowResult.getRow()));
                             data.put("label","ip");
                             data.put("createdAt", InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));

                             Cat.logMetricForCount("ip.prop.empty");
                             return JSON.toJSONString(data);
                         }
                    }
                    return "{}";
                } catch (Exception e) {
                    log.warn("parse hbase data error, res: " + rowResult);
                }
                return "{}";
            }

            private String getHBaseString(Result rowResult, String column) {
                byte[] columnValue = rowResult.getValue(HBASE_FAMILY_KEY, Bytes.toBytes(column));
                if (columnValue == null) {
                    return null;
                }
                return Bytes.toString(columnValue);
            }

            private Long getHBaseLong(Result rowResult, String column) {
                byte[] columnValue = rowResult.getValue(HBASE_FAMILY_KEY, Bytes.toBytes(column));
                if (columnValue == null) {
                    return null;
                }
                BigDecimal decimal = Bytes.toBigDecimal(columnValue);
                return decimal.longValue();
            }

            private Boolean getHBaseBoolean(Result rowResult, String column) {
                byte[] columnValue = rowResult.getValue(HBASE_FAMILY_KEY, Bytes.toBytes(column));
                if (columnValue == null) {
                    return null;
                }
                return Bytes.toBoolean(columnValue);
            }

            @Override
            public HBaseInputSplitReadThread initReadThread(HBaseSplit.BucketSplit bucket, HTable currentTable, String hbaseTableName, String threadName, TsTableInfo tsTableInfo, LinkedBlockingQueue<Result> queue) {
                HBaseInputSplitReadThread readThread = new HBaseInputSplitReadThread(bucket,
                        currentTable,
                        queue,
                        hbaseTableName,
                        tsTableInfo.getTableId(),
                        tsTableInfo.getTableType(),
                        tsTableInfo.getColumnNames(),
                        threadName
                ) {
                    public Scan initScan() {
                        Scan scan = super.initScan();
                        List<Filter> filters = new ArrayList();


                        //过滤event
                        SingleColumnValueFilter filterLogin =
                                new SingleColumnValueFilter(HBaseConstants.HBASE_FAMILY_KEY,
                                        org.apache.hadoop.hbase.util.Bytes.toBytes("label"), CompareOperator.NOT_EQUAL,
                                        new BinaryComparator(org.apache.hadoop.hbase.util.Bytes.toBytes("device")));
                        filters.add(filterLogin);

                        FilterList filterList = new FilterList(filters);
                        scan.setFilter(filterList);
                        return scan;
                    }

//                    @Override
//                    public void sleepPerRow() {
//                        this.hbaseReadLimiter.acquire();
//                    }
//                    public void initRate(){
//                        this.hbaseReadLimiter = RateLimiter.create(rate);
//                    }
                };
                return readThread;
            }
        }).name("read_hbase[g_vertex]").filter(elem->!HBaseUtil.emptyResultJson(elem))
//                        .setParallelism(1)
               .addSink(ckSink);
    }
}
