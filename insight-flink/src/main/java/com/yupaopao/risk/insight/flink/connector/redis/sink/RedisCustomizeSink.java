package com.yupaopao.risk.insight.flink.connector.redis.sink;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dtstack.flinkx.constants.Metrics;
import com.google.common.util.concurrent.RateLimiter;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.FactorTimeProperties;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.flink.constants.FactorConstants;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.flink.windows.FactorCaches;
import com.yupaopao.risk.insight.flink.windows.aggregate.AggregateResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.flink.api.common.accumulators.Accumulator;
import org.apache.flink.api.common.accumulators.IntCounter;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Gauge;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.util.Preconditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Pipeline;

import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class RedisCustomizeSink extends RichSinkFunction<AggregateResult> {

    private static Logger LOG = LoggerFactory.getLogger(RedisCustomizeSink.class);

    private RedisProperties redisProperties;
    //    private Jedis jedis;
    private JedisPool jedisPool;

    private Map<String, Tuple2<Double, Long>> minMap = new HashMap<>(2048);
    private Map<String, Tuple2<Double, Long>> hourMap = new HashMap<>(2048);
    private Map<String, Tuple2<Double, Long>> dayMap = new HashMap<>(2048);
    private long minLastTimeStamp = 0;
    private long hourLastTimeStamp = 0;
    private long dayLastTimeStamp = 0;

    private transient volatile Map<String, RateLimiter> rateLimiterMap;

    private volatile String currentFlushRatio;

    public RedisCustomizeSink(RedisProperties redisProperties) {
        Preconditions.checkNotNull(redisProperties, "Redis connection pool config should not be null");
        this.redisProperties = redisProperties;
    }

    @Override
    public void invoke(AggregateResult input, Context context) throws Exception {
        FactorTimeProperties instance = FactorTimeProperties.getInstance();

        String[] split = input.getKey().split("#");
        split[0] = "FACTOR-DATA";
        input.setKey(String.join("#", split));
        if (input.isPurge()) {
            removeKey(input);
            return;
        }
        String factorId = split[1];
        if (split.length > 1 && instance.getRealTimeList().size() > 0 && instance.getRealTimeList().contains(factorId)) {
            flush(input.getKey(), input.getResult(), getExpireTime(factorId, input.getTimeSpan()));
            return;
        }

        if (input.getResult() == -1 && input.getDistinct() != null) {
            if (input.getDistinct().size() < 35) {
                flushAllList(input.getKey(), getExpireTime(factorId, input.getTimeSpan()), input.getDistinct());
            }
            return;
        }

        if (input.getTimeSpan() <= 60) {
            minMap.put(input.getKey(), new Tuple2<>(input.getResult(), getExpireTime(factorId, input.getTimeSpan())));
        } else if (input.getTimeSpan() <= 1440) {
            hourMap.put(input.getKey(), new Tuple2<>(input.getResult(), getExpireTime(factorId, input.getTimeSpan())));
        } else {
            dayMap.put(input.getKey(), new Tuple2<>(input.getResult(), getExpireTime(factorId, input.getTimeSpan())));
        }

        long now = System.currentTimeMillis();

        if (now - minLastTimeStamp > instance.getMinuteSinkSpan()) {
            minLastTimeStamp = now;
            flush(minMap, "minute");
        }
        if (now - hourLastTimeStamp > instance.getHourSinkSpan()) {
            hourLastTimeStamp = now;
            flush(hourMap, "hour");
        }
        if (now - dayLastTimeStamp > instance.getDaySinkSpan()) {
            dayLastTimeStamp = now;
            flush(dayMap, "day");
        }

    }

    private void removeKey(AggregateResult input) {
        try (Jedis jedis = jedisPool.getResource()) {
            minMap.remove(input.getKey());
            hourMap.remove(input.getKey());
            dayMap.remove(input.getKey());
            Long del = jedis.del(input.getKey());
            LOG.info("移除key: {}, 条数：{}", input.getKey(), del);
        }
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(redisProperties.getMaxIdle());
        genericObjectPoolConfig.setMaxTotal(redisProperties.getMaxActive());
        genericObjectPoolConfig.setMinIdle(redisProperties.getMinIdle());

        jedisPool = new JedisPool(genericObjectPoolConfig,
                redisProperties.getHost(),
                redisProperties.getPort(),
                FactorConstants.REDIS_DEFAULT_TIMEOUT,
                redisProperties.getPassword(),
                redisProperties.getDatabase());

        createNewRateLimiter();
        LOG.info("init rateLimiter: {}", currentFlushRatio);


        // init metrics

        metricNames.stream().forEach(elem -> {
            LongCounter acc = new LongCounter();
            getRuntimeContext().addAccumulator(elem,acc);
            getRuntimeContext().getMetricGroup().addGroup("redisSink").gauge(elem,
                    new CustomAccumulatorGauge(acc));
        });

    }

    @Override
    public void close() throws IOException {
        flush(dayMap, "day");
        flush(hourMap, "hour");
        flush(minMap, "minute");
        if (jedisPool != null) {
            jedisPool.close();
        }
        FactorCaches.closeFactorCache();
    }

    private void flush(Map<String, Tuple2<Double, Long>> map, String mapType) {
        try (Jedis jedis = jedisPool.getResource(); Pipeline pipeline = jedis.pipelined();) {
            int redisCount = 0;
            for (Map.Entry<String, Tuple2<Double, Long>> entry : map.entrySet()) {
                if (entry.getValue().f1.intValue() <= 0) {
                    LOG.info("{} 缓存时间为负数 {}", entry.getKey(), entry.getValue().f1);
                    continue;
                }
                String functionType = getFunctionType(entry.getKey());
                RateLimiter limiter = getRateLimiter(functionType);
                String waitLimit = PortraitApolloProperties.getConfigByKey("factor_cal.rateLimiter.waitTime", "100");
                boolean acquired = false;
                if (limiter != null) {
                    acquired = limiter.tryAcquire(1, Long.valueOf(waitLimit), TimeUnit.MILLISECONDS);
                }
                if (!acquired) {
                    Cat.logMetricForCount("factor.redis.count.falseAcquired." + functionType);
                    try {
                        TimeUnit.MILLISECONDS.sleep(Long.valueOf(waitLimit));
                    } catch (InterruptedException e) {

                    }
                }
                LOG.info("更新聚合数据： {} - {} - {}", entry.getKey(), entry.getValue().f0, entry.getValue().f1);
                Cat.logMetricForCount("factor.redis.count." + functionType);
                Cat.logMetricForCount("factor.redis.count.factorId." + mapType);
                Cat.logMetricForCount("factor.redis.count");
                getRuntimeContext().getAccumulator("factor.redis.count." + functionType).add(1L);
                getRuntimeContext().getAccumulator("factor.redis.count").add(1L);
                redisCount++;
                pipeline.setex(entry.getKey(), entry.getValue().f1.intValue(), String.valueOf(entry.getValue().f0));
                if (redisCount % getPipelineBatchSize() == 0) {
                    pipeline.sync();
                }
            }
            pipeline.sync();
            map.clear();
        }
    }

    private String getFunctionType(String key) {
        if (StringUtils.isEmpty(key)) {
            return "null";
        }
        return FactorCaches.getFunctionType(key.split("#")[1]);
    }

    private int getPipelineBatchSize() {
        String strSize = ApolloProperties.getConfigStr("risk_factor_cal.pipeline_batch_size");
        if (StringUtils.isEmpty(strSize)) {
            return 20;
        } else {
            return Integer.valueOf(strSize);
        }
    }


    private void flush(String key, Double value, Long span) {
        try (Jedis jedis = jedisPool.getResource();) {
            LOG.info("实时更新聚合数据： {} - {} - {}", key, value, span);
            Cat.logMetricForCount("factor.redis.count");
            jedis.setex(key, span.intValue(), String.valueOf(value));
        }
    }

    private void flushAllList(String key, Long span, Set<String> values) {
        // 是否需要 script
        try (Jedis jedis = jedisPool.getResource(); Pipeline p = jedis.pipelined();) {
            List<String> update = new ArrayList<>();
            Map<String, Integer> cache = new HashMap<>();
            List<String> list = jedis.lrange(key, 0, -1);
            Cat.logMetricForCount("factor.redis.count.DISTINCT.get");
            getRuntimeContext().getAccumulator("factor.redis.count.DISTINCT.get").add(1L);
            for (String s : list) {
                if (!values.contains(s)) {
                    p.lrem(key, 1, s);
                    Cat.logMetricForCount("factor.redis.count.DISTINCT.remove");
                } else {
                    cache.put(s, 0); //redis中包含的数据
                }
            }
            for (String data : values) {
                if (!cache.containsKey(data)) {
                    update.add(data);
                }
            }
            LOG.info("更新聚合数据： {} - {}", key, span);
            if (update.size() > 0) {
                p.lpush(key, update.toArray(new String[]{}));
                Cat.logMetricForCount("factor.redis.count.DISTINCT.push");
            }
            p.expire(key, span.intValue());
            p.sync();
        }
    }


    /***
     *
     * @param factorId 累计因子id
     * @param timeSpan 窗口时间，分钟级别
     * @return
     */
    private long getExpireTime(String factorId, long timeSpan) {
        String strSlideNum = PortraitApolloProperties.getConfigByKey("factor.slide.avg.count", "10");
        Long slideNum = Long.valueOf(strSlideNum);
        Integer windowType = FactorCaches.getWindowType(factorId);
        if (windowType != null && windowType == 1) {
            //滑动窗口时间为滑动间隔*1.05
            return (long) (timeSpan * 60 * 1.05 / slideNum);
        }
        return timeSpan * 60;
    }


    /***
     * 每个类型的累计因子数量不同，峰值量也不一定相同，如果只有一个rateLimiter，平均设置qps时可能要考虑以最大量的哪个类型的qps为标准，否则最大qps的那一类数据一直积压：
     * eg，总的5wqps，20slot，7个不同类型也就7个线程, 平均是357.14，实际可能count的qps平时就达到了500qps，这样这个slot的线程就一直积压，导致checkpoint也会变慢
     * @param functionType 累计函数类型 COUNT/COUNT_DISTINCT/MAX/SUM/MIN/DISTINCT/AVG
     * @return
     */
    private RateLimiter getRateLimiter(String functionType) {
        if (flushRateChanged()) {
            //rate调整了，重新构建rateLimiter
            synchronized (this) {
                if (flushRateChanged()) {
                    createNewRateLimiter();
                }
            }
        }
        return rateLimiterMap.get(functionType);
    }

    private boolean flushRateChanged() {
        String oldFlushRatio = currentFlushRatio;
        String newFlushRatio = FactorTimeProperties.getInstance().getRedisFlushPerSecond();
        boolean changed = !oldFlushRatio.equals(newFlushRatio);
        if (changed) {
            LOG.info("old ratio: {}, new ratio: {}", oldFlushRatio, newFlushRatio);
        }
        return changed;
    }

    private void createNewRateLimiter() {
        String ratio = FactorTimeProperties.getInstance().getRedisFlushPerSecond();
        //map形式： ...{"COUNT":"500","SUM":"500"}
        Map<String, Object> ratioMap = JSON.parseObject(ratio, Map.class);
        Map<String, RateLimiter> newRateLimiterMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : ratioMap.entrySet()) {
            LOG.info("create rateLimiter for {} with {}", entry.getKey(), entry.getValue());
            newRateLimiterMap.put(entry.getKey(), RateLimiter.create(Double.valueOf(entry.getValue().toString())));
        }
        rateLimiterMap = newRateLimiterMap;
        this.currentFlushRatio = ratio;
        LOG.info("new flush rateLimiter ratio: {}", ratio);
    }


    public class CustomAccumulatorGauge<T extends Serializable> implements Gauge<T> {
        private Accumulator<T, T> accumulator;

        public CustomAccumulatorGauge(Accumulator<T, T> accumulator) {
            this.accumulator = accumulator;
        }

        public T getValue() {
            return this.accumulator.getLocalValue();
        }
    }



    private static List<String> metricNames = Arrays.asList(
            "factor.redis.count.COUNT",
            "factor.redis.count.COUNT_DISTINCT",
            "factor.redis.count.SUM",
            "factor.redis.count.MAX",
            "factor.redis.count.MIN",
            "factor.redis.count.AVG",
            "factor.redis.count.DISTINCT.get",
            "factor.redis.count"
    );


}
