package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.property.FactorTimeProperties;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.connector.mysql.model.Factor;
import com.yupaopao.risk.insight.flink.connector.redis.sink.client.RedisClient;
import com.yupaopao.risk.insight.flink.constants.FactorConstants;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class FactorBroadcastProcess extends BroadcastProcessFunction<String, List<Factor>, FactorCalDetail> {

    private static final long serialVersionUID = 8346674450140328082L;
    private static Logger LOGGER = LoggerFactory.getLogger(FactorBroadcastProcess.class);

    private static JedisPool jedisPool;

    private Jedis jedis;

    public final static MapStateDescriptor<Void, List<Factor>> CONFIG_DESCRIPTOR = new MapStateDescriptor<>("factors", Types.VOID,
            TypeInformation.of(new TypeHint<List<Factor>>() {}));

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        FactorTimeProperties.getInstance().initConfig();
        jedisPool = RedisClient.getClient();
        jedis = jedisPool.getResource();
    }

    @Override public void processElement(String value, ReadOnlyContext ctx, Collector<FactorCalDetail> out) throws Exception {
        List<Factor> factors = ctx.getBroadcastState(CONFIG_DESCRIPTOR).get(null);
        List<String> skipList = FactorTimeProperties.getInstance().getSkipList();
        for (String skip : skipList) {
            if (StringUtils.isNotEmpty(skip) && value.contains(skip)) {
                LOGGER.info("skip {}", skip);
                return;
            }
        }
        if (null == factors || factors.size() <= 0) {
            return;
        }
        JSONObject param = JSONObject.parseObject(value);
        if (param.containsKey("action")) {
            //reset
            if ("RESET".equals(param.getString("action"))) {
                assembleResetFactorCal(param, factors, out);
            }
        } else {
            // 正常累计
            assembleNormalFactorCal(param, factors, out);
        }
    }

    public static void main(String[] args) {
        String value = "{ \"factor\":[{\"factorId\":268},{\"factorId\":269},{\"factorId\":335}], " +
                "\"action\":\"RESET\", \"params\":{\"DeviceId\":\"202310201658162220c22656f1ec1edb55645a1961825d0122f9a244771ffb\"} } \n";

        JSONObject param = JSONObject.parseObject(value);
        List<Factor> factors = new ArrayList<>();
        Factor f = new Factor();
        f.setId(268);
        f.setGroupKey("DeviceId");
        f.setFunction("COUNT_DISTINCT##1");
        factors.add(f);
        assembleResetFactorCal(param, factors, new Collector<FactorCalDetail>() {
            @Override
            public void collect(FactorCalDetail record) {
                System.err.println(record);
            }

            @Override
            public void close() {

            }
        });
    }

    public void assembleNormalFactorCal(JSONObject param, List<Factor> factors, Collector<FactorCalDetail> out) {
        JSONObject data = param.getJSONObject("data");
        if (data == null || (data.containsKey("testFlag") && data.getBoolean("testFlag"))) {
            return;
        }
        data.put("RiskLevel", param.get("level"));
        data.put("Result", param.get("result"));
        data.put("kafkaMsgTime", param.get("kafkaMsgTime"));
        for (Factor factor : factors) {
            String groupKeyAndTime = factor.getGroupKey();
            String[] split = groupKeyAndTime.split("##");
            String[] functionSplit = factor.getFunction().split("##");
            long resetTime = 0;
            if (split.length == 2) {
                resetTime = Long.parseLong(split[1]);
            }
            if (checkCondition(data, factor)) {
                String aggValue = getAggValue(split[0], factor.getAggKey(), data);
                String groupKey = getGroupKey(split[0], data);
                if ((StringUtils.isNotEmpty(aggValue) || "COUNT".equals(functionSplit[0])) && StringUtils.isNotEmpty(groupKey)) {
                    FactorCalDetail result = new FactorCalDetail();

                    String factorKey = String.format(FactorConstants.FACTOR_DATA_PREFIX, factor.getId(), groupKey);
                    if (resetTime > 0) {
                        factorKey = resetTime + factorKey;
                    }
                    if (StringUtils.isNotEmpty(groupKey) && isIllegal(factorKey, factor.getId())) {
                        LOGGER.info("{} isIllegal", factorKey);
                        continue;
                    }

                    result.setGroupKey(factorKey);
                    result.setData(aggValue);
                    result.setFunction(functionSplit[0]);
                    result.setTimeSpan(factor.getTimeSpan());
                    result.setWindowType(functionSplit.length == 2 ? Integer.parseInt(functionSplit[1]) : 1);
                    result.setRequestTime(getEventTime(data));
                    out.collect(result);
                }
            }
        }
    }

    public Long getEventTime(JSONObject data) {
        Long requestTime = null;
        try {
            requestTime = data.getLong("riskRequestTime");
            if (requestTime == null) {
                requestTime = data.getLong("kafkaMsgTime");
            }
        } catch (Exception e) {
            requestTime = System.currentTimeMillis();
        }
        return requestTime == null ? System.currentTimeMillis(): requestTime;
    }

    @Override public void processBroadcastElement(List<Factor> value, Context ctx, Collector<FactorCalDetail> out) throws Exception {
        ctx.getBroadcastState(CONFIG_DESCRIPTOR).put(null, value);
    }

    public boolean checkCondition(JSONObject param, Factor factor) {
        if (StringUtils.isEmpty(factor.getCondition())) {
            return true;
        }

        try {
            Object result = ExpressionExecutor.execute(factor.getCondition(), param);
            LOGGER.debug("前置表达式执行结果:{} - {} - {}", result, factor.getCondition(), param);
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else {
                return result != null;
            }
        } catch (Throwable e) {
            LOGGER.error("执行前置表达式出错:" + factor.getCondition() + "\n" + param, e);
        }
        return false;
    }

    private static String getGroupKey(String groupKeys, JSONObject data){
        List<String> groupValues = new LinkedList<>();
        Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
        for (String groupKey : groupKeys.split(",")) {
            String groupValue = "";

            if (groupKey.contains("@")) {
                String[] split = groupKey.split("@");
                for (String key : split) {
                    if (dataMap.containsKey(key)) {
                        groupValue = dataMap.getOrDefault(key, "").toString();
                        break;
                    }
                }
            } else {
                groupValue = dataMap.getOrDefault(groupKey, "").toString();
            }
            // 缺失任意groupKeyValue则放弃
            if (StringUtils.isBlank(groupValue)) {
                return "";
            }
            groupValues.add(groupValue);
        }
        return String.join("$", groupValues);
    }


    private String getAggValue(String groupKey, String aggKey, JSONObject data){
        if(StringUtils.isEmpty(aggKey)){
            return "";
        } else {
            if (aggKey.contains(".")) {
                Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                if (aggKey.contains("@")) {
                    String[] split = aggKey.split("@");
                    for (String key : split) {
                        if (dataMap.containsKey(key)) {
                            return dataMap.getOrDefault(key, "").toString();
                        }
                    }
                    return "";
                } else {
                    return dataMap.getOrDefault(aggKey, "").toString();
                }
            } else if (aggKey.contains("@")) {
                String[] split = aggKey.split("@");
                for (String key : split) {
                    if (data.containsKey(key)) {
                        return data.getString(key);
                    }
                }
                return "";
            } else {
                return data.getString(aggKey);
            }
        }
    }

    public static void assembleResetFactorCal(JSONObject data, List<Factor> factors, Collector<FactorCalDetail> out) {
        try {
            JSONObject params = data.getJSONObject("params");
            JSONArray purgeFactor = data.getJSONArray("factor");
            for (int i = 0; i < purgeFactor.size(); i++) {
                JSONObject jsonObject = purgeFactor.getJSONObject(i);
                long factorId = jsonObject.getLong("factorId");
                factors.forEach(factor -> {
                    if (factor.getId() == factorId) {
                        String[] split = factor.getGroupKey().split("##");
                        String groupKey = getGroupKey(split[0], params);
                        if (StringUtils.isEmpty(groupKey)) {
                            LOGGER.error("累计因子 {} 清除失败，请检查参数: {}",factorId, data.toJSONString());
                            return;
                        }
                        String factorKey = String.format(FactorConstants.FACTOR_DATA_PREFIX, factor.getId(), groupKey);
                        long resetTime = 0;
                        if (split.length == 2) {
                            resetTime = Long.parseLong(split[1]);
                        }
                        if (resetTime > 0) {
                            factorKey = resetTime + factorKey;
                        }
                        String[] functionSplit = factor.getFunction().split("##");
                        FactorCalDetail result = new FactorCalDetail();
                        result.setPurge(true);
                        result.setFunction(functionSplit[0]);
                        result.setTimeSpan(factor.getTimeSpan());
                        result.setGroupKey(factorKey);
                        result.setData("0");
                        result.setRequestTime(System.currentTimeMillis());
                        out.collect(result);
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.error("清除累计因子异常：", e);
        }
    }

    private boolean isIllegal(String key, long factorId) {
        Map<Long, Long> factorLimitMap = FactorTimeProperties.getInstance().getFactorLimitMap();
        if (!factorLimitMap.containsKey(factorId)) {
            return false;
        }

        if (null == jedis) {
            jedis = jedisPool.getResource();
        }
        String value = jedis.get(key);
        if (StringUtils.isEmpty(value)) {
            return false;
        }
        double factorData = Double.parseDouble(value);
        boolean flag = factorData > factorLimitMap.get(factorId);
        if (flag) {
            LOGGER.info("get factor count {}", factorData);
        }
        return flag;
    }

    @Override public void close() throws Exception {
        super.close();
        RedisClient.closeJedis(jedis);
        RedisClient.closePool(jedisPool);
    }
}
