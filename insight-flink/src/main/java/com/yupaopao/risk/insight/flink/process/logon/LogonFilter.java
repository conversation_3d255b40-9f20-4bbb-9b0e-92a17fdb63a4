package com.yupaopao.risk.insight.flink.process.logon;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.connector.redis.sink.client.RedisClient;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFilterFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

public class LogonFilter extends RichFilterFunction<Tuple2<String, String>> {
    private static final long serialVersionUID = -8784266219641706542L;
    private JedisPool jedisPool;

    private String cacheEventCode;
    private Jedis jedis;

    public LogonFilter(String cacheEventCode) {
        this.cacheEventCode = cacheEventCode;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        jedisPool = RedisClient.getClient();
        jedis = jedisPool.getResource();
    }

    @Override
    public void close() throws Exception {
        super.close();
        RedisClient.closeJedis(jedis);
        RedisClient.closePool(jedisPool);
    }

    @Override
    public boolean filter(Tuple2<String, String> value) throws Exception {
        String eventCode = JSONObject.parseObject(value.f1).getString("eventCode");
        if (cacheEventCode.equals(eventCode)) {
            String cacheValue = jedis.get(FlinkConstants.FLINK_RISK_CK_REDIS_PREFIX + value.f0 + "post");
            if (StringUtils.isNotBlank(cacheValue)) {
                return true;
            }
            jedis.setex(FlinkConstants.FLINK_RISK_CK_REDIS_PREFIX + value.f0 + "before", 320, value.f1);
            return false;
        } else {
            return true;
        }
    }
}
