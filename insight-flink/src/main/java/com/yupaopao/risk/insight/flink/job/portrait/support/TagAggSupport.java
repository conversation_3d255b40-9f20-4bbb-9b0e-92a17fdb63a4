package com.yupaopao.risk.insight.flink.job.portrait.support;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import com.yupaopao.risk.insight.flink.bean.portrait.AggTag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc TagAggSupport
 * @date 2020/5/21
 */
@Slf4j
public class TagAggSupport {
    private static List<AggTag> aggTags = new ArrayList<>();

    private static long lastRefresh = -1;

    public static List<AggTag> getAggTagList() {
        if (aggTags == null || lastRefresh < System.currentTimeMillis() - 60 * 1000) {
            lastRefresh = System.currentTimeMillis();
            flush();
        }
        return aggTags;
    }

    public static AggTag getAggTagById(Integer id) {
        if (aggTags == null || lastRefresh < System.currentTimeMillis() - 60 * 1000) {
            lastRefresh = System.currentTimeMillis();
            flush();
        }
        AggTag aggTag = null;
        for (AggTag tag : aggTags) {
            if (id.equals(tag.getId())) {
                aggTag = tag;
                break;
            }
        }
        return aggTag;
    }

    public static List<AggTag> getAggTagByCodes(List<String> codes) {
        if (aggTags == null || lastRefresh < System.currentTimeMillis() - 60 * 1000) {
            lastRefresh = System.currentTimeMillis();
            flush();
        }
        List<AggTag> findTags = new ArrayList<>();
        for (AggTag tag : aggTags) {
            if (codes.contains(tag.getCode())) {
                findTags.add(tag);
            }
        }
        return findTags;
    }

    private static void flush() {
        List<AggTag> aggTagsTem = new ArrayList<>();

        Map<String, TagInfoBO> allTagInfo = TagCacheSupport.getAllTagInfo();
        for (Map.Entry<String, TagInfoBO> key : allTagInfo.entrySet()) {
            TagInfoBO value = key.getValue();
            if (!"1".equals(value.getSource())) {
                continue;
            }
            JSONObject dataContent = JSONObject.parseObject(value.getDataContent());
            if (Objects.isNull(dataContent)) {
                continue;
            }

            AggTag aggTag = new AggTag();
            aggTag.setName(value.getName());
            aggTag.setCode(value.getCode());
            aggTag.setId(value.getId());
            aggTag.setValueType(value.getValueType());

            aggTag.setGroupKey("Mobile".equals(dataContent.getString("groupKey")) ? "nationCode,Mobile" : dataContent.getString("groupKey"));
            aggTag.setAggKey(dataContent.getString("aggKey"));
            aggTag.setFunction(dataContent.getString("function"));
            aggTag.setTimeSpan(dataContent.getLong("timeSpan"));
            aggTag.setCondition(dataContent.getString("condition"));
            aggTag.setVersion(dataContent.getOrDefault("version", "").toString());
            String dataChannel = dataContent.getString("dataChannel");
            String alias = dataContent.getString("alias");
            if (StringUtils.isNotEmpty(alias)) {
                aggTag.setAlias(alias);
            }
            List<String> topics = new ArrayList<>();
            if (StringUtils.isNotEmpty(dataChannel)){
                topics = JSONObject.parseObject(dataChannel, List.class);
            }
            aggTag.setTopicList(topics);
            aggTagsTem.add(aggTag);
        }
//        log.info("flush tag success, size: {}", aggTagsTem.size());
        aggTags = aggTagsTem;
    }
}
