package com.yupaopao.risk.insight.flink.utils;

import java.io.*;

public class CKUtils {

    private static String getStringValue(File file) throws Exception{
        InputStream in = new BufferedInputStream(new FileInputStream(file));
        BufferedReader br
                = new BufferedReader(new InputStreamReader(in));
        String value = "";
        StringBuffer sb = new StringBuffer();
        while((value = br.readLine()) != null && !"".equals(value)) {
            sb.append(value).append(" ");
        }
        in.close();
        br.close();
        return sb.toString();

    }

}
