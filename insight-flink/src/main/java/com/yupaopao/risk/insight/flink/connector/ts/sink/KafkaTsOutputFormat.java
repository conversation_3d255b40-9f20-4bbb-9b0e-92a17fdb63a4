//package com.yupaopao.risk.insight.flink.connector.ts.sink;
//
//import com.alicloud.openservices.tablestore.model.Row;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsWriter;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TypeConvertUtils;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.flink.configuration.Configuration;
//
//import java.io.IOException;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.concurrent.BlockingQueue;
//import java.util.concurrent.LinkedBlockingQueue;
//
///**
// * kafka数据写入table store
// */
//
//@Slf4j
//public class KafkaTsOutputFormat extends TsRichOutputFormat<String> {
//
//    private BlockingQueue<Row> waitingRows = new LinkedBlockingQueue<>();
//
//    private final static int maxBatchRows = 50;  //批量写入tablestore最大行数,sdk限制200行，4MB
//
//
//    public KafkaTsOutputFormat(TsTableInfo tableInfo) {
//        super(tableInfo);
//    }
//
//    @Override
//    public void configure(Configuration configuration) {
//    }
//
//    @Override
//    public void open(int taskNumber, int numTasks) throws IOException {
//        if (taskNumber < 0 || numTasks < 1) {
//            throw new IllegalArgumentException("TaskNumber: " + taskNumber + ", numTasks: " + numTasks);
//        }
//        log.info("open taskNumber: {}, numTasks: {}", taskNumber, numTasks);
//
//    }
//
//    /***
//     * write every row in flink table to table store table
//     * @param row
//     * @throws IOException
//     */
//    @Override
//    public void writeRecord(String row) throws IOException {
//        //写数据
//        if (StringUtils.isEmpty(row)) {
//            return;
//        }
//        Row tsRow = TypeConvertUtils.transJsonToTsRow(row);
//        //批量写入
//        try {
//            waitingRows.put(tsRow);
//        } catch (InterruptedException e) {
//            log.error("write data to queue error", e);
//        }
//        if (waitingRows.size() >= maxBatchRows) {
//            flush();
//        }
//    }
//
//
//    public void flush() {
//        //从queue 提取maxBatchRows的数据
//        try {
//            List<Row> flushList = new LinkedList<>();
//            int fetchRows = Math.min(waitingRows.size(), maxBatchRows);
//            for (int i = 0; i < fetchRows; i++) {
//                com.alicloud.openservices.tablestore.model.Row row = waitingRows.take();
//                flushList.add(row);
//            }
//            if (CollectionUtils.isEmpty(flushList)) {
//                return;
//            }
//            Configuration configuration = (Configuration) this.getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
//            String subTaskName = this.getRuntimeContext().getTaskNameWithSubtasks();
//            TsWriter tsWriter = new TsWriter(createTsClient().getClient(), this.getTableInfo(), configuration, subTaskName);
//            tsWriter.batchWrite(flushList);
//        } catch (InterruptedException e) {
//            log.error("take element from queue error", e);
//        }
//    }
//
//
//    @Override
//    public void close() throws IOException {
//        try {
//            while (waitingRows != null && waitingRows.size() > 0) {
//                flush();
//            }
//        } finally {
//            waitingRows = null;
//            closeTsClient();
//        }
//    }
//
//
//}
