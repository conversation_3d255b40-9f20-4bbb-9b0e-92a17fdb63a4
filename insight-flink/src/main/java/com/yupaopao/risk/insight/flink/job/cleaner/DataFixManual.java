package com.yupaopao.risk.insight.flink.job.cleaner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.clickhouse.jdbc.ClickHouseStatement;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.OSSProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

/****
 * zengxiangcai
 * 2023/4/11 12:10
 ***/

@Slf4j
public class DataFixManual {

    public static ClickHouseConnection getPrdConn() {
        ClickHouseProperties ckProperties = ClickHouseUtil.getLongTimeoutProperties();
        ckProperties.setHost("cc-bp14di2b9y6g6qbxz.clickhouse.ads.aliyuncs.com:8123");
        ckProperties.setPassword("1TpzoV9E8bWcYN2F");
        ckProperties.setUsername("risk_rw");
        return ClickHouseUtil.createConnection(ckProperties);
    }


    private static void initData() {
        String startDate = "2022-02-13 00:00:00";
        String endTime = "2023-08-21 00:00:00";
        String currentStart = startDate;

        while (currentStart.compareTo(endTime) < 0) {
            String currentEnd = InsightDateUtils.addHoursStr(currentStart,
                    InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss,
                    24 * 7);
            if (currentEnd.compareTo(endTime) > 0) {
                currentEnd = endTime;
            }
            //execute
            System.err.println("(" + currentStart + "," + currentEnd + ")"+" time:"+new Date());

            // leftJoin right table time
            String rightCurrentStart = InsightDateUtils.addMinutesStr(currentStart,
                    InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss,-1);
            String rightCurrentEnd = InsightDateUtils.addMinutesStr(currentEnd,
                    InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss,1);
            try (ClickHouseConnection conn = getPrdConn(); ClickHouseStatement stmt = conn.createStatement()) {
                String currentSql = String.format(sql,currentStart,currentEnd);
                stmt.executeUpdate(currentSql);
                Thread.sleep(1000*5);
            } catch (Exception e) {
                e.printStackTrace();

            }
            currentStart = currentEnd;
        }


    }

    private static String sql = "INSERT INTO payment_risk_snapshot_trade (\n" +
            "    id,biz_id,uid,opposite_uid,amount,currency,opposite_currency,business_line,mask,\n" +
            "    app_id,change_type,_procedure,pay_platform,client_ip,device_id,merchant_id,trade_type,\n" +
            "    balance_amount,income_amount,diamond_amount,noble_diamond_amount,charm_amount,star_amount,star_diamond_amount,\n" +
            "    room_id,room_type,country,province,city,biz_name,trade_name,trade_time,create_time,update_time\n" +
            ")\n" +
            "SELECT\n" +
            "    id,business_order_no,uid,opposite_uid,amount,currency,opposite_currency,0,mask,\n" +
            "    0,change_type,\n" +
            "    case when service_name='payment-trade' then 'DIRECT'\n" +
            "         when transaction_type='REFUND' then 'PAID_REFUND'\n" +
            "         when change_type='O' then 'PAID'\n" +
            "         else 'RECEIVED' end as _procedure,\n" +
            "    pay_platform,client_ip,device_id,merchant_id,\n" +
            "    case when trade_type>'' then trade_type else scenario end as trade_type,\n" +
            "    balance_amount,income_amount,diamond_amount,noble_diamond_amount,charm_amount,star_amount,star_diamond_amount,\n" +
            "    room_id,room_type,country,province,city,biz_name,trade_name,trade_time,create_time,update_time\n" +
            "FROM payment_trade_snapshot\n" +
            "where trade_time >= '%s'\n" +
            "  and trade_time < '%s'\n" +
            "  and service_name in ('payment-trade', 'payment-order', 'payment-pay')\n" +
            "\n";

    private static void ckTableField2Metabase(){
        if(true){
            sql = "select t1.id fieldId, t1.base_type baseType, t2.name tableName, t1.name fieldName, has_field_values,\n" +
                    "       (case t2.name\n" +
                    "       when 'payment_trade_snapshot' then 'trade_time'\n" +
                    "       else\n" +
                    "         'createdAt'\n" +
                    "       end ) as queryDateColumn\n" +
                    "from metabase_field t1\n" +
                    "         join\n" +
                    "     metabase_table t2\n" +
                    "     on t1.table_id = t2.id\n" +
                    "where t2.db_id = 2\n" +
                    "  and t1.semantic_type = 'type/Category'\n" +
                    "  and has_field_values in ('list', 'auto-list');";
            System.err.println(sql);
            return;
        }
        String tableName = "payment_risk_snapshot_trade_local";
        String tableId = "3654";
        try (ClickHouseConnection conn = getPrdConn(); ClickHouseStatement stmt = conn.createStatement()) {
            String sql = "select name, type from system.columns where table='%s'";
            ResultSet rs = stmt.executeQuery(String.format(sql,tableName));
//            insert into metabase_field(created_at,updated_at,name,base_type,table_id,display_name,database_type)

            List<String> rowList = new ArrayList<>();
            while(rs.next()){
                String columnName = rs.getString("name");
                String columnDbType = rs.getString("type");
                String rowData = "select now(),now(),'%s' as name,'%s' base_type,%s table_id,'%s' " +
                        "display_name,'%s' database_type\n";
                rowData = String.format(rowData,columnName,ckMetabaseMap.get(columnDbType),tableId,columnName,columnDbType);
                rowList.add(rowData);
            }
            System.err.println(String.join("union all\n",rowList));
        } catch (Exception e) {


        }
    }

    private static Map<String,String> ckMetabaseMap = new HashMap<>();
    static {
        ckMetabaseMap.put("DateTime","type/DateTime");
        ckMetabaseMap.put("String","type/Text");
        ckMetabaseMap.put("Float64","type/Float");
        ckMetabaseMap.put("Float32","type/Float");
        ckMetabaseMap.put("UFloat64","type/Float");
        ckMetabaseMap.put("UFloat32","type/Float");
        ckMetabaseMap.put("Int32","type/BigInteger");
        ckMetabaseMap.put("Int64","type/BigInteger");
        ckMetabaseMap.put("UInt64","type/BigInteger");
        ckMetabaseMap.put("UInt32","type/BigInteger");
        ckMetabaseMap.put("UInt8","type/BigInteger");

    }

    public static void main(String[] args) throws Exception {
        if (true) {
          ckTableField2Metabase();
            return;
        }

        List<String> toStringList;
        if (args.length > 0) {
            toStringList = Arrays.asList(args[0].split("#"));
        } else {
            toStringList = new ArrayList<>(1);
        }
//        execute(null);
        String tableName = "risk_punish_record_inside";
//        if (args != null && args.length > 0) {
//            tableName = args[0];
//        } else {
//            return;
//        }

        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        env.fromElements(1).flatMap(new FixFlatMapFunction(tableName)).output(new RichOutputFormat<String>() {
            @Override
            public void configure(Configuration parameters) {

            }

            @Override
            public void open(int taskNumber, int numTasks) throws IOException {

            }

            @Override
            public void writeRecord(String record) throws IOException {
                log.info("write record finish repair: " + record);
//                System.err.println(record);
            }

            @Override
            public void close() throws IOException {

            }
        });
        env.execute("repair");
    }


    public static class FixFlatMapFunction extends RichFlatMapFunction<Integer, String> {
        private transient ClickHouseConnection connection = null;
        private Map<String, String> ckColumn = null;

        private String fixTableName;

        public FixFlatMapFunction(String tableName) {
            this.fixTableName = tableName;
        }

        @Override
        public void close() throws Exception {
            super.close();
            connection.close();
        }

        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            connection = getPrdConn();
            this.ckColumn = ClickHouseUtil.fetchColumns(fixTableName, connection);
        }

        @Override
        public void flatMap(Integer value, Collector<String> out) throws Exception {
            System.err.println("start to fix table file: " + fixTableName);
            execute(out, ckColumn, connection, fixTableName);
        }
    }


    public static void execute(Collector<String> out,
                               Map<String, String> ckColumns,
                               ClickHouseConnection conn,
                               String fixTableName) {
        OSSProperties config = OSSProperties.getProperties(PropertyType.OSS);

        OSS ossClient = new OSSClientBuilder().build(config.getEndpoint(), config.getAccessKeyId(), config.getAccessKeySecret());
        int total = 0, repair = 0;
        try {
            List<String> fileList = listFile(ossClient, config.getBucket(), fixTableName);
            log.info("all file size: {}, table: {}", fileList.size(), fixTableName);
            System.err.println("all file size: " + fileList.size() + " ,table" + fixTableName);

            for (String file : fileList) {
                log.info("start to fix file: {}", file);
                System.err.println("start to fix file: " + file);
                List<String> list = readFile(ossClient, config.getBucket(), file, ckColumns, fixTableName);
                if (list.size() == 0) {
                    continue;
                }
//                String tableName = getTableName(list.get(0));
//                if (StringUtils.isBlank(tableName)) {
//                    continue;
//                }
                total += list.size();
                int count;
                if ((count = tryFix(file, list, conn)) > 0) {
                    repair += count;
                    log.info("{} temp修复成功，修复条数: {}", file, count);
                    System.err.println(file + " temp修复成功，修复条数: " + count);
                    deleteFile(ossClient, config.getBucket(), file);
                    out.collect(count + ", success: " + file);
                } else {
                    log.info("{} temp修复失败", file);
                    System.err.println(file + " temp修复失败");
                    out.collect("fail: " + file);
                }
            }
        } catch (Exception e) {
            log.error("temp修复出错: ", e);
            e.printStackTrace();
        } finally {
            ossClient.shutdown();
        }

    }

    private static int tryFix(String fileName, List<String> lines, ClickHouseConnection conn) throws SQLException {

        try (ClickHouseStatement stmt = conn.createStatement()) {
            stmt.executeUpdate(buildSql(lines));
            return lines.size();
        } catch (SQLException e) {
            System.err.println("tryFix error: " + fileName);
            e.printStackTrace();
            return 0;
        }

    }


    private static String getTableName(String line) {
        String[] split = line.split(" ");
        if (split.length == 5) {
            return split[2];
        }
        return "";
    }

    private static List<String> readFile(OSS ossClient, String bucketName,
                                         String path,
                                         Map<String, String> ckColumns,
                                         String fixTableName) throws IOException {
        List<String> stringColList = new ArrayList<>();
        for (Map.Entry<String, String> type : ckColumns.entrySet()) {
            if (type.getValue().contains("String")) {
                stringColList.add(type.getKey());
            }
        }
        List<String> lines = new ArrayList<>();
        OSSObject ossObject = ossClient.getObject(bucketName, path);
        // 调用ossObject.getObjectContent获取文件输入流，可读取此输入流获取其内容。
        InputStream content = ossObject.getObjectContent();
        if (content != null) {
            BufferedReader reader = new BufferedReader(new InputStreamReader(content));
            String line;
            while (StringUtils.isNotBlank(line = reader.readLine())) {
                if (line.contains("test-code")) {
                    continue;
                }
                if (line.contains(String.format("INSERT INTO %s", fixTableName))) {
                    lines.add(line);
                } else {
                    JSONObject row = JSON.parseObject(line);
                    List<String> columnList = new ArrayList<>(row.keySet());

                    for (String k : columnList) {
                        if (k.contains(".")) {
                            String newK = k.replaceAll("\\.", "_");
                            row.put(newK, row.remove(k));
                        }
                    }
                    fixType(row);
                    for (String col : stringColList) {
                        String strValue = row.getString(col);
                        if (strValue != null) {
                            row.put(col, row.getString(col));
                        }

                    }
                    lines.add(row.toJSONString());
                }


            }
            content.close();
        }
        return lines;
    }

    private static void fixType(JSONObject row) {
        if (row.containsKey("extConfMap_period") && !(row.get("extConfMap_period") instanceof String)) {
            row.put("extConfMap_period", row.getString("extConfMap_period"));
        }
        if (row.containsKey("strategyResult") && !(row.get("strategyResult") instanceof String)) {
            row.put("strategyResult", row.getString("strategyResult"));
        }
        if (row.containsKey("osVersion") && !(row.get("osVersion") instanceof String)) {
            row.put("osVersion", row.getString("osVersion"));
        }
        if (row.containsKey("uid") && !(row.get("uid") instanceof String)) {
            row.put("uid", row.getString("uid"));
        }
        if (row.containsKey("deviceDetail_date") && !(row.get("deviceDetail_date") instanceof Number)) {
            row.remove("deviceDetail_date");
        }

        if ((row.get("data_receiveUid") instanceof String) && (row.getString("data_receiveUid").startsWith("["))) {
            row.remove("data_receiveUid");
        }

        if (row.get("data_deviceDetail_date") instanceof String) {
            row.remove("data_deviceDetail_date");
        }
    }

    private static List<String> listFile(OSS ossClient, String bucketName, String fixTableName) {
        List<String> fileList = new ArrayList<>();
        ObjectListing objectListing;
        String nextMarker = null;
        do {
            objectListing = ossClient.listObjects(new ListObjectsRequest(bucketName)
                    .withMarker(nextMarker)
                    .withPrefix("risk-flink/prod/sql")
                    .withMaxKeys(800));

            for (OSSObjectSummary s : objectListing.getObjectSummaries()) {
                if (s.getKey().endsWith(".sql") && s.getKey().contains(fixTableName) && !s.getKey().contains("hug")) {
                    fileList.add(s.getKey());
                }
            }
            nextMarker = objectListing.getNextMarker();

        } while (objectListing.isTruncated());
        return fileList;
    }

    private static void deleteFile(OSS ossClient, String bucketName, String file) {
        ossClient.deleteObject(bucketName, file);
    }

    private static String buildSql(List<String> datas) {
        return String.join("\n", datas);
    }

}
