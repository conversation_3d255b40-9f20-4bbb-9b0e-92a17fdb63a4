package com.yupaopao.risk.insight.flink.windows.assigner;

import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import com.yupaopao.risk.insight.flink.windows.triggers.FactorSlidingEventTimeTrigger;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.WindowAssigner;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class FactorSlidingEventTimeWindowAssigner extends WindowAssigner<FactorCalDetail, TimeWindow> {
    private static final long serialVersionUID = -7621328270959777953L;
    private long slideAvgNum;

    public FactorSlidingEventTimeWindowAssigner() {
        String slideAvg = PortraitApolloProperties.getConfigByKey("factor.slide.avg.count", "10");
        slideAvgNum = Long.parseLong(slideAvg);
    }

    @Override public Collection<TimeWindow> assignWindows(FactorCalDetail element, long timestamp, WindowAssignerContext context) {

        long size = element.getTimeSpan() * 60 * 1000;
        long slide = size / slideAvgNum;
        timestamp = element.getTimeStamp();

        List<TimeWindow> windows = new ArrayList<>((int) (size / slide));
        long lastStart = TimeWindow.getWindowStartWithOffset(timestamp, 16 * 60 * 60 * 1000, slide);
        for (long start = lastStart; start > timestamp - size; start -= slide) {
            if (start + size <= context.getCurrentProcessingTime()) {
                continue;
            }
            windows.add(new TimeWindow(start, start + size));
        }
        return windows;
    }

    @Override public Trigger<FactorCalDetail, TimeWindow> getDefaultTrigger(StreamExecutionEnvironment env) {
        return FactorSlidingEventTimeTrigger.create(slideAvgNum);
    }

    @Override public TypeSerializer<TimeWindow> getWindowSerializer(ExecutionConfig executionConfig) {
        return new TimeWindow.Serializer();
    }

    @Override public boolean isEventTime() {
        return false;
    }
}
