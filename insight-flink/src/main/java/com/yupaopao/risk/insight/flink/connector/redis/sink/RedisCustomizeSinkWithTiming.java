package com.yupaopao.risk.insight.flink.connector.redis.sink;

import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.flink.windows.aggregate.AggregateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.util.Preconditions;

import java.io.IOException;

@Slf4j
public class RedisCustomizeSinkWithTiming extends RichSinkFunction<AggregateResult> {

    private RedisProperties redisProperties;
    private static RedisFlushProcessor redisFlushProcessor;

    public RedisCustomizeSinkWithTiming(RedisProperties redisProperties) {
        Preconditions.checkNotNull(redisProperties, "Redis connection pool config should not be null");
        this.redisProperties = redisProperties;
    }

    @Override public void invoke(AggregateResult input, Context context) throws Exception {
        redisFlushProcessor.addData(input);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        redisFlushProcessor = new RedisFlushProcessor(redisProperties);
    }

    @Override
    public void close() throws IOException {
        if(redisFlushProcessor!=null){
            redisFlushProcessor.close();
        }
    }



}
