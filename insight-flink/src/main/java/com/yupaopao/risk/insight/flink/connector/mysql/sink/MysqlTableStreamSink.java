package com.yupaopao.risk.insight.flink.connector.mysql.sink;

import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import com.yupaopao.risk.insight.common.support.InsightFlinkUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.sinks.AppendStreamTableSink;
import org.apache.flink.table.sinks.TableSink;
import org.apache.flink.table.types.DataType;
import org.apache.flink.types.Row;

import java.io.Serializable;

public class MysqlTableStreamSink implements AppendStreamTableSink<Row>, Serializable {
    private DBProperties dbProperties;

    private FlinkMetaInfo flinkMetaInfo;

    private String tableName;

    public MysqlTableStreamSink(String tableName, DBProperties dbProperties, FlinkMetaInfo flinkMetaInfo) {
        this.tableName = tableName;
        this.dbProperties = dbProperties;
        this.flinkMetaInfo = flinkMetaInfo;
    }


    @Override public DataStreamSink<?> consumeDataStream(DataStream<Row> dataStream) {
        MysqlStreamSinkBase sinkBase = new MysqlStreamSinkBase(tableName, dbProperties);

        String[] fieldNames = flinkMetaInfo.getFieldNames();
        return dataStream.map((MapFunction<Row, String>) row -> {
            int rowSize = row.getArity();
            StringBuilder key = new StringBuilder();
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < rowSize; i++) {
                key.append(fieldNames[i]).append(",");
                DataType dataType = flinkMetaInfo.getFieldDataType(i);
                value.append(InsightFlinkUtils.getMysqlStr(row.getField(i), dataType)).append(",");
            }
            return "insert into " + tableName +  "(" + key.deleteCharAt(key.length() - 1) + ") values(" + value.deleteCharAt(value.length() - 1) +");";
        }).addSink(sinkBase).name("kafka sink");
    }

    @Override
    public TableSink<Row> configure(String[] fieldNames, TypeInformation<?>[] fieldTypes) {
        return new MysqlTableStreamSink(tableName, dbProperties, flinkMetaInfo);
    }

    @Override
    public TableSchema getTableSchema() {
        return flinkMetaInfo.toTableSchema();
    }

    @Override
    public TypeInformation<Row> getOutputType() {
        return flinkMetaInfo.toTableSchema().toRowType();
    }

}
