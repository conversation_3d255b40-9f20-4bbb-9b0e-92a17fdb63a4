package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.flink.windows.AggTagDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.roaringbitmap.longlong.Roaring64NavigableMap;

import java.util.HashSet;
import java.util.Set;

@Slf4j
public class TagDistinctCountFunction implements AggregateFunction<AggTagDetail, TagAggregateResult, TagAggregateResult> {

    @Override public TagAggregateResult createAccumulator() {
        TagAggregateResult result = new TagAggregateResult();
        result.setDistinct(new HashSet<>());
        return result;
    }

    @Override public TagAggregateResult add(AggTagDetail value, TagAggregateResult accumulator) {
        accumulator.setId(value.getId());
        accumulator.setGroupKey(value.getGroupKey());
        accumulator.setType(value.getType());
        Cat.logMetricForCount("tag.distinct");

        Set<String> distinct = accumulator.getDistinct();
        try {
            long data = Long.parseLong(value.getData());
            Roaring64NavigableMap bitMap = accumulator.getBitMap();
            if (null == bitMap) {
                bitMap = new Roaring64NavigableMap();
            }
            if (!bitMap.contains(data)) {
                bitMap.add(data);
                accumulator.setCount(accumulator.getCount() + 1);
            }
            if (distinct.size() > 0) {
                for (String distinctValue : distinct) {
                    bitMap.add(Long.parseLong(distinctValue));
                }
                accumulator.setCount((double) bitMap.getLongCardinality());
                distinct.clear();
            }
            accumulator.setBitMap(bitMap);
        } catch (NumberFormatException e) {
            double size = distinct.size();
            if (size > 100) {
                log.info("discount tag: {}, value: {}, size: {}", accumulator.getId(), accumulator.getGroupKey(), size);
            }
            distinct.add(value.getData());
            accumulator.setCount(size);
        }
        return accumulator;
    }

    @Override public TagAggregateResult getResult(TagAggregateResult accumulator) {
        return accumulator;
    }

    @Override public TagAggregateResult merge(TagAggregateResult a, TagAggregateResult b) {
        return a;
    }
}
