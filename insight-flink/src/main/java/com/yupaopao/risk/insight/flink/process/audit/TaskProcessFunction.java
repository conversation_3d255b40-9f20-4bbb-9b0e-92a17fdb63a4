package com.yupaopao.risk.insight.flink.process.audit;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.flink.bean.audit.MxTask;
import com.yupaopao.risk.insight.flink.bean.audit.PerformanceMxTask;
import com.yupaopao.risk.insight.flink.job.audit.SideOutputConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-16 13:36
 *
 ***/

@Slf4j
public class TaskProcessFunction extends KeyedProcessFunction<String, MxTask, String> {

    public static final String DELETED_TASK_BATCH_ID = "DELETE";
    //异常情况需要清除不记录的批次数据
    private ListState<MxTask> taskProcessListSate;

    @Override
    public void open(Configuration parameters) throws Exception {
        taskProcessListSate = getRuntimeContext().getListState(new ListStateDescriptor<>("processList", MxTask.class));
    }


    @Override
    public void processElement(MxTask mxTask, Context context, Collector<String> collector) throws Exception {
        //maserati 保证有序性，只需要是已经完成状态就可以处理
        if (!mxTask.finished()) {
            addToState(mxTask);
            return;
        }
        //已完成状态数据
        List<MxTask> allStateElements = getAllSateElements();
        allStateElements.add(mxTask);
        //处理滞留状态数据
        allStateElements = filterDetainedTask(allStateElements);
        if (StringUtils.isNotEmpty(mxTask.getBatchId())) {
            //包含batchId，向后处理
            if (!DELETED_TASK_BATCH_ID.equals(mxTask.getBatchId())) {
                context.output(SideOutputConstants.batchTaskTag, allStateElements);
            }
        } else {
            //处理完成, 计算所有指标
            PerformanceMxTask resultTask = PerformanceCalculator.calculate(allStateElements);
            //无batchId,直接输出到sink
            String taskSinkJson = JSON.toJSONString(resultTask);
            collector.collect(taskSinkJson);
            context.output(SideOutputConstants.performanceResultSingleTask,resultTask);
        }
        taskProcessListSate.clear();
    }


    private void addToState(MxTask mxTask) throws Exception {
        //防止数据重复，如果当前记录和上一条记录状态相同直接忽略
        MxTask last = null;
        for (MxTask task : taskProcessListSate.get()) {
            last = task;
        }
        if (last != null && last.getState().equals(mxTask)) {
            return;
        }
        taskProcessListSate.add(mxTask);
    }


    private List<MxTask> getAllSateElements() throws Exception {
        List<MxTask> mxTasks = new ArrayList<>();
        for (MxTask task : taskProcessListSate.get()) {
            mxTasks.add(task);
        }
        return mxTasks;
    }

    /***
     * 滞留数据状态比较特殊而且没有指标计算，需要将其删除，然后把他前一个已分配状态也删除
     * @param originalTaskList
     * @return
     */
    private List<MxTask> filterDetainedTask(List<MxTask> originalTaskList) {

        List<MxTask> deletedTask = new ArrayList<>();
        for (int i = 1; i < originalTaskList.size(); i++) {
            MxTask elem = originalTaskList.get(i);
            if (elem.detained()) {
                deletedTask.add(elem);
                deletedTask.add(originalTaskList.get(i - 1));
            }
        }

        for (MxTask elem : deletedTask) {
            originalTaskList.remove(elem);
        }

        return originalTaskList;
    }

}
