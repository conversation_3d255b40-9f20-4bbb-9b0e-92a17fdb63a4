package com.yupaopao.risk.insight.flink.job.etl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.CommonFlattenKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.OriginalStringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.StringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.base.CheckpointSetting;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.CommonKafkaToCkSyncProcessor;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;

import java.net.URLEncoder;
import java.util.*;

import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.KAFKA_GROUP_ID_DEVICE_RAW_DATA_FETCHER_JOB;

/****
 * zengxiangcai
 * 2023/6/18 13:27
 ***/
public class RiskAsyncRuleResultSyncJob {
    public static void main(String[] args) throws Exception {
        new FlinkJobBuilder().withJobName("risk_async_rule_result_log")
                .withCheckpointSetting(new CheckpointSetting(60000, 30000, true))
                .withMainProcessor(new RiskAsyncRuleResultProcessor())
                .start(args);
    }

    public static class RiskAsyncRuleResultProcessor implements FlinkJobBuilder.MainProcessor {

        @Override
        public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
            KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA_RISK_ALI);
            Properties kafkaProps = kafkaProperties.getProperties();
            kafkaProps.put("group.id", FlinkConstants.SYNC_GROUP_ID);
            List<String> topics = Collections.singletonList(FlinkConstants.KAFKA_TOPIC_ASYNC_RULE_RESULT_LOG);
            FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics, new OriginalStringKafkaDeserializationSchemaWrapper(new SimpleStringSchema(), false), kafkaProps);
//            consumer.setStartFromEarliest();//遗漏两天数据从最开始时间计算
            consumer.setStartFromTimestamp(System.currentTimeMillis() - 60000 * 2); //2min before


            ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
            ClickHouseStreamSink ck = new ClickHouseStreamSink(ckProperties, "risk_async_rule_result_log",
                    1 * 60 * 1000, 8192 * 7, true);

            env.addSource(consumer).name("read_async_rule_result")
                    .flatMap(new AsyncRuleProcessMap()).name("process_row")
                    .addSink(ck).name("write_to_ck");

        }
    }

    private static class AsyncRuleProcessMap extends RichFlatMapFunction<String, String> {

        @Override
        public void flatMap(String value, Collector<String> out) throws Exception {
            if (StringUtils.isEmpty(value)) {
                return;
            }
            JSONObject obj = JSON.parseObject(value);
            String traceId = obj.getString("TraceId");
            String createdAt = obj.getString("createdAt");
            String eventCode = obj.getString("Event");
            String rule = obj.getString("rule");
            String level = obj.getString("level");
            if (createdAt.compareTo("2023-06-16 14:20:30") <= 0) {
                return;
            }
            Map<String, String> row = new HashMap<>();
            row.put("createdAt", createdAt);
            row.put("Event", eventCode);
            row.put("rule", rule);
            row.put("level", level);
            row.put("TraceId", traceId);
            row.put("jsonData", obj.toJSONString());
            out.collect(JSON.toJSONString(row));
        }
    }
}
