package com.yupaopao.risk.insight.flink.process.logon;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.connector.ts.util.EsUtil;
import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.functions.RichFilterFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Map;

@Slf4j public class ExistFilter extends RichFilterFunction<Tuple2<String, String>> {
    private static final long serialVersionUID = 9033921440847749651L;
    private EsProperties esProperties;
    private transient RestHighLevelClient client;
    private String index;
    private static DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    private LongCounter writeCounter = new LongCounter();

    public ExistFilter(EsProperties esProperties, String index) {
        this.esProperties = esProperties;
        this.index = index;
    }

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        this.client = EsUtil.getClient(esProperties);
        if (getRuntimeContext().getAccumulator("esSupplementCount") == null) {
            getRuntimeContext().addAccumulator("esSupplementCount", this.writeCounter);
        }
    }

    @Override public boolean filter(Tuple2<String, String> value) throws Exception {
        GetRequest doc = new GetRequest(index, "_doc", value.f0);
        GetResponse response = client.get(doc);
        Map<String, Object> source = response.getSource();

        try {
            if (null != source && source.size() > 0 && source.containsKey("createdAt")) {
                String esTime = source.get("createdAt").toString();
                DateTime parse = DateTime.parse(esTime, formatter);

                JSONObject jsonObject = JSONObject.parseObject(value.f1);
                String createdAt = jsonObject.getString("createdAt");
                if (StringUtils.isNotEmpty(createdAt)) {
                    boolean before = parse.isBefore(DateTime.parse(createdAt, formatter));
                    if (before) {
                        writeCounter.add(1);
                    }
                    return before;
                }
            }
        } catch (Exception e) {
            log.error("filter error", e);
        }
        writeCounter.add(1);
        return true;
    }

    @Override public void close() throws Exception {
        super.close();
    }
}
