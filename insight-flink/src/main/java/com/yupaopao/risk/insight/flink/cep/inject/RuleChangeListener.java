package com.yupaopao.risk.insight.flink.cep.inject;

import com.yupaopao.risk.insight.common.cep.beans.CepRule;
import com.yupaopao.risk.insight.common.cep.beans.RiskEvent;
import com.yupaopao.risk.insight.common.cep.utils.RuleUtils;
import org.apache.flink.cep.pattern.Pattern;

import java.util.HashMap;
import java.util.Map;

import static com.yupaopao.risk.insight.common.cep.constants.CepConstants.ruleCheckInterval;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-13 16:43
 *
 ***/
public abstract class RuleChangeListener implements CepListener<RiskEvent> {

    private PatternStore patternStore;

    public RuleChangeListener() {
    }

    @Override
    public void init() throws Exception {
        patternStore = new PatternStore(getDataSource());
        patternStore.createTimer(getPeriod());
    }

    @Override
    public Map<String, Pattern<RiskEvent, ?>> getPatterns() throws Exception {
        Map<String, CepRule> ruleMap = patternStore.getRunningRules();
        if (ruleMap == null) {
            throw new IllegalArgumentException("pattern not configured or not initialize");
        }
        Map<String, Pattern<RiskEvent, ?>> patternMap = new HashMap<>();

        for (Map.Entry<String, CepRule> rule : ruleMap.entrySet()) {
            patternMap.put(rule.getKey(), RuleUtils.transToPattern(rule.getValue()));
        }
        return patternMap;
    }

    //1min 查询一次
    @Override
    public long getPeriod() {
        return ruleCheckInterval;
    }

    @Override
    public NfaKeySelector<RiskEvent, String> getNfaKeySelector() {
        return new NfaKeySelector<RiskEvent, String>() {
            @Override
            public String getKey(RiskEvent value) throws Exception {
                return value.getRuleId();
            }
        };
    }

    @Override
    public Integer getChangedVersion() {
        return patternStore.getChangedVersion();
    }

    @Override
    public Map<String, CepRule> getRuleMap() {
        return patternStore.getRunningRules();
    }

    @Override
    public void close() {
        if(patternStore==null){
            return;
        }
        patternStore.close();
    }


}
