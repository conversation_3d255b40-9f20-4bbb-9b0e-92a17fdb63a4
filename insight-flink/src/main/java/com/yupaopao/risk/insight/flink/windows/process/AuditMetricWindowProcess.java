package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.bean.audit.AuditMetric;
import com.yupaopao.risk.insight.flink.bean.audit.MxMetric;
import com.yupaopao.risk.insight.flink.job.portrait.support.GroovyRunSupport;
import com.yupaopao.risk.insight.flink.utils.MxMetricCacheUtils;
import groovy.lang.Binding;
import groovy.lang.Script;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.joda.time.DateTime;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc AuditMetricWindowProcess
 * @date 2020/7/3
 */
public class AuditMetricWindowProcess extends ProcessWindowFunction<AuditMetric, String, String, TimeWindow> {

    private static final long serialVersionUID = 7322334661949977987L;

    @Override public void process(String o, Context context, Iterable<AuditMetric> elements, Collector<String> out) throws Exception {
        List<MxMetric> function = MxMetricCacheUtils.getMetricByFunc("FUNCTION");
        if (function.size() == 0) {
            return;
        }
        Map<String, Double> elementMap = new HashMap<>();
        elements.forEach(item -> {
            elementMap.put(item.getMetric(), item.getValue());
        });
        if (elementMap.size() == 0) {
            return;
        }
        AuditMetric auditMetric = elements.iterator().next();
        DateTime createTime = new DateTime();
        DateTime endTime = createTime.withMillisOfSecond(0).withMinuteOfHour(0);

        function.forEach(item -> {
            Object result = null;
            Script script = item.getScript();
            if (Objects.isNull(script)) {
                return;
            }
            Binding binding = new Binding(elementMap);
            script.setBinding(binding);
            result = script.run();
            if (Objects.isNull(result)) {
                return;
            }
            auditMetric.setValue((Double) result);
            auditMetric.setMetric(item.getCode());
            auditMetric.setCreatedAt(createTime.toString("yyyy-MM-dd HH:mm:ss"));
            auditMetric.setStartTime(endTime.minusHours(1).toString("yyyy-MM-dd HH:mm:ss"));
            auditMetric.setEndTime(endTime.toString("yyyy-MM-dd HH:mm:ss"));
            out.collect(JSONObject.toJSONString(auditMetric));
        });
    }
}
