package com.yupaopao.risk.insight.flink.job.classification;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.source.CKInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.sink.CommonHBaseOutputFormat;
import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import org.apache.flink.api.java.ExecutionEnvironment;

import java.util.HashMap;
import java.util.Map;

/****
 * zengxiangcai
 * 2023/12/5 10:53
 ***/
public class InitialTagJob {
    public static void main(String[] args) {
        HBaseProperties hProperties  = HBaseProperties.getProperties(PropertyType.HBASE) ;
        try{
            HBaseUtil.createTable("risk_card_tag",hProperties);
        }catch (Exception e) {
            e.printStackTrace();
        }


    }
//    public static void main(String[] args) throws Exception {
//        new BatchJobBuilder().withJobName("initial-tag")
//                .withProcessor(new InitialTagProcessor())
//                .start(args);
//    }

    public static class InitialTagProcessor implements BatchJobBuilder.MainProcessor {

        @Override
        public void internalProcess(ExecutionEnvironment env, Map<String, String> argMap) throws Exception {
            String sql = "select userId from temp_user_active_times where createdAt ='2023-12-01 12:00:00' order by userId\n";
            ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
            HBaseProperties hBaseProperties = HBaseProperties.getProperties(PropertyType.HBASE);

            CKInputFormat ckInput = new CKInputFormat(ckProperties,sql,"");
            env.createInput(ckInput)
                    .filter(elem-> !HBaseUtil.emptyResultJson(elem)).map(elem->{
                        Map<String,Object> row = new HashMap<>();
                        row.put("userId", JSON.parseObject(elem).getString("userId"));
                        row.put("zombieUser",true);
                        return JSON.toJSONString(row);
                    }).output(new CommonHBaseOutputFormat("risk_user_tag",hBaseProperties){
                        @Override
                        public String getRowKey(Map<String, Object> flattenMap) {
                            return flattenMap.get("userId").toString();
                        }
                    });
        }
    }

}
