package com.yupaopao.risk.insight.flink.job.etl;

import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.WhiteListSyncProcessor;
import lombok.extern.slf4j.Slf4j;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-11-12 11:41
 * 每天凌晨同步一次白名单,方便离线分析过滤使用
 ***/

@Slf4j
public class WhiteListSyncJob {

    public static void main(String[] args) throws Exception {
        new FlinkJobBuilder()
                .withJobName("risk-gray-list-sync-job")
                .isBatchJob(true)
                .withMainProcessor(new WhiteListSyncProcessor())
                .start(args);
    }

}
