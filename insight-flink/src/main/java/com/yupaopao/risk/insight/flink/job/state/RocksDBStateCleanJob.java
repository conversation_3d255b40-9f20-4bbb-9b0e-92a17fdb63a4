package com.yupaopao.risk.insight.flink.job.state;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.core.fs.Path;
import org.apache.flink.runtime.checkpoint.MasterState;
import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.OperatorSubtaskState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.runtime.state.*;
import org.apache.flink.runtime.state.filesystem.FileStateHandle;
import org.apache.flink.state.api.runtime.SavepointLoader;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-14 18:46
 * 定期清理flink checkpoints 、savepoints
 * 默认保留10个cp数据,每个cp关联的数据不会自动删除，只需要把任务已经结束的并且和当前运行任务没关联的任务清理掉
 ***/

@Slf4j
public class RocksDBStateCleanJob {

    public static final String KEY_STATE = "keyState";
    public static final String OPERATOR_STATE = "operatorState";
    public static final String IGNORE_JOB = "ignoreJob";

    public static void main(String[] args) {

//        System.err.println(listAllDirs("C:\\Users\\<USER>\\Desktop\\seqtest"));
        try {
            ExecutionEnvironment bEnv = ExecutionEnvironment.getExecutionEnvironment();
//            final String overviewJobIds;
//            String activeProfile = System.getProperty("spring.profiles.active");
////            if("prod".equalsIgnoreCase(activeProfile)){
////                if(args!=null && args.length>0){
////                    overviewJobIds = args[0];
////                }else{
////                    throw new IllegalArgumentException("prod环境本集群不允许通过域名调用本集群服务,只能外部传入");
////                }
////            }else{
////                overviewJobIds = null;
////            }

            String overviewJobIds = "";
            bEnv.fromElements("1").map(elem -> {
                List<String> allJobs = getAllJobs(overviewJobIds);
                log.info("计划保留状态的jobIds: {}", allJobs);
                listRelationShipForAllJobs(allJobs);

                return "1";
            }).print();

        } catch (Exception e) {
            log.error("read savepoint error", e);
        }

    }

    private static Set<String> listAllDirs(String path) {
        File f = new File(path);
        Set<String> leafDirs = new HashSet<>();

        if (f.isDirectory() && f.exists()) {
            //列出所有文件的上层目录
            String[] dirs = f.list();
            if (dirs == null || dirs.length == 0) {
                leafDirs.add(path);
            }
            for (String dir : dirs) {
                String currentFilePath = path + File.separator + dir;
                if (new File(currentFilePath).isDirectory()) {
                    leafDirs.addAll(listAllDirs(currentFilePath));
                } else {
                    leafDirs.add(path);
                }
            }
        }
        return leafDirs;
    }

    private static List<String> getAllJobs(String inputOverviewJobs) {
        List<String> overviewJobs = null;
        if (StringUtils.isEmpty(inputOverviewJobs)) {
            overviewJobs = listOverviewJob();
        } else {
            overviewJobs = Arrays.asList(inputOverviewJobs.split(","));
        }
        if (CollectionUtils.isEmpty(overviewJobs)) {
            log.warn("there is no overview jobs, exit");
            return new ArrayList<>();
        }

        List<String> recentJobs = listRecentStateChangedJob("/flink/rocksdb");
        List<String> allJobs = new ArrayList<>();
        allJobs.addAll(overviewJobs);
        List<String> othersJob = recentJobs.stream().filter(elem -> !allJobs.contains(elem)).collect(Collectors.toList());
        allJobs.addAll(othersJob);
        return allJobs;
    }

    public static List<String> parseManagedOperatorState(OperatorSubtaskState subtaskState) {
        List<String> operatorStateLocation = new ArrayList<>();
        for (OperatorStateHandle operatorStateHandle : subtaskState.getManagedOperatorState()) {
            StreamStateHandle delegateState = operatorStateHandle.getDelegateStateHandle();
            if (delegateState instanceof FileStateHandle) {
                Path filePath = ((FileStateHandle) delegateState).getFilePath();
//                log.warn("managedOperatorStatePath: {}", filePath.getPath());
                operatorStateLocation.add(filePath.getPath());
            }
        }
        return operatorStateLocation;
    }

    public static List<String> parseManagedKeyedState(OperatorSubtaskState subtaskState) {
        // 遍历当前 subtask 的 KeyedState
        List<String> keyStateLocations = new ArrayList<>();
        for (KeyedStateHandle keyedStateHandle : subtaskState.getManagedKeyedState()) {
            // 本案例针对 Flink RocksDB 的增量 Checkpoint 引发的问题，
            // 因此仅处理 IncrementalRemoteKeyedStateHandle
            if (keyedStateHandle instanceof IncrementalRemoteKeyedStateHandle) {
                // 获取 RocksDB 的 sharedState
                Map<StateHandleID, StreamStateHandle> sharedState =
                        ((IncrementalRemoteKeyedStateHandle) keyedStateHandle).getSharedState();
                // 遍历所有的 sst 文件，key 为 sst 文件名，value 为对应的 hdfs 文件 Handle
                for (Map.Entry<StateHandleID, StreamStateHandle> entry : sharedState.entrySet()) {
                    // 打印 sst 文件名
//                    log.warn("sstable 文件名：{}", entry.getKey());
                    if (entry.getValue() instanceof FileStateHandle) {
                        Path filePath = ((FileStateHandle) entry.getValue()).getFilePath();
                        // 打印 sst 文件对应的 hdfs 文件位置
//                        log.warn("sstable文件对应的hdfs位置: {}", filePath.getPath());
//                        keyStateLocations.add("fileName=" + entry.getKey() + ", filePath=" + filePath.getPath());
                        keyStateLocations.add(filePath.getPath());
                    }
                }
            }
        }
        return keyStateLocations;
    }

    /***
     * 列出近1天更新过的job目录
     * @return
     */
    private static List<String> listRecentStateChangedJob(String dir) {
        long startTime = System.currentTimeMillis() - 1000L * 3600 * 24;
        File rocksdbDir = new File(dir);
        List<String> resultJobList = new ArrayList<>();
        if (!(rocksdbDir.exists() && rocksdbDir.isDirectory())) {
            return resultJobList;
        }
        for (String file : rocksdbDir.list()) {
            File f = new File(rocksdbDir + File.separator + file);
            if (!f.isDirectory() && f.lastModified() > startTime) {
                resultJobList.add(file);
            }
        }
        return resultJobList;
    }

    private static List<String> listOverviewJob() {
        List<String> overviewJobs = new ArrayList<>();
        String activeProfile = System.getProperty("spring.profiles.active");
        String url = "https://test-flink.yupaopao.com/jobs/overview";
        if ("prod".equalsIgnoreCase(activeProfile)) {
            url = ApolloProperties.getConfigStr("application", "flink.cluster.webUrl") + "/jobs/overview";
        }
        try (CloseableHttpClient client = HttpClientBuilder.create().build();) {
            HttpGet get = new HttpGet(url);
            HttpResponse response = client.execute(get);
            if (response.getStatusLine().getStatusCode() / 100 != 2) {
                log.warn("return with error: status: {}", response.getStatusLine());
                return overviewJobs;
            } else {
                JSONObject obj = JSONObject.parseObject(EntityUtils.toString(response.getEntity()));
                JSONArray array = obj.getJSONArray("jobs");
                if (array != null) {
                    overviewJobs = array.stream().map(elem -> {
                        JSONObject jobDetail = (JSONObject) elem;
                        String state = jobDetail.getString("state");
                        if ("CANCELED".equalsIgnoreCase(state) || "FINISHED".equalsIgnoreCase(state)) {
                            return null;
                        }
                        return jobDetail.getString("jid");
                    }).filter(elem -> StringUtils.isNotEmpty(elem)).collect(Collectors.toList());
                }
            }

        } catch (Exception e) {
            log.warn("get jobs error:", e);
        }
        return overviewJobs;
    }

    /***
     * 有可能在处理的时候文件已经被清理了
     * @param jobs
     */
    private static void listRelationShipForAllJobs(List<String> jobs) {
        jobs = Arrays.asList("a0f7d3e7e9290bb00d65505e20d93bd2");
        Set<String> allDirs = listAllDirs("/flink/rocksdb");

        List<StateLocation> keyLocationList = new ArrayList<>();
        List<StateLocation> operatorLocationList = new ArrayList<>();
        List<String> ignoreJobs = new ArrayList<>();
        for (String job : jobs) {
            File f = new File("/flink/rocksdb" + File.separator + job);
            List<String> chkdirs = new ArrayList<>();
            if (f.isDirectory() && f.exists()) {
                //检查checks
                String[] dirs = f.list();
                for (String temp : dirs) {
                    if (temp.startsWith("chk-")) {
                        chkdirs.add(temp);
                    }
                }
            }
            for (String chkdir : chkdirs) {
                Map<String, List<StateLocation>> resultMap =
                        readRelatedFile("/flink/rocksdb" + File.separator + job + File.separator + chkdir);
                keyLocationList.addAll(resultMap.get(KEY_STATE));
                operatorLocationList.addAll(resultMap.get(OPERATOR_STATE));
                ignoreJobs.addAll(resultMap.get(IGNORE_JOB).stream().map(elem -> elem.getPath()).collect(Collectors.toList()));
            }
        }
        log.warn("all key state location: {}", JSON.toJSONString(keyLocationList));
        log.warn("all operator state location: {}", JSON.toJSONString(operatorLocationList));


        Set<String> allStateDir = new HashSet<>();
        //不含状态的state的目录
        for (StateLocation key : keyLocationList) {
            for (String location : key.getLocation()) {
                int index = location.lastIndexOf("/");
                allStateDir.add(location.substring(0, index));
            }
        }
        for (StateLocation key : operatorLocationList) {
            for (String location : key.getLocation()) {
                int index = location.lastIndexOf("/");
                allStateDir.add(location.substring(0, index));
            }
        }

        log.warn("all dirs: {}", allDirs);

        log.warn("all state dirs: {}", JSON.toJSONString(allStateDir));
        //差集：
        allDirs.removeAll(allStateDir);


        log.warn("left dirs: {}", JSON.toJSONString(allDirs.stream().sorted().collect(Collectors.toList())));

        List<String> deletedPaths = new ArrayList<>();
        for (String dir : allDirs) {
            boolean isIgnore = ignoreJobs.stream().filter(elem -> dir.startsWith(elem)).findFirst().isPresent();
            if (isIgnore) {
                continue;
            }
            if(dir.contains("/flink/rocksdb/cep")||dir.contains("/flink/rocksdb/externalTag")){
                continue;
            }
            if(jobs.stream().filter(elem->dir.contains(elem)).findFirst().isPresent()){
                continue;
            }
            deletedPaths.add(dir);
        }
        log.warn("all deleted dirs path : {}", JSON.toJSONString(deletedPaths));

        //delete all files

//        deleteFiles(deletedPaths);

    }

    private static void deleteFiles(List<String> deletedPaths) {
        for (String path : deletedPaths) {
            File f = new File(path);
            if(f.exists() && f.lastModified()>System.currentTimeMillis() - 1000L * 3600 * 24){
                log.info("start to delete continue dir as modified latest 24h: {}",path);
                continue;
            }
            try {
                log.info("start to delete dir: {}",path);
                FileUtils.deleteDirectory(f);
            } catch (IOException e) {
                log.warn("error to delete file: {}", path);
            }
        }
    }
    private static Map<String, List<StateLocation>> readRelatedFile(String path) {
        Map<String, List<StateLocation>> resultMap = new HashMap<>();
        resultMap.put(KEY_STATE, new ArrayList<>());
        resultMap.put(OPERATOR_STATE, new ArrayList<>());
        resultMap.put(IGNORE_JOB, new ArrayList<>());
        try {
//            Savepoint savepoint = SavepointLoader.loadSavepoint(path);
            CheckpointMetadata savepointMetadata = SavepointLoader.loadSavepointMetadata(path);
            log.warn("path: {},checkpoint id: {}", path, savepointMetadata.getCheckpointId());
            Collection<MasterState> list = savepointMetadata.getMasterStates();
            if (CollectionUtils.isEmpty(list)) {
                log.warn("path: {},empty masterState", path);
            }
            for (MasterState state : list) {
                log.warn("path: {},master state: {}", path, state);
            }
            for (OperatorState operatorState : savepointMetadata.getOperatorStates()) {
                log.warn("path: {},operatorState: {}", path, operatorState);
                if (operatorState.getStateSize() == 0) {
                    continue;
                }
                for (OperatorSubtaskState operatorSubtaskState : operatorState.getStates()) {
                    // 解析 operatorSubtaskState 的 ManagedKeyedState
                    List<String> keyStateLocation = parseManagedKeyedState(operatorSubtaskState);
                    log.warn("path: {},keyStateLocation: {}", path, keyStateLocation);
                    if (CollectionUtils.isNotEmpty(keyStateLocation)) {
                        resultMap.get(KEY_STATE).add(new StateLocation(path, keyStateLocation));
                    }

                    // 解析 operatorSubtaskState 的 ManagedOperatorState
                    List<String> operatorStateLocation = parseManagedOperatorState(operatorSubtaskState);
                    log.warn("path: {},operatorStateLocation: {}", path, operatorStateLocation);
                    if (CollectionUtils.isNotEmpty(operatorStateLocation)) {
                        resultMap.get(OPERATOR_STATE).add(new StateLocation(path, operatorStateLocation));
                    }
                    log.warn("*******");
                }
            }
        } catch (Exception e) {
            log.warn("read savepoint error,path=" + path, e);
            int index = path.lastIndexOf("/chk");
            String ignoreJob = path.substring(0, index);
            resultMap.get(IGNORE_JOB).add(new StateLocation(ignoreJob, null));
        }
        return resultMap;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StateLocation {

        private String path;

        private List<String> location;
    }
}
