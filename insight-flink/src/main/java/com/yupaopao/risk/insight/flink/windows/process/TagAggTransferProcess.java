package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.bean.portrait.AggTag;
import com.yupaopao.risk.insight.flink.constants.FactorConstants;
import com.yupaopao.risk.insight.flink.job.portrait.support.TagAggSupport;
import com.yupaopao.risk.insight.common.property.TagAggProperties;
import com.yupaopao.risk.insight.flink.windows.AggTagDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.streaming.api.functions.co.CoProcessFunction;
import org.apache.flink.util.CollectionUtil;
import org.apache.flink.util.Collector;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc TagAggTransferProcess
 * @date 2020/5/21
 */
@Slf4j
public class TagAggTransferProcess extends CoProcessFunction<String, String, AggTagDetail> {

    private static final long serialVersionUID = 4656617554782991576L;

    @Override public void processElement1(String value, Context ctx, Collector<AggTagDetail> out) throws Exception {
        List<AggTag> aggTags = TagAggSupport.getAggTagList();
        List<String> skipList = TagAggProperties.getInstance().getSkipList();
        for (String skip : skipList) {
            if (StringUtils.isNotEmpty(skip) && value.contains(skip)) {
                return;
            }
        }
        if (aggTags.size() <= 0) {
            return;
        }
        if (value.startsWith("[") && value.endsWith("]")){
            //AUDIT_10
            List<JSONObject> list = JSONObject.parseObject(value, List.class);
            for (JSONObject jsonObject : list) {
                jsonObject.put("UserId", jsonObject.getString("uid"));
                doProcess(out, aggTags, jsonObject, jsonObject.getLong("sendTime"));
            }
        }else {
            JSONObject param = JSONObject.parseObject(value);
            if (param.containsKey("action")) {
                if ("TAG-RESET".equals(param.getString("action"))) {
                    assembleFactorCal(param, aggTags, out);
                }
                return;
            }
            if (param.containsKey("data")){
                JSONObject data = param.getJSONObject("data");
                data.put("RiskLevel", param.get("level"));
                data.put("Result", param.get("result"));
                data.put("kafkaTopic", param.getString("kafkaTopic"));
                doProcess(out, aggTags, data, param.getLong("createdAt"));
            }else {
                //AUDIT_RESULT_70
                param.put("UserId", param.getString("uid"));
                doProcess(out, aggTags, param, param.getLong("sendTime"));
            }

        }
    }

    private void doProcess(Collector<AggTagDetail> out, List<AggTag> aggTags, JSONObject data, Long createdAt) {
        aggTags.forEach(aggTag -> {
            if (checkCondition(data, aggTag)) {
                String groupKey = "";
                if (StringUtils.isNotEmpty(aggTag.getAlias())){
                    groupKey = getAliasKey(aggTag, data);
                }else {
                    groupKey = getGroupKey(aggTag, data);
                }
                String aggValue = getAggValue(aggTag, data);

                if ((StringUtils.isNotEmpty(aggValue) || "COUNT".equals(aggTag.getFunction())) && StringUtils.isNotEmpty(groupKey) && !"0".equals(groupKey)) {
                    AggTagDetail result = new AggTagDetail();
                    result.setId(aggTag.getId());
                    result.setGroupKey(String.format(FactorConstants.TAG_DATA_PREFIX, aggTag.getVersion(), aggTag.getId(), groupKey));
                    result.setData(aggValue);
                    result.setFunction(aggTag.getFunction());
                    result.setTimeSpan(aggTag.getTimeSpan());
                    result.setTimeStamp(createdAt!=null?createdAt: data.getLong("timestamp"));
                    result.setType("aggregate");
                    out.collect(result);
                }
            }
        });
    }

    @Override public void processElement2(String value, Context ctx, Collector<AggTagDetail> out) throws Exception {
        JSONObject param = JSONObject.parseObject(value);
        AggTagDetail result = new AggTagDetail();
        result.setId(param.getInteger("id"));
        result.setGroupKey(param.getString("groupKey"));
        result.setData(param.getString("aggValue"));
        result.setFunction(param.getString("function"));
        result.setTimeSpan(param.getLong("timeSpan"));
        result.setTimeStamp(param.getLong("timeStamp"));
        result.setType(param.getString("type"));
        out.collect(result);
    }

    private boolean checkCondition(JSONObject param, AggTag tag) {
        if (StringUtils.isEmpty(tag.getCondition())) {
            return true;
        }
        String kafkaTopic = param.getString("kafkaTopic");
        if (!CollectionUtil.isNullOrEmpty(tag.getTopicList()) && !tag.getTopicList().contains(kafkaTopic)){
            return false;
        }
        try {
            Expression expression = new SpelExpressionParser().parseExpression(tag.getCondition());
            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setVariables(param);
            Object result = expression.getValue(context);
            log.debug("前置表达式执行结果:{} - {} - {}", result, tag.getCondition(), param);
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else {
                return result != null;
            }
        } catch (Throwable e) {
            log.error("执行聚合标签表达式出错: " + tag.getCondition() + param.toJSONString(), e);
        }
        return false;
    }

    private String getAliasKey(AggTag aggTag, JSONObject data){
        List<String> groupValues = new LinkedList<>();
        for (String aliasKey : aggTag.getAlias().split(",")) {
            String groupValue;

            if (aliasKey.contains(".")) {
                Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                groupValue = dataMap.getOrDefault(aliasKey, "").toString();
            } else {
                groupValue = data.getString(aliasKey);
            }
            // 缺失任意groupKeyValue则放弃
            if (StringUtils.isBlank(groupValue)) {
                return "";
            }
            groupValues.add(groupValue);
        }
        return String.join("$", groupValues);
    }
    private String getGroupKey(AggTag aggTag, JSONObject data){
        List<String> groupValues = new LinkedList<>();
        for (String groupKey : aggTag.getGroupKey().split(",")) {
            String groupValue;

            if (groupKey.contains(".")) {
                Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                groupValue = dataMap.getOrDefault(groupKey, "").toString();
            } else {
                groupValue = data.getString(groupKey);
            }
            // 缺失任意groupKeyValue则放弃
            if (StringUtils.isBlank(groupValue)) {
                return "";
            }
            groupValues.add(groupValue);
        }
        return String.join("", groupValues);
    }

    private String getAggValue(AggTag aggTag, JSONObject data){
        data.put("day", InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd));
        if(StringUtils.isEmpty(aggTag.getAggKey())){
            return "";
        } else {
            List<String> aggValues = new LinkedList<>();
            boolean hasValue = false;
            for (String aggKey : aggTag.getAggKey().split(",")) {
                String aggValue;

                if (aggKey.contains(".")) {
                    Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                    aggValue = dataMap.getOrDefault(aggKey, "").toString();
                } else {
                    aggValue = data.getOrDefault(aggKey, "").toString();
                }
                if (StringUtils.isNotBlank(aggValue)) {
                    hasValue = true;
                }
                aggValues.add(aggValue);
            }
            if (hasValue) {
                return String.join("$", aggValues);
            } else {
                return "";
            }
        }
    }

    private void assembleFactorCal(JSONObject data, List<AggTag> aggTags, Collector<AggTagDetail> out) {
        try {
            JSONObject params = data.getJSONObject("params");
            JSONArray purgeFactor = data.getJSONArray("tags");
            for (int i = 0; i < purgeFactor.size(); i++) {
                JSONObject jsonObject = purgeFactor.getJSONObject(i);
                long factorId = jsonObject.getLong("tagId");
                aggTags.forEach(aggTag -> {
                    if (aggTag.getId() == factorId) {
                        String groupKey = "";
                        if (StringUtils.isNotEmpty(aggTag.getAlias())){
                            groupKey = getAliasKey(aggTag, data);
                        }else {
                            groupKey = getGroupKey(aggTag, params);
                        }
                        if (StringUtils.isEmpty(groupKey)) {
                            return;
                        }

                        AggTagDetail result = new AggTagDetail();
                        result.setId(aggTag.getId());
                        result.setGroupKey(String.format(FactorConstants.TAG_DATA_PREFIX, aggTag.getVersion(), aggTag.getId(), groupKey));
                        result.setFunction(aggTag.getFunction());
                        result.setTimeSpan(aggTag.getTimeSpan());
                        result.setData("");
                        result.setTimeStamp(System.currentTimeMillis());
                        result.setType("purge");
                        result.setPurge(true);
                        log.info("清除聚合标签: {}", JSONObject.toJSONString(result));
                        out.collect(result);
                    }
                });
            }
        } catch (Exception e) {
            log.error("清除聚集标签异常：", e);
        }
    }
}
