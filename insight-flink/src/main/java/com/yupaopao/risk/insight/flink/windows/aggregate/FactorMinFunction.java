package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.flink.utils.FactorUtil;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import org.apache.flink.api.common.functions.AggregateFunction;

public class FactorMinFunction implements AggregateFunction<FactorCalDetail, AggregateResult, AggregateResult> {
    @Override public AggregateResult createAccumulator() {
        AggregateResult result = new AggregateResult();
        result.setResult(Double.MAX_VALUE);
        return result;
    }

    @Override public AggregateResult add(FactorCalDetail value, AggregateResult accumulator) {
        accumulator.setKey(value.getGroupKey());
        accumulator.setResult(Math.min(accumulator.getResult(), Double.parseDouble(value.getData())) );
        accumulator.setTimeSpan(FactorUtil.getRemainTime(value));
        Cat.logMetricForCount("factor.min");
        accumulator.setPurge(value.isPurge());
        return accumulator;
    }

    @Override public AggregateResult getResult(AggregateResult accumulator) {
        return accumulator;
    }

    @Override public AggregateResult merge(AggregateResult a, AggregateResult b) {
        if (a.getKey().equals(b.getKey())) {
            a.setResult(Math.min(a.getResult(), b.getResult()));
        }
        return a;
    }
}
