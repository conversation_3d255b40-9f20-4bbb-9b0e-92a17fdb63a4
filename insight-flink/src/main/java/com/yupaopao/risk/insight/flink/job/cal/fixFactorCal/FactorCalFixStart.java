package com.yupaopao.risk.insight.flink.job.cal.fixFactorCal;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.utils.InnerFlinkJobInvoker;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.ExecutionEnvironment;

import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/****
 * zengxiangcai
 * 2023/2/1 16:49
 *
 * 累计因子修复任务
 * 1、加载老的正确的checkpoint1 并写入flink_checkpoint_data
 * 2、加载新的错误的checkpoint2 并写入flink_checkpoint_data
 * 3、准备需要处理数据到 flink_checkpoint_rollback
 * 4、启动回滚任务【为方便check，手动执行，不放在整个流程中自动执行】
 *
 * jar name factor_fix.jar
 *
 *
 * [run jobs]
 * factor_fix.jar
 * fix_factor_cal_checkpoints_load.jar
 * fix_factor_cal_prepare_data.jar
 * fix_factor_cal_rollback.jar
 *
 *
 ***/
public class FactorCalFixStart {


    private static void buildJobArgs() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put(FixConstants.CORRECT_CK_PATH, "file:/flink/risk_test_113/checkpoints" +
                "/f2f5590d75e2533485039b44f3e1112a/chk-12911");
        params.put(FixConstants.CORRECT_CK_TIME, "2023-02-06 10:59:55");
        params.put(FixConstants.LATEST_CK_PATH, "file:/flink/risk_test_113/checkpoints" +
                "/f2f5590d75e2533485039b44f3e1112a/chk-12919");
        params.put(FixConstants.LATEST_CK_TIME, "2023-02-06 11:23:55");
//        params.put(FixConstants.JOB_CREATED_AT, "2023-02-01 19:58:51");


        //prod
//        params = new HashMap<>();
//        params.put(FixConstants.CORRECT_CK_PATH, "file:/flink/flink_1.13" +
//                ".3/checkpoints/349897d4c251003a26c38b87244f31c7/chk-320202");
//        params.put(FixConstants.CORRECT_CK_TIME, "2023-02-03 11:18:41");
//        params.put(FixConstants.LATEST_CK_PATH, "file:/flink/flink_1.13.3/checkpoints/349897d4c251003a26c38b87244f31c7/chk-320211");
//        params.put(FixConstants.LATEST_CK_TIME, "2023-02-03 11:45:41");

        System.err.println(URLEncoder.encode(JSON.toJSONString(params), "UTF-8"));
    }

    public static void main(String[] args) throws Exception {

        if(true){
            buildJobArgs();
            return;
        }
        Map<String, String> argMap = getParams(args);

        FixFactorCalLogOutputFormat outputFormat = new FixFactorCalLogOutputFormat() {
            @Override
            public void finalizeGlobal(int parallelism) throws IOException {
                //执行load 程序
                String runtime = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
                if (!argMap.containsKey(FixConstants.JOB_CREATED_AT)) {
                    argMap.put("createdAt", runtime);
                }
                Map<String, String> correctMap = new HashMap<>();
                correctMap.put(FixConstants.JOB_CREATED_AT, runtime);
                correctMap.put(FixConstants.CK_PATH, argMap.get(FixConstants.CORRECT_CK_PATH));
                correctMap.put(FixConstants.CK_TIME, argMap.get(FixConstants.CORRECT_CK_TIME));
                correctMap.put("jobName", FixConstants.LOAD_CORRECT_CK_JOB_NAME);
                correctMap.put("runType", "loadState");

                Map<String, String> latestMap = new HashMap<>();
                latestMap.put(FixConstants.JOB_CREATED_AT, runtime);
                latestMap.put(FixConstants.CK_PATH, argMap.get(FixConstants.LATEST_CK_PATH));
                latestMap.put(FixConstants.CK_TIME, argMap.get(FixConstants.LATEST_CK_TIME));
                latestMap.put("jobName", FixConstants.LOAD_LATEST_CK_JOB_NAME);
                latestMap.put("runType", "loadState");

                Map<String,String> extendParam = argMap;
                correctMap.put(FixConstants.ORIGINAL_EXTEND_PARAM, JSON.toJSONString(extendParam));
                latestMap.put(FixConstants.ORIGINAL_EXTEND_PARAM, JSON.toJSONString(extendParam));


                InnerFlinkJobInvoker.invokeJob(FixConstants.LOAD_CHECKPOINT_DATA_JAR,
                        URLEncoder.encode(JSON.toJSONString(correctMap),
                                "UTF-8"));
                InnerFlinkJobInvoker.invokeJob(FixConstants.LOAD_CHECKPOINT_DATA_JAR,
                        URLEncoder.encode(JSON.toJSONString(latestMap),
                                "UTF-8"));
            }
        };

        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        env.fromElements("start fix factor cal process... ")
                .output(outputFormat);
        env.execute("fix_factor_cal_job");
    }


    public static Map<String, String> getParams(String args[]) throws Exception {
        if (args == null || args.length < 1) {
            throw new Exception("correct checkpoint and latest error checkpoint must be provided...");
        }

        Map<String, String> argMap = JSON.parseObject(URLDecoder.decode(args[0], "UTF-8"), Map.class);

        for (String needParam : FixConstants.START_JOB_NEED_PARAMS) {
            if (StringUtils.isEmpty(argMap.get(needParam))) {
                throw new Exception(needParam + " cannot be empty ...");
            }
        }
        return argMap;
    }
}
