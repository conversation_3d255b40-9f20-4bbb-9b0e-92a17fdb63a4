package com.yupaopao.risk.insight.flink.job.cal.fixFactorCal;

import java.util.Arrays;
import java.util.List;

/****
 * zengxiangcai
 * 2023/2/2 19:23
 ***/
public class FixConstants {

    public static String CORRECT_CK_PATH = "correctCkPath";

    public static String CORRECT_CK_TIME = "correctCkTime";

    public static String LATEST_CK_PATH = "latestCkPath";

    public static String LATEST_CK_TIME = "latestCkTime";

    public static String JOB_CREATED_AT = "createdAt";


    public static List<String> START_JOB_NEED_PARAMS = Arrays.asList(CORRECT_CK_PATH, CORRECT_CK_TIME, LATEST_CK_PATH,
            LATEST_CK_TIME);


    public static List<String> PREPARE_DATA_JOB_NEED_PARAMS = Arrays.asList(CORRECT_CK_PATH, CORRECT_CK_TIME,
            LATEST_CK_PATH,
            LATEST_CK_TIME, JOB_CREATED_AT);


    public static String CK_TIME = "ckTime";

    public static String CK_PATH = "ckPath";

    public static String ORIGINAL_EXTEND_PARAM = "extendParam";

    public static String LOAD_CORRECT_CK_JOB_NAME = "factor_cal_fix:load_correct_checkpoint";

    public static String LOAD_LATEST_CK_JOB_NAME = "factor_cal_fix:load_latest_checkpoint";

    public static String PREPARE_JOB_START= "factor_cal_fix:start_prepare";


    public static String LOAD_CHECKPOINT_DATA_JAR = "fix_factor_cal_checkpoints_load.jar";

    public static String PREPARE_ROLLBACK_DATA_JAR = "fix_factor_cal_prepare_data.jar";

}
