package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.bean.audit.AuditMetric;
import com.yupaopao.risk.insight.flink.bean.audit.MxMetric;
import com.yupaopao.risk.insight.flink.utils.FactorUtil;
import com.yupaopao.risk.insight.flink.utils.MxMetricCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;

import java.util.List;
import java.util.Objects;

@Slf4j
public class AuditMetricCalFlatMap implements FlatMapFunction<AuditMetric, AuditMetric> {

    @Override public void flatMap(AuditMetric value, Collector<AuditMetric> out) throws Exception {
        JSONObject param = (JSONObject) JSONObject.toJSON(value);
        List<MxMetric> sum = MxMetricCacheUtils.getMetricByFunc("SUM");
        if (Objects.isNull(sum)  || sum.size() == 0) {
            return;
        }
        if (null == param) {
            return;
        }

        sum.forEach(item -> {
            if (FactorUtil.checkCondition(item, param)) {
                String groupKey = FactorUtil.getGroupKey(item, param);
                String aggValue = FactorUtil.getAggValue(item, param);

                if (StringUtils.isNotEmpty(aggValue) && StringUtils.isNotEmpty(groupKey)) {
                    AuditMetric auditMetric = new AuditMetric();
                    auditMetric.cloneData(value);
                    auditMetric.setMetric(item.getCode());
                    auditMetric.setGroupKey(groupKey);
                    out.collect(auditMetric);
                }
            }
        });
    }
}
