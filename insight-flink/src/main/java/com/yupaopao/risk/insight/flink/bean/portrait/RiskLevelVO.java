package com.yupaopao.risk.insight.flink.bean.portrait;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class RiskLevelVO implements Serializable {

    private static final long serialVersionUID = 7983101450871392237L;
    private String code;
    private String name;
    private Integer level;
    private Double scope;
    private Long createTs;
    private String fieldName;
}
