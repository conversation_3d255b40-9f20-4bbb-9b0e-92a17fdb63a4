package com.yupaopao.risk.insight.flink.job.etl.common.map;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.platform.common.utils.StringUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.dubbo.core.DubboConfigConstants;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.es.core.EsConfigKeys;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.es.core.EsUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.options.MapperConfig;
import io.searchbox.client.JestClient;
import io.searchbox.client.JestClientFactory;
import io.searchbox.client.config.HttpClientConfig;
import io.searchbox.core.Search;
import io.searchbox.core.SearchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.*;

/**
 * Copyright (C), 2022, jimmy
 *
 * <AUTHOR>
 * @desc ElasticSearchMapper
 * @date 2022/5/16
 */
@Slf4j
public class EsMapper extends BaseMapFunction {
    protected final Logger LOG = LoggerFactory.getLogger(getClass());

    private static final String REPLACE = "\\$\\(%s\\)";

    private String address;
    private String username;
    private String password;
    private String query;

    private String[] index;
    private String[] doc;
    private Integer batchSize;

    private List<String> argKeys = new ArrayList<>();

    private Set<String> emitKeys = new HashSet<>();

    private List<String> paramKeys = new ArrayList<>();

    private transient JestClient jestClient;

    LongCounter readerCounter;

    LongCounter writerCounter;

    public EsMapper(MapperConfig config) throws Exception {
        super(config);
        address = "http://" + config.getStringVal(EsConfigKeys.KEY_ADDRESS);
        username = config.getStringVal(EsConfigKeys.KEY_USERNAME);
        password = config.getStringVal(EsConfigKeys.KEY_PASSWORD);
        index = EsUtil.getStringArray(config.getVal(EsConfigKeys.KEY_INDEX));
        doc = EsUtil.getStringArray(config.getVal(EsConfigKeys.KEY_DOC));
        query = config.getStringVal(EsConfigKeys.KEY_QUERY);
        batchSize = config.getIntVal(EsConfigKeys.KEY_BATCH_SIZE, 100);

        argKeys.addAll(Arrays.asList(config.getStringVal(DubboConfigConstants.KEY_ARG_KEYS).split(",")));
        String[] list = config.getStringVal("emitKeys").split(",");
        for (String key : list) {
            if (key.contains("params")) {
                paramKeys.add(key);
            } else {
                emitKeys.add(key);
            }
        }
    }

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        JestClientFactory jestClientFactory = new JestClientFactory();
        try {
            HttpClientConfig httpClientConfig;
            if (StringUtil.isNotBlank(username) && StringUtil.isNotBlank(password)) {
                httpClientConfig = new HttpClientConfig.Builder(address)
                    .defaultCredentials(username, password)
                    .multiThreaded(true).build();
            } else {
                httpClientConfig = new HttpClientConfig.Builder(address)
                    .multiThreaded(true).build();
            }
            jestClientFactory.setHttpClientConfig(httpClientConfig);
            jestClient = jestClientFactory.getObject();
            readerCounter = context.getLongCounter("reader_count_" + id);
            writerCounter = context.getLongCounter("writer_count_" + id);

            mapperMetric.addMetric("reader_count_" + id, readerCounter);
            mapperMetric.addMetric("writer_count_" + id, writerCounter);
        } catch (RuntimeException ex) {
            throw new IllegalStateException("Invalid ES nodes ", ex);
        }
    }

    @Override public void flatMap(Map<String, Object> value, Collector<Map<String, Object>> out) throws Exception {
        super.flatMap(value, out);
        if (isDebug && value.containsKey(KEY_DEBUG)) {
            return;
        }
        readerCounter.add(1);
        Map<String, Object> tmp = new HashMap<>(argKeys.size());
        for (String key : argKeys) {
            Object keyValue = value.get(key);
            if (Objects.isNull(keyValue) || keyValue.toString().length() == 0) {
                LOG.info("es数据处理存在空值，返回");
                return;
            }
            tmp.put(key, keyValue);
        }

        String query = buildQuery(tmp);
        if (checkSql(query)) {
            LOG.info("es数据处理 sql 存在不能替换变量");
            return;
        }
        Search.Builder searchBuilder = new Search.Builder(query)
            .addIndices(Arrays.asList(index))
            .addTypes(Arrays.asList(doc))
            ;

        SearchResult execute = jestClient.execute(searchBuilder.build());
        if (execute.getTotal() > 0) {
            Map<String, Object> result = new HashMap<>();
            JSONObject row = JSONObject.parseObject(execute.getJsonString());
            for (String key : emitKeys) {
                if ("queryResult".equals(key)) {
                    result.putAll(row);
                } else if (row.containsKey(key)) {
                    result.put(key, row.get(key));
                }
            }

            for (String key : paramKeys) {
                if ("params".equals(key)) {
                    result.putAll(value);
                } else {
                    String[] split = key.split("\\.");
                    result.put(key, value.get(split[1]));
                }
            }
            writerCounter.add(1);
            out.collect(result);
        }
    }

    private boolean checkSql(String query) {
        return query.length() == 0 || query.contains("$(");
    }

    private String buildQuery(Map<String, Object> data) {
        String currentQuery = query;
        for (String key : data.keySet()) {
            currentQuery =
                currentQuery.replaceAll(String.format(REPLACE, key), data.get(key).toString());
        }
        return currentQuery;
    }

    @Override public void close() throws IOException {
        if (jestClient != null) {

            jestClient.close();
            jestClient = null;
        }
    }

}
