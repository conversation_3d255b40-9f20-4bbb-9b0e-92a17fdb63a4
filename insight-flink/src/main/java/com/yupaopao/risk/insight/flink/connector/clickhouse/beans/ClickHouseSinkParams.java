package com.yupaopao.risk.insight.flink.connector.clickhouse.beans;

import com.google.common.base.Preconditions;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;


/**
 * Created by Avalon on 2020/3/11 18:51
 */
@Data
public class ClickHouseSinkParams {

    private List<String> hostsWithPorts;
    private String username;
    private String password;
    private String credentials;
    private boolean authorizationRequired;

    private int currentHostId = 0;

    private int numWriters = 10;
    private int queueMaxCapacity = 5000;

    private int timeout = 60;
    private int maxRetries = 30;


    public ClickHouseSinkParams(ClickHouseProperties properties) {
        Preconditions.checkNotNull(properties);

        String hostsString = properties.getHost();
        Preconditions.checkNotNull(hostsString);

        hostsWithPorts = buildHostsAndPort(hostsString);
        Preconditions.checkArgument(hostsWithPorts.size() > 0);

        String usr = properties.getUsername();
        String pass = properties.getPassword();

        if (StringUtils.isNotEmpty(usr) && StringUtils.isNotEmpty(pass)) {
            username = usr;
            password = pass;

            credentials = buildCredentials(username, password);
            authorizationRequired = true;
        } else {
            // avoid NPE
            credentials = "";
            password = "";
            username = "";
            authorizationRequired = false;
        }

        try {
            int numWriters = properties.getNumWriters();
            if (numWriters > 0) {
                this.numWriters = numWriters;
            }
            int queueMaxCapacity = properties.getQueueMaxCapacity();
            if (numWriters > 0) {
                this.queueMaxCapacity = queueMaxCapacity;
            }
            int maxRetries = properties.getMaxRetries();
            if (numWriters > 0) {
                this.maxRetries = maxRetries;
            }
            int timeout = properties.getTimeout();
            if (numWriters > 0) {
                this.timeout = timeout;
            }
        } catch (Exception e) {}

    }

    private static List<String> buildHostsAndPort(String hostsString) {
        return Arrays.stream(hostsString
                .split(", "))
                .map(ClickHouseSinkParams::checkHttpAndAdd)
                .collect(Collectors.toList());
    }

    private static String checkHttpAndAdd(String host) {
        String newHost = host.replace(" ", "");
        if (!newHost.contains("http")) {
            return "http://" + newHost;
        }
        return newHost;
    }

    private static String buildCredentials(String user, String password) {
        Base64.Encoder x = Base64.getEncoder();
        String credentials = String.join(":", user, password);
        return new String(x.encode(credentials.getBytes()));
    }

    public String getRandomHostUrl() {
        currentHostId = ThreadLocalRandom.current().nextInt(hostsWithPorts.size());
        return hostsWithPorts.get(currentHostId);
    }
}
