package com.yupaopao.risk.insight.flink.job.etl.common.connect.kafkanew.core.decoder;

import com.alibaba.fastjson.JSONObject;
import com.dtstack.flinkx.decoder.IDecode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;

public class JsonDecoder implements IDecode, Serializable {
    private static final Logger LOG = LoggerFactory.getLogger(JsonDecoder.class);

    private static final String KEY_MESSAGE = "message";

    @Override
    public Map<String, Object> decode(final String message) {
        try {
            return JSONObject.parseObject(message);
        } catch (Exception e) {
            LOG.error(e.getMessage());
            return Collections.singletonMap(KEY_MESSAGE, message);
        }
    }
}
