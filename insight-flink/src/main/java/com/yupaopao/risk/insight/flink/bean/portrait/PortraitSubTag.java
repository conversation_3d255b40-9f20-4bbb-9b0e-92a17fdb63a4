package com.yupaopao.risk.insight.flink.bean.portrait;

import lombok.Data;

import java.io.Serializable;

@Data
public class PortraitSubTag implements Comparable, Serializable {
    private String code;
    private String name;
    private String createAt;
    private Double score;
    private String updateTime;
    private Integer riskLevel;
    private Integer riskTotal = 0 ;

    @Override
    public int compareTo(Object o) {
        PortraitSubTag other = (PortraitSubTag) o;
        if (this.riskLevel.intValue()!=other.getRiskLevel().intValue()){
            return this.riskLevel.compareTo(other.getRiskTotal()) * -1;
        }else if (this.riskTotal.intValue() != other.getRiskTotal()){
            return this.riskTotal.compareTo(other.riskTotal) * -1;
        }else {
            return this.name.compareTo(other.getName()) * -1;
        }
    }


}
