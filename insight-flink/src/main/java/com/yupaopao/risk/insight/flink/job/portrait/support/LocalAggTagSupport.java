package com.yupaopao.risk.insight.flink.job.portrait.support;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitBean;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitFirstTag;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitSubTag;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.flink.utils.ip2region.IPDetail;
import com.yupaopao.risk.insight.flink.utils.ip2region.IPSearchUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

/**
 * <AUTHOR>
 */
public class LocalAggTagSupport {

    private static String LOCAL_IM_TAG_PREFIX = "im";
    private static String LOCAL_TAG_PREFIX = "疑似";
    private static String REJECT = "reject";
    private static String EVENT_IM = "im-message";
    private static final String ZERO = "0";

    public static void buildTag(PortraitBean portraitBean, TreeSet<PortraitFirstTag> tagMap, JSONObject riskLog, String currentDayStr) throws Exception {

        JSONObject riskResult = riskLog.getJSONObject("riskResult");
        JSONObject riskAction = riskLog.getJSONObject("riskAction");
        if (CommonTagSupport.isEmpty(riskAction) || CommonTagSupport.isEmpty(riskAction)){
            return;
        }
        //当前使用设备
        portraitBean.setCurrentIsmId(riskAction.getString("deviceId"));
        //当前所在城市
        String clientIp = riskAction.getString("clientIp");
        if (StringUtils.isNotEmpty(clientIp)){
            IPDetail ipInfo = IPSearchUtil.getIpInfo(clientIp);
            StringBuilder cityBuilder = new StringBuilder();
            if (StringUtils.isNotEmpty(ipInfo.getCountry()) && !ZERO.equals(ipInfo.getCountry())){
                cityBuilder.append(ipInfo.getCountry()).append(" ");
            }
            if (StringUtils.isNotEmpty(ipInfo.getProvince()) && !ZERO.equals(ipInfo.getProvince())){
                cityBuilder.append(ipInfo.getProvince()).append(" ");
            }
            cityBuilder.append(ipInfo.getCity());
            portraitBean.setCurrentCity(cityBuilder.toString());
        }
        String level = riskResult.getString("level");

        if (StringUtils.isNotEmpty(level) && REJECT.equalsIgnoreCase(level)){
            doAggTag(riskLog.toJSONString(), tagMap, currentDayStr, riskAction.getString("eventCode"));
        }
    }

    /**
     * 累计违规记录 生成标签
     */
    private static void doAggTag(String data, TreeSet<PortraitFirstTag> tagMap, String currentDate, String eventCode) throws Exception {
        Map keyWordMap = PortraitApolloProperties.getBehaviorTagByKey();

        Map<String, String> nameCode = PortraitApolloProperties.getNameCode();
        long startTime = System.currentTimeMillis();
        Set<Map.Entry> set = keyWordMap.entrySet();
        for (Map.Entry entry : set) {
            List<String> keyWordList = (List) entry.getValue();
            for (String keyWord : keyWordList) {
                if (data.contains(keyWord)) {
                    PortraitFirstTag portraitFirstTag = getDoubtTag(eventCode, nameCode, entry, keyWord);
                    tagMap.add(portraitFirstTag);
                }
            }
        }
    }

    private static PortraitFirstTag getDoubtTag(String eventCode, Map<String, String> nameCode, Map.Entry entry, String keyWord) throws Exception {
        StringBuilder name = new StringBuilder();
        StringBuilder code = new StringBuilder();
        if (EVENT_IM.equals(eventCode)) {
            name.append(LOCAL_IM_TAG_PREFIX).append(LOCAL_TAG_PREFIX).append(entry.getKey());
            code.append(LOCAL_IM_TAG_PREFIX).append(nameCode.get(entry.getKey()));
        }else {
            name.append(LOCAL_TAG_PREFIX).append(entry.getKey());
            code.append(nameCode.get(entry.getKey()));
        }
        String currentDayStr = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);

        PortraitFirstTag portraitFirstTag = new PortraitFirstTag();
        portraitFirstTag.setName(name.toString());
        portraitFirstTag.setCode(code.toString());
        portraitFirstTag.setRiskTotal(1);
        portraitFirstTag.setRiskLevel(1);
        portraitFirstTag.setScore(0d);
        portraitFirstTag.setCreateAt(currentDayStr);
        portraitFirstTag.setUpdateTime(currentDayStr);

        TreeSet<PortraitSubTag> subTags = new TreeSet<>();

        PortraitSubTag portraitSubTag = new PortraitSubTag();
        //TODO 二级标签取具体违规事件
        String eventNameByCode = PortraitApolloProperties.getEventNameByCode(eventCode);
        portraitSubTag.setCode(CommonTagSupport.eventFieldCover("doubt-"+eventCode, CommonTagSupport.pattern));
        StringBuilder subtagName = new StringBuilder();
        subtagName.append(LOCAL_TAG_PREFIX).append(eventNameByCode).append(keyWord);
        portraitSubTag.setName(subtagName.toString());
        portraitSubTag.setCreateAt(currentDayStr);
        portraitSubTag.setUpdateTime(currentDayStr);
        //TODO 风险级别配置化
        portraitSubTag.setRiskLevel(1);
        portraitSubTag.setRiskTotal(1);
        portraitSubTag.setScore(0d);
        subTags.add(portraitSubTag);
        portraitFirstTag.setSubtag(subTags);
        return portraitFirstTag;
    }


}
