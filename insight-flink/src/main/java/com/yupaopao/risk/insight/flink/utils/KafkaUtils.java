package com.yupaopao.risk.insight.flink.utils;

import com.yupaopao.risk.insight.flink.connector.kafka.sink.serialization.KafkaObjSerializationSchema;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.PortraitMQDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.streaming.connectors.kafka.internals.KafkaDeserializationSchemaWrapper;

import java.util.List;
import java.util.Properties;

public class KafkaUtils {

    public static FlinkKafkaConsumer getKafkaConsumer(List<String> topicList, PropertyType propertyType, String groupId, String clientId){
        return getKafkaConsumer(topicList, propertyType, groupId, clientId, System.currentTimeMillis() - 1000);
    }

    public static FlinkKafkaConsumer getKafkaConsumer(List<String> topicList, PropertyType propertyType, String groupId, String clientId, KafkaDeserializationSchemaWrapper kafkaDeserializationSchemaWrapper){
        return getKafkaConsumer(topicList, propertyType, groupId, clientId, System.currentTimeMillis() - 1000,kafkaDeserializationSchemaWrapper);
    }

    public static FlinkKafkaConsumer getKafkaConsumer(List<String> topicList, PropertyType propertyType, String groupId, String clientId,long startupOffsetsTimestamp, KafkaDeserializationSchemaWrapper kafkaDeserializationSchemaWrapper){
        KafkaProperties kafkaProperties = KafkaProperties.getProperties(propertyType);
        Properties etlProperties = kafkaProperties.getProperties();
        etlProperties.put("group.id", groupId);
        etlProperties.put("client.id", clientId);
        FlinkKafkaConsumer flinkKafkaConsumer = new FlinkKafkaConsumer<>(topicList,
                kafkaDeserializationSchemaWrapper,
                kafkaProperties.getProperties());
        flinkKafkaConsumer.setStartFromTimestamp(startupOffsetsTimestamp);
        return flinkKafkaConsumer;
    }


    public static FlinkKafkaConsumer getKafkaConsumer(List<String> topicList, PropertyType propertyType, String groupId, String clientId,long startupOffsetsTimestamp){
        KafkaProperties kafkaProperties = KafkaProperties.getProperties(propertyType);
        Properties etlProperties = kafkaProperties.getProperties();
        etlProperties.put("group.id", groupId);
        etlProperties.put("client.id", clientId);
        FlinkKafkaConsumer flinkKafkaConsumer = new FlinkKafkaConsumer<>(topicList,
                new PortraitMQDeserializationSchemaWrapper(new SimpleStringSchema()),
                kafkaProperties.getProperties());
        flinkKafkaConsumer.setStartFromTimestamp(startupOffsetsTimestamp);
        return flinkKafkaConsumer;
    }

    public static FlinkKafkaProducer getKafkaProducer(String topic, PropertyType propertyType) {
        return getKafkaProducer(topic, propertyType, 300000);
    }


    public static FlinkKafkaProducer getKafkaProducer(String topic, PropertyType propertyType, Integer timeOut) {
        KafkaProperties kafkaProperties = KafkaProperties.getProperties(propertyType);
        Properties kafkaProps = kafkaProperties.getProperties();
        kafkaProps.put("transaction.timeout.ms", timeOut);
        kafkaProps.put("request.timeout.ms", 300000);
        return new FlinkKafkaProducer<>(
                topic,
                new KafkaObjSerializationSchema<>(topic),
                kafkaProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE);
    }

}
