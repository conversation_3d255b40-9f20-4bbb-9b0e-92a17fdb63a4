package com.yupaopao.risk.insight.flink.connector.hbase.source;

import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.io.DefaultInputSplitAssigner;
import org.apache.flink.api.common.io.RichInputFormat;
import org.apache.flink.api.common.io.statistics.BaseStatistics;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.InputSplitAssigner;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc HBaseCommonInputFormat
 * @date 2020/4/15
 */
@Slf4j
@Getter
public abstract class HBaseCommonInputFormat<T> extends RichInputFormat<T, HBaseSplit> {
    private HBaseProperties hBaseProperties;
    private LinkedBlockingQueue<Result> cacheQueue;
    private transient Connection conn;
    private TsTableInfo tsTableInfo;
    private List<HBaseInputSplitReadThread> threadList = new ArrayList<>();
    private LongCounter readerCounter = new LongCounter();

    public HBaseCommonInputFormat(TsTableInfo tsTableInfo, HBaseProperties hBaseProperties, LinkedBlockingQueue<Result> cacheQueue) {
        this.tsTableInfo = tsTableInfo;
        this.hBaseProperties = hBaseProperties;
        this.cacheQueue = cacheQueue;
    }

    @Override public void configure(Configuration parameters) {
    }

    @Override public BaseStatistics getStatistics(BaseStatistics cachedStatistics) throws IOException {
        return null;
    }

    @Override public InputSplitAssigner getInputSplitAssigner(HBaseSplit[] inputSplits) {
        return new DefaultInputSplitAssigner(inputSplits);
    }


    @Override
    public void open(HBaseSplit split) throws IOException {
        try {
            if (split == null) {
                return;
            }
            //按split的bucket数量多线程读
            List<HBaseSplit.BucketSplit> bucketList = split.fetchBucketList();
            if (CollectionUtils.isEmpty(bucketList)) {
                return;
            }
            int bucketIndex = 0;
            for (HBaseSplit.BucketSplit bucket : bucketList) {
                String threadName =
                    "hbase-split-" + split.getSplitNumber() + "-bucket-" + bucketIndex++;
                String hbaseTableName = tsTableInfo.getTableName();
                HTable currentTable = HBaseUtil.getTable(hbaseTableName, createConnection());
                HBaseInputSplitReadThread readThread = initReadThread(bucket,
                    currentTable,
                    hbaseTableName,
                    threadName,
                    tsTableInfo,
                    cacheQueue
                );
                threadList.add(readThread);
                readThread.start();
                log.info("read split: {}, thread: {}", bucket, readThread.getName());
            }
            if(getRuntimeContext().getAccumulator("hbaseReader") == null){
                getRuntimeContext().addAccumulator("hbaseReader",readerCounter);
            }
        } catch (Exception e) {
            log.error("open input format error: ", e);
            throw e;
        }
    }

    public HBaseInputSplitReadThread initReadThread(HBaseSplit.BucketSplit bucket,HTable currentTable, String hbaseTableName,
                               String threadName, TsTableInfo tsTableInfo,LinkedBlockingQueue<Result> queue){
        HBaseInputSplitReadThread readThread = new HBaseInputSplitReadThread(bucket,
                currentTable,
                queue,
                hbaseTableName,
                tsTableInfo.getTableId(),
                tsTableInfo.getTableType(),
                tsTableInfo.getColumnNames(),
                threadName
        );
        return readThread;
    }

    @Override public boolean reachedEnd() throws IOException {
        threadList.removeIf(t -> t.exit);
        return CollectionUtils.isEmpty(threadList) && CollectionUtils.isEmpty(cacheQueue);
    }

    @Override public void close() throws IOException {
        if(CollectionUtils.isEmpty(threadList)){
            return;
        }
        for (HBaseInputSplitReadThread t : threadList) {
            t.setExitFlag(true);
        }
    }

    @Override
    public void closeInputFormat() throws IOException {
        cacheQueue = null;
        HBaseUtil.closeResource(conn);
    }

    private Connection createConnection() throws IOException {
        if (conn != null) {
            return conn;
        }
        conn = HBaseUtil.createConnection(hBaseProperties);
        return conn;
    }
}
