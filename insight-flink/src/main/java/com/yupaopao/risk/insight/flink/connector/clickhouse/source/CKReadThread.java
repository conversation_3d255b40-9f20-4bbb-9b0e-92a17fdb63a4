package com.yupaopao.risk.insight.flink.connector.clickhouse.source;

import com.alibaba.fastjson.JSON;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.clickhouse.jdbc.ClickHouseStatement;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-21 14:45
 *
 ***/

@Slf4j
public class CKReadThread extends Thread {

    private String sql;
    private LinkedBlockingQueue<String> fetchedCacheList;

    private ClickHouseProperties properties;

    public volatile boolean exit = false;

    public CKReadThread(String sql, LinkedBlockingQueue<String> queue, ClickHouseProperties properties) {
        sql = sql.trim();
        if (sql.endsWith(";")) {
            sql = sql.substring(0, sql.length() - 1);
        }
        this.sql = sql;
        this.fetchedCacheList = queue;
        this.properties = properties;
    }

    @Override
    public void run() {
        try {
            int totalRetry = 0;
            while (!Thread.interrupted()) {
                Long totalCount = 0L;
                //分页查询
                String totalCountSql = "select count(1) from (" + sql + ") as tmp_count";
                try (Connection connection = ClickHouseUtil.getCkConn(properties);
                     Statement stmt = connection.createStatement();
                     ResultSet rs = stmt.executeQuery(totalCountSql);) {
                    if (rs.next()) {
                        totalCount = rs.getLong(1);
                    }
                    log.info("totalCount: {}", totalCount);
                } catch (Exception e) {
                    log.warn("fetch totalCount error: sql="+totalCountSql, e);
                    return;
                }
                //每次查询5w条
                Long pageSize = 30000L;
                Long totalPages = (long) Math.ceil(totalCount * 1.0 / pageSize);
                int pageNumber = 0;
                while (pageNumber++ < totalPages) {
                    try (Connection conn = ClickHouseUtil.getCkConn(properties);) {
                        String pageSql =
                                "select * from (" + sql + ") limit " + ((pageNumber - 1) <= 0 ? 0 : (pageNumber - 1)) * pageSize + ", " + pageSize;
                        List<Map<String, String>> resList = ClickHouseUtil.executeQuery(conn, pageSql);
                        if (CollectionUtils.isNotEmpty(resList)) {
                            for (Map<String, String> rowMap : resList) {
                                fetchedCacheList.put(JSON.toJSONString(rowMap));
                            }
                        }

                        log.info("current loop: " + pageNumber + ", total page: " + totalPages);
                    } catch (Exception e) {
                        log.warn("error to get ck page: ", e);
                        pageNumber--;
                        if (++totalCount > 2) {
                            //max retry 2 times
                            throw e;
                        }
                    }
                }
                break;
            }
        } catch (Exception e) {
            log.error("ck read data error: ", e);
        } finally {
            exit = true;
        }
    }


}
