package com.yupaopao.risk.insight.flink.bean.audit;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.beanutils.PropertyUtils;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-16 16:50
 *
 ***/

@Getter
@Setter
public class PerformanceMxTask extends MxTask {

    public PerformanceMxTask() {
        super();
    }

    public PerformanceMxTask(MxTask task) {
        try {
            PropertyUtils.copyProperties(this,task);
        } catch (Exception e) {
        }
        this.assignCost = 0;
        this.processCost = 0;
        this.reassignCost = 0;
        this.wholeProcessCost = 0;
        this.flinkHandleTime = System.currentTimeMillis();
    }

    //人效指标,耗时秒数
    private long assignCost;
    private long processCost;
    private long reassignCost;
    private long wholeProcessCost;
    private long timeoutCount;

    private long flinkHandleTime; //flink处理时间

    private String flinkRemark;

    public boolean illegalTask() {
        if (finished() && (assignCost <= 0 && processCost <= 0 && reassignCost <= 0 && wholeProcessCost <= 0)) {
            return true;
        }
        return false;
    }
}
