package com.yupaopao.risk.insight.flink.connector.gdb.sink;

import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.common.property.connection.GDBProperties;
import com.yupaopao.risk.insight.common.support.GDBUtil;
import com.yupaopao.risk.insight.flink.bean.graph.EventGraphElement;
import com.yupaopao.risk.insight.common.thread.InsightFlinkThreadFactory;
import com.yupaopao.risk.insight.flink.utils.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.configuration.Configuration;
import org.apache.tinkerpop.gremlin.driver.Client;
import org.apache.tinkerpop.gremlin.driver.Cluster;

import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-21 17:33
 *
 * 写入gdb，入参为sql
 *
 ***/

@Slf4j
public class GDBOutputFormat extends RichOutputFormat<EventGraphElement> {

    private GDBProperties gdbProperties;

    private transient Cluster cluster = null;
    private transient Client client = null;

    public GDBOutputFormat(GDBProperties gdbProperties) {
        this.gdbProperties = gdbProperties;
    }

    private transient ExecutorService executorService;

    @Override
    public void configure(Configuration parameters) {

    }

    @Override
    public void open(int taskNumber, int numTasks) throws IOException {
        executorService = new ThreadPoolExecutor(6, 6,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(6000),
                new InsightFlinkThreadFactory("gdb-write"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
        cluster = GDBUtil.getCluster(gdbProperties);
        client = GDBUtil.initClient(cluster);
    }

    /***
     *
     * @param record the add dsl
     * @throws IOException
     */
    @Override
    public void writeRecord(EventGraphElement record) throws IOException {
        executorService.submit(() -> {
            try {
                if (directInsert()) {
                    //直接add写入，通过gdb id唯一去处理不同的id
                    GDBUtil.insertGraphElements(client, record.getVertexes());
                    GDBUtil.insertGraphElements(client, record.getEdges());
                } else {
                    GDBUtil.addGraphElements(client, record.getVertexes());
                    GDBUtil.addGraphElements(client, record.getEdges());
                }
                Cat.logMetricForCount("gdb.write.send.count");
            } catch (Exception e) {
                if (e.getMessage() != null && e.getMessage().contains("GraphDB id exists")) {
                    //相同id的顶点和边插入会报错，忽略,也可以先插入再插入
                } else {
                    log.warn("execute error, param=" + record, e);
                }
            }
        });
    }


    @Override
    public void close() throws IOException {
        if (client != null) {
            client.close();
        }
        if (cluster != null) {
            cluster.close();
        }
        if (executorService != null) {
            try {
                ThreadUtil.shutdownExecutorService(executorService);
            } catch (InterruptedException e) {
                log.error("shutdown executorService error: ", e);
            }
        }
    }

    public boolean directInsert() {
        return false;
    }


}
