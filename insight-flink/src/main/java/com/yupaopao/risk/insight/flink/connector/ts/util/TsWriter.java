//package com.yupaopao.risk.insight.flink.connector.ts.util;
//
//import com.alicloud.openservices.tablestore.SyncClient;
//import com.alicloud.openservices.tablestore.model.*;
//import com.yupaopao.risk.insight.flink.constants.ParamConstants;
//import com.yupaopao.risk.insight.flink.constants.TsConstants;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import com.yupaopao.risk.insight.flink.utils.InsightFlinkUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.time.DateFormatUtils;
//import org.apache.flink.configuration.Configuration;
//
//import java.util.Arrays;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//
///***
// * table store 写数据表主键是必须有的，非主键列可以动态增加(宽表)
// */
//@Slf4j
//public class TsWriter {
//
//    private SyncClient syncClient;
//
//    private TsTableInfo tsTableInfo;
//
//    private Configuration conf;
//
//    private String currentSubTaskName;//方便构建临时表主键
//
//    public TsWriter(SyncClient syncClient, TsTableInfo tsTableInfo, Configuration conf, String subTaskName) {
//        this.syncClient = syncClient;
//        this.tsTableInfo = tsTableInfo;
//        this.conf = conf;
//        this.currentSubTaskName = subTaskName;
//    }
//
//
//    private PrimaryKey buildRiskAnalysisResultPk(int batchIndex) {
//        //结果统一的临时表, ts按主键排序，插入时需要保证顺序
//        long timestamp = System.currentTimeMillis();
//        String jobId = conf.getString(ParamConstants.CURRENT_JOB_ID, "noJobId");
//        String uuid = InsightFlinkUtils.getUUID();
//        PrimaryKeyBuilder pkBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        pkBuilder.addPrimaryKeyColumn(TsConstants.RISK_ANALYSIS_RESULT_PK_JOB_ID, PrimaryKeyValue.fromString(jobId));
//        pkBuilder.addPrimaryKeyColumn(TsConstants.RISK_ANALYSIS_RESULT_PK_UUID,
//                PrimaryKeyValue.fromLong(timestamp + batchIndex));
//        return pkBuilder.build();
//    }
//
//
//    private PrimaryKey buildStandardPk(Row row) {
//        Map<String, PrimaryKeyType> pkTypeMap = tsTableInfo.getPrimaryKeyTypeMap();
//        try {
//            if (pkTypeMap == null || pkTypeMap.isEmpty()) {
//                throw new IllegalArgumentException("the output tablestore pk info is empty");
//            }
//            PrimaryKeyBuilder pkBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//            for (Map.Entry<String, PrimaryKeyType> pkType : pkTypeMap.entrySet()) {
//                Column column = row.getLatestColumn(pkType.getKey());
//                if (column == null) {
//                    return row.getPrimaryKey();
//                } else {
//                    pkBuilder.addPrimaryKeyColumn(pkType.getKey(), PrimaryKeyValue.fromColumn(column.getValue()));
//                }
//
//            }
//            return pkBuilder.build();
//        } catch (RuntimeException e) {
//            log.error("pk build error: row=: " + row + ", pkTypeMap: " + pkTypeMap, e);
//            throw e;
//        }
//        //pkNames = new ArrayList<>(pkTypeMap.keySet());
//    }
//
//
//    public void batchWrite(List<Row> batchRows) {
//
//        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
//        for (int i = 0; i < batchRows.size(); i++) {
//            Row row = batchRows.get(i);
//            final List<String> pkNames;
//            final PrimaryKey primaryKey;
//            if (TsConstants.isAnalysisTable(tsTableInfo.getTableName())) {
//                primaryKey = buildRiskAnalysisResultPk(i);
////                log.info("write data is:{}", primaryKey);
//            } else {
//                primaryKey = buildStandardPk(row);
//            }
//            RowPutChange putChange = new RowPutChange(tsTableInfo.getTableName(), primaryKey);
//            //add column
//            //排除主键列
//            List<Column> columnList = Arrays.stream(row.getColumns()).filter(elem -> !primaryKey.getPrimaryKeyColumnsMap().keySet().contains(elem.getName()))
//                    .collect(Collectors.toList());
//            //排除空的
//            if (CollectionUtils.isEmpty(columnList)) {
//                continue;
//            }
//            columnList = columnList.stream().filter(elem -> elem.getValue() != null).collect(Collectors.toList());
//            putChange.addColumns(columnList);
//
//            if (TsConstants.isAnalysisTable(tsTableInfo.getTableName())) {
//                //增加一个数据创建时间：
//                List<Column> insertTime = putChange.getColumnsToPut(TsConstants.RISK_ANALYSIS_RESULT_INSERT_TIME);
//                if (CollectionUtils.isEmpty(insertTime)) {
//                    putChange.addColumn(TsConstants.RISK_ANALYSIS_RESULT_INSERT_TIME,
//                            ColumnValue.fromString(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
//                }
//
//            }
//
//            batchWriteRowRequest.addRowChange(putChange);
//        }
//        BatchWriteRowResponse response = syncClient.batchWriteRow(batchWriteRowRequest);
//        if (!response.isAllSucceed()) {
//            for (BatchWriteRowResponse.RowResult rowResult : response.getFailedRows()) {
//                log.info("insert failed row: {}", rowResult.getRow());
//                log.info("insert the failed waitingRows for batch write：" + batchWriteRowRequest.getRowChange(rowResult.getTableName(), rowResult.getIndex()).getPrimaryKey());
//                log.info("insert failed reason：" + rowResult.getError());
//            }
//        }else{
//            log.info("batch write success, success count: {}",response.getSucceedRows().size());
//        }
//
//    }
//}
