package com.yupaopao.risk.insight.flink.connector.ts.serialization;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.flink.connector.ts.util.HitResultLogUtils;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.streaming.connectors.kafka.internals.KafkaDeserializationSchemaWrapper;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.List;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-11-20 11:12
 *
 ***/

@Slf4j
public class StringKafkaDeserializationSchemaWrapper extends KafkaDeserializationSchemaWrapper<String> {

    private DeserializationSchema<String> deserializationSchema;

    private boolean needLog = false;

    public StringKafkaDeserializationSchemaWrapper(DeserializationSchema<String> deserializationSchema) {
        super(deserializationSchema);
        this.deserializationSchema = deserializationSchema;
        this.needLog = false;
    }

    public StringKafkaDeserializationSchemaWrapper(DeserializationSchema<String> deserializationSchema, boolean needLog) {
        super(deserializationSchema);
        this.deserializationSchema = deserializationSchema;
        this.needLog = needLog;
    }


    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> message, Collector<String> out) throws Exception {
        try {

            String value = deserializationSchema.deserialize((byte[]) message.value());
            String key = null;
            if (message.key() != null) {
                key = deserializationSchema.deserialize((byte[]) message.key());
            }

            if (needLog) {
                log.info(topicMeta(message) + ",message key: {}, value: {}", key, value);
            }
            //value对应的 HitResultLog, 将其转为原来的es中的结构
            String json = getJsonResult(value, message.timestamp(), message.topic());
            if (StringUtils.isNotEmpty(json)) {
                out.collect(json);
            }

        } catch (Exception e) {
            log.error("deserialize kafka data error: ", e);
        }
    }

    public String getJsonResult(String originalJsonStr, long timestamp, String topic) {
        if (FlinkConstants.KAFKA_TOPIC_RISK_ONLINE_RESULT_LOG.equals(topic) || FlinkConstants.KAFKA_TOPIC_PAYMENT_RISK_ONLINE_RESULT_LOG.equals(topic)) {
            JSONObject map = HitResultLogUtils.translateToStoreMap(originalJsonStr, timestamp);
            String logSwitch = ApolloProperties.getConfigStr("logKafkaTraceId");
            if ("true".equals(logSwitch) ) {
                log.info("kafka msg traceId: {}", map.getString("traceId"));
            }
            map.put("kafkaTopic", topic);
            String json = JSONObject.toJSONString(map, (PropertyFilter) (object, name, value) -> {
                if (value == null || (value instanceof Map && ((Map) value).isEmpty())) {
                    return false;
                } else if (name.length() > 64 || (name.length() > 16 && name.contains("-"))) {
                    return false;
                }
                return true;
            });
            return json;
        }
        if (originalJsonStr.startsWith("[") && originalJsonStr.endsWith("]")) {
            List<JSONObject> list = JSONObject.parseObject(originalJsonStr, List.class);
            for (JSONObject jsonObject : list) {
                jsonObject.put("kafkaTopic", topic);
            }
            return JSONObject.toJSONString(list);
        } else {
            JSONObject obj = JSONObject.parseObject(originalJsonStr);
            String key = "";
            obj.remove(key);
            obj.put("kafkaTopic", topic);
            return obj.toJSONString();
        }


    }

    private String topicMeta(ConsumerRecord<byte[], byte[]> record) {
        long now = System.currentTimeMillis();
        return String.format("[delay: %d ms, topic: %s, partition: %s , offset: %s ]", (now - record.timestamp()),
                record.topic(),
                record.partition(), record.offset());
    }

}
