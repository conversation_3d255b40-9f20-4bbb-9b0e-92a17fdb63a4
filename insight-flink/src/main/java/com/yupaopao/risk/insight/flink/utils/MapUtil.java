package com.yupaopao.risk.insight.flink.utils;

import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.internal.LinkedHashTreeMap;
import com.google.gson.internal.LinkedTreeMap;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * Reason:
 * Date: 2019/8/9
 * Company: www.dtstack.com
 *
 * <AUTHOR>
 */

public class MapUtil {


    private static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * convert LinkedTreeMap or LinkedHashTreeMap Map to HashMap,for LinkedTreeMap,LinkedHashTreeMap can not serialize
     *
     * @param target
     * @return
     */
    public static Map<String, Object> convertToHashMap(Map target) {
        Map<String, Object> result = new HashMap<>(target.size());

        for (Object key : target.keySet()) {
            Object value = target.get(key);
            if (null == value) {
                continue;
            }

            if (value.getClass().equals(LinkedTreeMap.class) || value.getClass().equals(LinkedHashTreeMap.class)) {
                Map<String, Object> convert = convertToHashMap((Map) value);
                HashMap<String, Object> hashMap = new HashMap<>(convert.size());
                hashMap.putAll(convert);
                result.put(key.toString(), hashMap);
            } else {
                result.put(key.toString(), value);
            }
        }

        return result;
    }

    public static Map<String, Object> objectToMap(Object obj) throws Exception {
        if (obj instanceof Map) {
            return convertToHashMap((Map) obj);
        } else {
            return objectToMapReflect(obj);
        }
    }


    public static Map<String, Object> objectToMapReflect(Object obj) throws Exception {
        Class<?> clazz = obj.getClass();
        Map<String, Object> map = new HashMap<>(clazz.getDeclaredFields().length);
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object value = field.get(obj);
            map.put(fieldName, value);
        }
        return map;
    }

    public static <T> T jsonStrToObject(String jsonStr, Class<T> clazz) throws JsonParseException, JsonMappingException, JsonGenerationException, IOException {
        return objectMapper.readValue(jsonStr, clazz);
    }
}
