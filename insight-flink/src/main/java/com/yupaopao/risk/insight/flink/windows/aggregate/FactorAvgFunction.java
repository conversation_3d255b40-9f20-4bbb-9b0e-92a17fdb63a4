package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.flink.utils.FactorUtil;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import org.apache.flink.api.common.functions.AggregateFunction;

public class FactorAvgFunction implements AggregateFunction<FactorCalDetail, AggregateResult, AggregateResult> {
    @Override public AggregateResult createAccumulator() {
        AggregateResult result = new AggregateResult();
        result.setResult(0.0);
        return result;
    }

    @Override public AggregateResult add(FactorCalDetail value, AggregateResult accumulator) {
        accumulator.setKey(value.getGroupKey());
        double sum = accumulator.getResult() * accumulator.getCount() + Double.parseDouble(value.getData());
        int count = accumulator.getCount() + 1;
        accumulator.setResult(sum / count);
        accumulator.setCount(count);
        accumulator.setTimeSpan(FactorUtil.getRemainTime(value));
        Cat.logMetricForCount("factor.avg");
        accumulator.setPurge(value.isPurge());
        return accumulator;
    }

    @Override public AggregateResult getResult(AggregateResult accumulator) {
        return accumulator;
    }

    @Override public AggregateResult merge(AggregateResult a, AggregateResult b) {
        if (a.getKey().equals(b.getKey())) {
            double sum = a.getResult() * a.getCount() + b.getResult() * b.getCount();
            int count = a.getCount() + b.getCount();
            a.setResult(sum / count);
        }
        return a;
    }
}
