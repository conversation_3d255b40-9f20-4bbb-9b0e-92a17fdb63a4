package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.yupaopao.risk.insight.common.beans.aggregate.AccumulateResult;
import com.yupaopao.risk.insight.flink.windows.AccumulateCalDetail;
import org.apache.flink.api.common.functions.AggregateFunction;

public class AccumulateCountFunction implements AggregateFunction<AccumulateCalDetail, AccumulateResult, AccumulateResult> {

    @Override public AccumulateResult createAccumulator() {
        AccumulateResult result = new AccumulateResult();
        result.setResult(0.0);
        return result;
    }

    @Override public AccumulateResult add(AccumulateCalDetail value, AccumulateResult accumulator) {
        accumulator.setId(value.getId());
        accumulator.setKey(value.getGroupKey());
        accumulator.setResult(accumulator.getResult() + 1);
        accumulator.setTimeSpan(value.getTimeSpan());
        accumulator.setPurge(value.isPurge());
        accumulator.setThresholds(value.getThresholds());
        accumulator.setUid(value.getUid());
        accumulator.setDeviceId(value.getDeviceId());
        accumulator.setIp(value.getIp());
        accumulator.setAppId(value.getAppId());
        accumulator.setAppVersion(value.getAppVersion());
        return accumulator;
    }

    @Override public AccumulateResult getResult(AccumulateResult accumulator) {
        return accumulator;
    }

    @Override public AccumulateResult merge(AccumulateResult a, AccumulateResult b) {
        if (a.getKey().equals(b.getKey())) {
            a.setResult(a.getResult() + b.getResult());
        }
        return a;
    }
}
