package com.yupaopao.risk.insight.flink.connector.clickhouse.source;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.flink.core.io.InputSplit;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-11 18:48
 *
 ***/

@Getter
@AllArgsConstructor
public class CKInputSplit implements InputSplit {

    private String sql;
    private int splitNumber;

    @Override
    public int getSplitNumber() {
        return splitNumber;
    }
}
