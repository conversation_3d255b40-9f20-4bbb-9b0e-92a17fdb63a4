package com.yupaopao.risk.insight.flink.connector.kafka.sink.serialization;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;

import javax.annotation.Nullable;

@Slf4j
public class KafkaObjSerializationSchema<T> implements org.apache.flink.streaming.connectors.kafka.KafkaSerializationSchema<T> {

    private String topic;

    public KafkaObjSerializationSchema(String topic) {
        super();
        this.topic = topic;
    }

    @Override public ProducerRecord<byte[], byte[]> serialize(T element, @Nullable Long timestamp) {
        byte[] bytes = JSONObject.toJSONBytes(element);
        return new ProducerRecord<>(topic, bytes);
    }
}
