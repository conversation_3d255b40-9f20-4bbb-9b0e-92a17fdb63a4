//package com.yupaopao.risk.insight.flink.connector.ts.util;

//import com.alicloud.openservices.tablestore.model.*;
//import com.yupaopao.risk.insight.flink.constants.TsConstants;
//
//import java.util.Arrays;
//import java.util.List;
//import java.util.stream.Collectors;

///****
// *
// * @Author: zengxiangcai
// * @Date: 2019-12-28 16:23
// *
// ***/
//public class TsRiskHitLogCleanerUtil {
//    /***
//     * 主键切换
//     * @param oldRow
//     * @param tableName
//     * @return
//     */
//    public static Row toNewRow(Row oldRow, String tableName) {
//        if(oldRow==null){
//            return null;
//        }
//        if(!TsConstants.TABLE_RISK_HIT_LOG.equalsIgnoreCase(tableName)){
//           return null;
//        }
//        PrimaryKeyColumn partition =
//                oldRow.getPrimaryKey().getPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_DATE_PARTITION);
//        PrimaryKeyColumn trace= oldRow.getPrimaryKey().getPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_TRACE_ID);
//        String partitionValue = partition.getValue().asString();
//        //如果不是yyyyMMdd_0000格式则不需要清洗
//        if(partitionValue.charAt(8)!='_'){
//            return null;
//        }
//
//        String split[] = partitionValue.split("_");
//        String newPartition = split[1]+"_"+split[0];
//        PrimaryKeyBuilder builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_DATE_PARTITION, PrimaryKeyValue.fromString(newPartition));
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_TRACE_ID, trace.getValue());
//
//        PrimaryKey pk = builder.build();
//
//        List<Column> newColumnList =  Arrays.stream(oldRow.getColumns()).map(elem->{
//            ColumnValue value;
//            Column c = new Column(elem.getName(), elem.getValue());
//            return c;
//        }).collect(Collectors.toList());
//
//        return new Row(pk,newColumnList);
//    }


//}
