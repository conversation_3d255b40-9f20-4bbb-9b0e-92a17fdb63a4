package com.yupaopao.risk.insight.flink.utils;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-28 20:21
 * 类型兼容转换处理
 ***/
public class ETLTransTypeUtil {


    private static List<TypeTrans> configTypeTransColumns = null;

    static {
        String transTypeColumns = ApolloProperties.getConfigStr("application", "transTypeColumns");
        if (StringUtils.isNotEmpty(transTypeColumns)) {
            configTypeTransColumns = JSON.parseArray(transTypeColumns, TypeTrans.class);
        }
        if (CollectionUtils.isEmpty(configTypeTransColumns)) {
            configTypeTransColumns = new ArrayList<>();
            configTypeTransColumns.add(new TypeTrans("result_requestParams_returnAllImg", "Int", "String"));
        }
    }

    public static Optional<TypeTrans> findTypeTrans(String name) {
        return configTypeTransColumns.stream().filter(type -> type.getColumnName().equals(name)).findFirst();
    }


    public static Object transType(String currentKey, Object currentValue) {
        boolean isString = currentValue instanceof String;
        boolean isNumber = currentValue instanceof BigDecimal || currentValue instanceof Number;
        Optional<ETLTransTypeUtil.TypeTrans> typeTransOptional = ETLTransTypeUtil.findTypeTrans(currentKey);
        if (typeTransOptional.isPresent()) {
            if (isString && typeTransOptional.get().getDbType().contains("Int")) {
                //输入是string,需要的是int
                return Long.valueOf((String) currentValue);
            } else if (isNumber && typeTransOptional.get().getDbType().contains("String")) {
                //输入是number,需要string
                return currentValue instanceof BigDecimal ?
                        ((BigDecimal) currentValue).toPlainString() :
                        String.valueOf(currentValue);
            }
        }
        return null;
    }


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TypeTrans {
        private String columnName;
        private String errorInputType;
        private String dbType;
    }


}
