package com.yupaopao.risk.insight.flink.connector.es.map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.constants.TsConstants;
import com.yupaopao.risk.insight.flink.utils.TsPrimaryKeyTools;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.io.Serializable;

/**
 * Created by Avalon on 2020/1/9 20:14
 */
public class EsFlatMap extends RichFlatMapFunction<String, String> implements Serializable {

    private FastDateFormat fastDateFormat = FastDateFormat.getInstance("yyyy-MM-dd'T'HH:mm:ssXXX");
    private FastDateFormat fdf = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
    private LongCounter processCount = new LongCounter();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        getRuntimeContext().addAccumulator("processCount", this.processCount);
    }

    @Override
    public void flatMap(String value, Collector<String> out) throws Exception {
        JSONObject obj = JSON.parseObject(value);
        String traceId = obj.getString("traceId");
        String createdAt = obj.getString("createdAt");
        processCount.add(1);
        if (createdAt == null) {
            return;
        }
        long ts = fastDateFormat.parse(createdAt).getTime();
        obj.put(TsConstants.RISK_HIT_LOG_DATE_PARTITION, TsPrimaryKeyTools.getRiskHitLogPk(ts, traceId));
        obj.put("createdAt", fdf.format(ts));
        out.collect(JSON.toJSONString(obj));
    }
}
