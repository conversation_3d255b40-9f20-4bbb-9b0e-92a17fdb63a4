package com.yupaopao.risk.insight.flink.job.cep;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.OSSProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.connector.clickhouse.source.CKInputFormat;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.behavior.PrefixSpanProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.fs.FileSystem;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.util.*;


/****
 * risk_hit_log数据写入失败重新写入
 */
@Slf4j
public class RewriteCkJob {

    public static void main(String[] args) throws Exception {

//        new BatchJobBuilder()
//                .withJobName("rewrite_ck_from_oss")
//                .withProcessor(new ReWriteTOCKFromOss())
//                .start(args);




        Map<String, String> argMap = new HashMap<>();
        argMap.put("objList","risk_async_rule_result_log/20230310114358.sql,risk_async_rule_result_log/20230310114429.sql,risk_async_rule_result_log/20230310114458.sql,risk_async_rule_result_log/20230310114529.sql,risk_async_rule_result_log/20230310114602.sql,risk_async_rule_result_log/20230310114630.sql,risk_async_rule_result_log/20230310114702.sql,risk_async_rule_result_log/20230310114731.sql,risk_async_rule_result_log/20230310114802.sql,risk_async_rule_result_log/20230310114833.sql,risk_async_rule_result_log/20230310114905.sql,risk_async_rule_result_log/20230310114934.sql,risk_async_rule_result_log/20230310115003.sql,risk_async_rule_result_log/20230310115035.sql,risk_async_rule_result_log/20230310115105.sql,risk_async_rule_result_log/20230310115134.sql,risk_async_rule_result_log/20230310115136.sql,risk_async_rule_result_log/20230310115207.sql,risk_async_rule_result_log/20230310115236.sql,risk_async_rule_result_log/20230310115307.sql,risk_async_rule_result_log/20230310115335.sql,risk_async_rule_result_log/20230310115337.sql,risk_async_rule_result_log/20230310115409.sql,risk_async_rule_result_log/20230320210029.sql");

        System.err.println(URLEncoder.encode(JSON.toJSONString(argMap),"UTF-8"));

    }

    public static class ReWriteTOCKFromOss implements BatchJobBuilder.MainProcessor {

        @Override
        public void internalProcess(ExecutionEnvironment env, Map<String, String> argMap) throws Exception {
            ClickHouseProperties ckProperties = ClickHouseUtil.getLongTimeoutProperties();
            CKOutputFormat ckOutputFormat = new CKOutputFormat(ckProperties, "rewrite_ck_oss");
//            "risk_hit_log/20210702115131.sql,risk_hit_log/20210707030402.sql,
            String objList = argMap.get("objList");
            String[] objArr = objList.split(",");

            String createdAt = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
            env.fromCollection(Arrays.asList(objArr)).map(new RichMapFunction<String, String>() {

                private OSS ossClient;
                private String bucket;

                @Override
                public void open(Configuration parameters) throws Exception {
                    super.open(parameters);
                    OSSProperties ossProperties = OSSProperties.getProperties(PropertyType.OSS);
                    ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(),
                            ossProperties.getAccessKeySecret());
                    bucket = ossProperties.getBucket();
                }

                @Override
                public void close() throws Exception {
                    super.close();
                    ossClient.shutdown();
                }

                @Override
                public String map(String elem) throws Exception {
                    String objectName = FlinkConstants.getCKSqlBasePath() + elem;
                    OSSObject ossObject = ossClient.getObject(bucket, objectName);
//                    String sqlContent = IOUtils.toString(ossObject.getObjectContent());
                    StringBuffer sqlContent = new StringBuffer();
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(ossObject.getObjectContent()));) {

                        String line = null;
                        while ((line = reader.readLine()) != null) {
                            if (line.startsWith("{")) {
                                // json行类型处理
                                JSONObject objRow = JSON.parseObject(line);
                                for(Map.Entry<String,Object> entry: objRow.entrySet()){
                                    Object colValue = entry.getValue();
                                    if (null == colValue) {
                                        continue;
                                    }
                                    //ck类型处理，boolean转为int , list 转为string
                                    if (colValue instanceof List) {
                                        if (((List<?>) colValue).isEmpty()) {
                                            entry.setValue(null);
                                            continue;
                                        }
                                        entry.setValue(JSONObject.toJSONString(colValue));
                                    }
                                }
                                sqlContent.append(objRow.toJSONString() + "\n");
                            } else {
                                sqlContent.append(line + "\n");
                            }

                        }
                    }

                    ClickHouseUtil.executeUpdate(ClickHouseUtil.createConnection(ckProperties), sqlContent.toString());
                    log.info("insert into ck,content = {}", objectName);

                    sqlContent = null;
                    Map<String, String> obj = new HashMap<>();
                    obj.put("objName", elem);
                    obj.put("createdAt", createdAt);
                    return JSON.toJSONString(obj);
                }
            }).output(ckOutputFormat);
        }
    }


    private List<String> listFile(OSS ossClient, String bucketName) {
        List<String> fileList = new ArrayList<>();
        ObjectListing objectListing;
        String nextMarker = null;
        do {
            objectListing = ossClient.listObjects(new ListObjectsRequest(bucketName)
                    .withMarker(nextMarker)
                    .withPrefix("risk-flink/prod/sql")
                    .withMaxKeys(200));

            for (OSSObjectSummary s : objectListing.getObjectSummaries()) {
                if (s.getKey().endsWith(".sql") && !s.getKey().contains("risk_async_rule_result_log")) {
                    fileList.add(s.getKey());
                }
            }
            nextMarker = objectListing.getNextMarker();

        } while (objectListing.isTruncated());
        return fileList;
    }


}
