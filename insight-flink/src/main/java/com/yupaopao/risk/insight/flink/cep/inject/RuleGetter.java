package com.yupaopao.risk.insight.flink.cep.inject;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.cep.beans.CepRule;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.List;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-03 23:28
 *
 ***/

public class RuleGetter {

    private static final Logger LOG = LoggerFactory.getLogger(RuleGetter.class);

    public static List<CepRule> getLatestRules(JedisPool jedisPool, String querySource, String dataSource) {
        if (jedisPool == null) {
            LOG.warn("jedis pool is empty");
            return null;
        }
        if(StringUtils.isEmpty(dataSource)){
            LOG.warn("dataSource is empty");
            return null;
        }
        try (Jedis jedis = jedisPool.getResource();) {
            // 缓存处理中
            String cacheLock = jedis.get("patternUpdateLock");
            if (StringUtils.isNotBlank(cacheLock)) {
                return null;
            }
            Long size = jedis.llen("allRules");
            List<String> allRules = jedis.lrange("allRules", 0, size);
            if (CollectionUtils.isEmpty(allRules)) {
                return null;
            }
            List<CepRule> patternList =
                    allRules.stream().map(elem -> JSON.parseObject(elem, CepRule.class)).filter(elem->dataSource.equals(elem.getDataSource()))
                            .collect(Collectors.toList());
            LOG.info("query from: {}, result: {}", querySource, JSON.toJSONString(patternList));
            return patternList;
        }
    }
}
