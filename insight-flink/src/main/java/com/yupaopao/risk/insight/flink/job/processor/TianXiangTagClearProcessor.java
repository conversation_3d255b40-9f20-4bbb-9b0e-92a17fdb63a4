package com.yupaopao.risk.insight.flink.job.processor;

import com.google.gson.Gson;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import com.yupaopao.risk.insight.common.support.TagUtil;
import com.yupaopao.risk.insight.flink.connector.hbase.source.BaseHBaseSingleInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseSplit;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseStreamSource;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.portrait.support.TianXiangGroovyFlagMapFunction;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.utils.KafkaUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.hadoop.hbase.client.Result;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class TianXiangTagClearProcessor implements FlinkJobBuilder.MainProcessor {


    @Override
    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {

        HBaseProperties hBaseProperties = HBaseProperties.getProperties(PropertyType.HBASE);
        TsTableInfo tsInTable = new TsTableInfo()
                .withTableName("risk_device_tag")
                .withBucketPerSplit(1)
                .withTableType("SYSTEM");
        env.addSource(new HBaseStreamSource(getInputFormat(tsInTable, hBaseProperties)))
                .returns(TypeInformation.of(String.class)).name("clear tx read_hbase risk_device_tag")
                .filter(elem -> !HBaseUtil.emptyResultJson(elem+""))
                .flatMap(new TianXiangGroovyFlagMapFunction()).name("groovy flagMap function_risk_device_tag")
                .setParallelism(4)
                .addSink(KafkaUtils.getKafkaProducer(FlinkConstants.TOPIC_RISK_INSIGHT_CUSTOM_TAG,
                        PropertyType.KAFKA_RISK_ALI))
                .setParallelism(1)
                .name("sink kafka_risk_device_tag");
    }

    private static BaseHBaseSingleInputFormat<String> getInputFormat(TsTableInfo tsInTable, HBaseProperties hBaseProperties) {
        BaseHBaseSingleInputFormat<String> inputFormat = new BaseHBaseSingleInputFormat<String>(tsInTable, hBaseProperties) {
            private static final long serialVersionUID = -5340118073055142605L;

            @Override public HBaseSplit[] createInputSplits(int minNumSplits) throws IOException {
                List<HBaseSplit.BucketSplit> bucketList = new ArrayList<>();
                HBaseSplit.BucketSplit bucketSplit = new HBaseSplit.BucketSplit();
                bucketList.add(bucketSplit);
                HBaseSplit inputSplit = new HBaseSplit(1, new Gson().toJson(bucketList));
                return new HBaseSplit[] {inputSplit};
            }
            @Override public String parseResult(Result rowResult) {
                return TagUtil.transHBaseRowToTagJson(rowResult);
            }

            @Override
            public void close() throws IOException {
                super.close();
                TagCacheSupport.cleanResource(this.getClass().getClassLoader());
            }
        };
        return inputFormat;
    }

}
