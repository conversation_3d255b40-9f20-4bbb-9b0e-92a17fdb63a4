package com.yupaopao.risk.insight.flink.job.processor;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import com.yupaopao.risk.insight.flink.job.service.GrayListUtils;
import com.yupaopao.risk.insight.flink.utils.DBUtil;
import com.yupaopao.risk.insight.flink.utils.FastJsonUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.Collector;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.yupaopao.risk.insight.common.property.enums.PropertyType.CLICK_HOUSE;
import static com.yupaopao.risk.insight.common.property.enums.PropertyType.DB;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-11-12 14:07
 *
 ***/

@Slf4j
public class WhiteListSyncProcessor implements FlinkJobBuilder.MainProcessor {

    @Override
    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
        DBProperties dbProperties = DBProperties.getProperties(DB);

        ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(CLICK_HOUSE);

        env.fromElements("1").flatMap(new FlatMapFunction<String, String>() {

            @Override
            public void flatMap(String value, Collector<String> out) throws Exception {
                GrayListUtils.getWhiteList(dbProperties).stream().forEach(elem -> out.collect(elem));
            }
        }).addSink(new ClickHouseStreamSink(ckProperties, "risk_gray_list_sync")).name("sync to risk_gray_list_sync");
    }

}
