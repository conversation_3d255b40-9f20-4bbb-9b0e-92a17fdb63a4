//package com.yupaopao.risk.insight.flink.connector.ts.sink;
//
//import org.apache.flink.api.common.functions.RuntimeContext;
//import org.apache.flink.configuration.Configuration;
//import org.apache.flink.runtime.state.FunctionInitializationContext;
//import org.apache.flink.runtime.state.FunctionSnapshotContext;
//import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
//import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
//
///**
// * json写入ts中
// */
//public class TsStreamSink extends RichSinkFunction<String> implements CheckpointedFunction {
//
//    final KafkaTsOutputFormat outputFormat;
//
//    public TsStreamSink(KafkaTsOutputFormat format) {
//        this.outputFormat = format;
//    }
//
//    @Override
//    public void snapshotState(FunctionSnapshotContext functionSnapshotContext) throws Exception {
//        outputFormat.flush();
//    }
//
//    @Override
//    public void initializeState(FunctionInitializationContext functionInitializationContext) throws Exception {
//
//    }
//
//    @Override
//    public void invoke(String value) throws Exception {
//        outputFormat.writeRecord(value);
//    }
//
//    @Override
//    public void invoke(String value, Context context) throws Exception {
//        outputFormat.writeRecord(value);
//    }
//
//    @Override
//    public void open(Configuration parameters) throws Exception {
//        super.open(parameters);
//        RuntimeContext ctx = getRuntimeContext();
//        outputFormat.setRuntimeContext(ctx);
//        outputFormat.open(ctx.getIndexOfThisSubtask(), ctx.getNumberOfParallelSubtasks());
//    }
//
//}
