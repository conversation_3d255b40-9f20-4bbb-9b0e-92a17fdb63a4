package com.yupaopao.risk.insight.flink.connector.es.sink;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.audit.MxTask;
import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.connectors.elasticsearch.ActionRequestFailureHandler;
import org.apache.flink.streaming.connectors.elasticsearch.ElasticsearchSinkFunction;
import org.apache.flink.streaming.connectors.elasticsearch.RequestIndexer;
import org.apache.flink.streaming.connectors.elasticsearch6.ElasticsearchSink;
import org.apache.flink.util.ExceptionUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.ElasticsearchParseException;
import org.elasticsearch.action.ActionRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.Requests;
import org.elasticsearch.common.util.concurrent.EsRejectedExecutionException;
import org.elasticsearch.common.xcontent.XContentType;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-17 14:52
 * 创建ElasticsearchSink
 * 直接使用flink中的connector
 ***/

@Slf4j
public class SinkCreator {

    public static ElasticsearchSink<MxTask> createAuditMxSink(EsProperties esProperties, String tableName) {
        List<HttpHost> httpHosts = new ArrayList<>();
        String[] split = esProperties.getHosts().split(":");
        String host = split[0].trim();
        Integer port = Integer.parseInt(split[1].trim());
        String protocol = StringUtils.isEmpty(esProperties.getProtocol()) ? null : esProperties.getProtocol();
        httpHosts.add(new HttpHost(host, port, protocol));

        ElasticsearchSink.Builder<MxTask> esSinkBuilder = new ElasticsearchSink.Builder<>(
                httpHosts,
                new ElasticsearchSinkFunction<MxTask>() {

                    public IndexRequest createIndexRequest(MxTask element) {
                        return Requests.indexRequest()
                                .index(createIndexWithTime(tableName, element.getUpdateTime()))
                                .type("_doc")
//                                .id(element.getId()+element.getKafkaTopic())
                                .source(JSONObject.toJSONString(element), XContentType.JSON);
                    }

                    @Override
                    public void process(MxTask element, RuntimeContext ctx, RequestIndexer indexer) {
                        indexer.add(createIndexRequest(element));
                    }
                }
        );

        //buckInterval
        esSinkBuilder.setBulkFlushMaxActions(esProperties.getBatchSize());
        esSinkBuilder.setBulkFlushInterval(esProperties.getFlushIntervalInMillis());
        esSinkBuilder.setFailureHandler(failureHandler);

        esSinkBuilder.setRestClientFactory(restClientBuilder -> {
            if (StringUtils.isNotEmpty(esProperties.getUsername()) && StringUtils.isNotEmpty(esProperties.getPassword())) {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(esProperties.getUsername(), esProperties.getPassword()));
                restClientBuilder.setHttpClientConfigCallback(httpClient -> httpClient.setDefaultCredentialsProvider(credentialsProvider));
            }
        });

        return esSinkBuilder.build();
    }


    private static ActionRequestFailureHandler failureHandler = new ActionRequestFailureHandler() {
        @Override
        public void onFailure(ActionRequest actionRequest, Throwable failure, int i, RequestIndexer requestIndexer) throws Throwable {
            if (ExceptionUtils.findThrowable(failure, EsRejectedExecutionException.class).isPresent()) {
                // full queue; re-add document for indexing
//                requestIndexer.add(actionRequest);
                log.warn("es write with EsRejectedExecutionException exception", failure);
            } else if (ExceptionUtils.findThrowable(failure, ElasticsearchParseException.class).isPresent()) {
                // malformed document; simply drop request without failing sink
                log.warn("parse data error:", failure);
            } else {
                // for all other failures, fail the sink
                // here the failure is simply rethrown, but users can also choose to throw custom exceptions
                log.error("es write error: ", failure);

//                throw failure;
            }
        }
    };

    public static ElasticsearchSink<JSONObject> createPaymentTransactionSink(EsProperties esProperties,
                                                                             String tableName) {
        List<HttpHost> httpHosts = new ArrayList<>();
        String[] split = esProperties.getHosts().split(":");
        String host = split[0].trim();
        Integer port = Integer.parseInt(split[1].trim());
        String protocol = StringUtils.isEmpty(esProperties.getProtocol()) ? null : esProperties.getProtocol();
        httpHosts.add(new HttpHost(host, port, protocol));

        ElasticsearchSink.Builder<JSONObject> esSinkBuilder = new ElasticsearchSink.Builder<>(
                httpHosts,
                new ElasticsearchSinkFunction<JSONObject>() {

                    public IndexRequest createIndexRequest(JSONObject element) {
                        return Requests.indexRequest()
                                .index(createIndexWithTime(tableName, new Date()))
                                .type("_doc")
                                .source(element.toJSONString(), XContentType.JSON);
                    }

                    @Override
                    public void process(JSONObject element, RuntimeContext ctx, RequestIndexer indexer) {
                        indexer.add(createIndexRequest(element));
                    }
                }
        );

        //buckInterval
        esSinkBuilder.setBulkFlushMaxActions(esProperties.getBatchSize());
        esSinkBuilder.setBulkFlushInterval(esProperties.getFlushIntervalInMillis());
        esSinkBuilder.setFailureHandler(failureHandler);

        esSinkBuilder.setRestClientFactory(restClientBuilder -> {
            if (StringUtils.isNotEmpty(esProperties.getUsername()) && StringUtils.isNotEmpty(esProperties.getPassword())) {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(esProperties.getUsername(), esProperties.getPassword()));
                restClientBuilder.setHttpClientConfigCallback(httpClient -> httpClient.setDefaultCredentialsProvider(credentialsProvider));
            }
        });

        return esSinkBuilder.build();
    }

    public static ElasticsearchSink<Tuple2<String, String>> createLogonSink(EsProperties esProperties, String tableName) {
        List<HttpHost> httpHosts = new ArrayList<>();
        String[] split = esProperties.getHosts().split(":");
        String host = split[0].trim();
        int port = Integer.parseInt(split[1].trim());
        String protocol = StringUtils.isEmpty(esProperties.getProtocol()) ? null : esProperties.getProtocol();
        httpHosts.add(new HttpHost(host, port, protocol));

        ElasticsearchSink.Builder<Tuple2<String, String>> esSinkBuilder = new ElasticsearchSink.Builder<>(httpHosts, new ElasticsearchSinkFunction<Tuple2<String, String>>() {
            private static final long serialVersionUID = -2668512536683319307L;

            IndexRequest createRequest(JSONObject element, String id) {
                return Requests.indexRequest()
                        .id(id)
                        .index(tableName)
                        .type("_doc")
                        .source(element.toJSONString(), XContentType.JSON);
            }

            @Override
            public void process(Tuple2<String, String> element, RuntimeContext ctx, RequestIndexer indexer) {
                try {
                    Cat.logMetricForCount("es." + tableName + ".count");
                    JSONObject jsonObject = JSONObject.parseObject(element.f1);
                    jsonObject.remove("pkDatePartition");
                    indexer.add(createRequest(jsonObject, element.f0));
                } catch (Exception e) {
                    log.error("error data: " + element.f1 + " , " + element.f0, e);
                }
            }
        }
        );

        //buckInterval
        esSinkBuilder.setBulkFlushMaxActions(esProperties.getBatchSize());
        esSinkBuilder.setBulkFlushInterval(esProperties.getFlushIntervalInMillis());
        esSinkBuilder.setFailureHandler(failureHandler);

        esSinkBuilder.setRestClientFactory(restClientBuilder -> {
            if (StringUtils.isNotEmpty(esProperties.getUsername()) && StringUtils.isNotEmpty(esProperties.getPassword())) {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(esProperties.getUsername(), esProperties.getPassword()));
                restClientBuilder.setHttpClientConfigCallback(httpClient -> httpClient.setDefaultCredentialsProvider(credentialsProvider));
            }
            restClientBuilder.setMaxRetryTimeoutMillis(1000);
        });

        return esSinkBuilder.build();
    }

    public static ElasticsearchSink<String> createLogonSinkWithoutId(EsProperties esProperties, String tableName) {
        List<HttpHost> httpHosts = getHttpHosts(esProperties);

        ElasticsearchSink.Builder<String> esSinkBuilder = new ElasticsearchSink.Builder<>(httpHosts, new ElasticsearchSinkFunction<String>() {

            private static final long serialVersionUID = 138346431767986174L;

            IndexRequest createRequest(JSONObject element) {
                return Requests.indexRequest().index(createIndexWithDate(tableName, new Date())).type("_doc").source(element.toJSONString(), XContentType.JSON);
            }

            @Override
            public void process(String element, RuntimeContext ctx, RequestIndexer indexer) {
                Cat.logMetricForCount("es." + tableName + ".count");
                JSONObject jsonObject = JSONObject.parseObject(element);
                jsonObject.remove("pkDatePartition");
                indexer.add(createRequest(jsonObject));
            }
        });

        //buckInterval
        esSinkBuilder.setBulkFlushMaxActions(esProperties.getBatchSize());
        esSinkBuilder.setBulkFlushInterval(esProperties.getFlushIntervalInMillis());
        esSinkBuilder.setFailureHandler(failureHandler);

        esSinkBuilder.setRestClientFactory(restClientBuilder -> {
            if (StringUtils.isNotEmpty(esProperties.getUsername()) && StringUtils.isNotEmpty(esProperties.getPassword())) {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(esProperties.getUsername(), esProperties.getPassword()));
                restClientBuilder.setHttpClientConfigCallback(httpClient -> httpClient.setDefaultCredentialsProvider(credentialsProvider));
            }
        });

        return esSinkBuilder.build();
    }

    private static String createIndexWithTime(String prefix, Date date) {
        return prefix + "_" + InsightDateUtils.getDateStr(date, InsightDateUtils.DATE_FORMAT_yyyyMMdd);
    }

    private static String createIndexWithDate(String prefix, Date date) {
        return prefix + "_" + InsightDateUtils.getDateStr(date, InsightDateUtils.DATE_FORMAT_yyyyMM);
    }

    private static List<HttpHost> getHttpHosts(EsProperties esProperties) {
        List<HttpHost> httpHosts = new ArrayList<>();
        String[] split = esProperties.getHosts().split(":");
        String host = split[0].trim();
        int port = Integer.parseInt(split[1].trim());
        String protocol = StringUtils.isEmpty(esProperties.getProtocol()) ? null : esProperties.getProtocol();
        httpHosts.add(new HttpHost(host, port, protocol));
        return httpHosts;
    }
}
