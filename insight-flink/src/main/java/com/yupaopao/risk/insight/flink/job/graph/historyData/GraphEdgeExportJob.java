package com.yupaopao.risk.insight.flink.job.graph.historyData;

import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.graph.EdgeExportProcessor;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-11 16:57
 *
 ***/
public class GraphEdgeExportJob {
    public static void main(String[] args) throws Exception {
        new BatchJobBuilder()
                .withDefaultExecute(true)
                .withProcessor(new EdgeExportProcessor())
                .start(args);
    }

}
