package com.yupaopao.risk.insight.flink.job.graph;

import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.GraphCleanProcessor;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2021-06-29 15:00
 *
 ***/
public class GraphCleanJob {

    public static void main(String[] args) throws Exception {
        new FlinkJobBuilder()
                .withJobName("graph-clean")
                .withNeedCheckpoint(false)
                .isBatchJob(false)
                .withJobDesc("关系图过期清理")
                .withMainProcessor(new GraphCleanProcessor())
                .start(args);
    }
}
