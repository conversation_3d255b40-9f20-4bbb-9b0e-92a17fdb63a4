package com.yupaopao.risk.insight.flink.connector.hbase.source;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.yupaopao.risk.insight.common.meta.JobParams;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.utils.TsPrimaryKeyTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-01-07 15:29
 *
 ***/

@Slf4j
public class HBaseSplitBuilder {
    private TsTableInfo tsTableInfo;

    private HBaseProperties hBaseProperties;

    public HBaseSplitBuilder(TsTableInfo tsTableInfo, HBaseProperties hBaseProperties) {
        this.tsTableInfo = tsTableInfo;
        this.hBaseProperties = hBaseProperties;
    }


    private transient static Gson gson = new Gson();

    public HBaseSplit[] buildUserDefineDataSplits() {
        //用户自定义表查询的数据是：table定义的数据,startEnd都不一样
        if (StringUtils.isEmpty(tsTableInfo.getTableId())) {
            throw new RuntimeException("tableId cannot be empty for user defined table");
        }
        List<HBaseSplit.BucketSplit> resultList = new ArrayList<>();
        String lower = tsTableInfo.getTableId() + "_" + "!";
        String upper = tsTableInfo.getTableId() + "_" + "~";
        HBaseSplit.BucketSplit pair = new HBaseSplit.BucketSplit(lower, upper);
        resultList.add(pair);
        HBaseSplit split = new HBaseSplit(0, gson.toJson(resultList));
        HBaseSplit[] splits = new HBaseSplit[1];
        splits[0] = split;
        return splits;
    }

    public HBaseSplit[] buildRiskHitLogSplits() throws IOException {
        HBaseSplit[] splits = null;
        if (tsTableInfo.getDatePeriod() == null) {
//            log.info("create table split by size {}", tsTableInfo.getTableName());
//            splits = createSplitBySize();
            throw new RuntimeException("not support current type buildSplits");
        } else {
            log.info("create table split with custom {}", tsTableInfo.getTableName());
            splits = createSplitByCustom();
        }
        log.info("all splits: {}", JSON.toJSONString(Arrays.asList(splits)));
        return splits;
    }


    private HBaseSplit[] createSplitByCustom() throws IOException {

        /***
         * 目前每天总共有1024个桶，每天的数据最大分摊到1024个分区
         * 0000_yyyyMMdd 到1023_yyyyMMdd
         */
        JobParams.TableQueryDatePeriod period = tsTableInfo.getDatePeriod();
        Date startDate = InsightDateUtils.getDateFromString(period.getBeginDate(), InsightDateUtils.DATE_FORMAT_yyyyMMdd);
        Date endDate = StringUtils.isEmpty(period.getEndDate()) ?
                InsightDateUtils.getDateFromString(InsightDateUtils.getNextDayStr(), InsightDateUtils.DATE_FORMAT_yyyyMMdd)
                : InsightDateUtils.getDateFromString(period.getEndDate(), InsightDateUtils.DATE_FORMAT_yyyyMMdd);
        List<HBaseSplit.BucketSplit> allDayBucket = new ArrayList<>(1024);
        while (startDate.before(endDate)) {
            //splitList.addAll(createDaySplit(startDate, dayLoop));
            addDaySplitToList(startDate, allDayBucket);
            startDate = DateUtils.addDays(startDate, 1);
        }
        List<HBaseSplit> splitList = createSplitFromDayBucketSet(allDayBucket);
        return splitList.toArray(new HBaseSplit[0]);
    }


    private void addDaySplitToList(Date day, List<HBaseSplit.BucketSplit> resultList) {
        String dayPrefix = DateFormatUtils.format(day, InsightDateUtils.DATE_FORMAT_yyyyMMdd);
        //每天的数据分为1024分片
        for (int i = 0; i < TsPrimaryKeyTools.RISK_HIT_LOG_BUCKET_PER_DAY; i++) {
            String lower = TsPrimaryKeyTools.buildRiskHitLogStartKey(dayPrefix, i);
            String upper = TsPrimaryKeyTools.buildRiskHitLogEndKey(dayPrefix, i);
            HBaseSplit.BucketSplit pair = new HBaseSplit.BucketSplit(lower, upper);
            resultList.add(pair);
        }
    }

    /***
     * 对当前查询的所有分区键进行处理，生成inputSplit
     * @param allDayBucket
     */
    private List<HBaseSplit> createSplitFromDayBucketSet(List<HBaseSplit.BucketSplit> allDayBucket) {
        //读取数据量比较大，增加并发读，一个split处理过程中，启动多个线程读取
        int allDayBucketSize = allDayBucket.size();
        int bucketPerSplit = tsTableInfo.getBucketPerSplit();
        int totalSplit = (int) Math.ceil(allDayBucketSize * 1.00 / bucketPerSplit);
        List<HBaseSplit> inputSplits = new ArrayList<>(totalSplit);
        for (int i = 0; i < totalSplit; i++) {
            int startIndex = i * bucketPerSplit;
            int lastIndex = (i + 1) * bucketPerSplit;
            if (lastIndex >= allDayBucketSize) {
                lastIndex = allDayBucketSize;
            }
            if (startIndex >= lastIndex) {
                break;
            }
            List<HBaseSplit.BucketSplit> list = allDayBucket.subList(startIndex, lastIndex);
            HBaseSplit split = new HBaseSplit(i, gson.toJson(list));
            inputSplits.add(i, split);
        }
        return inputSplits;
    }

}
