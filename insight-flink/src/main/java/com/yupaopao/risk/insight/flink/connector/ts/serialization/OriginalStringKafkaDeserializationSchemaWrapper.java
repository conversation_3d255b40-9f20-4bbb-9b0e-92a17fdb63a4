package com.yupaopao.risk.insight.flink.connector.ts.serialization;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.serialization.DeserializationSchema;

import java.util.Date;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-08 14:05
 * kafka json串增加topic，timestamp后直接返回
 ***/
public class OriginalStringKafkaDeserializationSchemaWrapper extends StringKafkaDeserializationSchemaWrapper {

    public OriginalStringKafkaDeserializationSchemaWrapper(DeserializationSchema<String> deserializationSchema) {
        super(deserializationSchema);
    }

    public OriginalStringKafkaDeserializationSchemaWrapper(DeserializationSchema<String> deserializationSchema,
                                                           boolean needLog){
        super(deserializationSchema,needLog);
    }

    public String getJsonResult(String value, long timestamp, String topic) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(value);
        if (!jsonObject.containsKey("createdAt")) {
            String createdAt = InsightDateUtils.getDateStr(new Date(timestamp),
                    InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
            jsonObject.put("createdAt", createdAt);
        }
        jsonObject.put("kafkaTopic", topic);
        return jsonObject.toJSONString();
    }
}
