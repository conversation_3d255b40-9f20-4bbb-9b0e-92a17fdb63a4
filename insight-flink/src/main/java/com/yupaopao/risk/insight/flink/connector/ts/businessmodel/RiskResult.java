package com.yupaopao.risk.insight.flink.connector.ts.businessmodel;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-11-20 11:54
 *
 ***/

@Getter
@Setter
public class RiskResult  implements Serializable {
    private String traceId;
    private String eventCode; // 事件码
    private Handler handler; // 处理器
    private boolean success; // 请求处理是否成功
    private RiskLevel level; //风险指数
    private Integer rule; // 命中的规则id
    private String ruleName;
    private String businessCode;
    private String reply; // 惩罚话术
    private String reason;
    private RiskError error; // 调用出错信息
    private Map<String, String> returnMap = new HashMap<>(); // 业务参数返回集
    private Map<String, String> auditMap = new HashMap<>(); // 业务参数返回集
    private Map<String, String> detail = new HashMap<>();

    public RiskResult() {
    }

    public RiskResult(String traceId) {
        this.traceId = traceId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public Handler getHandler() {
        return handler;
    }

    public void setHandler(Handler handler) {
        this.handler = handler;
    }

    public Map<String, String> getReturnMap() {
        return returnMap;
    }

    public void setReturnMap(Map<String, String> returnMap) {
        this.returnMap = returnMap;
    }

    public Map<String, String> getAuditMap() {
        return auditMap;
    }

    public RiskResult setAuditMap(Map<String, String> auditMap) {
        this.auditMap = auditMap;
        return this;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public RiskLevel getLevel() {
        return level;
    }

    public void setLevel(RiskLevel level) {
        this.level = level;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getReply() {
        return reply;
    }

    public void setReply(String reply) {
        this.reply = reply;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public RiskError getError() {
        return error;
    }

    public void setError(RiskError error) {
        this.error = error;
    }

    public Integer getRule() {
        return rule;
    }

    public void setRule(Integer rule) {
        this.rule = rule;
    }

    public static RiskResult error(String traceId, String eventCode, String businessCode, RiskError error) {
        RiskResult result = new RiskResult(traceId);
        result.eventCode = eventCode;
        result.businessCode = businessCode;
        result.success = false;
        result.error = error;
        return result;
    }

    public static RiskResult success(String traceId, String eventCode, String businessCode) {
        RiskResult result = new RiskResult(traceId);
        result.eventCode = eventCode;
        result.businessCode = businessCode;
        result.success = true;
        return result;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", RiskResult.class.getSimpleName() + "[", "]")
                .add("traceId='" + traceId + "'")
                .add("eventCode='" + eventCode + "'")
                .add("handler=" + handler)
                .add("success=" + success)
                .add("level=" + level)
                .add("rule=" + rule)
                .add("businessCode='" + businessCode + "'")
                .add("reply='" + reply + "'")
                .add("reason='" + reason + "'")
                .add("error=" + error)
                .add("returnMap=" + returnMap)
                .toString();
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }
}
