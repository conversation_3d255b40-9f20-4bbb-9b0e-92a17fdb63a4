package com.yupaopao.risk.insight.flink.job.cal.fixFactorCal;


/****
 * zengxiangcai
 * 2023/1/9 15:50
 * 针对累计因子如果某段时间数据逻辑错误，回滚到某个checkpoint时段的数据（将正确checkpoint时段的数据，根据后续活跃的风控数据刷新到redis中）
 * --------------------
 * 1、关闭累计因子任务
 * 2、手动在https://risk-flink.yupaopao.com/#/submit 从最新的正确checkpoint启动
 * 3、对checkpoint启动点后到来的所有风控数据，判断累计因子统计的前置跳前全部返回true
 * (计算的数据量回很大，一般逻辑异常也是因为有新的数据到来后新数据累计出错，所以都设置为true的话就会对所有累计因子用checkpoint中的状态数据进行刷新)
 *  考虑到每个数据都进行一次处理数据量大，同一个累计因子key的数据指存一份(缓存1小时)
 *
 *  如果故障时间比较长，恢复的时候需要处理的数据也会增加，比如高峰1小时85万数据，故障1h，然后累计因子数量是700，则需要处理85w*700差不多五六亿的数据，
 *  耗时会很长，这中间肯定会有重复数据，如果多数是设备，ip，用户维度的话，按目前线上数据有：6万多，等于是整体量级缩小10分之一
 *
 *
 * 4、因为目标是触发写入redis，而不是进行累计，所以各种累计因子中数据都不能进行加减(count不能+1，sum不能加，distinct_count也不能加,
 * avg,min,max,distinct同样的道理)
 * 5、待数据消费到当前任务启动的时间点后停止任务(可以人工观察目前消费的数据点，或则cloud平台看下日志，会记录数据已经消费到最新)
 *
 * 方案问题：可以将状态中的的数据刷新到redis，但是有可能让这些数据的缓存时间延长直到新的覆盖，不过累计的结果不会有问题
 *
 * 数据量太大，不考虑该方案
 *
 *
 * --------------------
 ***/

//@Deprecated
//public class FactorCalRollbackOld {
//
//    public static void main(String[] args) throws Exception {
////        FactorCal.startFactorCal(FactorCal.StartType.ROLLBACK);
//    }
//}
