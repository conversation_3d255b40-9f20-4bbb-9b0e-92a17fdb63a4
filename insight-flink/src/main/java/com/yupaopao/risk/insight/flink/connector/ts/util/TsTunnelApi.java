package com.yupaopao.risk.insight.flink.connector.ts.util;

//import com.alicloud.openservices.tablestore.TunnelClient;
//import com.alicloud.openservices.tablestore.TunnelClientInterface;
//import com.alicloud.openservices.tablestore.model.tunnel.*;
//import com.alicloud.openservices.tablestore.model.tunnel.internal.*;
//import com.alicloud.openservices.tablestore.tunnel.worker.TunnelClientConfig;
//import com.yupaopao.risk.insight.flink.property.connection.TsProperties;
//import lombok.Getter;
//import lombok.Setter;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;

//import java.io.Serializable;
//import java.util.ArrayList;
//import java.util.List;

//public class TsTunnelApi implements Serializable {

//    private static final long serialVersionUID = 5140401837365096463L;
//    private static Logger LOG = LoggerFactory.getLogger(TsTunnelApi.class);
//
//    private static String BLINK_CLIENT_TAG = "flink-client";
//
//    private TunnelMeta meta;
//    private TsProperties properties;
//    private transient TunnelClient tunnelClient;
//    private String clientId;
//
//    public TsTunnelApi(TsProperties tableProperties, String tableName, String tunnelName) {
//        this.properties = tableProperties;
//        tunnelPreCheck(tableName, tunnelName);
//    }
//
//    private void tunnelPreCheck(String tableName, String tunnelName) {
//        ListTunnelRequest request = new ListTunnelRequest(tableName);
//        ListTunnelResponse resp = getTunnelClient().listTunnel(request);
//
//        boolean exist = false;
//        for (TunnelInfo info : resp.getTunnelInfos()) {
//            if (tunnelName.equals(info.getTunnelName())) {
//                this.meta = new TunnelMeta(properties.getInstanceName(), tableName, tunnelName, info.getTunnelId());
//                exist = true;
//            }
//        }
//
//        if (!exist) {
//            LOG.info(String.format("tunnel：%s不存在，创建增量tunnel....", tunnelName));
//            CreateTunnelRequest createTunnelRequest = new CreateTunnelRequest(tableName, tunnelName, TunnelType.Stream);
//            CreateTunnelResponse createTunnelResponse = getTunnelClient().createTunnel(createTunnelRequest);
//            LOG.info(String.format("Create tunnel requestId [%s] tunnelId [%s]", createTunnelResponse.getRequestId(), createTunnelResponse.getTunnelId()));
//            this.meta = new TunnelMeta(properties.getInstanceName(), tableName, tunnelName, createTunnelResponse.getTunnelId());
//        }
//    }
//
//    /**
//     * 读取tunnel相关ots信息
//     *
//     * @return Tunnel ots meta
//     */
//    public TunnelMeta getTunnelMeta() {
//        return meta;
//    }
//
//    /**
//     * 访问ots表分区调度服务tunnel service， 返回本task负责的splits list
//     *
//     * @param numberOfParallelSubTasks blink task并发度
//     * @param indexOfThisSubTask       当前task的索引
//     * @return 本task负责的splits list
//     */
//    public List<TsTunnelInputSplit> getScheduledSplits(int numberOfParallelSubTasks, int indexOfThisSubTask) {
//        LOG.info(String.format("getScheduledSplits numberOfParallelSubTasks [%d] indexOfThisSubTask [%d]", numberOfParallelSubTasks, indexOfThisSubTask));
//        DescribeTunnelRequest describeTunnelRequest = new DescribeTunnelRequest(meta.getTableName(), meta.getTunnelName());
//        DescribeTunnelResponse describeTunnelResponse = getTunnelClient().describeTunnel(describeTunnelRequest);
//        if (meta.getTunnelId() == null) {
//            meta.setTunnelId(describeTunnelResponse.getTunnelInfo().getTunnelId());;
//        }
//        List<TsTunnelInputSplit> inputSplits = new ArrayList<>();
//        for (ChannelInfo ci : describeTunnelResponse.getChannelInfos()) {
//            if (ci.getChannelStatus() == ChannelStatus.OPEN) {
//                int channelNumber = Math.abs(ci.getChannelId().hashCode());
//                if (channelNumber % numberOfParallelSubTasks == indexOfThisSubTask) {
//                    LOG.info(String.format("add %s channelNumber %d", ci.getChannelId(), channelNumber));
//                    inputSplits.add(new TsTunnelInputSplit(ci.getChannelId(), channelNumber));
//                }
//            }
//        }
//        return inputSplits;
//    }
//
//    /**
//     * 向ots tunnel service汇报已经消费完毕的token，用于tunnel service监控消费进度
//     *
//     * @param splitId 消费的splitId
//     * @param token   已经消费过的最新tunnel token
//     * @throws Exception 网络exception或tunnel service返回的error exception
//     */
//    public void checkpoint(String splitId, String token) throws Exception {
//        LOG.debug(String.format("checkpoint splitId [%s] token [%s]", splitId, token));
//        CheckpointRequest checkpointRequest = new CheckpointRequest(meta.getTunnelId(), clientId, splitId, token, 0);
//        getTunnelClient().checkpoint(checkpointRequest);
//    }
//
//    /**
//     * 获取当前最新checkpoint值
//     *
//     * @param splitId 消费的splitId
//     * @return tunnel service中的初始checkpoint值或最新checkpoint值
//     */
//    public String getCheckpoint(String splitId) {
//        GetCheckpointRequest getCheckpointRequest = new GetCheckpointRequest(meta.getTunnelId(), clientId, splitId);
//        GetCheckpointResponse getCheckpointResponse = getTunnelClient().getCheckpoint(getCheckpointRequest);
//        LOG.info(String.format("getCheckpoint splitId [%s] checkpoint [%s]", splitId, getCheckpointResponse.getCheckpoint()));
//        return getCheckpointResponse.getCheckpoint();
//    }
//
//    /**
//     * 读取指定split中的ots数据
//     *
//     * @param splitId 消费的splitId
//     * @param token   getScheduledSplits返回的split初始token或上次消费返回的next token
//     * @return tunnel records list
//     */
//    public ReadRecordsResponse readRecords(String splitId, String token) {
//        ReadRecordsRequest readRecordsRequest = new ReadRecordsRequest(meta.getTunnelId(), clientId, splitId, token);
//        ReadRecordsResponse readRecordsResponse = getTunnelClient().readRecords(readRecordsRequest);
//        if (readRecordsResponse.getNextToken() == null) {
//            LOG.info("获取下个token失败");
//            readRecordsResponse.setNextToken(TsTunnelRecordReader.OTS_FINISHED);
//        }
//        return readRecordsResponse;
//    }
//
//    /**
//     * 连接tunnel
//     */
//    public void connectTunnel() {
//        TunnelClientConfig conf = new TunnelClientConfig();
//        conf.setClientTag(BLINK_CLIENT_TAG);
//        ConnectTunnelRequest request = new ConnectTunnelRequest(meta.getTunnelId(), conf);
//        clientId = getTunnelClient().connectTunnel(request).getClientId();
//    }
//
//    /**
//     * 心跳检测，刷新channel
//     * @return 新通道
//     * @throws Exception
//     */
//    public List<TsTunnelInputSplit> heartbeat() throws Exception {
//        List<Channel> channels = new ArrayList<>();
//        HeartbeatRequest request = new HeartbeatRequest(meta.getTunnelId(), clientId, channels);
//        HeartbeatResponse resp = getTunnelClient().heartbeat(request);
//        LOG.info("刷新channel 共: {} 个", resp.getChannels().size());
//
//        return this.getScheduledSplits(1, 0);
//    }
//
//
//    /**
//     * 刷新tunnel
//     */
//    public void refreshTunnel() {
//        DeleteTunnelRequest request = new DeleteTunnelRequest(meta.getTableName(), meta.getTunnelName());
//        DeleteTunnelResponse resp = tunnelClient.deleteTunnel(request);
//        LOG.info("refresh tunnel RequestId: {}, TraceId: {}", resp.getRequestId(), resp.getTraceId());
//        refreshTunnelClient();
//        tunnelPreCheck(meta.getTableName(), meta.getTunnelName());
//    }
//
//    /**
//     * 回收OtsTunnelApi client申请的资源，关闭client
//     */
//    public void close() {
//        getTunnelClient().shutdown();
//    }
//
//    /**
//     * 获取tunnelClient
//     * @return tunnelClient
//     */
//    private synchronized TunnelClientInterface getTunnelClient() {
//        if (null == tunnelClient) {
//            String endpoint = properties.getEndPoint();
//            String instanceName = properties.getInstanceName();
//            if (StringUtils.isNotEmpty(instanceName) && StringUtils.isNotEmpty(endpoint)) {
//                tunnelClient = new TunnelClient(properties.getEndPoint(), properties.getAccessKeyId(), properties.getAccessKeySecret(), properties.getInstanceName());
//            }
//        }
//        return tunnelClient;
//    }
//
//    /**
//     * 强制更新 tunnel client
//     * @return
//     */
//    public synchronized void refreshTunnelClient() {
//        if (null != tunnelClient) {
//            tunnelClient.shutdown();
//            tunnelClient = null;
//        }
//    }
//}

//@Getter
//@Setter
//class TunnelMeta implements Serializable {
//    private static final long serialVersionUID = 7378217042769533913L;
//    private String instanceName;
//    private String tableName;
//    private String tunnelName;
//    private String tunnelId;
//
//    public TunnelMeta(String instanceName, String tableName, String tunnelName, String tunnelId) {
//        this.instanceName = instanceName;
//        this.tableName = tableName;
//        this.tunnelName = tunnelName;
//        this.tunnelId = tunnelId;
//    }
//
//    @Override
//    public String toString() {
//        return String.format("instance:%s-table:%s-tunnelName:%s", instanceName, tableName, tunnelName);
//    }
//}

