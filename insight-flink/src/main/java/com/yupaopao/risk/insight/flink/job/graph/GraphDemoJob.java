package com.yupaopao.risk.insight.flink.job.graph;

import com.alibaba.fastjson.JSON;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.io.FileInputFormat;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.BasicTypeInfo;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.DataSet;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.io.CsvInputFormat;
import org.apache.flink.api.java.io.TupleCsvInputFormat;
import org.apache.flink.api.java.tuple.Tuple1;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.typeutils.TupleTypeInfo;
import org.apache.flink.api.java.typeutils.TupleTypeInfoBase;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.fs.Path;
import org.apache.flink.graph.Edge;
import org.apache.flink.graph.Graph;
import org.apache.flink.graph.Vertex;
import org.apache.flink.graph.library.CommunityDetection;
import org.apache.flink.graph.library.LabelPropagation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.io.*;
import java.util.*;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-03 15:30
 *
 ***/

@Deprecated
public class GraphDemoJob {

//
//
//    final  static OutputTag<String> followerTag = new OutputTag<String>("follower"){};
//    final  static OutputTag<String> followeeTag = new OutputTag<String>("followee"){};
//
    public static void main(String[] args) throws Exception {

//        transformEdges();

        transIdFromFraudarResult();
    }

    private static void transWarningUser() throws Exception {
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        Path path = Path.fromLocalFile(new File("C:\\Users\\<USER>\\Desktop\\dynamic\\graph" +
                "\\edgestop100-gelly.csv"));
        final TupleTypeInfo<Tuple2<String, String>> typeInfo = TupleTypeInfo.getBasicTupleTypeInfo(String.class,
                String.class);

        final CsvInputFormat<Tuple2<String, String>> format = new TupleCsvInputFormat<Tuple2<String, String>>(path,
                typeInfo);
//        SingleOutputStreamOperator<Tuple2<String, String>> oldNewIdStream =
//                env.createInput(format, typeInfo)
//                .process(new ProcessFunction<Tuple2<String, String>, Tuple2<String, String>>() {
//                    @Override
//                    public void processElement(Tuple2<String, String> value, Context ctx, Collector<Tuple2<String, String>> out) throws Exception {
//                        ctx.output(followerTag, value.getField(0));
//                        ctx.output(followeeTag, value.getField(1));
//                    }
//                }).setParallelism(1);


//        edges.getSideOutput(followerTag);

                DataSet<Tuple2<String,  String>> oldNewIdStream = env.readCsvFile("C:\\Users\\<USER>\\Desktop\\dynamic" +
                        "\\graph" +
                "\\0509_0515_oldNewidMap.csv").types(String.class,String.class).setParallelism(1);

                DataSet<Tuple1<String>> warningUser = env.readCsvFile("C:\\Users\\<USER>\\Desktop\\dynamic" +
                        "\\graph" +
                "\\warnUser0509_0515.csv").types(String.class).setParallelism(1);
        DataSet<Tuple1<String>> warningUserNewId = oldNewIdStream.join(warningUser).where(0).equalTo(0).projectFirst(1);

        warningUserNewId.writeAsCsv("C:\\Users\\<USER>\\Desktop\\dynamic\\graph" +
                "\\0509_0515_warningUserNewId.csv").setParallelism(1);

        env.execute("lllll");
    }

    //
////    private static void test() throws Exception{
////
////
////        ExecutionEnvironment env =ExecutionEnvironment.getExecutionEnvironment();
//////        Path path = Path.fromLocalFile(new File("C:\\Users\\<USER>\\Desktop\\dynamic\\graph" +
//////                "\\edgestop100-gelly.csv"));
////        Path path = Path.fromLocalFile(new File("C:\\Users\\<USER>\\Desktop\\dynamic\\graph" +
////                "\\newEdges.csv"));
////        final TupleTypeInfo<Tuple2<String, String>> typeInfo = TupleTypeInfo.getBasicTupleTypeInfo(String.class,
////                String.class);
////
////        final CsvInputFormat<Tuple2<String, String>> format = new TupleCsvInputFormat<Tuple2<String, String>>(path,
////                typeInfo);
////
////        SingleOutputStreamOperator<Tuple2<String,String>> edges =
////                env.createInput(format,typeInfo).process(new ProcessFunction<Tuple2<String, String>, Tuple2<String, String>>() {
////                    @Override
////                    public void processElement(Tuple2<String, String> value, Context ctx, Collector<Tuple2<String, String>> out) throws Exception {
////                        ctx.output(followerTag,value.getField(0));
////                        ctx.output(followeeTag,value.getField(1));
////                    }
////                }).setParallelism(1);
////
////        edges.getSideOutput(followerTag);
////
////
////        DataSet<Tuple2<String,  String>> input = env.readCsvFile("C:\\Users\\<USER>\\Desktop\\dynamic\\graph" +
////                "\\newEdges.csv").types(String.class,String.class).setParallelism(1);
////        System.err.println("total follower:   "+ input.distinct(0).count());
////        System.err.println( "total followee:   "+input.distinct(1).count());
////    }}
//
//
//
    private static void transIdFromFraudarResult(){
        try{
            File f1 = new File("C:\\Users\\<USER>\\Desktop\\camo\\out\\out.rows");
            File f2 = new File("C:\\Users\\<USER>\\Desktop\\camo\\out\\out.cols");

            String directory = "C:\\Users\\<USER>\\Desktop\\dynamic\\graph" +
                    "\\";
            String oldNewIdMappingFile = "order_oldNewIdMapping.csv";

            File idMap = new File(directory+oldNewIdMappingFile);
            Map<String,String> newOldIdMap = new HashMap<>();
            List<String> row = new ArrayList<>();
            List<String> column = new ArrayList<>();

            try(
                    BufferedReader b1 = new BufferedReader(new FileReader(f1));
                    BufferedReader b2 = new BufferedReader(new FileReader(f2));
                    BufferedReader bIdMap = new BufferedReader(new FileReader(idMap));
            ){
                String line =null;
                while((line=bIdMap.readLine())!=null){
                    newOldIdMap.put(line.split(",")[1],line.split(",")[0]);
                }
                while((line=b1.readLine())!=null){
                    row.add(newOldIdMap.get(line));
                }
                while((line=b2.readLine())!=null){
                    column.add(newOldIdMap.get(line));
                }
            }
            System.err.println(JSON.toJSONString(row));

            System.err.println("****");
            System.err.println("****");
            System.err.println("****");
            System.err.println(JSON.toJSONString(column));
            System.err.println("finished...");
        }catch (Exception e){
                e.printStackTrace();
        }
    }


    private static void reDefineNodeId() throws Exception {

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        String directory = "C:\\Users\\<USER>\\Desktop\\dynamic\\graph" +
                "\\";
        String inputEdgeFile = "edge_order.csv";

        String newEdgeFile = "order_newEdges.csv";
        String oldNewIdMappingFile = "order_oldNewIdMapping.csv";
        Path path = Path.fromLocalFile(new File(directory+inputEdgeFile));
        final TupleTypeInfo<Tuple2<String, String>> typeInfo = TupleTypeInfo.getBasicTupleTypeInfo(String.class,
                String.class);

        MapStateDescriptor<String, Long> userStateDesc = new MapStateDescriptor<String,
                Long>(
                "user",
                BasicTypeInfo.STRING_TYPE_INFO,
                TypeInformation.of(new TypeHint<Long>() {
                    @Override
                    public TypeInformation<Long> getTypeInfo() {
                        return super.getTypeInfo();
                    }
                })
        );

        final CsvInputFormat<Tuple2<String, String>> format =
                new TupleCsvInputFormat<Tuple2<String, String>>(path,  typeInfo);

        DataStream<Tuple2<String,String>> edges =
                env.createInput(format,typeInfo).setParallelism(1)
                        .keyBy(new KeySelector<Tuple2<String, String>, String>() {
                    @Override
                    public String getKey(Tuple2<String, String> value) throws Exception {
                        return "1";
                    }
                });

        OutputTag<Tuple2<String,Long>> oldUserIdNewIdMapping = new OutputTag<Tuple2<String,Long>>("oldUserIdNewIdMapping"){};


        //将用户id转为比较小的数据
        SingleOutputStreamOperator<Tuple2<Long,
                Long>> newEdges = edges.process(new ProcessFunction<Tuple2<String, String>,Tuple2<Long,
                Long>>(){

            MapState<String, Long> userState = null;
            ValueState<Long>  longValue  = null;
            @Override
            public void open(Configuration parameters) throws Exception {
//                getRuntimeContext().addAccumulator("nodeCounter",new LongCounter());
                userState = getRuntimeContext().getMapState(userStateDesc);
                longValue = getRuntimeContext().getState(new ValueStateDescriptor<Long>("counter",
                        BasicTypeInfo.LONG_TYPE_INFO));


            }

            @Override
            public void processElement(Tuple2<String, String> value, Context ctx,
                                       Collector<Tuple2<Long,Long>> out) throws Exception {
                String follower = value.getField(0);
                String followee = value.getField(1);
                Long current = longValue.value();
                if(current==null){
                    current = 0L;
                }

                Long followerId = userState.get(follower);
                if(followerId == null){
                    //无
                    followerId = current;
                    userState.put(follower,followerId);
                    current++;
                    ctx.output(oldUserIdNewIdMapping,new Tuple2<>(follower,followerId));
                }
                Long followeeId = userState.get(followee);
                if(followeeId == null){
                    //无
                    followeeId = current;
                    userState.put(followee,followeeId);
                    current++;
                    ctx.output(oldUserIdNewIdMapping,new Tuple2<>(followee,followeeId));
                }
                longValue.update(current);

                out.collect(new Tuple2(followerId,followeeId));
            }

        }).setParallelism(1);

        //
        newEdges.writeAsCsv(directory+newEdgeFile).setParallelism(1);

        newEdges.getSideOutput(oldUserIdNewIdMapping).writeAsCsv(directory+oldNewIdMappingFile).setParallelism(1);

        env.execute("reDefineNodeId job");
    }
//
//
//
//    private void runTop100CommunityDetection() throws Exception {
//        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
//
//        GraphCsvReader csvReader = Graph.fromCsvReader("C:\\Users\\<USER>\\Desktop\\dynamic\\graph\\edgestop100-gelly.csv",
//                env);
//        Graph<String, NullValue, NullValue> graph = csvReader.keyType(String.class);
//        Graph<String, NullValue, Double> g1 = graph.mapEdges(new MapFunction<Edge<String, NullValue>, Double>() {
//            @Override
//            public Double map(Edge<String, NullValue> stringNullValueEdge) throws Exception {
//                return 1.0;
//            }
//        });
//
//        Graph<String, Long, Double> g2 = g1.mapVertices(new MapFunction<Vertex<String, NullValue>, Long>() {
//            @Override
//            public Long map(Vertex<String, NullValue> vertex) throws Exception {
//                return Long.valueOf(vertex.getId());
//            }
//        });
//
//        CommunityDetection<String> communityDetection = new CommunityDetection(10,0.5);
//        Graph<String, Long, Double> result = g2.run(communityDetection);
//        System.err.println(result.getVertices().count());
//        result.getVertices().map(elem->elem.getValue()).distinct().print();
//
//
//    }
////
////    private static void runTop100CommunityLabelPropagation() throws Exception {
////        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
////
////        GraphCsvReader csvReader = Graph.fromCsvReader("C:\\Users\\<USER>\\Desktop\\dynamic\\graph\\edgestop100-gelly.csv",
////                env);
////        Graph<String, NullValue, NullValue> graph = csvReader.keyType(String.class);
////        Graph<String, NullValue, Double> g1 = graph.mapEdges(new MapFunction<Edge<String, NullValue>, Double>() {
////            @Override
////            public Double map(Edge<String, NullValue> stringNullValueEdge) throws Exception {
////                return 1.0;
////            }
////        });
////
////        Graph<String, Long, Double> g2 = g1.mapVertices(new MapFunction<Vertex<String, NullValue>, Long>() {
////            @Override
////            public Long map(Vertex<String, NullValue> vertex) throws Exception {
////                return Long.valueOf(vertex.getId());
////            }
////        });
////
////        LabelPropagation<String, Long, Double> communityDetection = new LabelPropagation(100);
////        DataSet<Vertex<String, Long>> result = g2.run(communityDetection);
////        System.err.println(result.count());
////        System.err.println(  result.map(elem->elem.getValue()).distinct().count());
////
////
////    }
//////
//////
    private static void local() throws Exception {
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        List<Vertex<String, Long>> vertices = Arrays.asList(new Vertex<String, Long>("a", 1L),

                new Vertex<String, Long>("b", 2L),
                new Vertex<String, Long>("c", 3L),
                new Vertex<String, Long>("d", 4L),
                new Vertex<String, Long>("e", 5L),
                new Vertex<String, Long>("f", 6L)
        );
        List<Edge<String, Double>> edges = Arrays.asList(
                new Edge<String, Double>("a", "b", 1.0),
                new Edge<String, Double>("c", "b", 1.0),
                new Edge<String, Double>("d", "b", 1.0),
                new Edge<String, Double>("f", "e", 1.0)
        );
        Graph<String, Long, Double> g = Graph.fromCollection(vertices, edges, env);
        // g = g.getUndirected();
        LabelPropagation<String, Long, Double> communityDetection = new LabelPropagation(10);
        DataSet<Vertex<String, Long>> result = g.run(communityDetection);
        System.err.println(result.count());
        System.err.println(result.map(elem -> elem.getValue()).distinct().count());
        result.print();

    }
}
