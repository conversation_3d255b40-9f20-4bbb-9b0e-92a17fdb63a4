package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.api.common.functions.AggregateFunction;

public class MetricSumFunction implements AggregateFunction<JSONObject, JSONObject, JSONObject> {

    private static final long serialVersionUID = -3865695545258912697L;

    @Override public JSONObject createAccumulator() {
        JSONObject result = new JSONObject();
        result.put("result", 0);
        result.put("count", 0);
        return result;
    }

    @Override public JSONObject add(JSONObject value, JSONObject accumulator) {
        accumulator.put("result", accumulator.getDouble("result") + value.getDouble("data"));
        accumulator.put("count", accumulator.getLong("count") + 1);
        value.remove("data");
        value.remove("groupKey");
        accumulator.putAll(value);
        return accumulator;
    }

    @Override public JSONObject getResult(JSONObject accumulator) {
        return accumulator;
    }

    @Override public JSONObject merge(JSONObject a, JSONObject b) {
        return a;
    }
}
