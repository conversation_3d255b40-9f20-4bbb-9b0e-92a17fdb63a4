package com.yupaopao.risk.insight.flink.job.portrait.process;

import com.yupaopao.risk.insight.flink.bean.portrait.*;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.flink.job.portrait.support.RiskLevelSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;

import java.util.*;
@Slf4j
public class PortraitTagMergeSupport {


    public static TreeSet<PortraitFirstTag> mergeTag(Tuple3<Long, String, TreeSet<PortraitFirstTag>> oldTags, Tuple3<Long, String, TreeSet<PortraitFirstTag>> newTags){
        TreeSet<PortraitFirstTag> portraitFirstTags = null;
        try {
            portraitFirstTags = mergeTag(oldTags.f2,newTags.f2);
        } catch (Exception e) {
            log.warn("合并数据错误 oldTags:{},newTags:{}", oldTags, oldTags, e);
        }
        return portraitFirstTags;
    }

    public static TreeSet<PortraitFirstTag> mergeTag(TreeSet<PortraitFirstTag> resultPortraitTags,  TreeSet<PortraitFirstTag> newRiskTags) throws Exception {
        Map<String, String> nameCode = PortraitApolloProperties.getNameCode();
        for (PortraitFirstTag newRiskTag : newRiskTags) {
            if (containsCode(resultPortraitTags, newRiskTag)) {
                PortraitFirstTag firstTag = getTag(resultPortraitTags, newRiskTag.getCode()).get();
                if (!hasShuMeiTag(firstTag.getCode())){
                    //本地标签 举报标签 没有配置标签 都按叠加 riskTotal 处理
                    firstTag.setRiskTotal(firstTag.getRiskTotal()+newRiskTag.getRiskTotal());

                }else {
                    //数美标签修正 riskTotal,未配置标签叠加后修正处理
                    firstTag.setRiskTotal(0);
                }
                Integer level = RiskLevelSupport.getRiskLevelByCode(firstTag.getCode(), firstTag.getRiskTotal());
                TreeSet<PortraitSubTag> subtag = firstTag.getSubtag();
                firstTag.setRiskLevel(level);
                firstTag.setName(newRiskTag.getName());
                firstTag.setUpdateTime(newRiskTag.getUpdateTime());
                resultPortraitTags.add(firstTag);

                mergeSubtag(firstTag.getSubtag(), newRiskTag.getSubtag());
            }else {
                resultPortraitTags.add(newRiskTag);
            }
        }
        return resultPortraitTags;
    }

    public static TreeSet<PortraitFirstTag> mergeTag(Tuple2<Long, PortraitBean> portraitA, Tuple2<Long, PortraitBean> portraitB) throws Exception {

        //通过交换 始终 把画像结果定位在 最新时间的记录上，并把次新结果合并
        if (portraitA.f1.getTimestamp()>portraitB.f1.getTimestamp()){
           return mergeTag(portraitA.f1.getRiskTags(), portraitB.f1.getRiskTags());
        }else {
            return mergeTag(portraitB.f1.getRiskTags(), portraitA.f1.getRiskTags());
        }
    }

    private static boolean containsCode(TreeSet<PortraitFirstTag> resultPortraitTags, PortraitFirstTag newPortraitTag){
        for (PortraitFirstTag oldTag : resultPortraitTags) {
            if (oldTag.getCode().equals(newPortraitTag.getCode())) {
                return true;
            }
        }
        return false;
    }


    private static Optional<PortraitFirstTag> getTag(TreeSet<PortraitFirstTag> bRiskTags, String code){
        for (PortraitFirstTag bRiskTag : bRiskTags) {
            if (bRiskTag.getCode().equals(code)) {
                return Optional.of(bRiskTag);
            }
        }
        return Optional.empty();
    }

    private static void mergeSubtag(TreeSet<PortraitSubTag> oldSubTags, TreeSet<PortraitSubTag> newSubTags)throws Exception{
        try {
            if (CollectionUtils.isEmpty(newSubTags) || oldSubTags==newSubTags){
                return;
            }
            Iterator<PortraitSubTag> iterator = newSubTags.iterator();
            for (;iterator.hasNext();){
                PortraitSubTag newSubTag = iterator.next();
                Optional<PortraitSubTag> oldSubtag = getSubtagByCode(newSubTag.getCode(), oldSubTags);
                if (oldSubtag.isPresent()) {
                    //TODO 数美标签 支持名称 级别 修改 数美、举报、本地
                    if (!hasShuMeiTag(oldSubtag.get().getCode())){
                        //本地 举报支持次数叠加
                        oldSubtag.get().setRiskTotal(newSubTag.getRiskTotal()+oldSubtag.get().getRiskTotal());
                    }else {
                        oldSubtag.get().setRiskTotal(0);
                    }
                    //举报标签
                    //本地聚合标签
                    oldSubtag.get().setName(newSubTag.getName());
                    Integer level = RiskLevelSupport.getRiskLevelByCode(oldSubtag.get().getCode(), oldSubtag.get().getRiskTotal());
                    oldSubtag.get().setRiskLevel(level);
                    oldSubTags.add(oldSubtag.get());
                }else {
                    oldSubTags.add(newSubTag);
                }
            }
        } catch (Exception e) {
//            log.warn("出错记录 oldSubTags{}, newSubTags:{},{}",oldSubTags.hashCode(), newSubTags.hashCode(), oldSubTags==newSubTags);
            throw e;
        }
    }

    private static Optional<PortraitSubTag> getSubtagByCode(String code, TreeSet<PortraitSubTag> subTags){
        if (null == subTags){
            subTags = new TreeSet<PortraitSubTag>();
        }
        for (PortraitSubTag subTag : subTags) {
            if (subTag.getCode().equals(code)) {
                return Optional.of(subTag);
            }
        }
        return Optional.empty();
    }

    private static boolean hasShuMeiTag(String code){
        Map<String, RiskLevelVO> riskLevelConfMap = PortraitApolloProperties.getRiskLevelConfMap();
        if (riskLevelConfMap.containsKey(code)){
            return true;
        }
        Collection<RiskLevelVO> values = riskLevelConfMap.values();
        for (RiskLevelVO value : values) {
            if (code.equals(value.getFieldName())){
                return true;
            }
        }
        return false;
    }


    public static boolean mergeTagScore(OpenAiOutPutVO openAiOutPutVO, PortraitBean portraitBean){
        OutputMess outputMess = openAiOutPutVO.getOutputMess();
        List<ResultVO> resultVOList = outputMess.getResult();
        TreeSet<PortraitFirstTag> newRiskTag = new TreeSet<>();
        TreeSet<PortraitFirstTag> riskTags = portraitBean.getRiskTags();
        for (ResultVO resultVO : resultVOList) {
            if ("AllResult".equals(resultVO.getType())) {
                portraitBean.setScore(resultVO.getScore());
            }
            Optional<PortraitFirstTag> tag = getTag(riskTags, resultVO.getType());
            if (tag.isPresent()){
                PortraitFirstTag portraitFirstTag = tag.get();
                portraitFirstTag.setScore(resultVO.getScore());
                newRiskTag.add(portraitFirstTag);
            }
        }
        riskTags.addAll(newRiskTag);
        portraitBean.setRiskTags(riskTags);
        return true;
    }

}
