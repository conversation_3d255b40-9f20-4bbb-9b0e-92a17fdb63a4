package com.yupaopao.risk.insight.flink.job.base;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.ReadableConfig;
import org.apache.flink.contrib.streaming.state.*;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.rocksdb.ColumnFamilyOptions;
import org.rocksdb.DBOptions;

import java.io.Serializable;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;


/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-07 15:35
 * flink job builder with streamExecutionEnvironment
 ***/

@Slf4j
@Getter
@Setter
public class FlinkJobBuilder {
    private CheckpointSetting checkpointSetting;

    private String jobName;

    private MainProcessor processor;

    private boolean batchJob = false;

    private String jobDesc;

    private boolean needCheckPoint = true;

    /****
     * start job
     * @param args
     * @throws Exception
     */
    public void start(String[] args) throws Exception {
        if (this.getProcessor() == null) {
            throw new IllegalArgumentException("请设置job主流程处理器");
        }

        StreamExecutionEnvironment env = getEnvironment();
        configCheckpoint(env);
        Map<String, String> argMap = getCommandLineArgs(args);
        internalProcess(env, argMap);
        //set config
        Configuration configuration = new Configuration();
        configuration.setString("args", JSON.toJSONString(argMap));
        if (StringUtils.isNotEmpty(jobDesc)) {
            configuration.setString("jobDesc", jobDesc);
        }
        env.getConfig().setGlobalJobParameters(configuration);
        log.info(env.getExecutionPlan());
        if (argMap != null) {
            String jobSuffix = argMap.get("jobSuffix");
            if (StringUtils.isNotEmpty(jobSuffix)) {
                jobName = jobName + jobSuffix;
            }
        }

        executeJob(env, jobName);
    }


    /***
     * 获取environment
     * @return
     */
    public StreamExecutionEnvironment getEnvironment() {
        //get env
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        return env;
    }

    /***
     * 配置checkpoint相关信息
     * @param env
     */
    public void configCheckpoint(StreamExecutionEnvironment env) throws Exception {

        if (!needCheckPoint) {
            return;
        }

        long checkpointInterval = 1 * 60000; // 1min
        long minPauseBetweenCheckpoints = 30000; // 30s
        if (this.getCheckpointSetting() != null) {
            if (this.getCheckpointSetting().getCheckpointInterval() > 0) {
                checkpointInterval = this.getCheckpointSetting().getCheckpointInterval();
            }
            if (this.getCheckpointSetting().getMinPauseBetweenCheckpoints() > 0) {
                minPauseBetweenCheckpoints = this.getCheckpointSetting().getMinPauseBetweenCheckpoints();
            }
            if (this.getCheckpointSetting().isUseRocksDB() && !FlinkConstants.isLocalProfile()) {
                //方便本地调试没有设置目录情况，localProfile不使用rocksdb设置
                //rocksdb 设置
                EmbeddedRocksDBStateBackend rocksdbBackend = new EmbeddedRocksDBStateBackend(true);
                CustomRocksdbOptionsFactory factory = new CustomRocksdbOptionsFactory();
                rocksdbBackend.setRocksDBOptions(factory);
                env.setStateBackend(rocksdbBackend);
                env.getCheckpointConfig().setCheckpointStorage(FlinkConstants.getRocksdbPathWithNFS());
            }
        }

        //checkpoint setting
        env.enableCheckpointing(checkpointInterval);// 1min进行一次checkpoint
        // make sure 30s of progress happen between checkpoints, checkpoint最小间隔时间
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(minPauseBetweenCheckpoints);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        if (isBatchJob()) {
            env.setRestartStrategy(RestartStrategies.noRestart());
            env.getCheckpointConfig().enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.DELETE_ON_CANCELLATION);
        } else {
            env.getCheckpointConfig().enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
            env.setRestartStrategy(RestartStrategies.fixedDelayRestart(Integer.MAX_VALUE, Time.seconds(60)));
        }

    }


    public static class CustomRocksdbOptionsFactory extends DefaultConfigurableOptionsFactory {

        @Override
        public DBOptions createDBOptions(DBOptions currentOptions, Collection<AutoCloseable> handlesToClose) {
            // increase the max background flush threads when we have many states in one operator,
            // which means we would have many column families in one DB instance.
            DBOptions defaultOptions = super.createDBOptions(currentOptions, handlesToClose);
            defaultOptions.setMaxBackgroundFlushes(4).setDbWriteBufferSize(64 * 1024 * 1024);
            return defaultOptions;

        }

        @Override
        public ColumnFamilyOptions createColumnOptions(
                ColumnFamilyOptions currentOptions, Collection<AutoCloseable> handlesToClose) {
            // decrease the arena block size from default 8MB to 1MB.
            ColumnFamilyOptions cfOptions = super.createColumnOptions(currentOptions, handlesToClose);
            cfOptions
//                    .setArenaBlockSize(1 * 1024 * 1024)
                    .setMaxWriteBufferNumber(4).setMinWriteBufferNumberToMerge(2);
            return cfOptions;
        }

        @Override
        public DefaultConfigurableOptionsFactory configure(ReadableConfig configuration) {
            DefaultConfigurableOptionsFactory factory = super.configure(configuration);
            factory.setBlockCacheSize("512MB");
//            factory.setMetadataBlockSize("8KB");
            factory.setBlockSize("32KB");
            return factory;
        }
    }

    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
        this.getProcessor().internalProcess(env, argMap);
    }

    public void executeJob(StreamExecutionEnvironment env, String jobName) throws Exception {
        if (StringUtils.isEmpty(jobName)) {
            jobName = "Stream_" + System.currentTimeMillis();
        }
        env.execute(jobName);
    }

    public Map<String, String> getCommandLineArgs(String[] args) {
        try {
            if (args != null && args.length > 0) {
                String inputParam = args[0];
                if (StringUtils.isNotEmpty(inputParam)) {
                    inputParam = URLDecoder.decode(inputParam, "UTF-8");
                    log.info("input params : {}", inputParam);
                    return JSON.parseObject(inputParam, Map.class);
                }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("parse input args error: " + e);
        }
        return new HashMap<>();
    }

    public FlinkJobBuilder withCheckpointSetting(CheckpointSetting checkpointSetting) {
        this.checkpointSetting = checkpointSetting;
        return this;
    }

    public FlinkJobBuilder withJobName(String jobName) {
        this.jobName = jobName;
        return this;
    }

    public FlinkJobBuilder withMainProcessor(MainProcessor processor) {
        this.processor = processor;
        return this;
    }

    public FlinkJobBuilder isBatchJob(boolean isBatchJob) {
        this.batchJob = isBatchJob;
        return this;
    }

    public FlinkJobBuilder withJobDesc(String jobDesc) {
        this.jobDesc = jobDesc;
        return this;
    }

    public FlinkJobBuilder withNeedCheckpoint(boolean needCheckPoint) {
        this.needCheckPoint = needCheckPoint;
        return this;
    }


    /***
     * main job process
     */
    public interface MainProcessor extends Serializable {
        void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap);
    }
}

