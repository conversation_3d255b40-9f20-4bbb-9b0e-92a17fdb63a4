package com.yupaopao.risk.insight.flink.connector.ts.util;

import lombok.Getter;
import org.apache.flink.core.io.InputSplit;

import java.io.Serializable;

@Getter
public class TsTunnelInputSplit implements InputSplit, Serializable {

    private static final long serialVersionUID = -4126531187925262550L;
    private String initToken;
    private String splitId;
    private int splitNumber;

    public TsTunnelInputSplit(String splitId, int splitNumber) {
        this.splitId = splitId;
        this.splitNumber = splitNumber;
    }

    @Override
    public int getSplitNumber() {
        return splitNumber;
    }

    public void setInitToken(String token) {
        initToken = token;
    }

}
