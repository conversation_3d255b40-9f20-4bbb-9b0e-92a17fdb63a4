package com.yupaopao.risk.insight.flink.job.cleaner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.map.DataFlatMap;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CkColumnRepairMapFunction;
import com.yupaopao.risk.insight.flink.connector.clickhouse.source.CKInputFormat;
import com.yupaopao.risk.insight.flink.connector.ts.util.EsUtil;
import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/****
 * zengxiangcai
 * 2022/5/25 14:54
 * 针对单条记录需要补偿的，更具traceId从es获取,
 * 少量数据可以使用该方式,大量数据还是需要批量任务HistoryClickHouseETL
 ***/

@Slf4j
public class FixDataFromEsETL {
    public static void main(String[] args) throws Exception {
        Map<String,String> argMap = new HashMap<>();
        argMap.put("esIndex","risk_hit_log_20220527");
        argMap.put("baseQuery","select riskBaseTraceId from data_fix_input where createdAt>='2022-05-28 00:00:00' " +
                "order by riskBaseTraceId asc");
        System.err.println(URLEncoder.encode(JSON.toJSONString(argMap),"UTF-8"));
    }

    private static void executeFix(String[] args) throws Exception {
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();

        EsProperties esProperties = EsProperties.getProperties(PropertyType.ES);

        Map<String, String> argMap = BatchJobBuilder.parseArgs(args);
        String[] esIndex;
        if (argMap.containsKey("esIndex")) {
            esIndex = argMap.get("esIndex").split(",");
        } else {
            esIndex = "risk_hit_log_20220523,risk_hit_log_20220524".split(",");
        }

        String baseQuery = "select riskBaseTraceId from data_fix_input order by riskBaseTraceId asc";
        if (argMap.containsKey("baseQuery")) {
            baseQuery = argMap.get("baseQuery");
        }

        ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        CKInputFormat ckInputFormat = new CKInputFormat(ckProperties, baseQuery, null);
        CKOutputFormat ckOutputFormat = new CKOutputFormat(ckProperties, "risk_hit_log");


        env.createInput(ckInputFormat).flatMap(new RichFlatMapFunction<String, String>() {
            @Override
            public void flatMap(String value, Collector<String> out) throws Exception {
                if (HBaseUtil.emptyResultJson(value)) {
                    return;
                }
                SearchRequest request = new SearchRequest(esIndex);
                QueryBuilder query = QueryBuilders.termQuery("data.riskBaseTraceId",
                        JSONObject.parseObject(value).getString("riskBaseTraceId"));
//                        QueryBuilder query = QueryBuilders.termQuery("traceId", "41c3dc18dd9c45d495d167df9c297e6c");
                log.info("fix data for : {}", value);
                SearchSourceBuilder builder = new SearchSourceBuilder();
                builder.query(query);
                request.source(builder);
                SearchResponse response = client.search(request);
                for (SearchHit hit : response.getHits()) {
                    out.collect(hit.getSourceAsString());
                }

            }

            private transient RestHighLevelClient client;

            @Override
            public void open(Configuration parameters) throws Exception {
                super.open(parameters);
                client = EsUtil.getClient(esProperties);
            }

            @Override
            public void close() throws Exception {
                if (client != null) {
                    client.close();
                }
            }


        }).flatMap(new DataFlatMap()).map(new CkColumnRepairMapFunction("risk_hit_log", ckProperties)).output(ckOutputFormat).name("fix data ETL (es ---> ck)").setParallelism(1);
        ;


        env.execute("start fix data");
    }
}
