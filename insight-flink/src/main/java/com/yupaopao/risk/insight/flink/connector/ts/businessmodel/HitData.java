package com.yupaopao.risk.insight.flink.connector.ts.businessmodel;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class HitData implements Serializable {

    private String traceId;


    private String actionData;


    private String resultData;

    private static final long serialVersionUID = 1L;



    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", traceId=").append(traceId);
        sb.append(", actionData=").append(actionData);
        sb.append(", resultData=").append(resultData);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}