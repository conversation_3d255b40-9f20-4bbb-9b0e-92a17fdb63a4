//package com.yupaopao.risk.insight.flink.connector.hbase.source;
//
//import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
//import com.yupaopao.risk.insight.common.meta.TsTableInfo;
//import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
//import org.apache.flink.connector.hbase.util.HBaseTableSchema;
//import org.apache.flink.connector.hbase2.HBase2DynamicTableFactory;
//import org.apache.flink.connector.hbase2.source.HBaseDynamicTableSource;
//import org.apache.flink.table.catalog.CatalogTableImpl;
//import org.apache.flink.table.catalog.ObjectIdentifier;
//import org.apache.flink.table.factories.FactoryUtil;
//import org.apache.hadoop.conf.Configuration;
//
///****
// * zengxiangcai
// * 2021/2/23 5:53 下午
// ***/
//public class GraphDynamicTableSource  extends HBaseDynamicTableSource{
//
//    private TsTableInfo tsTableInfo;
//
//    private HBaseProperties hBaseProperties;
//
//    private FlinkMetaInfo flinkMetaInfo;
//
//    public GraphDynamicTableSource createTable(TsTableInfo tsTableInfo,HBaseProperties hBaseProperties,
//                                               FlinkMetaInfo flinkMetaInfo){
//      return null;
//
//    }
//
//    public GraphDynamicTableSource(Configuration conf, String tableName, HBaseTableSchema hbaseSchema, String nullStringLiteral) {
//        super(conf, tableName, hbaseSchema, nullStringLiteral);
//    }
//}
