package com.yupaopao.risk.insight.flink.job.cep;

import com.alibaba.fastjson.JSON;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import com.yupaopao.risk.insight.common.support.InsightFlinkUtils;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.cep.DynamicCEP;
import com.yupaopao.risk.insight.flink.cep.PatternStream;
import com.yupaopao.risk.insight.common.cep.beans.CepRule;
import com.yupaopao.risk.insight.common.cep.beans.RiskEvent;
import com.yupaopao.risk.insight.common.cep.constants.CepConstants;
import com.yupaopao.risk.insight.flink.cep.inject.RuleChangeListener;
import com.yupaopao.risk.insight.flink.connector.redis.source.RedisRuleSourceFunction;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.StringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.service.GrayListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.eventtime.TimestampAssignerSupplier;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.functions.RichFilterFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.common.typeutils.base.ListSerializer;
import org.apache.flink.api.common.typeutils.base.StringSerializer;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.cep.functions.PatternProcessFunction;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.configuration.ReadableConfig;
import org.apache.flink.contrib.streaming.state.DefaultConfigurableOptionsFactory;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.contrib.streaming.state.RocksDBStateBackend;
import org.apache.flink.runtime.state.StateBackend;
import org.apache.flink.streaming.api.TimeCharacteristic;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;
import org.rocksdb.DBOptions;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.KAFKA_GROUP_ID_CEP_JOB;


/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-24 14:02
 * 动态cep
 ***/

@Slf4j
public class CepJob {

    public static void main(String[] args) throws Exception {

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        //checkpoint config

        env.enableCheckpointing(1 * 60 * 1000);
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(30 * 1000);
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        env.getCheckpointConfig().enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        env.getCheckpointConfig().setCheckpointTimeout(5 * 60 * 1000);
        EmbeddedRocksDBStateBackend rocksDBStateBackend = new EmbeddedRocksDBStateBackend(true);
        rocksDBStateBackend.setRocksDBOptions(new RocksdbOptionsFactory());
        env.setStateBackend(rocksDBStateBackend);
        env.getCheckpointConfig().setCheckpointStorage(FlinkConstants.getRocksdbPathWithNFS());


        TypeSerializer<CepRule> elemSerializer = TypeInformation.of(CepRule.class).createSerializer(env.getConfig());
        ListSerializer<CepRule> valueSerializer = new ListSerializer<>(elemSerializer);
        StringSerializer keySerializer = StringSerializer.INSTANCE;

        //ruleList
        MapStateDescriptor<String, List<CepRule>> ruleListState = new MapStateDescriptor<String,
                List<CepRule>>(
                "ruleListState",
                keySerializer,
                valueSerializer
        );


        //load property from apollo
        KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA_RISK_ALI);
        Properties kafkaProps = kafkaProperties.getProperties();
        kafkaProps.put("group.id", KAFKA_GROUP_ID_CEP_JOB);
        kafkaProps.put("request.timeout.ms", "60000");
        List<String> topics = Collections.singletonList(FlinkConstants.KAFKA_TOPIC_RISK_ONLINE_RESULT_LOG);
        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics,
                new StringKafkaDeserializationSchemaWrapper(new SimpleStringSchema(), false),
                kafkaProps);

        String configParallelism = ApolloProperties.getConfigStr("cep.sourceParallelism");
        if (StringUtils.isEmpty(configParallelism)) {
            configParallelism = "1";
        }
        int sourceParallelism = Integer.valueOf(configParallelism.trim());
//        consumer.setStartFromTimestamp(System.currentTimeMillis()-1000);
        consumer.setStartFromLatest();
        //add source stream
        DataStream<String> kafkaStream = env
                .addSource(consumer)
                .name("read_kafka_cep_job")
                .setParallelism(sourceParallelism);


        DataStream<RiskEvent> riskEventStream = kafkaStream.map(new MapFunction<String, RiskEvent>() {
                    @Override
                    public RiskEvent map(String s) throws Exception {
                        if (StringUtils.isEmpty(s)) {
                            return null;
                        }
                        //json扁平化处理
                        Map<String, Object> inputMap = JsonFlatterUtils.toCkMap(s);

                        Object riskBaseTraceId = inputMap.get("data_riskBaseTraceId");

                        if (riskBaseTraceId != null && StringUtils.isNotEmpty(riskBaseTraceId.toString())) {
                            return null;
                        }

                        return new RiskEvent(inputMap);
                    }
                }).name("columnMap").setParallelism(sourceParallelism)
                .filter(new RiskEventFilterFunction())
                .name("riskEvent").setParallelism(sourceParallelism);


        //get rules from redis
        RedisProperties redisProperties = RedisProperties.getProperties(PropertyType.REDIS);

        DataStream<List<CepRule>> ruleStream = env.addSource(new RedisRuleSourceFunction(redisProperties, FlinkConstants.KAFKA_TOPIC_RISK_ONLINE_RESULT_LOG),
                "ruleEvent");
        //broad cast stream
        BroadcastStream<List<CepRule>> broadcastStream = ruleStream.broadcast(ruleListState);

        //connect non-broadcast stream to broadcastStream
        SingleOutputStreamOperator<RiskEvent> outputStream =
                riskEventStream.connect(broadcastStream).process(new BroadcastProcessFunction<RiskEvent,
                                List<CepRule>, RiskEvent>() {
                            @Override
                            public void processElement(RiskEvent riskEvent, ReadOnlyContext readOnlyContext, Collector<RiskEvent> collector) throws Exception {
                                //最新的规则
                                List<CepRule> latestRules = readOnlyContext.getBroadcastState(ruleListState).get("allRules");
                                if (CollectionUtils.isEmpty(latestRules)) {
                                    log.info("latest rules is empty , maybe not initialized...");
                                    return;
                                }

                                //output all riskEvent join rules
                                for (CepRule rule : latestRules) {
                                    riskEvent.addRuleId(rule.getRuleId());
                                    riskEvent.addGroupByColumnName(rule.getGroupByColumns());
                                    collector.collect(riskEvent);
                                }

                            }

                            @Override
                            public void processBroadcastElement(List<CepRule> cepRules, Context context, Collector<RiskEvent> collector) throws Exception {
                                context.getBroadcastState(ruleListState).put("allRules", cepRules);
                            }
                        }).setParallelism(sourceParallelism)
                        .assignTimestampsAndWatermarks(
                                WatermarkStrategy.<RiskEvent>forBoundedOutOfOrderness(Duration.ofSeconds(5))
                                        .withTimestampAssigner((event, recordTimestamp) -> event.getEventTime())
                        );


        KeyedStream<RiskEvent, String> ruleKeyedStream = outputStream.setParallelism(sourceParallelism)
                .filter(elem -> {
                    if (elem == null || elem.getData() == null) {
                        return false;
                    }
                    String groupByName = elem.getGroupByColumnName();
                    if (StringUtils.isEmpty(groupByName)) {
                        return false;
                    }
                    String keyedColumnValue = elem.extractPartitionKey(groupByName);
                    if (StringUtils.isEmpty(keyedColumnValue)) {
                        return false;
                    }
                    //防止数据积压一次性消费对策略影响
                    if (elem.getEventTime() + 1000 * 30 < System.currentTimeMillis()) {
                        //过滤积压超过30秒的数据
                        return false;
                    }
                    return true;
                }).setParallelism(sourceParallelism)
                .keyBy(new KeySelector<RiskEvent, String>() {
                    @Override
                    public String getKey(RiskEvent value) throws Exception {
                        try {
                            String keyedColumnValue = value.extractPartitionKey(value.getGroupByColumnName());
                            String key = value.getRuleId() + CepConstants.RULE_KEY_SEPARATOR + keyedColumnValue;
//                            log.warn("cep key is: {}",key);
                            return key;
                        } catch (Exception e) {
                            log.error("get key from riskEvent error: ", e);
                            throw e;
                        }
                    }
                });


        // 这个只是一个假设的测试模式,方便构建job,正式处理后这个模式会被替代
        Pattern defaultPattern = Pattern.<RiskEvent>begin("defaultPattern").where(new SimpleCondition<RiskEvent>() {
            @Override
            public boolean filter(RiskEvent value) throws Exception {
                return value != null && "cep-stater-pattern".equals(value.getData().get("userId"));
            }
        });


        PatternStream<RiskEvent> ps = DynamicCEP.pattern(ruleKeyedStream, defaultPattern, new RuleChangeListener() {

            @Override
            public String getDataSource() {
                return FlinkConstants.KAFKA_TOPIC_RISK_ONLINE_RESULT_LOG;
            }
        }).inEventTime();


        //匹配的模式结果处理
        DataStream<Map<String, Object>> result = ps.process(new PatternProcessFunction<RiskEvent, Map<String, Object>>() {
            @Override
            public void processMatch(Map<String, List<RiskEvent>> map, Context context, Collector<Map<String, Object>> collector) throws Exception {
                if (map == null) {
                    return;
                }
                String batchId = InsightFlinkUtils.getUUID();
                //方便某些时候一个模式只对分组值做处理(如黑名单),一个模式标记第一个
                int subBatchId = 0;
                Date currentTime = new Date();
                for (Map.Entry<String, List<RiskEvent>> entry : map.entrySet()) {
                    String currentRuleId = entry.getValue().get(0).getRuleId();
                    //每一个模式中匹配到的不同模式名
                    String currentSinglePatternName = entry.getKey();
                    for (RiskEvent r : entry.getValue()) {
                        String traceId = r.getStringValue("traceId");
                        String groupByColumn = r.getGroupByColumnName();
                        String groupByColumnValue = r.extractPartitionKey(groupByColumn);
                        String rowKey = currentRuleId + "#" + currentSinglePatternName + "#" + traceId + "#" + System.currentTimeMillis();

                        Map<String, Object> resultMap = new HashMap<>();
                        resultMap.put("ruleId", r.getRuleId());
                        resultMap.put("sRuleName", currentSinglePatternName);
                        resultMap.put("groupByColumnValue", groupByColumnValue);
                        resultMap.put("groupByColumn", groupByColumn);
                        resultMap.put("userId", r.getData().get("userId"));
                        resultMap.put("traceId", r.getData().get("traceId"));
                        resultMap.put("batchId", batchId); //标识某一个模式序列组
                        resultMap.put("subBatchId", String.valueOf(subBatchId++));
                        resultMap.put("createTime", currentTime.getTime());
                        String dataString = JSON.toJSONString(r.getData());
                        resultMap.put("detail", JsonFlatterUtils.toJson(dataString, FlattenMode.KEEP_ARRAYS));
                        collector.collect(resultMap);
                        log.info("matched, traceId: {}, patternId: {}", traceId, r.getRuleId());
                    }
                }

                //先存入hbase做后续的处理
                //key: ruleId#patternName#traceId, groupByValue,
                // groupByColumn,traceId，userId，eventCode，其他字段统一当做json串放到一个字段
            }
        }).setParallelism(sourceParallelism * 2);


        //写入kafka，后续需要处理的直接消费kafka
//        result.addSink(MatchResultHandler.createDBSink()).name("write cep match");
//        result.print();
        result.addSink(MatchResultHandler.createKafkaSink()).name("write cep match");

        env.setRestartStrategy(RestartStrategies.failureRateRestart(3,Time.of(10,TimeUnit.MINUTES), Time.minutes(1)));
        env.execute("dynamic cep job");
    }

    public static class RiskEventFilterFunction extends RichFilterFunction<RiskEvent> {

        private transient Map<String, String> whiteList;

        private transient ScheduledExecutorService scheduler;

        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            whiteList = new ConcurrentHashMap<>();
            scheduler = Executors.newSingleThreadScheduledExecutor();
            scheduler.scheduleAtFixedRate(() -> refreshWhiteList(), 0, 120, TimeUnit.MINUTES);
        }

        private void refreshWhiteList() {
            Long startTime = System.currentTimeMillis();
            try {
                List<String> list = GrayListUtils.getWhiteList(DBProperties.getProperties(PropertyType.DB));
                Map<String, String> whiteData = new ConcurrentHashMap<>(list.size());
                list.stream().forEach(elem -> {
                    String whiteValue = JSON.parseObject(elem,
                            GrayListUtils.WhiteListData.class).getValueData();
                    if (StringUtils.isNotEmpty(whiteValue)) {
                        whiteData.put(whiteValue, "true");
                    }
                });
                whiteList = whiteData;
            } catch (Exception e) {
                log.warn("refresh whiteList error");
            } finally {
                log.info("refreshWhiteList cost: {} ms", (System.currentTimeMillis() - startTime));
            }
        }

        @Override
        public boolean filter(RiskEvent value) throws Exception {
            if (value == null) {
                return false;
            }
            //白名单数据不做处理
            String deviceId = value.getStringValue("deviceId");
            String userId = value.getStringValue("userId");
            String clientIp = value.getStringValue("clientIp");

            if (StringUtils.isNotEmpty(deviceId) && whiteList.containsKey(deviceId)) {
                return false;
            }
            if (StringUtils.isNotEmpty(userId) && whiteList.containsKey(userId)) {
                return false;
            }
            if (StringUtils.isNotEmpty(clientIp) && whiteList.containsKey(clientIp)) {
                return false;
            }

            return true;
        }

        @Override
        public void close() throws Exception {
            super.close();
            if (scheduler != null) {
                scheduler.shutdownNow();
                scheduler.awaitTermination(10, TimeUnit.SECONDS);
                scheduler = null;
            }

        }

    }

    public static class RocksdbOptionsFactory extends FlinkJobBuilder.CustomRocksdbOptionsFactory {

        @Override
        public DBOptions createDBOptions(DBOptions currentOptions, Collection<AutoCloseable> handlesToClose) {
            return super.createDBOptions(currentOptions, handlesToClose).setUseDirectReads(true);
        }

        @Override
        public DefaultConfigurableOptionsFactory configure(ReadableConfig configuration) {
            DefaultConfigurableOptionsFactory factory = super.configure(configuration);
            factory.setBlockCacheSize("512MB");
            factory.setMetadataBlockSize("8KB");
            factory.setBlockSize("128KB");
            return factory;
        }
    }
}
