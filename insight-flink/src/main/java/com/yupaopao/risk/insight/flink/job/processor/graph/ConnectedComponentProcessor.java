package com.yupaopao.risk.insight.flink.job.processor.graph;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.graph.CCAnalysisParams;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.graph.algorithm.ConnectedComponentWithParam;
import com.yupaopao.risk.insight.flink.job.processor.graph.algorithm.UndirectedVertexDegree;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.flink.utils.InnerFlinkJobInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.DataSet;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.graph.Graph;
import org.apache.flink.graph.Vertex;
import org.apache.flink.graph.asm.degree.annotate.directed.VertexDegrees;
import org.apache.flink.types.LongValue;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yupaopao.risk.insight.common.property.enums.PropertyType.CLICK_HOUSE;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-11 13:54
 * 图连通社区检测
 ***/

@Slf4j
public class ConnectedComponentProcessor implements BatchJobBuilder.MainProcessor {


    public static final int CC_MAX_ITERATIONS = 15;
    public static final String MAX_ITERATIONS_PARAM_NAME = "maxIterations";
    public static final String CC_DETECTION_RESULT_TABLE_NAME = "cc_detection_result";

    @Override
    public void internalProcess(ExecutionEnvironment env, Map<String, String> argMap) throws Exception {

        String runDay = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);


        //sink
        ClickHouseProperties clickHouseProperties = ClickHouseProperties.getProperties(CLICK_HOUSE);
        clickHouseProperties.setBatchSize(100000);
        CKOutputFormat ckOutputFormat = new CKOutputFormat(clickHouseProperties, CC_DETECTION_RESULT_TABLE_NAME){
            @Override
            public void finalizeGlobal(int parallelism) throws IOException {
//                //结束后在jobManager调用
//                String jarName = "connected-result-analysis.jar";
//                String params = ApolloProperties.getConfigStr("application","connected-component.analysis.params");
//                CCAnalysisParams analysisParams = JSON.parseObject(params,CCAnalysisParams.class);
//                analysisParams.setRunDay(runDay);
//                InnerFlinkJobInvoker.invokeJob(jarName, URLEncoder.encode(JSON.toJSONString(analysisParams),"UTF-8"));
            }
        };

        int graphParallelism  = 8;
        env.setParallelism(graphParallelism);
        if(argMap==null){
            argMap= new HashMap<>();
        }
        argMap.put("dayLimit","14");
        Graph<String, String, Long> inputGraph = GraphGenerator.getGraphFromEdge(env, argMap);

        //return vertex set , node belong to same label (the label is vertex value)
        DataSet<Vertex<String, String>> resultDs =
                inputGraph.run(new ConnectedComponentWithParam<>(getMaxIteration(argMap), graphParallelism));

        //计算各图中各定点的度
        DataSet<Vertex<String, LongValue>> degree = inputGraph
                .run(new UndirectedVertexDegree<String, String, Long>().setParallelism(graphParallelism));

        resultDs.join(degree).where(0).equalTo(0).map(new MapFunction<Tuple2<Vertex<String, String>, Vertex<String,
                LongValue>>, String>() {
            @Override
            public String map(Tuple2<Vertex<String, String>, Vertex<String, LongValue>> value) throws Exception {
                Map<String, Object> resultRow = new HashMap<>();
                resultRow.put("id", value.f0.getId());
                resultRow.put("connectedLabel", value.f0.getValue());
                resultRow.put("degree", value.f1.getValue().getValue());
                resultRow.put("runDay", runDay);
                return JSON.toJSONString(resultRow);
            }
        }).output(ckOutputFormat).setParallelism(1).name("write_detection_result");
    }

    private Graph<String, String, Long> filterLargeDegreeNodes(Graph<String, String, Long> graph,DataSet<Vertex<String, LongValue>> degree){
        try {
            List<String> degreeLarge30 =
                    degree.filter(elem -> elem.getValue().getValue() > 30).map(elem -> elem.getId()).collect();
            return graph.filterOnVertices(new FilterFunction<Vertex<String, String>>() {
                @Override
                public boolean filter(Vertex<String, String> v) throws Exception {
                    return !degreeLarge30.contains(v.getId());
                }
            });
        } catch (Exception e) {
            log.warn("filter large degree nodes error: ", e);
        }
        return graph;

    }

    private int getMaxIteration(Map<String, String> argMap) {
        int maxIterations = argMap.get(MAX_ITERATIONS_PARAM_NAME) != null ? Integer.parseInt(argMap.get(MAX_ITERATIONS_PARAM_NAME)) : CC_MAX_ITERATIONS;
        return maxIterations;
    }


}
