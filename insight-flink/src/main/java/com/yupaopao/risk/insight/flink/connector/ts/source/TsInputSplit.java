//package com.yupaopao.risk.insight.flink.connector.ts.source;
//
//import com.alibaba.fastjson.JSON;
//import com.alicloud.openservices.tablestore.model.PrimaryKey;
//import com.google.gson.Gson;
//import com.google.gson.reflect.TypeToken;
//import org.apache.flink.core.io.InputSplit;
//
//import java.io.Serializable;
//import java.util.List;
//
//public class TsInputSplit implements InputSplit, Serializable {
//    private transient static Gson gson = new Gson();
//
//    private int splitNumber; //分片号
//
//
//    private String bucketJson; //每个分片包含若干个分区键，可以按照分区键并行读取数据
//
//    @Override
//    public int getSplitNumber() {
//        return splitNumber;
//    }
//
//
//
//
//
//    private List<SplitBucket> bucketList;
//
//    public TsInputSplit(int splitNumber, String bucketJson) {
//        this.splitNumber = splitNumber;
//        this.bucketJson = bucketJson;
//        bucketList = fetchBucketList();
//    }
//
//
//    public List<SplitBucket> fetchBucketList() {
//        return gson.fromJson(bucketJson, new TypeToken<List<SplitBucket>>() {
//        }.getType());
//    }
//
//
//
//    public String getBucketJson(){
//        return JSON.parseObject(bucketJson, String.class);
//    }
//
//    @Override
//    public String toString() {
//        return "{splitNumber:" + this.splitNumber + ",bucketJson:" + bucketJson + "}";
//    }
//
//
//    public static class SplitBucket implements Serializable {
//        private String lowerBound; //当前分片主键下界
//        private String upperBound; ////当前分片主键上界
//        //List<String> primaryKeySchemaString; //主键 schema
//
//        public SplitBucket(PrimaryKey lower, PrimaryKey upper) {
//            //this.primaryKeySchemaString = primaryKeySchemaString;
//            this.setLowerBound(lower);
//            this.setUpperBound(upper);
//        }
//
//        public void setLowerBound(PrimaryKey lowerBound) {
//            this.lowerBound = gson.toJson(lowerBound);
//        }
//
//        public void setUpperBound(PrimaryKey upperBound) {
//            this.upperBound = gson.toJson(upperBound);
//        }
//
//
//        public PrimaryKey getLowerBound() {
//            PrimaryKey startPrimaryLey = gson.fromJson(lowerBound, PrimaryKey.class);
//            return startPrimaryLey;
//        }
//
//        public PrimaryKey getUpperBound() {
//            PrimaryKey endPK = gson.fromJson(upperBound, PrimaryKey.class);
//            return endPK;
//        }
//
//        @Override
//        public String toString() {
//            return "{lowerBound:" + this.lowerBound + ",upperBound:" + upperBound + "}";
//        }
//
//    }
//}
