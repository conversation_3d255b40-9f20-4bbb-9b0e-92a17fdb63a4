package com.yupaopao.risk.insight.flink.windows.watermark;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.flink.windows.AggTagDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.AssignerWithPeriodicWatermarks;
import org.apache.flink.streaming.api.watermark.Watermark;

import javax.annotation.Nullable;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc FactorWaterMark
 * @date 2020/5/26
 */

@Slf4j
public class TagWaterMark implements AssignerWithPeriodicWatermarks<AggTagDetail> {
    private static final long serialVersionUID = -5036563763616681765L;
    private Long currentMaxTimestamp = 0L;
    private Long maxOutOfOrderness;

    public TagWaterMark(long maxOutOfOrderness) {
        this.maxOutOfOrderness = maxOutOfOrderness;
    }

    @Nullable
    @Override public Watermark getCurrentWatermark() {
        return new Watermark(currentMaxTimestamp - maxOutOfOrderness);
    }


    @Override public long extractTimestamp(AggTagDetail element, long previousElementTimestamp) {
        try {
            long timestamp = element.getTimeStamp();
            currentMaxTimestamp = Math.max(timestamp, currentMaxTimestamp);
            return timestamp;
        }catch (Exception e){
            log.error("exception with elem: "+ JSON.toJSONString(element), e);
            throw e;
        }
    }
}
