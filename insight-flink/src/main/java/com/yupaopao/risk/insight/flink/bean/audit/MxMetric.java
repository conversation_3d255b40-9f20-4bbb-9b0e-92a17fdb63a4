package com.yupaopao.risk.insight.flink.bean.audit;

import groovy.lang.Script;
import lombok.Data;

/**
 * Created by Avalon on 2020/7/1 22:09
 */
@Data
public class MxMetric {

    /**
     * 指标code
     */
    private String code;

    /**
     * 指标名
     */
    private String name;

    /**
     * 分组字段
     */
    private String groupKey;

    /**
     * 聚合字段
     */
    private String aggKey;

    /**
     * 聚合时长(m)
     */
    private Long span;

    /**
     * 聚合方法
     */
    private String function;

    /**
     * 前置条件
     */
    private String condition;

    /**
     * 方法体
     */
    private String content;

    private Script script;

    private String topic;
}
