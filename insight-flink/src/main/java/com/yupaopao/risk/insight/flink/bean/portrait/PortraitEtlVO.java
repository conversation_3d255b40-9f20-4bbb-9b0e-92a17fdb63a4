package com.yupaopao.risk.insight.flink.bean.portrait;


import lombok.Data;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.ResultTypeQueryable;

import java.io.Serializable;
import java.util.TreeSet;

@Data
public class PortraitEtlVO implements Serializable, ResultTypeQueryable {

    private static final long serialVersionUID = 2040478230247106896L;
    private TreeSet<DeviceVO> activeIpCount = new TreeSet<>();
    private TreeSet<DeviceVO> activeDeviceCount = new TreeSet<>();

    @Override
    public TypeInformation getProducedType() {
        return TypeInformation.of(PortraitEtlVO.class);
    }
}
