package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.common.property.FactorTimeProperties;
import com.yupaopao.risk.insight.flink.utils.FactorUtil;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.roaringbitmap.longlong.Roaring64NavigableMap;

import java.util.HashSet;
import java.util.Set;

@Slf4j
public class FactorDistinctCountFunction implements AggregateFunction<FactorCalDetail, AggregateResult, AggregateResult> {

    @Override public AggregateResult createAccumulator() {
        FactorTimeProperties.getInstance().initConfig();
        AggregateResult result = new AggregateResult();
        result.setDistinct(new HashSet<>());
        result.setResult(0.0);
        return result;
    }

    @Override public AggregateResult add(FactorCalDetail value, AggregateResult accumulator) {
        accumulator.setKey(value.getGroupKey());
        accumulator.setTimeSpan(FactorUtil.getRemainTime(value));
        accumulator.setPurge(value.isPurge());
        Cat.logMetricForCount("factor.distinct");

        Set<String> distinct = accumulator.getDistinct();
        try {
            long data = Long.parseLong(value.getData());
            Roaring64NavigableMap bitMap = accumulator.getBitMap();
            if (null == bitMap) {
                bitMap = new Roaring64NavigableMap();
            }
            if (!bitMap.contains(data)) {
                bitMap.add(data);
                accumulator.setResult(accumulator.getResult() + 1);
            }
            if (distinct.size() > 0) {
                for (String distinctValue : distinct) {
                    bitMap.add(Long.parseLong(distinctValue));
                }
                accumulator.setResult((double) bitMap.getLongCardinality());
                distinct.clear();
            }
            if (bitMap.getLongCardinality() > 5000) {
                log.info("largeDistinct key: {}, byte size: {}, map size: {}", accumulator.getKey(), bitMap.getLongSizeInBytes(), bitMap.getLongCardinality());
            }
            accumulator.setBitMap(bitMap);
        } catch (NumberFormatException e) {
            if (distinct.size() > value.getLimit()) {
                log.info("distinct size limit {} : {}", value.getLimit(), accumulator.getKey());
                return accumulator;
            }
            distinct.add(value.getData());
            accumulator.setResult((double) distinct.size());
        }
        return accumulator;
    }

    @Override public AggregateResult getResult(AggregateResult accumulator) {
        return accumulator;
    }

    @Override public AggregateResult merge(AggregateResult a, AggregateResult b) {
        if (a.getKey().equals(b.getKey()) && a.getResult().equals(b.getResult())) {
            a.setCount(a.getCount() + b.getCount());
        }
        return a;
    }
}
