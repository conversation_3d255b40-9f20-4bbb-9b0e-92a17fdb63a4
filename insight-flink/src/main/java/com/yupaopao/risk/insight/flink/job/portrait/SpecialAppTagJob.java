package com.yupaopao.risk.insight.flink.job.portrait;

import com.yupaopao.risk.insight.flink.job.base.CheckpointSetting;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.portrait.SpecialAppTagProcessor;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-11-27 14:38
 * 对G特殊app记录上报监听
 ***/
public class SpecialAppTagJob {


    public static void main(String[] args) throws Exception {

        new FlinkJobBuilder()
                .withJobName("special-app-monitor")
                .withCheckpointSetting(new CheckpointSetting(60000, 30000, true))
                .withMainProcessor(new SpecialAppTagProcessor())
                .withJobDesc("特殊app消费监听")
                .start(args);
    }
}
