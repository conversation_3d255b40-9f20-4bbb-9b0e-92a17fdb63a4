package com.yupaopao.risk.insight.flink.job.etl.common.connect.kafka.core.decoder;

import com.dtstack.flinkx.decoder.IDecode;
import com.dtstack.flinkx.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Map;

/**
 * Date: 2019/11/21
 * Company: www.dtstack.com
 *
 * <AUTHOR>
 */
public class JsonDecoder implements IDecode {
    private static Logger LOG = LoggerFactory.getLogger(com.dtstack.flinkx.decoder.JsonDecoder.class);

    private static final String KEY_MESSAGE = "message";

    @Override
    public Map<String, Object> decode(final String message) {
        try {
            Map<String, Object> event =  MapUtil.jsonStrToObject(message,Map.class);
            return event;
        } catch (Exception e) {
            LOG.error(e.getMessage());
            return Collections.singletonMap(KEY_MESSAGE, message);
        }
    }
}
