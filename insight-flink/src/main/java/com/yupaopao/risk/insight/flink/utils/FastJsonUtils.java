package com.yupaopao.risk.insight.flink.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.util.TypeUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;


public class FastJsonUtils {

    public final static String EMPTY_JSON = "{}";

    private final static Logger LOGGER = LoggerFactory.getLogger(FastJsonUtils.class);

    /**
     * 获取指定jsonobject上的某个属性,支持嵌套,如data.result.total
     *
     * @param object
     * @param key
     * @return
     */
    public static String getString(JSONObject object, String key) {
        Object value = get(object, key);
        return value == null ? null : value.toString();
    }

    public static Object get(JSONObject object, String key) {
        if (StringUtils.isNotBlank(key)) {
            String[] keys = key.split("\\.");
            for (int i = 0; i < keys.length && object != null; i++) {
                if (i + 1 == keys.length) {
                    return object.get(keys[i]);
                }
                Object value = object.get(keys[i]);
                if (value == null || value instanceof JSONObject) {
                    object = (JSONObject) value;
                } else if (value instanceof Map) {
                    object = new JSONObject((Map) value);
                } else {
                    LOGGER.error("不支持的类型,无法继续向下解析:{} - {} - {}", key, keys[i], value.getClass());
                    break;
                }
            }
        }
        return null;
    }

    public static <T> T read(Object obj, String expr, Class<T> clazz) {
        Object result = JSONPath.eval(obj, expr);
        return TypeUtils.castToJavaBean(result, clazz);
    }

    /**
     * 将对象的大写转换为下划线加小写，例如：userName-->user_name
     *
     * @param object
     * @return
     * @throws JsonProcessingException
     */
    public static String toUnderlineJSONString(Object object) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        String reqJson = mapper.writeValueAsString(object);
        return reqJson;
    }

    /**
     * 将下划线转换为驼峰的形式，例如：user_name-->userName
     *
     * @param json
     * @param clazz
     * @return
     * @throws IOException
     */
    public static <T> T toSnakeObject(String json, Class<T> clazz) throws IOException {
        ObjectMapper mapper = new ObjectMapper(); // mapper的configure方法可以设置多种配置（例如：多字段 少字段的处理）　　　　　　 //mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        T reqJson =  mapper.readValue(json, clazz);
        return reqJson;
    }

}
