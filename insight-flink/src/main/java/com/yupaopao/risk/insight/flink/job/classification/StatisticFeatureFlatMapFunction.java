package com.yupaopao.risk.insight.flink.job.classification;

import com.clickhouse.jdbc.ClickHouseConnection;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.odps.reader.SQLUtil;
import com.yupaopao.risk.insight.flink.utils.DBUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.sql.Connection;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/****
 * zengxiangcai
 * 2023/10/17 14:15
 ***/


@Slf4j
public class StatisticFeatureFlatMapFunction extends RichFlatMapFunction<String, String> {

    private transient ExecutorService executorService;
    private transient List<Map<String, Object>> features;

    private String partition;

    private String engineType;

    private LongCounter successCounter = new LongCounter();
    private LongCounter errorCounter = new LongCounter();

    public StatisticFeatureFlatMapFunction(String partition, String engineType) {
        this.partition = partition;
        this.engineType = engineType;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        //wait when thread is full
        int threadCount = 15;
        if ("clickhouse".equalsIgnoreCase(engineType)) {
            threadCount = 10;
        }
        executorService = new ThreadPoolExecutor(threadCount, threadCount, 0L, TimeUnit.MILLISECONDS, new SynchronousQueue<>(),
                Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

        getRuntimeContext().addAccumulator("successCount", successCounter);
        getRuntimeContext().addAccumulator("errorCount", errorCounter);

    }


    @Override
    public void close() throws Exception {
        super.close();
        if (executorService != null) {
            executorService.shutdown();
        }
    }

    /****
     *
     * @param value The input value. its feature config sql
     * @param out The collector for returning result values.
     * @throws Exception
     */
    @Override
    public void flatMap(String value, Collector<String> out) throws Exception {
        String featureSql = value;
        try (Connection conn = DBUtil.getConnection(DBProperties.getProperties(PropertyType.DB_INSIGHT));) {
            List<Map<String, Object>> featureList = DBUtil.queryList(conn, featureSql, null);
            features = featureList;
        }

        if (CollectionUtils.isEmpty(features)) {
            log.info("features is empty");
            return;
        }
        /***
         *  并行执行sql
         *  因为天级别有依赖关系，需要前一批次完成下一次才开始
         */
        CountDownLatch countDown = new CountDownLatch(features.size());

        for (Map<String, Object> feature : features) {
            Callable<Boolean> task = createExecuteTask(feature, countDown);
            executorService.submit(task);
        }
        countDown.await();

    }

    private Callable<Boolean> createExecuteTask(Map<String, Object> feature, CountDownLatch countDown) {
        Callable<Boolean> call = null;
        if (engineType.equalsIgnoreCase("hive")) {
            call = new HiveFeatureExecutor(feature, partition, countDown, successCounter,
                    errorCounter);

        } else if (engineType.equalsIgnoreCase("clickhouse")) {
            call = new CKFeatureExecutor(feature, partition, countDown, successCounter,
                    errorCounter);
        } else {
            throw new RuntimeException("not support execute engine: " + engineType);
        }
        return call;
    }


    public static class HiveFeatureExecutor implements Callable<Boolean> {

        private transient SQLUtil sqlUtil;

        private transient Map<String, Object> feature;

        private String partition;

        private transient CountDownLatch countDown;

        private transient LongCounter successCounter;
        private transient LongCounter errorCounter;


        public HiveFeatureExecutor(Map<String, Object> feature, String partition, CountDownLatch countDown,
                                   LongCounter successCounter, LongCounter errorCounter) {
            this.feature = feature;
            this.partition = partition;
            this.countDown = countDown;
            this.successCounter = successCounter;
            this.errorCounter = errorCounter;
            sqlUtil = new SQLUtil(HiveKeyConstants.KRB5_CFG, HiveKeyConstants.PUBLIC_DATA_CFG);
        }

        @Override
        public Boolean call() throws Exception {
            String finalQuerySql = "";
            try {
                Object hiveSql = feature.get("feature_sql");
                if (hiveSql == null || StringUtils.isEmpty(hiveSql.toString())) {
                    return false;
                }
                Long startTime = System.currentTimeMillis();
                String featureCode = feature.get("featureCode").toString();
                log.info("start to create feature: {}", featureCode);
                String insertSql = "insert OVERWRITE table risk_control.ods_risk_feature_df " + "PARTITION" +
                        "(dt='${partition}'," +
                        "fname='${featureCode}')\n";
                insertSql += hiveSql.toString();
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("idtype", feature.get("groupType"));
                paramMap.put("partition", partition);
                paramMap.put("featureCode", featureCode);
                finalQuerySql = new StrSubstitutor(paramMap).replace(insertSql);

                sqlUtil.checkKrbAuth();
                try (Connection hiveConn = sqlUtil.getHiveConnection();
                     Statement stmt = hiveConn.createStatement();) {
                    stmt.execute(finalQuerySql);
                }
                Long endTime = System.currentTimeMillis();
                log.info("finish create feature {} with cost: {} ms", featureCode, (endTime - startTime));
                successCounter.add(1);
                return true;
            } catch (Exception e) {
                log.error("error create feature: " + finalQuerySql, e);
                errorCounter.add(1);
                return false;
            } finally {
                countDown.countDown();
            }
        }
    }

    public static class CKFeatureExecutor implements Callable<Boolean> {

        private transient Map<String, Object> feature;

        private String partition;

        private transient CountDownLatch countDown;

        private transient LongCounter successCounter;
        private transient LongCounter errorCounter;

        public CKFeatureExecutor(Map<String, Object> feature, String partition, CountDownLatch countDown,
                                 LongCounter successCounter, LongCounter errorCounter) {
            this.feature = feature;
            this.partition = partition;
            this.countDown = countDown;
            this.successCounter = successCounter;
            this.errorCounter = errorCounter;
        }

        @Override
        public Boolean call() throws Exception {
            String finalQuerySql = "";

            try {
                Object ckSql = feature.get("feature_sql");
                if (ckSql == null || StringUtils.isEmpty(ckSql.toString())) {
                    return false;
                }
                Long startTime = System.currentTimeMillis();
                String featureCode = feature.get("featureCode").toString();
                log.info("start to create feature: {}", featureCode);

                String formatPartition = InsightDateUtils.getDateStr(InsightDateUtils.getDateFromString(partition,
                        InsightDateUtils.DATE_FORMAT_yyyyMMdd), InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
                Map<String, Object> sqlParam = new HashMap<>();
                sqlParam.put("idtype", Integer.valueOf(feature.get("groupType").toString()));
                sqlParam.put("partition", formatPartition);
                sqlParam.put("featureCode", featureCode);
                finalQuerySql = new StrSubstitutor(sqlParam).replace(ckSql);
                ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICKHOUSE_RISK);
                try (ClickHouseConnection conn = ClickHouseUtil.createConnection(ckProperties)) {
                    //drop partition first
                    String dropPartitionSql = "alter table ods_risk_feature_df_local on cluster default DROP " +
                            "PARTITION ('${partition}','${featureCode}')";
                    ClickHouseUtil.executeUpdate(conn, new StrSubstitutor(sqlParam).replace(dropPartitionSql));
                    Thread.sleep(1000);
                    // then execute insert
                    ClickHouseUtil.executeUpdate(conn, finalQuerySql);
                }
                Long endTime = System.currentTimeMillis();
                log.info("finish create feature {} with cost: {} ms", featureCode, (endTime - startTime));
                successCounter.add(1);
                return true;
            } catch (Exception e) {
                log.error("error create feature: " + finalQuerySql, e);
                errorCounter.add(1);
                return false;
            } finally {
                countDown.countDown();
            }
        }
    }


}
