package com.yupaopao.risk.insight.flink.connector.clickhouse.sink;

import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.io.FinalizeOnMaster;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import ru.yandex.clickhouse.BalancedClickhouseDataSource;
import ru.yandex.clickhouse.ClickHouseConnection;
import ru.yandex.clickhouse.ClickHouseDataSource;

import java.io.IOException;
import java.io.Serializable;
import java.sql.SQLException;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-10 10:35
 *
 ***/

@Slf4j
public class CKOutputFormat extends RichOutputFormat<String> implements FinalizeOnMaster, Serializable {

    private ClickHouseProperties properties;
    private String tableName;
    private transient ClickHouseConnection connection;
    private transient ClickHouseFlushWithTimeProcessor flushWithTimeProcessor;
    private transient Counter errorCounter;
    private Counter writeCounter;


    public CKOutputFormat(ClickHouseProperties properties, String tableName) {
        this.properties = properties;
        this.tableName = tableName;
    }

    @Override
    public void configure(Configuration parameters) {

    }

    @Override
    public void open(int taskNumber, int numTasks) throws IOException {
        try {
            this.errorCounter = getRuntimeContext().getMetricGroup().counter("ckErrorCount");
            this.writeCounter = getRuntimeContext().getMetricGroup().counter("ckWriteRecords");
            flushWithTimeProcessor = new ClickHouseFlushWithTimeProcessor(this.properties, this.tableName, errorCounter,
                    writeCounter, getRuntimeContext().getIndexOfThisSubtask());
        } catch (Exception e) {
            log.error("open in CkOutputFormat error: ", e);
        }
    }

    @Override
    public void writeRecord(String record) throws IOException {
        try {
            flushWithTimeProcessor.addRecord(record);
        } catch (Exception e) {
            log.error("add record error: ", e);
        }
    }

    @Override
    public void close() throws IOException {
        log.info("CkOutputFormat close ...");
        if (flushWithTimeProcessor != null) {
            flushWithTimeProcessor.close();
        }
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                log.error("close ck connection error: ", e);
            }
        }
    }


    @Override
    public void finalizeGlobal(int parallelism) throws IOException {
        //结束后在jobManager调用
    }
}
