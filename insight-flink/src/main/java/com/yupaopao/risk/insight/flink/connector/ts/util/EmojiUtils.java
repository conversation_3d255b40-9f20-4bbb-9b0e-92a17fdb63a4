package com.yupaopao.risk.insight.flink.connector.ts.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2018/12/27 12:42 PM
 */
public class EmojiUtils {
    public EmojiUtils() {
    }

    public static boolean containsEmoji(String source) {
        if (StringUtils.isBlank(source)) {
            return false;
        } else {
            int len = source.length();

            for (int i = 0; i < len; ++i) {
                char codePoint = source.charAt(i);
                if (!isNotEmojiCharacter(codePoint)) {
                    return true;
                }
            }

            return false;
        }
    }

    private static boolean isNotEmojiCharacter(char codePoint) {
        return codePoint == 0 || codePoint == '\t' || codePoint == '\n' || codePoint == '\r' || codePoint >= ' ' && codePoint <= '\ud7ff' || codePoint >= '\ue000' && codePoint <= '�' || codePoint >= 65536 && codePoint <= 1114111;
    }

    public static String filterEmoji(String source) {
        if (StringUtils.isBlank(source)) {
            return source;
        } else if (!containsEmoji(source)) {
            return source;
        } else {
            StringBuilder buf = new StringBuilder();
            int len = source.length();

            for (int i = 0; i < len; ++i) {
                char codePoint = source.charAt(i);
                if (isNotEmojiCharacter(codePoint)) {
                    buf.append(codePoint);
                }
            }

            return buf.toString().trim();
        }
    }
}
