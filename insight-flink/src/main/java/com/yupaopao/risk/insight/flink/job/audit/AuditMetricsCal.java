package com.yupaopao.risk.insight.flink.job.audit;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.ConfigService;
import com.yupaopao.risk.insight.common.property.PropertiesFactory;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.connector.kafka.sink.serialization.KafkaObjSerializationSchema;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.OriginalStringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.StringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.job.base.CheckpointSetting;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.windows.aggregate.*;
import com.yupaopao.risk.insight.flink.windows.process.AuditMetricProcess;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.joda.time.DateTime;

import java.util.*;

import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.KAFKA_GROUP_ID_AUDIT_METRIC;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc AuditMetricCal
 * @date 2020/7/2
 */
public class AuditMetricsCal {
    public static void main(String[] args) throws Exception {

        new FlinkJobBuilder()
            .withJobName("audit-metric")
            .withCheckpointSetting(new CheckpointSetting(3 * 60000, 2 * 60000, true))
            .withMainProcessor(new AuditMetricsCal.AuditMetricCalProcessor())
            .withJobDesc("审核指标计算")
            .start(args);
    }

    private static final OutputTag<String> CK_METRIC = new OutputTag<String>("calMetric") {
        private static final long serialVersionUID = 855295857498873115L;
    };

    private static class AuditMetricCalProcessor implements FlinkJobBuilder.MainProcessor {

        private static final long serialVersionUID = -2564876722695864573L;

        ClickHouseProperties ckProperties = (ClickHouseProperties) PropertiesFactory.loadProperty(PropertyType.CLICK_HOUSE);
        KafkaProperties kafkaProperties = (KafkaProperties) PropertiesFactory.loadProperty(PropertyType.KAFKA_RISK_ALI);
        String topics = ConfigService.getAppConfig().getProperty("audit.metric.topic", "dwd_audit_response_record");

        @Override public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
            SingleOutputStreamOperator<JSONObject> process =
                env.addSource(getKafkaConsumer(kafkaProperties, new OriginalStringKafkaDeserializationSchemaWrapper(new SimpleStringSchema()), topics.split(",")))
                    .process(new AuditMetricProcess())
                    .process(new ProcessFunction<JSONObject, JSONObject>() {
                        private static final long serialVersionUID = 63680095991475110L;
                        @Override public void processElement(JSONObject value, Context ctx, Collector<JSONObject> out) throws Exception {
                            FactorEnum enumData = FactorEnum.getEnumData(value.getString("function"));
                            if (null != enumData) {
                                ctx.output(enumData.getOutputTag(), value);
                            }
                        }
                    });

            for (FactorEnum factorEnum : FactorEnum.values()) {
                SingleOutputStreamOperator<JSONObject> streamOperator =
                    process.getSideOutput(factorEnum.getOutputTag())
                        .keyBy(p -> p.getString("groupKey")  + p.getString("code") + p.getString("topic"))
                        .window(TumblingProcessingTimeWindows.of(Time.minutes(1)))
                        .aggregate(factorEnum.getAggregateFunction()).process(new CalOutputProcess());
                streamOperator.addSink(getKafkaProducer(kafkaProperties, "AUDIT_METRIC_LOG"))
                    .name("sink to kafka");

                streamOperator.getSideOutput(CK_METRIC)
                    .addSink(new ClickHouseStreamSink(ckProperties, "audit_metric", null, null, true))
                    .name("sink metric to ck");
            }
        }
    }

    private static FlinkKafkaConsumer<String> getKafkaConsumer(KafkaProperties kafkaProperties, StringKafkaDeserializationSchemaWrapper wrapper, String... topic) {
        Properties etlProperties = kafkaProperties.getProperties();
        etlProperties.put("group.id", KAFKA_GROUP_ID_AUDIT_METRIC);

        List<String> topics = Arrays.asList(topic);

        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics,
            wrapper,
            kafkaProperties.getProperties());
        // 设定项目启动读取时间，不影响从checkpoint 恢复
        consumer.setStartFromTimestamp(System.currentTimeMillis() - 1000);
        return consumer;
    }

    private static FlinkKafkaProducer<JSONObject> getKafkaProducer(KafkaProperties kafkaProperties, String topic) {
        Properties kafkaProps = kafkaProperties.getProperties();
        kafkaProps.put("transaction.timeout.ms", 1000 * 60 * 5);

        return new FlinkKafkaProducer<>(
            topic,
            new KafkaObjSerializationSchema<>(topic),
            kafkaProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE);
    }

    private static class CalOutputProcess extends ProcessFunction<JSONObject, JSONObject> {

        private static final long serialVersionUID = 880264712369611126L;

        @Override public void processElement(JSONObject value, Context ctx, Collector<JSONObject> out) throws Exception {
            value.put("createdAt", new DateTime().toString("yyyy-MM-dd HH:mm:ss"));
            ctx.output(CK_METRIC, value.toJSONString());
            out.collect(value);
        }
    }

    public enum FactorEnum {
        /**
         * OutputTag与AggregateFunction enum
         */
        COUNT("COUNT", new OutputTag<JSONObject>("count") {
            private static final long serialVersionUID = -97863418419475810L;
        }, new MetricCountFunction()),
        SUM("SUM", new OutputTag<JSONObject>("sum") {
            private static final long serialVersionUID = -6330371469488330785L;
        }, new MetricSumFunction()),
        AVG("SUM_S", new OutputTag<JSONObject>("sum_s") {
            private static final long serialVersionUID = -5095251198147643863L;
        }, new MetricSumFunction()),
        MAX("MAX", new OutputTag<JSONObject>("max") {
            private static final long serialVersionUID = -5095251198147643863L;
        }, new MetricMaxFunction()),
        MIN("MIN", new OutputTag<JSONObject>("min") {
            private static final long serialVersionUID = -3724653234897667639L;
        }, new MetricMinFunction()),
        ;

        FactorEnum(String key, OutputTag<JSONObject> outputTag, AggregateFunction<JSONObject, JSONObject, JSONObject> aggregateFunction) {
            this.key = key;
            this.outputTag = outputTag;
            this.aggregateFunction = aggregateFunction;
        }

        private final String key;
        private final OutputTag<JSONObject> outputTag;
        private final AggregateFunction<JSONObject, JSONObject, JSONObject> aggregateFunction;

        public String getKey() {
            return key;
        }

        public OutputTag<JSONObject> getOutputTag() {
            return outputTag;
        }

        public AggregateFunction<JSONObject, JSONObject, JSONObject> getAggregateFunction() {
            return aggregateFunction;
        }

        public static FactorEnum getEnumData(String key) {
            return Arrays.stream(FactorEnum.values()).filter(p -> p.getKey().equals(key)).findFirst().orElse(null);
        }
    }
}
