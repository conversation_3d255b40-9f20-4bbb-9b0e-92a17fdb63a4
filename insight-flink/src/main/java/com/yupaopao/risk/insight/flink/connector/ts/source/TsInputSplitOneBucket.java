//package com.yupaopao.risk.insight.flink.connector.ts.source;
//
//import com.alicloud.openservices.tablestore.model.PrimaryKey;
//import com.alicloud.openservices.tablestore.model.PrimaryKeyColumn;
//import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
//import com.google.gson.Gson;
//import org.apache.flink.core.io.InputSplit;
//
//import java.io.Serializable;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 参考阿里ots blink connector,
// * 废弃，使用TsInputSplit
// */
//@Deprecated
//public class TsInputSplitOneBucket implements InputSplit, Serializable {
//
//
//    private int splitNumber; //分片号
//    private String lowerBound; //当前分片主键上界
//    private String upperBound; ////当前分片主键上界
//    List<String> primaryKeySchemaString; //主键 schema
//
//    private static Gson gson = new Gson();
//
//    public TsInputSplitOneBucket(int splitNumber, PrimaryKey lowerBound, PrimaryKey upperBound, List<String> primaryKeySchemaString) {
//        this.splitNumber = splitNumber;
//        setLowerBound(lowerBound);
//        setUpperBound(upperBound);
//        this.primaryKeySchemaString = primaryKeySchemaString;
//    }
//
//    @Override
//    public int getSplitNumber() {
//        return splitNumber;
//    }
//
//    public PrimaryKey getLowerBound() {
//        PrimaryKey startPK = gson.fromJson(lowerBound, PrimaryKey.class);
//        if (startPK.getPrimaryKeyColumns().length <= 1) {
//            List<PrimaryKeyColumn> startpkcl = new ArrayList<>();
//            startpkcl.add(startPK.getPrimaryKeyColumn(0));
//            for (int i = 1; i < getPrimaryKeySchemaString().size(); ++i) {
//                String ss = getPrimaryKeySchemaString().get(i);
//                startpkcl.add(new PrimaryKeyColumn(ss, PrimaryKeyValue.INF_MIN));
//            }
//            startPK = new PrimaryKey(startpkcl);
//        }
//        return startPK;
//    }
//
//    public void setLowerBound(PrimaryKey lowerBound) {
//        this.lowerBound = gson.toJson(lowerBound);
//    }
//
//    public PrimaryKey getUpperBound() {
//        PrimaryKey endPK = gson.fromJson(upperBound, PrimaryKey.class);
//        if (endPK.getPrimaryKeyColumns().length <= 1) {
//            List<PrimaryKeyColumn> endpkcl = new ArrayList<>();
//            endpkcl.add(endPK.getPrimaryKeyColumn(0));
//            for (int i = 1; i < getPrimaryKeySchemaString().size(); ++i) {
//                String ss = getPrimaryKeySchemaString().get(i);
//                endpkcl.add(new PrimaryKeyColumn(ss, PrimaryKeyValue.INF_MIN));
//            }
//            endPK = new PrimaryKey(endpkcl);
//        }
//        return endPK;
//    }
//
//    public void setUpperBound(PrimaryKey upperBound) {
//        this.upperBound = gson.toJson(upperBound);
//    }
//
//    public List<String> getPrimaryKeySchemaString() {
//        return primaryKeySchemaString;
//    }
//}
