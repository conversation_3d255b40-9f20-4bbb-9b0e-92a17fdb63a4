package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.yupaopao.risk.insight.flink.bean.audit.AuditMetric;
import org.apache.flink.api.common.functions.AggregateFunction;

public class AuditMetricSumFunction implements AggregateFunction<AuditMetric, AuditMetric, AuditMetric> {
    @Override public AuditMetric createAccumulator() {
        AuditMetric result = new AuditMetric();
        result.setValue(0.0);
        return result;
    }

    @Override public AuditMetric add(AuditMetric value, AuditMetric accumulator) {
        Double sum = value.getValue() + accumulator.getValue();
        accumulator.cloneData(value);
        accumulator.setValue(sum);
        return accumulator;
    }

    @Override public AuditMetric getResult(AuditMetric accumulator) {
        return accumulator;
    }

    @Override public AuditMetric merge(AuditMetric a, AuditMetric b) {
        return a;
    }
}
