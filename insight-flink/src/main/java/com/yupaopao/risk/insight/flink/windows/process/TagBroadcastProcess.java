package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSONObject;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.bean.portrait.AggTag;
import com.yupaopao.risk.insight.flink.constants.FactorConstants;
import com.yupaopao.risk.insight.flink.windows.AggTagDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Slf4j
public class TagBroadcastProcess extends BroadcastProcessFunction<String, List<AggTag>, AggTagDetail> {

    public final static MapStateDescriptor<Void, List<AggTag>> CONFIG_DESCRIPTOR = new MapStateDescriptor<>("tags", Types.VOID,
        TypeInformation.of(new TypeHint<List<AggTag>>() {}));

    @Override public void processElement(String value, ReadOnlyContext ctx, Collector<AggTagDetail> out) throws Exception {
        List<AggTag> aggTags = ctx.getBroadcastState(CONFIG_DESCRIPTOR).get(null);
        if (null == aggTags || aggTags.size() <= 0) {
            return;
        }
        aggTags.forEach(aggTag -> {
            JSONObject param = JSONObject.parseObject(value);
            JSONObject data = param.getJSONObject("data");
            data.put("RiskLevel", param.get("level"));
            data.put("Result", param.get("result"));
            if (checkCondition(data, aggTag)) {
                String aggValue = getAggValue(aggTag, data);
                String groupKey = getGroupKey(aggTag, data);
                if ((StringUtils.isNotEmpty(aggValue) || "COUNT".equals(aggTag.getFunction())) && StringUtils.isNotEmpty(groupKey)) {
                    AggTagDetail result = new AggTagDetail();
                    result.setId(aggTag.getId());
                    result.setGroupKey(String.format(FactorConstants.FACTOR_DATA_PREFIX, aggTag.getId(), groupKey));
                    result.setData(aggValue);
                    result.setFunction(aggTag.getFunction());
                    result.setTimeSpan(aggTag.getTimeSpan());
                    out.collect(result);
                }
            }
        });
    }

    @Override public void processBroadcastElement(List<AggTag> value, Context ctx, Collector<AggTagDetail> out) throws Exception {
        ctx.getBroadcastState(CONFIG_DESCRIPTOR).put(null, value);
    }

    private boolean checkCondition(JSONObject param, AggTag tag) {
        if (StringUtils.isEmpty(tag.getCondition())) {
            return true;
        }

        try {
            Expression expression = new SpelExpressionParser().parseExpression(tag.getCondition());
            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setVariables(param);
            Object result = expression.getValue(context);
            log.debug("前置表达式执行结果:{} - {} - {}", result, tag.getCondition(), param);
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else {
                return result != null;
            }
        } catch (Throwable e) {
            log.error("执行聚合标签表达式出错: " + tag.getCondition() + param.toJSONString(), e);
        }
        return false;
    }

    private String getGroupKey(AggTag aggTag, JSONObject data){
        List<String> groupValues = new LinkedList<>();
        for (String groupKey : aggTag.getGroupKey().split(",")) {
            String groupValue;

            if (groupKey.contains(".")) {
                Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                groupValue = dataMap.getOrDefault(groupKey, "").toString();
            } else {
                groupValue = data.getString(groupKey);
            }
            // 缺失任意groupKeyValue则放弃
            if (StringUtils.isBlank(groupValue)) {
                return "";
            }
            groupValues.add(groupValue);
        }
        return String.join("$", groupValues);
    }


    private String getAggValue(AggTag aggTag, JSONObject data){
        data.put("day", InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd));
        if(StringUtils.isEmpty(aggTag.getAggKey())){
            return "";
        } else {
            List<String> aggValues = new LinkedList<>();
            boolean hasValue = false;
            for (String aggKey : aggTag.getAggKey().split(",")) {
                String aggValue;

                if (aggKey.contains(".")) {
                    Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                    aggValue = dataMap.getOrDefault(aggKey, "").toString();
                } else {
                    aggValue = data.getOrDefault(aggKey, "").toString();
                }
                if (StringUtils.isNotBlank(aggValue)) {
                    hasValue = true;
                }
                aggValues.add(aggValue);
            }
            if (hasValue) {
                return String.join("$", aggValues);
            } else {
                return "";
            }
        }
    }
}
