package com.yupaopao.risk.insight.flink.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc AccumulateInfo
 * @date 2020/11/23
 */
@Data
public class AccumulateInfo implements Serializable {
    private Integer id;
    private String groupKey;
    private String aggKey;
    private String function;
    private long timeSpan;
    private String condition;
    private String version;
    private String thresholds;
}
