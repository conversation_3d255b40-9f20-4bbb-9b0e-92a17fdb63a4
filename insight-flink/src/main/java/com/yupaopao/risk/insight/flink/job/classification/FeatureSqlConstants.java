package com.yupaopao.risk.insight.flink.job.classification;

import com.alibaba.fastjson.util.TypeUtils;

import java.math.BigDecimal;

/****
 * zengxiangcai
 * 2023/11/2 00:14
 ***/
public class FeatureSqlConstants {
    public static void main(String[] args) {

        System.err.println(DEVICE_RAWDATA_FEATURE_TEMPLATE_SQL);
    }

    public static final String USER_TAG_FEATURE_TEMPLATE_SQL = "select rowKey as userId,${allFeatures} from risk_user_tag where " +
            "syncDate = addDays(toDate('${activeDate}'),1) and rowKey global in (\n" +
            "    select id from risk_dtree_sample where batchId = '${batchId}'\n" +
            ") order by userId";

    public static final String RISK_USER_FEATURE_TEMPLATE_SQL = "select uid as userId,${allFeatures} from " +
            " risk_user where " +
            " syncDate = addDays(toDate('${activeDate}'),1) and uid global in (\n" +
            "    select id from risk_dtree_sample where batchId = '${batchId}'\n" +
            ") order by userId";

    public static final String DEVICE_TAG_FEATURE_TEMPLATE_SQL = "select userId,${featuresNames} from \n" +
            "(\n" +
            "    select userId,relateId from ods_risk_active_ids \n" +
            "    where dt >=addDays(toDate('${activeDate}'), -6) and dt<=toDate('${activeDate}')\n" +
            "    and relateType = 1\n" +
            "    and userId global in (select id from risk_dtree_sample where batchId ='${batchId}') group by " +
            "    userId, relateId\n" +
            " ) aa join (\n" +
            "      select rowKey as deviceId,${allFeatures} from risk_device_tag where syncDate = addDays(toDate('${activeDate}'), 1)\n" +
            "      and rowKey global in (\n" +
            "       select deviceId from (\n" +
            "        select userId,arrayJoin(groupArray(1)(relateId)) as deviceId from (\n" +
            "          select userId,relateId from ods_risk_active_ids where dt >=addDays(toDate('${activeDate}'), -6)\n" +
            "          and dt<=toDate('${activeDate}')\n" +
            "          and relateType = 1\n" +
            "          and userId global in (select id from risk_dtree_sample where batchId ='${batchId}' )\n" +
            "          order by activeCount desc\n" +
            "        ) group by userId\n" +
            "      )\n" +
            "     )\n" +
            ") bb\n" +
            "on aa.relateId = bb.deviceId\n" +
            "order by userId";

    public static final String DEVICE_RAWDATA_FEATURE_TEMPLATE_SQL = "select userId,${featuresNames} from \n" +
            "(\n" +
            "    select userId,relateId from ods_risk_active_ids \n" +
            "    where dt >=addDays(toDate('${activeDate}'), -6) and dt<=toDate('${activeDate}')\n" +
            "    and relateType = 1\n" +
            "    and userId global in (select id from risk_dtree_sample where batchId ='${batchId}') group by " +
            "    userId, relateId\n" +
            " ) aa join (\n" +
            "      select deviceId,${allFeatures} from risk_device_rawdata where syncDate = addDays(toDate" +
            "('${activeDate}'), 1)\n" +
            "      and deviceId global in (\n" +
            "       select deviceId from (\n" +
            "        select userId,arrayJoin(groupArray(1)(relateId)) as deviceId from (\n" +
            "          select userId,relateId from ods_risk_active_ids where dt >=addDays(toDate('${activeDate}'), -6)\n" +
            "          and dt<=toDate('${activeDate}')\n" +
            "          and relateType = 1\n" +
            "          and userId global in (select id from risk_dtree_sample where batchId ='${batchId}' )\n" +
            "          order by activeCount desc\n" +
            "        ) group by userId\n" +
            "      )\n" +
            "     )\n" +
            ") bb\n" +
            "on aa.relateId = bb.deviceId\n" +
            "order by userId";

    public static final String MOBILE_TAG_FEATURE_TEMPLATE_SQL = "\n" +
            "select userId,${featuresNames} from (\n" +
            "  select userId,relateId from ods_risk_active_ids\n" +
            "  where dt >=addDays(toDate('${activeDate}'), -6) and dt<=toDate('${activeDate}')\n" +
            "    and relateType = 2\n" +
            "    and userId global in (select id from risk_dtree_sample where batchId ='${batchId}')\n" +
            "  group by userId,relateId\n" +
            ") aa join (\n" +
            "    select rowKey as mobile,${allFeatures} from risk_mobile_tag\n" +
            "     where syncDate = addDays(toDate('${activeDate}'),1)\n" +
            "     and rowKey global in (\n" +
            "            select relateId from ods_risk_active_ids where dt >=addDays(toDate('${activeDate}'), -6) and dt<=toDate('${activeDate}')\n" +
            "            and relateType = 2 and userId global in (\n" +
            "                    select id from risk_dtree_sample where batchId ='${batchId}'\n" +
            "            ) group by relateId\n" +
            "     )\n" +
            ") bb on aa.relateId = bb.mobile order by userId";


    public static final String USER_AGG_FEATURE_TEMPLATE_SQL = "select id as userId,${allFeatures} from " +
            "ods_risk_user_feature_agg_df " +
            "where dt = '${activeDate}' and id global in (\n" +
            "  select id from risk_dtree_sample where batchId ='${batchId}'\n" +
            ") order by userId";

    public static final String DEVICE_AGG_FEATURE_TEMPLATE_SQL = "select userId,${featuresNames} from (\n" +
            "    select userId,relateId from ods_risk_active_ids\n" +
            "    where dt >=addDays(toDate('${activeDate}'), -6) and dt<=toDate('${activeDate}')\n" +
            "      and relateType = 1\n" +
            "      and userId global in (select id from risk_dtree_sample where batchId ='${batchId}')\n" +
            "    group by userId,relateId\n" +
            ") aa join (\n" +
            "    select id,${allFeatures} from ods_risk_device_feature_agg_df\n" +
            "    where dt='${activeDate}'\n" +
            "      and id global in (\n" +
            "          select deviceId from (\n" +
            "            select userId,arrayJoin(groupArray(1)(relateId)) as deviceId from (\n" +
            "                    select userId,relateId from ods_risk_active_ids where dt >=addDays(toDate('${activeDate}'), -6)\n" +
            "                    and dt<=toDate('${activeDate}')\n" +
            "                    and relateType = 1\n" +
            "                    and userId global in (select id from risk_dtree_sample where batchId ='${batchId}' )\n" +
            "                     order by activeCount desc\n" +
            "                ) group by userId\n" +
            "          )\n" +
            "      )\n" +
            ") bb on aa.relateId = bb.id order by userId";

    public static final String MOBILE_AGG_FEATURE_TEMPLATE_SQL = "\n" +
            "select userId,${featuresNames} from (\n" +
            "    select userId,relateId from ods_risk_active_ids where dt >=addDays(toDate('${activeDate}'), -6) and dt<=toDate('${activeDate}') and relateType = 2\n" +
            "    group by userId,relateId\n" +
            ") aa join (\n" +
            "    select id ,${allFeatures} from ods_risk_mobile_feature_agg_df\n" +
            "    where dt='${activeDate}'\n" +
            "      and id global in (\n" +
            "        select relateId from ods_risk_active_ids where dt >=addDays(toDate('${activeDate}'), -6) and dt<=toDate('${activeDate}')\n" +
            "                                                   and relateType = 2 and userId global in (\n" +
            "                select id from risk_dtree_sample where batchId ='${batchId}'\n" +
            "        ) group by relateId\n" +
            "    )\n" +
            "    ) bb on aa.relateId = bb.id order by userId";

    public static final String SAMPLE_TEMPLATE_SQL = "select id ,label,weight from risk_dtree_sample where" +
            " " +
            "batchId='${batchId}'";


    public static final String ALL_DEVICE_TAG_FEATURE_SQL = "select rowKey as deviceId, ${allFeatures} from risk_device_tag where syncDate = addDays(toDate('${activeDate}'),1)\n" +
            "and deviceId global in (\n" +
            "   select deviceId from risk_hit_log where toDate(createdAt) = toDate('${activeDate}') and deviceId!='' and deviceId!='null' group by deviceId\n" +
            ")  order by deviceId";
    public static final String ALL_DEVICE_AGG_FEATURE_SQL = "select id as deviceId, ${allFeatures} from " +
            "ods_risk_device_feature_agg_df where dt = '${activeDate}'\n" +
            "and id global in (\n" +
            "   select deviceId from risk_hit_log where toDate(createdAt) = toDate('${activeDate}') and deviceId!='' and deviceId!='null' group by deviceId\n" +
            ")  order by deviceId";
    public static final String ALL_DEVICE_RAWDATA_FEATURE_SQL = "select deviceId, ${allFeatures} from " +
            " risk_device_rawdata" +
            " where syncDate = addDays(toDate('${activeDate}'),1)\n" +
            "and deviceId global in (\n" +
            "   select deviceId from risk_hit_log where toDate(createdAt) = toDate('${activeDate}') and deviceId!='' and deviceId!='null' group by deviceId\n" +
            ")  order by deviceId";

    public static final String ALL_USER_TAG_FEATURE_SQL = "select rowKey as userId, ${allFeatures} from " +
            "risk_user_tag where syncDate = addDays(toDate('${activeDate}'),1)\n" +
            "and userId global in (\n" +
            "       select userId from risk_hit_log where toDate(createdAt) = toDate('${activeDate}') and userId!='' and userId not in ('null','0') and data_Business!='riskPayment'\n" +
            "       and eventCode not in ('user-complain','complain-post')\n" +
            ") order by userId";
    public static final String ALL_USER_AGG_FEATURE_SQL = "select id as userId, ${allFeatures} from " +
            "ods_risk_user_feature_agg_df where dt = '${activeDate}'\n" +
            "and id global in (\n" +
            "       select userId from risk_hit_log where toDate(createdAt) = toDate('${activeDate}') and userId!='' and userId not in ('null','0') and data_Business!='riskPayment'\n" +
            "       and eventCode not in ('user-complain','complain-post')\n" +
            ") order by userId";
    public static final String ALL_RISK_USER_FEATURE_SQL = "select uid as userId, ${allFeatures} from " +
            " risk_user" +
            " where syncDate = addDays(toDate('${activeDate}'),1)\n" +
            "and uid global in (\n" +
            "       select userId from risk_hit_log where toDate(createdAt) = toDate('${activeDate}') and userId!='' and userId not in ('null','0') and data_Business!='riskPayment'\n" +
            "       and eventCode not in ('user-complain','complain-post')\n" +
            ") order by userId";

    public static final String ALL_MOBILE_TAG_FEATURE_SQL = "with userMobileInfo as (\n" +
            "   select uid,encryptedMobile from risk_user where syncDate =  addDays(toDate('${activeDate}'),1)\n" +
            "    and uid global in (\n" +
            "       select userId from risk_hit_log where toDate(createdAt) = toDate('${activeDate}') and userId!='' and userId not in ('null','0') and data_Business!='riskPayment'\n" +
            "       and eventCode not in ('user-complain','complain-post')\n" +
            "    )\n" +
            ")\n" +
            "select bb.uid as userId, ${featuresNames} from (\n" +
            "    select rowKey,${allFeatures} from risk_mobile_tag where  syncDate =  addDays(toDate('${activeDate}'),1)\n" +
            "    and rowKey global in (\n" +
            "        select encryptedMobile from userMobileInfo\n" +
            "    )\n" +
            ") aa global all inner join (\n" +
            "    select uid,encryptedMobile from userMobileInfo\n" +
            ") bb on aa.rowKey = bb.encryptedMobile  order by userId\n" +
            "\n";
    public static final String ALL_MOBILE_AGG_FEATURE_SQL = "with userMobileInfo as (\n" +
            "   select uid,encryptedMobile from risk_user where syncDate =  addDays(toDate('${activeDate}'),1)\n" +
            "    and uid global in (\n" +
            "       select userId from risk_hit_log where toDate(createdAt) = toDate('${activeDate}') and userId!='' and userId not in ('null','0') and data_Business!='riskPayment'\n" +
            "       and eventCode not in ('user-complain','complain-post')\n" +
            "    )\n" +
            ")\n" +
            "select bb.uid as userId, ${featuresNames} from (\n" +
            " select id,${allFeatures} from ods_risk_mobile_feature_agg_df where dt = '${activeDate}'\n" +
            "    and id global in (\n" +
            "        select encryptedMobile from userMobileInfo\n" +
            "    )\n" +
            ") aa global all inner join (\n" +
            "    select uid,encryptedMobile from userMobileInfo\n" +
            ") bb on aa.id = bb.encryptedMobile  order by userId\n" +
            "\n";

}
