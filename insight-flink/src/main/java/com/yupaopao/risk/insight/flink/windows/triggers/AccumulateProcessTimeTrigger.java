package com.yupaopao.risk.insight.flink.windows.triggers;

import com.yupaopao.risk.insight.flink.windows.AccumulateCalDetail;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;

public class AccumulateProcessTimeTrigger extends Trigger<AccumulateCalDetail, TimeWindow> {
    private long slideAvgNum;

    public AccumulateProcessTimeTrigger() {
    }

    public AccumulateProcessTimeTrigger(long slideAvgNum) {
        this.slideAvgNum = slideAvgNum;
    }

    @Override public TriggerResult onElement(AccumulateCalDetail element, long timestamp, TimeWindow window, TriggerContext ctx) throws Exception {
        ctx.registerProcessingTimeTimer(window.maxTimestamp());
//        timestamp = ctx.getCurrentProcessingTime();

        if (element.isPurge()) {
            return TriggerResult.FIRE_AND_PURGE;
        }
        return TriggerResult.FIRE;
//        long size = window.getEnd() - window.getStart();
//        if (window.getEnd() - timestamp <= size / slideAvgNum) {
//            return TriggerResult.FIRE;
//        } else {
//            return TriggerResult.CONTINUE;
//        }
    }

    @Override public TriggerResult onProcessingTime(long time, TimeWindow window, TriggerContext ctx) throws Exception {
        return TriggerResult.CONTINUE;
    }

    @Override public TriggerResult onEventTime(long time, TimeWindow window, TriggerContext ctx) throws Exception {
        return TriggerResult.CONTINUE;
    }

    @Override public void clear(TimeWindow window, TriggerContext ctx) throws Exception {
        ctx.deleteProcessingTimeTimer(window.maxTimestamp());
    }

    @Override
    public boolean canMerge() {
        return true;
    }

    @Override
    public void onMerge(TimeWindow window,
        OnMergeContext ctx) {
        // only register a timer if the time is not yet past the end of the merged window
        // this is in line with the logic in onElement(). If the time is past the end of
        // the window onElement() will fire and setting a timer here would fire the window twice.
        long windowMaxTimestamp = window.maxTimestamp();
        if (windowMaxTimestamp > ctx.getCurrentProcessingTime()) {
            ctx.registerProcessingTimeTimer(windowMaxTimestamp);
        }
    }

    public static AccumulateProcessTimeTrigger create() {
        return new AccumulateProcessTimeTrigger();
    }

    public static AccumulateProcessTimeTrigger create(long slideAvgNum) {
        return new AccumulateProcessTimeTrigger(slideAvgNum);
    }

}
