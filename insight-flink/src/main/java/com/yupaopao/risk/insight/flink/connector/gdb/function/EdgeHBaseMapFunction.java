package com.yupaopao.risk.insight.flink.connector.gdb.function;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yupaopao.risk.insight.common.beans.graph.EdgeInfo;
import org.apache.flink.api.common.functions.MapFunction;

import java.util.HashMap;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-08 18:41
 *
 ***/
public class EdgeHBaseMapFunction implements MapFunction<EdgeInfo,String> {
    @Override
    public String map(EdgeInfo elem) throws Exception {
        Map<String, Object> edge = new HashMap();
        edge.put("id", elem.getId());
        edge.put("label", elem.getLabel());
        edge.put("fromVertex",elem.getFromVertex());
        edge.put("toVertex",elem.getToVertex());
        edge.put(EdgeInfo.EDGE_PROP_CREATE_TIME,System.currentTimeMillis());
        edge.put(EdgeInfo.EDGE_PROP_UPDATE_TIME,System.currentTimeMillis());

        return JSON.toJSONString(edge, SerializerFeature.DisableCircularReferenceDetect);
    }
}
