package com.yupaopao.risk.insight.flink.connector.es.source;

import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.state.OperatorStateStore;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.source.InputFormatSourceFunction;

public class EsStreamSource extends InputFormatSourceFunction<String> implements CheckpointedFunction {

    private transient ListState<String> scrollState;
    private EsInputFormat inputFormat;

    public EsStreamSource(EsInputFormat format, TypeInformation<String> typeInfo) {
        super(format, typeInfo);
        inputFormat = format;
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        scrollState.clear();
        scrollState.add(inputFormat.getScrollId());
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        OperatorStateStore stateStore = context.getOperatorStateStore();
        scrollState = stateStore.getListState(new ListStateDescriptor(
                "ESScrollState",
                String.class));
        if (context.isRestored()) {
            inputFormat.setScrollId(scrollState.get().iterator().next());
        }
    }
}
