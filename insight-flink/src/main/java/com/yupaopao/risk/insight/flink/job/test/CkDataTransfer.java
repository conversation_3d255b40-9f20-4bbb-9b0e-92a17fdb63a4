package com.yupaopao.risk.insight.flink.job.test;

import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.connector.clickhouse.source.CKInputFormat;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import lombok.Getter;
import lombok.Setter;
import org.apache.flink.api.java.ExecutionEnvironment;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-09-28 18:53
 *
 ***/
public class CkDataTransfer {
    public static void main(String[] args) throws Exception {
        ClickHouseProperties ckProperties = ClickHouseUtil.getLongTimeoutProperties();


        ClickHouseProperties testCkProperties = ClickHouseUtil.getLongTimeoutProperties();
        testCkProperties.setUsername("risk");
        testCkProperties.setPassword("**********");
        testCkProperties.setHost("************");


        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();

        CKInputFormat inputFormat = new CKInputFormat(ckProperties, "select id,eventList,runDay from " +
                "behavior_sequence_daily where runDay='2020-09-29' order by id asc,runDay asc"
                , "{}");

        CKOutputFormat ckOutputFormat = new CKOutputFormat(testCkProperties, "behavior_sequence_daily");

        env.createInput(inputFormat).filter(elem-> !HBaseUtil.emptyResultJson(elem))
//              .groupBy(new KeySelector<String, String>() {
//                  @Override
//                  public String getKey(String value) throws Exception {
//                      return JSON.parseObject(value).getString("id");
//                  }
//              }).reduceGroup(new GroupReduceFunction<String,String>(){
//
//            @Override
//            public void reduce(Iterable<String> values, Collector<String> out) throws Exception {
//                List<EventInfo> allEvents = new ArrayList<>();
//                for(String value: values){
//                    allEvents.add(JSON.parseObject(value,EventInfo.class));
//                }
//                allEvents.sort(Comparator.comparing(EventInfo::getRunDay));
//                String lastEvent = "";
//                String returnValue = "";
//                for(EventInfo value: allEvents){
//                    String[] tempArr = value.getEventList().split(" ");
//                    for(String tmp: tempArr){
//                        if(tmp.equals(lastEvent)){
//                            continue;
//                        }
//                        returnValue+=" "+tmp;
//                        lastEvent = tmp;
//                    }
//                }
//
//                Map<String,String> row = new HashMap<>();
//                row.put("runDay","2020-09-29");
//                row.put("eventList",returnValue);
//                row.put("id",allEvents.get(0).getId());
//               out.collect(JSON.toJSONString(row));
//            }
//        })
                .output(ckOutputFormat);

        env.execute("test transfer data");
    }

    @Getter
    @Setter
    public static class EventInfo {
        private String id;
        private String eventList;
        private String runDay;
    }
}
