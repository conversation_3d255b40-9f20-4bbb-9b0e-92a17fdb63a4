
package com.yupaopao.risk.insight.flink.job.etl.common.connect.clickhouse.writer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.clickhouse.client.ClickHouseClient;
import com.clickhouse.client.ClickHouseNodes;
import com.clickhouse.data.ClickHouseFormat;
import com.dtstack.flinkx.exception.WriteRecordException;
import com.dtstack.flinkx.restore.FormatState;
import com.dtstack.flinkx.util.ClassUtil;
import com.dtstack.flinkx.util.ExceptionUtil;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.OSSProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.CommonClickHouseDataType;
import com.yupaopao.risk.insight.flink.connector.clickhouse.DataTransferUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ColumnSyncLocker;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.clickhouse.core.ClickhouseUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.rdb.core.util.DbUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.rdb.writer.JdbcOutputFormat;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

public class ClickhouseBatchOutputFormat extends JdbcOutputFormat {

    private static final Logger LOG = LoggerFactory.getLogger(ClickhouseBatchOutputFormat.class);
    private static final long INTERVAL = 30 * 60 * 1000;
    private static final long serialVersionUID = -6625254941248728551L;

    private long lastSync = 0L;
    private final Map<String, Map<String, String>> columnMap = new HashMap<>();

    protected Map<String, List<String>> fullColumn = new HashMap<>();

    protected Map<String, List<String>> fullColumnType = new HashMap<>();

    private List<String> tables;

    private transient ClickHouseClient client;

    private transient ClickHouseNodes server;

    @Override
    protected void openInternal(int taskNumber, int numTasks) {
        try {
            ClassUtil.forName(driverName, getClass().getClassLoader());
            dbConn = ClickhouseUtil.getConnection(dbUrl, username, password);
            ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
            this.client = ClickHouseUtil.getCkClient(ckProperties);
            this.server = ClickHouseUtil.getServers(ckProperties);
            if (restoreConfig.isRestore()) {
                dbConn.setAutoCommit(false);
            }

            tables = Arrays.asList(table.split(","));

            for (String table : tables) {
                initFullColumnAndType(table);
            }

            for (String t : table.split(",")) {
                String key = t + "_write_count";
                initMetric(key);
                initRestoreMetric(key);
            }

            LOG.info("subTask[{}}] wait finished", taskNumber);
        } catch (SQLException sqe) {
            throw new IllegalArgumentException("open() failed.", sqe);
        }
    }

    @Override
    protected void writeSingleRecordInternal(Map<String, Object> row) throws WriteRecordException {
        this.save(Collections.singletonList(row));
        if (lastSync < System.currentTimeMillis() - INTERVAL) {
            syncColumn();
            lastSync = System.currentTimeMillis();
        }
    }

    @Override
    protected void writeMultipleRecordsInternal() throws Exception {
        this.save(rows);
        if (lastSync < System.currentTimeMillis() - INTERVAL) {
            syncColumn();
            lastSync = System.currentTimeMillis();
        }
    }

    @Override
    public FormatState getFormatState() {
        if (formatState != null) {
            formatState.setMetric(outputMetric.getMetricCounters());
        }
        return formatState;
    }

    @Override
    public void close() throws IOException {
        syncColumn();
        super.close();
        client.close();
        server.shutdown();
    }

    private void initMetric(String key) {
        LongCounter longCounter = getRuntimeContext().getLongCounter(key);
        outputMetric.addMetric(key, longCounter, true);
    }

    private void initRestoreMetric(String key) {
        if (restoreConfig.isRestore()) {
            if (formatState != null) {
                long metricValue = 0;
                if (formatState.getMetric() != null && formatState.getMetric().containsKey(key)) {
                    metricValue = formatState.getMetricValue(key);
                }
                getRuntimeContext().getLongCounter(key).add(metricValue);
            }
        }
    }

    private void syncColumn() {
        String syncColumnSwitch = ApolloProperties.getConfigStr("global.ck.syncColumn.switch");
        if (!"true".equals(syncColumnSwitch)) {
            return;
        }
        for (String table : tables) {
            Map<String, String> notExitColumns = fetchNotExitColumns(table);
            if (!notExitColumns.isEmpty()) {
                LOG.info("准备为 {} 补充 {} 个字段: {}", table, notExitColumns.size(), JSON.toJSONString(notExitColumns));
                try {
                    flushDistributedTable(table);
                    addColumn(table + "_local", notExitColumns);
                    addColumn(table, notExitColumns);
                } finally {
                    ColumnSyncLocker.isSyncColumn = false;
                }
            } else {
                LOG.info("{} 暂无需补充字段, 数据字段个数: {}", table, fullColumn.get(table).size());
            }
        }
    }

    private Map<String, String> fetchNotExitColumns(String table) {
        initFullColumnAndType(table);
        Map<String, String> notExitColumns = new HashMap<>();
        Map<String, String> columnMapTmp = columnMap.getOrDefault(table, new HashMap<>());
        List<String> fullColumnList = fullColumn.getOrDefault(table, new ArrayList<>());
        //如果因为查询失败会导致不断的alter，需要注意
        columnMapTmp.forEach((key, value) -> {
            if (!fullColumnList.isEmpty() && !fullColumnList.contains(key)) {
                notExitColumns.put(key, value);
            }
        });
        return notExitColumns;
    }

    private void cacheColumn(Map<String, Object> value, String table) {
        List<String> fullColumnList = fullColumn.getOrDefault(table, new ArrayList<>());
        List<String> fullTypeList = fullColumnType.getOrDefault(table, new ArrayList<>());
        Map<String, String> columnMapTmp = columnMap.getOrDefault(table, new HashMap<>());
        int originalSize = columnMapTmp.size();
        for (String key : value.keySet()) {
            int index = fullColumnList.indexOf(key);
            Object originalValue = value.get(key);
            if (DataTransferUtils.isEmptyObject(originalValue)) {
                value.put(key, null);
                continue;
            }
            if (index > -1) {
                if (!isSameType(fullTypeList.get(index), originalValue)) {
                    value.put(key, valueParse(fullTypeList.get(index), originalValue));
                }
            } else {
                String clickHouseType = CommonClickHouseDataType.getClickHouseType(originalValue);
                if (StringUtils.isNotBlank(clickHouseType) && !isSpecialKey(clickHouseType)) {
                    columnMapTmp.put(key, clickHouseType);
                }
                fullColumnList.add(key);
                fullTypeList.add(clickHouseType);
            }
        }
        int finalSize = columnMapTmp.size();
        if ("risk_device_callback".equals(table) && finalSize > originalSize) {
            LOG.info("callback data: {}", value);
        }
        fullColumn.put(table, fullColumnList);
        fullColumnType.put(table, fullTypeList);
        columnMap.put(table, columnMapTmp);
    }

    private static boolean isSameType(String ckType, Object value) {
        ckType = ckType.toLowerCase();
        if (value instanceof Number) {
            return ckType.contains("int") || ckType.contains("float") || ckType.contains("decimal");
        } else {
            return value instanceof String;
        }
    }

    private static Object valueParse(String cktype, Object value) {
        if (value instanceof Boolean) {
            return (Boolean) value ? 1 : 0;
        }
        switch (cktype.toLowerCase()) {
            case "string":
            case "datetime":
            case "date":
                // 暂时只对字符串类型做强转处理
                return value.toString();
            default:
                return value;
        }
    }

    private static boolean isSpecialKey(String key) {
        return key.contains("\\");
    }

    private void initFullColumnAndType(String table) {
        List<String> nameList = new ArrayList<>();
        List<String> typeList = new ArrayList<>();

        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = dbConn.createStatement();
            rs = stmt.executeQuery("desc " + table);
            while (rs.next()) {
                nameList.add(rs.getString(1));
                typeList.add(rs.getString(2));
            }
        } catch (SQLException e) {
            LOG.error("error to get {} schema, e = {}", table, ExceptionUtil.getErrorMessage(e));
        } finally {
            DbUtil.closeDbResources(rs, stmt, null, false);
        }
        if (nameList.size() > 0 && typeList.size() > 0) {
            fullColumn.put(table, nameList);
            fullColumnType.put(table, typeList);
            LOG.info("{} init table columnAndType, names: {}, typeSize: {}", table, JSON.toJSONString(nameList),
                    JSON.toJSONString(typeList));
        }

    }

    private boolean flushDistributedTable(String table) {
        String sql = "SYSTEM FLUSH DISTRIBUTED " + table;
        Statement stmt = null;
        try {
            stmt = dbConn.createStatement();
            LOG.info("flush table: {}", table);
            return stmt.execute(sql);
        } catch (Exception e) {
            LOG.error("sql execute error: ", e);
        } finally {
            DbUtil.closeDbResources(null, stmt, null, false);
        }
        return true;
    }

    private boolean addColumn(String table, Map<String, String> newColumns) {
        if (MapUtils.isEmpty(newColumns)) {
            return false;
        }

        StringBuilder sql = new StringBuilder("ALTER TABLE ").append(table).append(" ON CLUSTER default");
        newColumns.forEach((key, value) -> {
            if (!ClickHouseUtil.isValidColumn(key)) {
                LOG.warn("invalid column name: {} in table {} has ignored", key, table);
                return;
            }
            sql.append(" ADD COLUMN IF NOT EXISTS ").append(key).append(" ").append(value).append(",");
        });

        Statement stmt = null;
        try {
            stmt = dbConn.createStatement();
            String alterSql = sql.deleteCharAt(sql.length() - 1).toString();
            LOG.info("alter sql: {}", alterSql);
            return stmt.execute(alterSql);
        } catch (RuntimeException e) {
            // ru.yandex.clickhouse 暂不支持 alter 结果解析，会抛出 JsonParseException，字段会正常修改，忽视该异常
            LOG.info("alter 结果解析异常, 字段会正常修改");
            return true;
        } catch (Exception e) {
            LOG.error("sql execute error: ", e);
        } finally {
            DbUtil.closeDbResources(null, stmt, null, false);
        }
        return false;
    }

    private void save(List<Map<String, Object>> records) {
        if (records.size() == 0) {
            return;
        }
        Map<String, List<String>> datas = new HashMap<>();
        for (Map<String, Object> record : records) {
            String table;
            if (mapping == null) {
                if (record.containsKey("data_source")) {
                    record.remove("data_source");
                } else {
                    record.remove("source");
                }
                table = tables.get(0);
            } else {
                String source;
                if (record.containsKey("data_source")) {
                    source = record.remove("data_source").toString();
                } else {
                    source = record.remove("source").toString();
                }
                if (StringUtils.isBlank(source) || !mapping.containsKey(source)) {
                    continue;
                }
                table = mapping.get(source);
            }
            getRuntimeContext().getLongCounter(table + "_write_count").add(1);

            List<String> tmp = datas.getOrDefault(table, new ArrayList<>());

            Map<String, Object> mapFlatter =
                    flatterKeyParse(JsonFlatterUtils.toMap(JSON.toJSONStringWithDateFormat(record, "yyyy-MM-dd " +
                            "HH:mm:ss.SSS"), FlattenMode.KEEP_ARRAYS));
            //list类型处理
            cacheColumn(mapFlatter, table);
            if (autoTimeCreate) {
                if (!mapFlatter.containsKey(timeColumn)) {
                    mapFlatter.put(timeColumn, DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
                } else {
                    mapFlatter.put(timeColumn, formatTime(mapFlatter.get(timeColumn)));
                }
            }
            tmp.add(JSONObject.toJSONString(mapFlatter));
            datas.put(table, tmp);
        }

        try {
            for (String key : datas.keySet()) {
                String currentTable = key;
                List<String> currDataList = datas.get(key);
                insertData(currDataList, currentTable, dbConn);
            }
        } catch (Exception e) {
            LOG.error("db statement create error: " + e.getMessage(), e);
        }
    }

    private void insertData(List<String> currDataList, String tableName, Connection conn) {
        String sql = "";
        Map<String, List<String>> batchParts = ClickHouseUtil.processBigTablesParts(currDataList, tableName);
        for (List<String> part : batchParts.values()) {
            try (Statement stmt = conn.createStatement();) {
                sql = buildSql(part, tableName);
//                stmt.execute(sql);
                client.read(server).format(ClickHouseFormat.JSONEachRow).write().query(sql).executeAndWait();
            } catch (Exception e) {
                String objectName = writeToOSS(sql, tableName);
                LOG.error("sql execute error: {}, objectName {}", e, objectName);
            }
        }

    }


    private String formatTime(Object createdAt) {
        if (createdAt instanceof Long) {
            return new DateTime(createdAt).toString("yyyy-MM-dd HH:mm:ss");
        } else if (createdAt instanceof String) {
            try {
                return new DateTime(createdAt).toString("yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                return (String) createdAt;
            }
        }
        return DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    private String buildSql(List<String> datas, String table) {
        String resultLines = String.join("\n", datas);
        String writeTable = table;
        if (ClickHouseUtil.getWriteToLocalTables().contains(table)) {
            writeTable = table + "_local";
        }
        String resultFormat = "INSERT INTO %s FORMAT JSONEachRow\n%s";
        return String.format(resultFormat, writeTable, resultLines);
    }

    private String writeToOSS(String content, String tableName) {
        try {
            String sqlFileName = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyyMMddHHmmss);
            String objectName = FlinkConstants.getCKSqlBasePath() + tableName + "/" + sqlFileName + ".sql";
            OSSProperties ossProperties = OSSProperties.getProperties(PropertyType.OSS);
            OSS ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());
            ossClient.putObject(ossProperties.getBucket(), objectName, new ByteArrayInputStream(content.getBytes()));
            ossClient.shutdown();
            return objectName;
        } catch (Exception e) {
            LOG.error("write to oss error: ", e);
        }
        return null;
    }


    public static Map<String, Object> flatterKeyParse1(Map<String, Object> flatterMap) {
        Map<String, Object> parse = new HashMap<>();
        for (Map.Entry<String, Object> entry : flatterMap.entrySet()) {
            String newKey;
            if (entry.getKey().contains("[\\\"") && entry.getKey().contains("\\\"]")) {
                newKey = entry.getKey().replace("[\\\"", "_").replace("\\\"]", "").replaceAll("^_", "");
                parse.put(newKey, entry.getValue());
            } else {
                parse.put(entry.getKey(), entry.getValue());
            }
        }
        return parse;
    }

    private static void cacheColumn1(Map<String, Object> value, String table) {
        List<String> nameList = new ArrayList<>();
        List<String> typeList = new ArrayList<>();
        Map<String, List<String>> fullColumn = new HashMap<>();
        Map<String, List<String>> fullColumnType = new HashMap<>();

        Statement stmt = null;
        ResultSet rs = null;
        try {

            stmt = ClickHouseUtil.createConnection(ClickHouseUtil.getLongTimeoutProperties()).createStatement();
            rs = stmt.executeQuery("desc " + table);
            while (rs.next()) {
                nameList.add(rs.getString(1));
                typeList.add(rs.getString(2));
            }
        } catch (SQLException e) {
            LOG.error("error to get {} schema, e = {}", table, ExceptionUtil.getErrorMessage(e));
        } finally {
            DbUtil.closeDbResources(rs, stmt, null, false);
        }
        if (nameList.size() > 0 && typeList.size() > 0) {
            fullColumn.put(table, nameList);
            fullColumnType.put(table, typeList);
            LOG.info("init table columnAndType, nameSize: {}, typeSize: {}", nameList.size(), typeList.size());
        }

        List<String> fullColumnList = fullColumn.getOrDefault(table, new ArrayList<>());
        List<String> fullTypeList = fullColumnType.getOrDefault(table, new ArrayList<>());
        Map<String, String> columnMapTmp = new HashMap<>();
        for (String key : value.keySet()) {
            int index = fullColumnList.indexOf(key);
            Object originalValue = value.get(key);
            if (DataTransferUtils.isEmptyObject(originalValue)) {
                value.put(key, null);
                continue;
            }
            if (index > -1) {
                if (!isSameType(fullTypeList.get(index), originalValue)) {
                    value.put(key, valueParse(fullTypeList.get(index), originalValue));
                }
            } else {
                String clickHouseType = CommonClickHouseDataType.getClickHouseType(originalValue);
                if (StringUtils.isNotBlank(clickHouseType) && !isSpecialKey(clickHouseType)) {
                    columnMapTmp.put(key, clickHouseType);
                }
                fullColumnList.add(key);
                fullTypeList.add(clickHouseType);
            }
        }
        fullColumn.put(table, fullColumnList);
        fullColumnType.put(table, fullTypeList);
//        columnMap.put(table, columnMapTmp);
    }
}
