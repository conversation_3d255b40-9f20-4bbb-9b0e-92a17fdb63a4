package com.yupaopao.risk.insight.flink.job.graph.historyData;

import com.yupaopao.risk.insight.flink.job.base.CheckpointSetting;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.graph.historyData.GraphHbaseVertexToGDBProcessor;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-16 15:24
 * hbase中g_vertex，g_edge数据写入gdb
 ***/
public class GDBVertexImportJob {


    public static void main(String[] args) throws Exception {

        new FlinkJobBuilder()
                .withJobName("gdb-vertex-import")
                .withCheckpointSetting(new CheckpointSetting(2*60000, 30000, true))
                .isBatchJob(true)
                .withJobDesc("通过hbase历史数据导入顶点")
                .withMainProcessor(new GraphHbaseVertexToGDBProcessor())
                .start(args);
    }
}
