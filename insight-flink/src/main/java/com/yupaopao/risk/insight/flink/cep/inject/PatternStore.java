package com.yupaopao.risk.insight.flink.cep.inject;

import com.yupaopao.risk.insight.common.cep.beans.CepRule;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.RedisPoolCreator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisPool;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-21 11:34
 *
 ***/
public class PatternStore implements Serializable {

    private static final transient Logger LOG = LoggerFactory.getLogger(PatternStore.class);

    private Map<String, CepRule> currentRunningMap = null;

    private transient JedisPool jedisPool;

    private volatile AtomicInteger changedVersion = new AtomicInteger(0);

    private transient Timer timer = null;

    private String dataSource;

    public PatternStore(String dataSource) {
        this.dataSource = dataSource;
        createPool();
        initRules();
    }

    private void createPool() {
        if (jedisPool != null) {
            return;
        }
        RedisProperties redisProperties = RedisProperties.getProperties(PropertyType.REDIS);
        jedisPool = RedisPoolCreator.createJedisPool(redisProperties);
    }

    /***
     * 从db初始化规则
     */
    private void initRules() {
        List<CepRule> ruleList = getRulesFromDB();
        if (CollectionUtils.isNotEmpty(ruleList)) {
            currentRunningMap = ruleList.stream().collect(Collectors.toMap(CepRule::getRuleId, Function.identity()));
            increaseVersion();
        }
    }


    /****
     * 返回当前需要运行的规则
     * @return
     */
    public Map<String, CepRule> getRunningRules() {
        return currentRunningMap;
    }

    public Integer getChangedVersion() {
        return changedVersion.get();
    }

    private List<CepRule> getRulesFromDB() {
        createPool();
        return RuleGetter.getLatestRules(jedisPool, "patternStore", dataSource);
    }

    public void createTimer(long period) {
        if (timer == null) {
            LOG.info("create rule updater ");
            timer = new Timer("timer-rule-updater");
        }
        timer.schedule(getTimerTask(), 60*1000L, period);
    }

    private TimerTask getTimerTask() {
        TimerTask updateTask = new TimerTask() {
            @Override
            public void run() {
                try {
                    List<CepRule> dbRules = getRulesFromDB();
                    if (CollectionUtils.isEmpty(dbRules)) {
                        // delete all
                        currentRunningMap.clear();
                        increaseVersion();
                        return;
                    }
                    //检查是否有更新的规则
                    if (MapUtils.isEmpty(currentRunningMap)) {
                        currentRunningMap = dbRules.stream().collect(Collectors.toMap(CepRule::getRuleId, Function.identity()));
                        increaseVersion();
                        return;
                    }
                    Collection<CepRule> runningList = currentRunningMap.values();
                    boolean changed = false;
                    for (CepRule rule : dbRules) {
                        CepRule runningRule = currentRunningMap.get(rule.getRuleId());
                        if (runningRule == null) {
                            //新规则
                            changed = true;
                        } else if (runningList.contains(rule)) {
                            //有相同的规则
                        } else {
                            //无相同的规则,change
                            changed = true;
                        }
                        if (changed) {
                            break;
                        }
                    }

                    //检查被删除的：currentRunningMap 包含，新的db中没有的
                    List<String> dbRuleIdList = dbRules.stream().map(elem -> elem.getRuleId()).collect(Collectors.toList());
                    for (String key : currentRunningMap.keySet()) {
                        if (!dbRuleIdList.contains(key)) {
                            //deleted
                            changed = true;
                        }
                        if (changed) {
                            break;
                        }
                    }
                    currentRunningMap = dbRules.stream().collect(Collectors.toMap(CepRule::getRuleId, Function.identity()));
                    if (changed) {
                        increaseVersion();
                        LOG.info("pattern changed...");
                    }
                } catch (Exception e) {
                    LOG.info("run update check error: ", e);
                }
            }
        };
        return updateTask;
    }

    private void increaseVersion() {
        if (changedVersion == null) {
            changedVersion = new AtomicInteger(0);
        }
        int version = changedVersion.incrementAndGet();
        LOG.info("now changedVersion: {}", version);

    }

    public void close() {
        LOG.info("close pattern store...., timer exist: " + (timer != null));
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
        currentRunningMap.clear();
        if (jedisPool != null) {
            jedisPool.close();
        }

    }
}
