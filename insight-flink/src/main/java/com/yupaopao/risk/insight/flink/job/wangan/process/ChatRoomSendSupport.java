package com.yupaopao.risk.insight.flink.job.wangan.process;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.yupaopao.platform.common.utils.EnDecryptUtil;
import com.yupaopao.risk.insight.flink.job.wangan.constant.TopicConstant;
import com.yupaopao.risk.insight.flink.job.wangan.dto.RiskChatRoomInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.util.Collector;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ChatRoomSendSupport {

    private WangAnHbaseTableSupport wangAnHbaseTableSupport;

    private ChatRoomSendSupport(){}

    public ChatRoomSendSupport(WangAnHbaseTableSupport wangAnHbaseTableSupport){
        this.wangAnHbaseTableSupport = wangAnHbaseTableSupport;
    }

    private  String passWord = "ICp#ffufJ#lMOA2n22";

    public void send(String topic, JSONObject messsage, Collector<Map> collector) throws Exception{
        try {
            if (!TopicConstant.T_CHATROOM_BEHAVIOR_LOG.equals(topic)) {
                return;
            }
            RiskChatRoomInfo chatRoomInfo = getChatRoomInfo(putHbase(messsage, topic));
            //补全用户信息
            setUserInfo(chatRoomInfo);
            if (
                    StringUtils.isNotBlank(chatRoomInfo.getRoomId())
                            && StringUtils.isNotBlank(chatRoomInfo.getAccount())
                            && StringUtils.isNotBlank(chatRoomInfo.getActionType())
                            && StringUtils.isNotBlank(chatRoomInfo.getUid())
                            && StringUtils.isNotBlank(chatRoomInfo.getLoginTime())
                            && StringUtils.isNotBlank(chatRoomInfo.getLoginSource())
                            && StringUtils.isNotBlank(chatRoomInfo.getLoginIp())
                            && StringUtils.isNotBlank(chatRoomInfo.getTerminalOsType())
                            && StringUtils.isNotBlank(chatRoomInfo.getTerminalOsVersion())
                            && StringUtils.isNotBlank(chatRoomInfo.getTerminalType())
                            && StringUtils.isNotBlank(chatRoomInfo.getTerminalVersion())
            ){
                Map<String, String> map = new HashMap<>();
                map.put("module", "chat_room");
                map.put("newJob", "true");
                map.put("riskChatRoomInfo",EnDecryptUtil.encrypt(JSONObject.toJSONString(chatRoomInfo),passWord));
                log.debug("网安上报聊天室信息:{}", map);
                Cat.logMetricForCount("wangan."+this.getClass().getName());
                collector.collect(map);
            }
        } catch (Exception e) {
            log.error("上报聊天室信息错误:{},{}",topic, messsage, e);
        }
    }


    public JSONObject putHbase(JSONObject dataJSON, String topic){

      JSONObject jsonObject = WangAnHbaseSupport.covertData(dataJSON.getJSONArray("data"));
      String rowKey = jsonObject.getString("uid");
        if (!com.aliyun.odps.utils.StringUtils.isEmpty(rowKey)){
            wangAnHbaseTableSupport.putRow(rowKey, topic, jsonObject);
        }
        return jsonObject;
    }


    private void setUserInfo(RiskChatRoomInfo chatRoomInfo){

        Map<String, Object> accountInfo = null;
        try {
            accountInfo = wangAnHbaseTableSupport.getRow(TopicConstant.ACCOUNT,chatRoomInfo.getUid(),
                    Arrays.asList("uid", "mobile"));
        } catch (Exception e) {
            log.error("query hbase error:{},{}", chatRoomInfo, TopicConstant.ACCOUNT);
        }
        Map<String, Object> loginDevice = null;
        try {
            loginDevice = wangAnHbaseTableSupport.getRow( TopicConstant.T_LOGIN_DEVICE,chatRoomInfo.getUid(),
                    Arrays.asList("uid", "os", "version", "equipment"));
        } catch (Exception e) {
            log.error("query hbase error:{},{}", chatRoomInfo,  TopicConstant.T_LOGIN_DEVICE);
        }
        Map<String, Object> loginRecord = null;
        try {
            loginRecord = wangAnHbaseTableSupport.getRow( TopicConstant.LOGIN_RECODE,chatRoomInfo.getUid(),
                    Arrays.asList("ip"));
        } catch (Exception e) {
            log.error("query hbase error:{},{}", chatRoomInfo,  TopicConstant.LOGIN_RECODE);
        }

        if (!CollectionUtils.isEmpty(accountInfo) && accountInfo.containsKey("mobile")) {
            chatRoomInfo.setAccount(accountInfo.get("mobile")+"");
        }

        if (!CollectionUtils.isEmpty(loginDevice)) {
            chatRoomInfo.setTerminalVersion(loginDevice.containsKey("equipment")?loginDevice.get("equipment")+"":"");
            chatRoomInfo.setTerminalOsType(loginDevice.containsKey("os")?loginDevice.get("os")+"":"");
            chatRoomInfo.setTerminalOsVersion(loginDevice.containsKey("version")?loginDevice.get("version")+"":"");
        }
        if (!CollectionUtils.isEmpty(loginRecord) && loginRecord.containsKey("ip")) {
            chatRoomInfo.setLoginIp(loginRecord.get("ip")+"");
        }
        log.debug("chatroom info:{},{},{},{}", chatRoomInfo,accountInfo,loginDevice,loginRecord);
    }

    private RiskChatRoomInfo getChatRoomInfo(JSONObject messsage){
        RiskChatRoomInfo riskChatRoomInfo = new RiskChatRoomInfo();
        riskChatRoomInfo.setUid(messsage.getString("uid"));
        riskChatRoomInfo.setActionType(messsage.getString("type"));
        riskChatRoomInfo.setLoginTime(messsage.getString("create_time"));
        riskChatRoomInfo.setRoomId(messsage.getString("room_id"));
        riskChatRoomInfo.setTerminalType("0");
        riskChatRoomInfo.setLoginSource(messsage.getString("platform"));

        return riskChatRoomInfo;
    }

}
