package com.yupaopao.risk.insight.flink.job.processor;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.common.beans.hbase.HBaseCellValue;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import com.yupaopao.risk.insight.common.support.TagUtil;
import com.yupaopao.risk.insight.flink.bean.portrait.InputMess;
import com.yupaopao.risk.insight.flink.bean.portrait.OpenAiInputVO;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseFullTableInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseInputSplitReadThread;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseSplit;
import com.yupaopao.risk.insight.flink.connector.kafka.sink.serialization.KafkaObjSerializationSchema;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.filter.BinaryComparator;
import org.apache.hadoop.hbase.filter.SingleColumnValueFilter;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.function.Function;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * 异常或初始化数据
 *
 ***/

@Deprecated
@Slf4j
public class AlgorithmModelInitProcessor implements FlinkJobBuilder.MainProcessor {
    public static void main(String[] args) throws Exception{
        Map<String,String> param = new HashMap<>();
        param.put("sleepTime","15");
        param.put("readSleepTime","1");
        System.err.println(URLEncoder.encode(JSON.toJSONString(param),"UTF-8"));
    }

    private  static String yesterday = "2020-06-06";

    @Override
    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
        final int sleepTime;
        final int readSleepTime;
        if(argMap!=null && StringUtils.isNotBlank(argMap.get("sleepTime"))){
            sleepTime = Integer.parseInt(argMap.get("sleepTime"));
        }else{
            sleepTime = 20;
        }
        if(argMap!=null && StringUtils.isNotBlank(argMap.get("readSleepTime"))){
            readSleepTime = Integer.parseInt(argMap.get("readSleepTime"));
        }else{
            readSleepTime = 2;
        }

        //所有需要跑的算法模型
        String models = ApolloProperties.getConfigStr("application", "algorithm.models");
        if (StringUtils.isEmpty(models)) {
            return;
        }
        List<String> modelList = Arrays.asList(models.split(","));
        //每个模型选取不一样的字段
        Map<String, List<InputMess>> messageConfigMap = new HashMap<>();
        Map<String, List<String>> messageConfigAllKeysPerModel = new HashMap<>();
        //elem对应于每个模型，及kafka message中的eventType,配置的时候InputMess value设置为默认值
        modelList.forEach(elem -> {
            String dataConfig = ApolloProperties.getConfigStr("application", "algorithm." + elem + ".dataList");
            List<InputMess> inputMessConfigList = JSON.parseArray(dataConfig, InputMess.class);
            messageConfigMap.put(elem, inputMessConfigList);
            messageConfigAllKeysPerModel.put(elem,
                    inputMessConfigList.stream().map(inputMess -> inputMess.getKey()).collect(Collectors.toList()));
        });
        //所有需要字段一次性取出来
        List<String> allColumns = new ArrayList<>();
        messageConfigMap.values().forEach(elem -> allColumns.addAll(elem.stream().map(InputMess::getKey).collect(Collectors.toList())));
        //适应HBase读取类型
        List<String> formatColumns = allColumns.stream().map(elem -> elem + "#String").collect(Collectors.toList());
//        formatColumns.add("updateTime#String");

        TsTableInfo tsInTable = new TsTableInfo()
                .withTableName("risk_device_tag")
                .withBucketPerSplit(1)
                .withColumnNameType(String.join(",", formatColumns))
                .withTableType("SYSTEM");
        LinkedBlockingQueue<Result> cache = new LinkedBlockingQueue<>(1024 * 20);
        HBaseProperties hBaseProperties = HBaseProperties.getProperties(PropertyType.HBASE);


        KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA_BIGDATA);
        Properties kafkaProps = kafkaProperties.getProperties();
//        kafkaProps.put(FlinkConstants.KAFKA_GROUP_ID, FlinkConstants.KAFKA_GROUP_ID_RISK_INSIGHT_BIG_DATA_MODEL);
        kafkaProps.put("transaction.timeout.ms", 1000 * 60 * 5 + "");
        String aiModelTopic = FlinkConstants.KAFKA_TOPIC_BIG_DATA_AI;

        FlinkKafkaProducer<OpenAiInputVO> aiKafkaProducer = new FlinkKafkaProducer<OpenAiInputVO>(
                aiModelTopic,         // target topic
                new KafkaObjSerializationSchema(aiModelTopic),   // serialization schema
                kafkaProps,
                FlinkKafkaProducer.Semantic.EXACTLY_ONCE
        );


        env.createInput(new HBaseFullTableInputFormat(tsInTable, hBaseProperties, cache) {
            @Override
            public String parseHBaseResult(Result rowResult) {
                return TagUtil.transHBaseRowToJsonForAI(rowResult);
            }

            @Override
            public HBaseInputSplitReadThread initReadThread(HBaseSplit.BucketSplit bucket, HTable currentTable, String hbaseTableName, String threadName, TsTableInfo tsTableInfo, LinkedBlockingQueue<Result> queue) {
                HBaseInputSplitReadThread readThread = new HBaseInputSplitReadThread(bucket,
                        currentTable,
                        queue,
                        hbaseTableName,
                        tsTableInfo.getTableId(),
                        tsTableInfo.getTableType(),
                        tsTableInfo.getColumnNames(),
                        threadName
                ) {
                    @Override
                    public Scan initScan() {
                        Scan scan = super.initScan();

                        SingleColumnValueFilter filter = new SingleColumnValueFilter(HBaseConstants.HBASE_FAMILY_KEY,
                                org.apache.hadoop.hbase.util.Bytes.toBytes("updateTime"), CompareOperator.GREATER_OR_EQUAL,
                                new BinaryComparator(Bytes.toBytes(yesterday + " 01:00:00")));
                        scan.setFilter(filter);
                        return scan;
                    }

                    @Override
                    public void sleepPerRow() {
                        try {
                            Thread.sleep(readSleepTime);//每条记录sleep 15ms, 1s做多66条
                        } catch (InterruptedException e) {
                            log.warn("sleep per row error: ", e);
                        }
                    }
                };
                return readThread;
            }

            public void close() throws IOException {
                super.close();
                TagCacheSupport.cleanResource(this.getClass().getClassLoader());
            }
        }).name("read_hbase_risk_device_tag").setParallelism(1)
                .flatMap(new FlatMapFunction<String, OpenAiInputVO>() {
                    @Override
                    public void flatMap(String value, Collector<OpenAiInputVO> out) throws Exception {
                        if (HBaseUtil.emptyResultJson(value)) {
                            return;
                        }
                        List<HBaseCellValue> row = JSON.parseArray(value, HBaseCellValue.class);
                        Map<String, HBaseCellValue> rowMap = row.stream().collect(Collectors.toMap(HBaseCellValue::getColumnName,
                                Function.identity()));

                        //按模型转为需要的模型数据
                        for (Map.Entry<String, List<InputMess>> modelData : messageConfigMap.entrySet()) {
                            //读取hbase，前一天所有更新的数据
                            String modelName = modelData.getKey();
                            OpenAiInputVO aiModelMessage = new OpenAiInputVO();
                            aiModelMessage.setBizType(3000);
                            aiModelMessage.setEventType(modelName);
                            aiModelMessage.setTime(System.currentTimeMillis());
                            aiModelMessage.setObjectId(rowMap.get("rowKey").getColumnValue().toString()); //业务主键id

                            //all needKey
                            List<String> allNeededKey = messageConfigAllKeysPerModel.get(modelName);
                            //检查key是否昨天更新
                            if (!isDataUpdateYesterday(allNeededKey, row)) {
                                continue;
                            }
                            //设置模型参数
                            List<InputMess> currentMsgList = new ArrayList<>();
                            for (InputMess msg : modelData.getValue()) {
                                String featureName = msg.getKey();
                                if(!rowMap.containsKey(featureName)){
                                    //没有值用默认的
                                    continue;
                                }
                                Object msgValue = rowMap.get(featureName).getColumnValue();
                                msgValue = isMsgValueEmpty(msgValue) ? msg.getValue() : msgValue;
                                if (msgValue == null) {
                                    //默认值
                                    currentMsgList.add(new InputMess(msg.getKey(), msg.getValue(), msg.getType()));
                                } else {
                                    currentMsgList.add(new InputMess(msg.getKey(), msgValue, msg.getType()));
                                }
                                //检查所有信息是否昨天更新
                                aiModelMessage.setInputMess(currentMsgList); //消息列表
                            }
                            try {
                                //控制发送频率
                                Thread.sleep(sleepTime);
                            }catch (Exception e){
                                log.warn("sleep error: ",e);
                            }
                            Cat.logMetricForCount("risk.insight.to.ai.model."+aiModelMessage.getEventType());
                            log.info("send to al: {}",JSON.toJSONString(aiModelMessage));
                            out.collect(aiModelMessage);
                        }
                    }
                }).setParallelism(1)
                .addSink(aiKafkaProducer).name("write_to_kafka_bigdata").setParallelism(1);


    }

    private static boolean isDataUpdateYesterday(List<String> allNeededKey, List<HBaseCellValue> row) {
        return row.stream().filter(elem -> allNeededKey.contains(elem.getColumnName()) && checkDataUpdateTime(elem)).findFirst().isPresent();
    }

    private static boolean checkDataUpdateTime(HBaseCellValue cellValue) {
        Date updateTime = new Date(cellValue.getTimestamp());
        String formatUpdateTime = InsightDateUtils.getDateStr(updateTime,
                InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        return formatUpdateTime.equals(yesterday);

    }

    private static boolean isMsgValueEmpty(Object msgValue) {
        if (msgValue instanceof String) {
            return StringUtils.isEmpty((String) msgValue);
        }
        return msgValue == null;
    }



}
