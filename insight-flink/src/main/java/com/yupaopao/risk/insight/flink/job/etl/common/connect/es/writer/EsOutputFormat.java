/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yupaopao.risk.insight.flink.job.etl.common.connect.es.writer;

import com.alibaba.fastjson.JSONObject;
import com.dtstack.flinkx.exception.WriteRecordException;
import com.dtstack.flinkx.util.StringUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.es.core.EsUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.format.BaseRichOutputFormat;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * The OutputFormat class of ElasticSearch
 * <p>
 * Company: www.dtstack.com
 *
 * <AUTHOR>
 */
public class EsOutputFormat extends BaseRichOutputFormat {

    protected String address;

    protected String username;

    protected String password;

    protected List<String> idColumnNames;

    protected List<String> idColumnValues;

    protected List<String> idColumnTypes;

    protected String index;

    protected String type;

    protected List<String> columnTypes;

    protected List<String> columnNames;

    protected Map<String, Object> clientConfig;

    private transient RestHighLevelClient client;

    private transient BulkRequest bulkRequest;


    @Override public void configure(Configuration configuration) {
        client = EsUtil.getClient(address, username, password, clientConfig);
        bulkRequest = new BulkRequest();
    }

    @Override public void openInternal(int taskNumber, int numTasks) throws IOException {

    }

    @Override protected void writeSingleRecordInternal(Map<String, Object> record) throws WriteRecordException {
        String id = getId(record);
        IndexRequest request = StringUtils.isBlank(id) ? new IndexRequest(index, type) : new IndexRequest(index, type, id);
        request = request.source(JSONObject.toJSONString(record), XContentType.JSON);
        try {
            client.index(request);
        } catch (Exception ex) {
            throw new WriteRecordException(ex.getMessage(), ex);
        }
    }

    @Override protected void writeMultipleRecordsInternal() throws Exception {
        bulkRequest = new BulkRequest();
        for (Map<String, Object> record : rows) {
            String id = getId(record);
            IndexRequest request = StringUtils.isBlank(id) ? new IndexRequest(index, type) : new IndexRequest(index, type, id);
            request = request.source(JSONObject.toJSONString(record), XContentType.JSON);
            bulkRequest.add(request);
        }

        BulkResponse response = client.bulk(bulkRequest);
        if (response.hasFailures()) {
            LOG.error("Writing record failed: {}", response.buildFailureMessage());
        }
    }

//    private void processFailResponse(BulkResponse response) {
//        BulkItemResponse[] itemResponses = response.getItems();
//        WriteRecordException exception;
//        for (int i = 0; i < itemResponses.length; i++) {
//            if (itemResponses[i].isFailed()) {
//                if (dirtyDataManager != null) {
//                    exception = new WriteRecordException(itemResponses[i].getFailureMessage(), itemResponses[i].getFailure().getCause());
//                    dirtyDataManager.writeData(rows.get(i), exception);
//                }
//
//                if (numWriteCounter != null) {
//                    numWriteCounter.add(1);
//                }
//            }
//        }
//    }

    @Override public void closeInternal() throws IOException {
        if (client != null) {
            client.close();
        }
    }


    private String getId(Map<String, Object> record) throws WriteRecordException {
        if (idColumnNames == null || idColumnNames.size() == 0) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        int i = 0;
        try {
            for (; i < idColumnNames.size(); ++i) {
                String name = idColumnNames.get(i);
                String type = idColumnTypes.get(i);
                if (StringUtils.isBlank(name)) {
                    String value = idColumnValues.get(i);
                    sb.append(value);
                } else {
                    String[] split = name.split("\\.");
                    Map<String, Object> tmp = record;
                    for (int index = 0; index < split.length; index++) {
                        if (index == split.length - 1) {
                            sb.append(StringUtil.col2string(tmp.get(split[index]), type));
                        } else {
                            tmp = (Map<String, Object>) tmp.get(split[index]);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            String msg = getClass().getName() + " Writing record error: when converting field[" + i + "] in Row(" + record + ")";
            throw new WriteRecordException(msg, ex);
        }

        return sb.toString();
    }

}
