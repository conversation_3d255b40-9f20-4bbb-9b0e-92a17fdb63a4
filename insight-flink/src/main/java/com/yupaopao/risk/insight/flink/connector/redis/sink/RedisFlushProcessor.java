package com.yupaopao.risk.insight.flink.connector.redis.sink;

import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.flink.connector.scheduler.IntervalFlushSchedulerTools;
import com.yupaopao.risk.insight.flink.constants.FactorConstants;
import com.yupaopao.risk.insight.common.property.FactorTimeProperties;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.flink.windows.aggregate.AggregateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.flink.api.java.tuple.Tuple2;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc ClickHouseColumnSync
 * @date 2020/5/14
 */
@Slf4j
public class RedisFlushProcessor implements Runnable {

    private final ScheduledThreadPoolExecutor scheduler;
    private static Jedis jedis;
    private static JedisPool jedisPool;

    private ConcurrentHashMap<String, Tuple2<Double, Long>> dataMap = new ConcurrentHashMap<>(2048);

    public RedisFlushProcessor(RedisProperties redisProperties) {
        FactorTimeProperties.getInstance().initConfig();
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(redisProperties.getMaxIdle());
        genericObjectPoolConfig.setMaxTotal(redisProperties.getMaxActive());
        genericObjectPoolConfig.setMinIdle(redisProperties.getMinIdle());

        jedisPool = new JedisPool(genericObjectPoolConfig,
            redisProperties.getHost(),
            redisProperties.getPort(),
            FactorConstants.REDIS_DEFAULT_TIMEOUT,
            redisProperties.getPassword(),
            redisProperties.getDatabase());

        jedis = jedisPool.getResource();

        this.scheduler = IntervalFlushSchedulerTools.createScheduler("redis-flush",5000L, 5000L, this);
    }

    @Override public void run() {
        flush(dataMap);
    }

    public void addData(AggregateResult input) {
        if (input.isPurge()) {
            dataMap.remove(input.getKey());
            Long del = jedis.del(input.getKey());
            log.info("移除key: {}, 条数：{}", input.getKey(), del);
            return;
        }

        FactorTimeProperties instance = FactorTimeProperties.getInstance();
        String[] split = input.getKey().split("#");
        if (split.length > 1 && instance.getRealTimeList().size() > 0 && instance.getRealTimeList().contains(split[1])) {
            flush(input.getKey(), input.getResult(), input.getTimeSpan() * 60);
            return;
        }
        dataMap.put(input.getKey(), new Tuple2<>(input.getResult(), input.getTimeSpan() * 60));
    }

    private void flush(Map<String, Tuple2<Double, Long>> map) {
        if (map.size() == 0) {
            return;
        }
        jedis = jedisPool.getResource();
        for (String key : map.keySet()) {
            log.info("更新聚合数据：{} - {} - {}", key, map.get(key).f0, map.get(key).f1);
            Cat.logMetricForCount("factor.redis.count");
            jedis.setex(key, map.get(key).f1.intValue(), String.valueOf(map.get(key).f0));
        }
        map.clear();
        jedis.close();
    }

    private void flush(String key, Double value, Long span) {
        jedis = jedisPool.getResource();
        log.info("实时更新聚合数据：{} - {} - {}", key, value, span);
        Cat.logMetricForCount("factor.redis.count");
        jedis.setex(key, span.intValue(), String.valueOf(value));
        jedis.close();
    }

    public void close() {
        flush(dataMap);
        if(jedisPool!=null){
            jedisPool.close();
            jedisPool = null;
        }
        if(jedis!=null){
            jedis.close();
            jedis = null;
        }
        IntervalFlushSchedulerTools.closeScheduler(scheduler);
    }
}
