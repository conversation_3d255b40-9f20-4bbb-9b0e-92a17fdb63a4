package com.yupaopao.risk.insight.flink.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.flink.job.portrait.process.HttpClientService;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.nio.reactor.ConnectingIOReactor;
import org.apache.http.nio.reactor.IOReactorException;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.Future;

/****
 * zengxiangcai
 * 2021/1/9 5:47 下午
 ***/

@Slf4j
public class ShuMeiInvoker {

    private static String deviceRawDataRequestTemplate = "{\"data\":{\"encryptAlgorithm\":\"NONE\"," +
            "\"deviceId\":\"${deviceId}\"},\"accessKey\":\"${accessKey}\"}";

    public static MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");


    public static String queryDeviceRawData(String deviceId) {
        //invoke shumei
        String requestData = buildRawDataRequest(deviceId);
        String responseData = HttpClientService.doPostJsonWithMetricName(getRawDataUrl(), requestData, "insight" +
                ".rawData");
        if (StringUtils.isEmpty(responseData)) {
            return FastJsonUtils.EMPTY_JSON;
        }
        return parseRawDataResponse(responseData);
    }

    public static String parseRawDataResponse(String responseData) {
        JSONObject respObj = JSON.parseObject(responseData);
        if (!"1100".equals(respObj.getString("code"))) {
            return FastJsonUtils.EMPTY_JSON;
        }

        String rawData = FastJsonUtils.read(respObj, "$.detail.raw_data", String.class);
        if (StringUtils.isEmpty(rawData)) {
            return FastJsonUtils.EMPTY_JSON;
        }
        String result = new String(Base64.getDecoder().decode(rawData));
        if (StringUtils.isEmpty(result)) {
            return FastJsonUtils.EMPTY_JSON;
        }
        return result;
    }

    public static String buildRawDataRequest(String deviceId) {
        String accessKey = ApolloProperties.getConfigStr("application", "shumei.accessKey");
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("deviceId", deviceId);
        paramMap.put("accessKey", accessKey);
        String requestData = new StrSubstitutor(paramMap).replace(deviceRawDataRequestTemplate);
        return requestData;
    }

    public static String getRawDataUrl() {
        String url = ApolloProperties.getConfigStr("application", "shumei.device.rawdata.url");
        return url;
    }


    public static Future<HttpResponse> queryAsync(String deviceId, CloseableHttpAsyncClient httpAsyncClient) {
        String requestData = buildRawDataRequest(deviceId);
        return invokeAsync(httpAsyncClient, getRawDataUrl(), requestData);
    }

    public static String parseAsyncResponse(HttpResponse httpResponse) {
        String statusCode = String.valueOf(httpResponse.getStatusLine().getStatusCode());
        if (!statusCode.startsWith("2")) {
            return FastJsonUtils.EMPTY_JSON;
        }
        try {
            String resp = EntityUtils.toString(httpResponse.getEntity(), "UTF-8");
            return parseRawDataResponse(resp);
        } catch (IOException e) {
            log.warn("parse rawData response error", e);
        }

        return FastJsonUtils.EMPTY_JSON;
    }

    public static Future<HttpResponse> invokeAsync(CloseableHttpAsyncClient httpClient, String url,
                                                   String data) {
        Cat.logMetricForCount("shumei.invokeAsync." + url);
        try {
            data = StringUtils.trimToEmpty(data);
            final HttpPost post = new HttpPost(url);
            StringEntity strEntity = new StringEntity(data, ContentType.APPLICATION_JSON);
            post.setEntity(strEntity);
            Future<HttpResponse> responseFuture = httpClient.execute(post, null);
            return responseFuture;
        } catch (Exception e) {
            log.error("invoke shumei error: ", e);
        }
        return null;
    }

    public static CloseableHttpAsyncClient createHttpClient() {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(1000)//连接超时,连接建立时间,三次握手完成时间
                .setSocketTimeout(1000)//请求超时,数据传输过程中数据包之间间隔的最大时间
                .setConnectionRequestTimeout(5000)//使用连接池来管理连接,从连接池获取连接的超时时间
                .build();

        //配置io线程
        IOReactorConfig ioReactorConfig = IOReactorConfig.custom().
                setIoThreadCount(Runtime.getRuntime().availableProcessors() * 2)
                .setSoKeepAlive(true)
                .build();
        //设置连接池大小
        ConnectingIOReactor ioReactor = null;
        try {
            ioReactor = new DefaultConnectingIOReactor(ioReactorConfig);
        } catch (IOReactorException e) {
            log.error("create httpClient error");
        }
        PoolingNHttpClientConnectionManager connManager = new PoolingNHttpClientConnectionManager(ioReactor);
        connManager.setMaxTotal(50);//最大连接数设置1
        connManager.setDefaultMaxPerRoute(50);//per route最大连接数设置

        CloseableHttpAsyncClient client = HttpAsyncClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .build();
        client.start();
        return client;
    }

    public static void main(String[] args) throws Exception {
        List<String> deviceIdList = Arrays.asList("202103152310446848c0027b19c98ab79074ab83a7849201617d0125be4dbc7",
                "202101262004062a31f433faa0316b263437a9079b1d64014a5524a8e4bd83a",
                "2021031400111200b5314db0713508b7be59843feee183012b925e08edf9aa2",
                "20210314001117ed6d299a4141d63944510b46b480196e019f9af0bfeb43408",
                "20210314122952f28650716ecb29690cede39100a2b5a90171a7dd67e8bd4av",
                "20210327120616bd9d95b32129cd294089c32a515de6ca01c9d2eed7929cbea",
                "2021030302533981e7f480437cc5036f70a2605c913f8f793ee0290063393",
                "20210310215522925d3900e628775dcab134a6ba6cc9eb01917445244cd142k",
                "20210329225946613f00b267d583451e32be1883a6791801cce500847fdb2d7",
                "2021033115531037b19cdfeebb82c08e66f66d53a34c180163532f6f5355853",
                "202103131230513130af04435f9d9ab222fe8176051eef01c8118e4f7bac446",
                "20210313122913f3926f127a1f2fe5681398245507ed0b019a722d628fddd56",
                "20200913174850b87a86ce5414d88ed0023f391cae1780019b81a65083afac",
                "202103112005329fa17dc3c455657c364d7451e0dca44801fccaf878dd2f01c",
                "20210327182822925345206703b4882d199822237bade00140429fe63298b75",
                "202103292258534595d74327b729beb64ad400edaa724c01dc4df0a40bd16cf",
                "20210327194128ad19d49a69ae87b3a54f98cc7cffdd70012381f5d3fd512cf",
                "2021031312293741087fab04a087b650f584f329c31bf201f833d1b64663f52",
                "202105261351304928586017d5175d85722412706cb37201bd64cff58439b6",
                "202103140023578c60ffd597f6d75e97381e81760c319c016e11bf6aea918a8",
                "202103281716516051903b91b776acc99d7485c275227c01c3392414542476a",
                "201910301518502ea4b1bf3cf72aa4da6efbb8221bd57b01c4205f4c60a11d",
                "202103302342154aa91ea2ed1757d15934f58a47dcd27201ea6896d08e50032",
                "20210606190054d10d29ef993b2ef710caa774e6d9a76b0195a512d43d7272",
                "20210314122952f28650716ecb29690cede39100a2b5a90171a7dd67e8bd4ae",
                "20210330234214207bde58a89678f86ae71d81bed7b78501592d1df7a3285d7",
                "20210329225841af5ea12023d6cca0cd3d2bf5d13f6b3e01a827dc54e16447b",
                "202007061145048a80533e81eb34bd729feadb619388b501105191cc7d90ce",
                "20210313123002285334007b5f8a2a15a96c6542d8db8d01dcc63002c16c612",
                "20210331155344b74b5615eaab668eae6c2e74238bafe701b8bb75818505405",
                "20210327120619465d9cb94b164f45c811eda875f9028301e2563c6d5f49565",
                "20201118191930f7ae66aeace58890f26be8ed9206d05c01a1faf094e7f3ff",
                "20210314140611de9f64ef19c5b03ea480f8aa2f8097df01daa47f818fe7a8l",
                "20210531165603b3992677fd95caf4194993df3b2c17d3017f4a5b31958a66",
                "20210428133702e09f9fd9bf535f3c9ae7a67df24611c20112a0b3ccde0c8b",
                "20210315203335c188653d549f3afa0e86285d12ce27b401432bf4917abdc6",
                "2021033109013563243beb824775156f937e8e559e6c8501db6d1d24c9935ea",
                "202103271941516946d240c773172768fcd4d3992f2047015271a5dfe92227d",
                "2021031120051237034ef0effe1f9c2e013e9d582ead6c014ee26bb51ca654e",
                "20210331155344b74b5615eaab668eae6c2e74238bafe701b8bb7581850540f",
                "20210405145822095bf061fe3d93a90544111b6079cbdcf5725b7bae3879d98",
                "20210712101512c608effc9786d344493b202faa966c6f019f9ff89f698d30",
                "202103141245002f1ef8344884e1aabefb78127f1270cb01b02889330753782",
                "20210330201232773ea773f4e45fea8c06c957d33250540151f879520cf8b8a",
                "2021031020015690668193d871bcc929f4b7cc6727e73e01939d0810d42bfas",
                "202103311552571e7b2be791562ea9b4908f99c378458d014b4ef1a1f5754e6",
                "202103162111375ec6f773aeb910532bc2786611950c4d01ff2c0e868b4219h",
                "202103141233162a5b5a043a8696f6270b21a65a8e131b019137e5fada1f69s",
                "20210324175443243f07acfd037ab0c50bb6e844ec367c014c77b1563d8d34s",
                "20210331155238dcd28ca56bbe962b8b3440084bb2e32b01661447957721ca8",
                "202107041719422818ceaee914a9d2b47fac700eae4d35012f69646a62a32c",
                "20210307132410821c37f79a569a12fcc891ecacf875de0990f6a35710e140h",
                "2021031021523430ebb65bc39d9ce48b6acd6507c39d5a01ad6a04ca218b51g",
                "20210311200508a73d3580b197729874336ed4b4a8a2c30167b8909e33c5d8b",
                "2021031020303907260bdcd3166443cbefe1aaf850b83772678213ef65c7123",
                "2021011920251808e44c4202d7abd50d99113bf1e1eb33011cdfe978c3273ch",
                "2021032719413426d8cc5d70a1cabd86d9d415592aadd901009082b746603da",
                "20210126200423778ae2df25ea15d474dc9f342c244caa0181786e19e57cbaw",
                "2021031021564005719de8858dc37207812ddaab30a13b0107e075d4435f69c",
                "202103102152148e99a945fc07bb006ba8b91282da03e20142241684770ae1g",
                "202007211144394fba8e5a5b6558573a8251f137990101012a6c10d5126618",
                "20210312201239450687078a1cd070d18b703f34e92790012bde72d3dc0c67a",
                "202103301156389ebbd48a4317ff003f44c2b17a66c0ef014ba04f50c54b9de",
                "20210122202103d9d3772b495d6f20c7fba844accf116e01503b84b30c1bc3l",
                "20200713084825f89c6fe6c1d5a06e80ae76117b472cb5018e82de98b36a2b",
                "202101262002494687ef8a6a4f4a91b8908ab7caddbfb601950c7deaeba55bq",
                "202103102156306067b87059eca7ce4e3a8c06581e31110199713d84bfc0d6b",
                "20210314123431a73a97d9bdda0850a9ee3fec4783a917014f8f1133a9ba5fd",
                "2021032712061547c8b3ce0b70d364ce4f45c5e9ad5eb9018078e605f6cccf5",
                "2021012220223357e5f0e5e4fd33234a24c829d95655f001f51e4c672e41b1i",
                "20210702164003f61a6e7113d2ebb5208c5d0eab74dc3e01f21bbe8e7a09a4",
                "20210313122812c559fdf8c6b3332ba8617525604c758c01d5c27c7ad7c72c5",
                "20210313123134e085dcd15c74a6f6123097250209c558016edb96394d51d16",
                "20210428132027e6fe90cea11fa4a6ee9c08420bdc38060180dd3faf3a24b8",
                "202103122013077a46a997a26aa6fb5aa93aa6eff8621f01400015d20fe292e",
                "20210331155304b2f5bc5296936335bcbdcc2f3be7071f019b2fe796636804f",
                "20210314122900ece192246b9a03673eb38684ed09a02001d13ea5c0795bcc3",
                "20210328135121efe339a0f120f6531932b94317904033015f925e603ee6770",
                "2021031312304464e33a4207cc149e10d81d0a826aea8f0111b2d9269d8eab1",
                "20210315231100c2d34acad44b939d2b28b79750bb18dd0191219849ccb5954",
                "20210123232443f59898ebdfb12a9dd09de2b7f624c709010b5294e0d81c2ab",
                "2020082420051804b20f490a9209c9ef4ab24f641d2dec019fd0317ed4c3bb",
                "20210314123535619eb59a990953fb30d53f9beaf47c8a016dbd73fc625737u",
                "20210329152726ef76ff819bc42c0eafd078327eecb12601e3b50c6b045d549",
                "20210329185204b914b4c8e6e1cf17b929261192e1879b015033081be80132b",
                "2021032816140297a019c194c4a6bed8fd87fd5a938a670150f5e0547b33593",
                "202107011811394bb85ae41296502f9b24ce935724ebc10159f0f197c49841",
                "20210314123216f2c31b0dfe905a8eb8ce0b56dc17491201ad2201cdd7c93ef",
                "20210122201613d59529ebbbbbf084076f6b6102b9d54c011c0f356102fbb2",
                "20210313123618b6f80d9bf34afc7c498eeb42d20c27ad017c7d32b450fad1",
                "202101262002550efe67e6919c87b1d4b12782de051ef10126f60c7c10f0a5n",
                "20210405134858c67c6f280bdf944ee00502a37f1588de5f4ded674c31d6356",
                "2021031400122601c3f9f8f852111a00dc8ddced3f508a01777a68510c6aa25",
                "20210331155331f033d9ade23f36a64f019e17a23c8f7101c0cf3aca2d6510a",
                "2021070921553930dc3d7bea132765c77b81cd4daa1a8b0101d0739f7499f2",
                "20210314123243d1f0fb589727cf5ef73a3d45a006cadd0117a6b951631db3i",
                "2021031412272358f748f0aa15004f1879374a3b3985270199c76192ee180e1",
                "20210314123148eb80827a7b69c6cf39a93af44bde5c2e01d4b08115ab02bcd",
                "2021012220212174c798f741a04be5e424458f52a75572018f3b08287aeb0bo",
                "202103152310313fa5e942b6d85329b77d7015d8505c0401538b4a09aa4d816",
                "202107132126175ac5dd7689ffa7e878f8f0b5b953588201c43f6e20389907",
                "202103301500514cbed283ccbe605b651494f13e49c1b501095e2bafa37ca43",
                "20210105231616490f193fcdc5e8763ab8379bfbb63c0d01620f3a53a82822",
                "202103131231093fb8f1c920d67398021ab26d92467d2601dffff65e9d084e5",
                "20210713200107ff93edeb872abee474186bb16fdda3a101715440210f83b3",
                "20210331155341b84605d1f36fed24724b741032c79dda01c1a77dbf9f6afc",
                "202101192027432a2291df0401bce09029de7dc22ab4d801c9cd057d0bbc29f",
                "2021040617075115c2a38da8540c75b8f93f3aa749d7c02422c9eb7a911bb46",
                "20210314002217f9c11012e154e58d77cc945690a9c5f701b5d8dd850871163",
                "20210406170130d3a8c4368446f0b2ae8ea4befef22389bbe84893007013084",
                "20210711234522ea03bfb53dc2288e75083eecf555764301e859864f5ce751",
                "20210327120616bd9d95b32129cd294089c32a515de6ca01c9d2eed7929cbea",
                "202101191559443834af5753372901cc189168f2bc814b0187e1ee3acf77ebg",
                "20210327182237cfaf95a40f6f53d793288ba1b4c9d9050160e03e28dab4422",
                "20210314123538bc0e7f6dfd3951e63f5a47a8b14eca87013e312bc1640fd5f",
                "20210122202256de1dfdfbd72cc1e59bb111f79f9bfc1c016268cf831a9a92d",
                "2021031723093670e2f39160782d06b8fd55aba128e8c601547145b591bb27m",
                "20210713114822e4378f8335c6539b257528392a89d50701cf5f5d95385934",
                "202103292258374ebee9f9ebe43692998d0306653ebe2301d816503d2f8f35d",
                "20210314002333b77ef5a9be2740e2b901b248ec61a68901816bd531ef31257",
                "20210314123148194aa85c381aae60b76fc1253d4851eb01e7c9f68dd43acea",
                "2021031400114494e94abb33c7f670b9f351afdd73aec401d73024d10a16022",
                "20210327120612a4ba5c2994d632c43e2ba1e4fe39facd01d2f62767605f413",
                "20210314122944907d459ee12546c6a45a320efd0fd5e201bd44d125c94c354",
                "2021012220211855cb7f739c2d63572fe85e32f35aca07011d96b8cd243b96a",
                "20210329152802001e54cb88ac72250bc9340636ebe51501b86538fbaaf0a36",
                "202103102153202463198f2ff9383146fd65daca50259f01250576eaa22b0ck",
                "20210311202723344785403764c5e9c2df062083c72f52013fc85b67e4760dd",
                "20210514113026a6c91fb3455112e76a8756a8d6c8650201f501836214988d",
                "2021031412473782c07d656c635d264d1154e2fc968568014a7aedd0ae7f9f1",
                "2021031412445424f5064787fa8bc6610770356831c362012082563fb545355",
                "202103140022480970409327fe87e8bf1c14f27019d6c1016bed5bea1f15b06",
                "20210306205437d60cae900753e778d465349eefd6a26c01879415bbb8e1bea",
                "20210328161404fb0dde461a633aa97585923b1f6c1e7d01c735f90f35b6b85",
                "20210329152804ce4b1a8aa1c22b3dded98bc92a23470a01433e5248ab54307",
                "20210102213513edcaa7dfa116ba7385477fd183bd182001670c66badcdbe2",
                "202103311553287ea71d21e51c0f0455c274c599fb0a20017a5f0b22b45507c",
                "20210314123456ad79d451d98caafb83c72854bd80fe5101ea10625a964b874",
                "2021031412295258be3612421c4443af0831c793533fb701a00a5b06393759v",
                "202103240055360408bec38ec13f325634292081048aaa0125d085bf0a34dc6",
                "2021031021403829695db5ab431318f9c67232dd5110b00173da84cee208f3e",
                "2021070217045824ea0bd213de2bc3faeb208f6cf4d88301b25a50adacb4f1",
                "20210327120612ae28f39afb6612da8b2c83c38949b56b0113026723268edf0",
                "202103140010362f20205392a9bbbe43d43e201b891b61011324af503248be6",
                "20210329185220023f26e9ff3cf30b42c6b9e77b42ea6001f401ebbc0b88fdb",
                "20210328171754d6a56b122fa71c30fc3a1323f00d922701936d2ec611578ca",
                "202103271829181dbd6dda40f1c9a87ed0621d2e714f8801b2f37d7e9520cd5",
                "20210314002334814a2a817d57da643b1d478dd38f46f701defcde76a77ff42",
                "20210323204052e29252973c1e705d44b3279aedce8df301fcb668f7176b363",
                "202104132200007ac5428a2dec63d739752f73569fcfb201c23428ba3a197f",
                "20210309191535af694bf7cc978baadc4dbabeeefc194cb306bc7cfe2c7b2",
                "202107132150352e01d9271c1b61f3f9796df53d1a88d601ecbfc8b685553d",
                "202103152311163eab2a7ea700ce809c9b003a928d2a70019900a0fd01f2224",
                "202107091704411df2852ece49677260b19f56e760ad88013bae0330abeb20",
                "202103271206121aaaac5dea2dcae92b99a5e28422977d01815eb5bf1a5be6f",
                "2021031412301974d28c20c389f5aed3d51d67f2858c9601099d92d90cc3d7m",
                "20210314123535742c0cbedf4c8f75fbcb123106d3517a01fb6c8e0c0a08c1v",
                "202103162110463db2705e7fcd94313ce35440c0b0582f012ef35e922066c6s",
                "202103311552571e7b2be791562ea9b4908f99c378458d014b4ef1a1f5754e6",
                "20210313123250d82819e73913541a5590fb3ef545b7e001eb8ab906fa9f8b3",
                "20210126200321361da76c3e3c4be32854f085de2e9796012340a04cf768fca",
                "20210327182820d33159e87ab741ca51dea6bc3eb702f701ce913a26f7d7dac",
                "202103311553168b7e22984b9361ca6138939e82a5be3b0125768b2add39492",
                "20210310215404362cd9f7654f45b1c8d8ab84c4aeca4c011a3c14074f7e2be",
                "20210314135641e97160dff7f3330e963962a5e76d0d080186cbbaa526731dg",
                "202103271634522537851ae303f3f5eb3ede33543b48f801b3224c33d8a8768",
                "20210327120617470c0f15e1e84382a7433b9e730f505501c9b5fc43540d283",
                "202103140034028efb334202040a59604b76e3893c07f50162f9dac485e41e0",
                "20210310215604dd438b6db9a0eb368e4f802897a521ff01b767d6bd745d8bh",
                "20210314002258c52edb6d859263450477af5b43e6017001d89fdf0388de6c2",
                "2021031021404867fbb5e72517275814f24eaf0d143a7901ad06fbf336bef3g",
                "20210509134404c305fb82e66c4077c160ac0cd9bc8bc5011347c375cb5743",
                "20210314122733691dd5dedea1e9e64a64ddbd0f8310f401e3f31d6e81c2884",
                "20210327120616bd9d95b32129cd294089c32a515de6ca01c9d2eed7929cbea",
                "20210314123240b5ef4cf33b7fb8489bf8ea6f33ff4643012d97eb0da17acfh",
                "202101262003286595582df59495ff5e49b0980a14b737010b91d9e2d86330x",
                "2021030620534910e8f00423ef14a8e2f0d6ece89cc9d801ed1ea6935f175di",
                "202103141235121ca1ef4d3018a186776225b6faf6d3df01782b78d4cea4e0n",
                "202103141245438471765c744738f92d8b43be71411db1019a4b57b3f605b35",
                "2021031412454382f50b8a569b9dd1405521a4f2af854001a0e2db7b510fef7",
                "20210122202258fe39eaa5a75faaca4633dc6454ce674a019feb43bac53ccbf",
                "202103140022584e3d258d9b3cee26119b6d7d9503b9cf0121a9669f79499a4",
                "20210415223604db8535d6560fb81f21baf907bb6fe28801f2fcb640954717",
                "2021042020394780a124c347e19e5145cc4480a917774d01b0807e1d1a3c80",
                "20210324010047897e99ba10cd153e4a5def1f8ada3633010c6e7b4bbcade74",
                "20210310215320928c145e82f9a1fa68e5721c15d8909c010578624983f081k",
                "20210706191541939fbf838a486cea3c4dedb495ff21d4016f80cd6d920c8d",
                "202101222023005f1aa5cd30bf6131e1ca88bfae87e7870197f5f99eb1fb40s",
                "20210314002110803339ec0d834957f48b3a2210979d2e019302e2f4db44304",
                "20210327120612a4ba5c2994d632c43e2ba1e4fe39facd01d2f62767605f412",
                "20200320012341cbcbff6c34c1a253c9326923880948e2012533dc35f48448",
                "2021033115542111fbc089d22b0167333db1c235e3838b015db521a639836",
                "20210327120612a4ba5c2994d632c43e2ba1e4fe39facd01d2f62767605f413",
                "2021030620555021d8266d6228db747fd838a817e7e23c01f535eaa76e0951h",
                "20210314123822d166b34bb5642272c81871bcc857ad690153c122122c5a64d",
                "2021033115532656c6209dcc0b50ed536dffa132ef33920147a97aadb156739",
                "20210406161853f6efb00f2a68ae2a21971c886fdf26a6411b4ca1c2bdeaaf7",
                "202103232042010665de57c5df2c516bef330a8eb4655a016477b580ed6c1c7",
                "20210321141620fb9fc8afa78bd452d5bfec6c061d17b001c85319c6a5cd376",
                "20210327183021f89d61370b1628cc364d11deb2485ea2013b9738a15373ec8",
                "20210707152817f1837dadb445cd4cbb83358bc49ca2b0010afc1b5ac507aaa",
                "202107041719422818ceaee914a9d2b47fac700eae4d35012f69646a62a32c",
                "20210314124545e26ea5f2e823bd2cf21ac5c406e980d301c59e3a9db298fb2",
                "20210321190609ffff966baba24fc45d520fa9e8526ea6011053a0e00e27a0e",
                "20210626183629de9ca6985608b283b4ddfd0d443fe1b401511d392a2eb095",
                "202103112005125399ad5747c405be2e4a48e6810513e4010f60ed24b83910a",
                "20210329185216d1365dae6bc0cb0afc863ee26571b9f401aa378cf861d9ebf",
                "2021032918522068e626ce75b0238b2da41904bef3ca0f01107befe09778ad9",
                "20210314122952df45488340ba949d7389aad61639a21401c08e71e020f4d8h",
                "2021032718195786ebe04eafa598f8b6a1e344ce5b182a019f277f8b10cc65a",
                "202103140022163f42e6832316dcf67023fd43ec54bfeb01244545e26791c35",
                "20210409200814dcdb3c9005a8a62aae3387472a9dd57e01d2e4b5bb0e4bea4",
                "202103131230513130af04435f9d9ab222fe8176051eef01c8118e4f7bac444",
                "20210327191359548ff6180e31c9df0310b95daff8b75a0158e709dcca6c029",
                "202103102153213578ef0070370478fe169c2f12d198fb01c6474775271b85k",
                "20210328191222ef703d41b5d18923080e3536cc89f295011aabda1ce9d78f7",
                "202103140010502052232cb0cf59d3f750e94e15031095011f8f77464d7b915",
                "2020110315251090f764b37aeee50afccd13fea37c616c01bfb12a85b0d42e",
                "20210314124527f6535218a1c44ae10697d50cbe52ea7e012c7a0fd0c63d434",
                "20210314123829a009cfe948c5792eb20270a1276d1d6a0134d0acce1186799",
                "20210329152804c68d88d2aca32cdf31c6943cb7bef0150155551034420a993",
                "20210409181559f57b89f17efccd43c051684177dbbdf9011e64016800c4ae4",
                "20210327182822925345206703b4882d199822237bade00140429fe63298b75",
                "202103232041486dfcaa7d1042dd149be3b8b7baeae6be01ea9ff9d408af814",
                "202103141245002f1ef8344884e1aabefb78127f1270cb01b02889330753781",
                "20210307193040331ebb74a5cdfa9e2e49b54e1a3a2a12017f5466433062a1d",
                "20210327182933d72139d441bf1174363f022ac08ea94b012ab3ecaea2fe619",
                "20210331155343f171d348ef99099604a07aecf570c8eb010fc0663a14b9307",
                "202103271941375a2eae785e18ab7b5adb26114e51104401adfd8003539fa0a",
                "20210310200340e19ca46fb7a4edc566adb2f72b4b719a012c894b6e309d4fd",
                "2021031412290022c91c56ac82546454771a3906f8833a01ec25701b52abad1",
                "20210313123231fbf4079f76e0c45dd82ec984e42ec9be01561d1ea3c2252c6",
                "20210329225905f2ce134b4519dc618f8724c08020777d011e585311aa9853h",
                "20210314122149b0c2e79fe54312f3b9e7f5526be2b6de0167817237359903d",
                "201907110120495b69948ccd86c5004677efd1cb0f6c2b010de8cf583a967c",
                "20210314122815bf89a3bf0208a01bf97d845125fcfd0401129d8f283797754",
                "20210313123544f3fd532e2959c261bc97f1541623cffd0118f23ac9e900124",
                "202103301500391ce2bc7ae90ec9e2db46ac8ddaef42190117f5316515eec73",
                "202103141231341865ec84e6c61f62aad6041ccd707e6e01ca49e8d6d8e1e8e",
                "202105082146134ad3a3b59103908b500ac0de2c8c0a7601d73300f9c341ff",
                "2021031219083218fef285701bd4433d0efb17e09d093301a8a3b41e68d5bbe",
                "2021033115532656c6209dcc0b50ed536dffa132ef33920147a97aadb15673d",
                "202107132153199b43cacee0da14fbc7276da9fb5016a901f3743c32486b35",
                "202104061614316df0f54438c1009231fa2f6447320e41217154484173709c4",
                "20210310215511d1cefd2f7ebcbf5683cc0b1c8f963090011df95379c58c2ei",
                "202101222022359547a6b9ca5d2a93f822787a1caca67401d496dc7981ab59k",
                "20210521162135b9bd9066dccf2e7948c6a46f6a9f3cad014e3b5845466dbe",
                "202103062054437e3f9d1428f79b93fd0d57379a811efd01a7a0134159f450n",
                "202103102152332114b13cfce2a0645498deb442f7c23e019a33be143df131g",
                "20210324213146ed3e0b1f336b3ec08ce70fe2f834012d01155e59874ee7d49",
                "20210119202548fa2cd8aa83be9a693c88154079cf0d5b015e182ec81c3651d",
                "20210331155341b84605d1f36fed24724b741032c79dda01c1a77dbf9f6afc5",
                "2021031019563210826430df7a11dab1c7328dc8066b1201f6eb8cf71c2441a",
                "202106090409457db07c12b1f9acbbb18bda226d90ea1101236b178fe2f0d3a",
                "20210327120617470c0f15e1e84382a7433b9e730f505501c9b5fc43540d28d",
                "20210312201757b448d24f7ddd9342a6c5d45236d335280139a5d94bf3f299d",
                "20210313123051186e40eace68a495a9f6805ebde2756b016ab4df4cd67dce6",
                "202103141234571bc79cf6b88dbd6b6841446b742d332e012a463d40786cb7i",
                "2021032718301996e65dc3173784a306f16695c04bba5a0151ca95bb1955387",
                "20210702192020bb12c4378cfa6d9461a6c3d2490d74860129a203d2f92458g",
                "202104011203473c95811daa0c2638db942a580834ed0501502ca8927ca321",
                "20210328171707835e4cf56260d40762fa56bf1d27122e0136629e0f4ab2d57",
                "20210329185216d1365dae6bc0cb0afc863ee26571b9f401aa378cf861d9ebf",
                "20210328191040f8debb7cc7d88d6409daaf7df91384370126de63722ab4177",
                "2021012620035758a1414e5fda5073c217b23da5bf9b8201819c4ef93d702do",
                "20210331221821f0b54ac26be20c6bffe14b232f54d2420124a10b9854449b",
                "20210330150034ed424360651cba7e77453fb0608e814201a824f45db4d291c",
                "202103242131008d3c309db4916570773c77b909694e4601b9cbb8a9782f7a3",
                "202103141234571bc79cf6b88dbd6b6841446b742d332e012a463d40786cb7j",
                "2021030620534146e4c1a440b80d334be2ff126705d437013cff873e2b1f23q",
                "20210122202255b08648dca35c6c4cd50378b37170552201f1a2a12638e5dco",
                "20210120165629ab9e406c9b5f6964e7bc2e939c5b13bf012712c64ad7097eb",
                "202101262002494687ef8a6a4f4a91b8908ab7caddbfb601950c7deaeba55bi",
                "202103281717408a33c08c6233000342c91c2895e29be201f9ad3871d1c21dc",
                "20210310215305abb60c6ed65a3b194edb84c40614b75e0136fb7c0b468554j",
                "20210314123036ca145fdb6ea0c7e24af4324a816931a4012d111145961d0ec",
                "20210327120615d29f0030044b6b80cc0d5236505cfa5e018779d1bf3ffeaa9",
                "2021031020121412d305844255feaef023a2857c7f51a101b908545071a47bd",
                "20210329185211ba2e8afd519b56f943b49a08fb4fe6b90112e39255b3a328f",
                "20210329225912524306c6d89025860389d8b88b1fc14501519cd203542c04c",
                "202103271819409b160d13c927bfd7783341827bb52889018cb34ab2e6ff0cf",
                "20210310215404362cd9f7654f45b1c8d8ab84c4aeca4c011a3c14074f7e2bk",
                "20210330150107acabc325be2735deab3a72be4cb44b99016409c43e9a6cd0d",
                "202104040828183a424ba639e338b17235da252130687d01e32560d69b5a47",
                "202103102152148e99a945fc07bb006ba8b91282da03e20142241684770ae1f",
                "20210313123032e10534d403d86edd6ec91eb0fe5f83f9012052b3c6c9da0b8",
                "202103131235266f73432cc3b465422d19b99f6229220901d0f24f4071fe3f9",
                "202103131235447e3192ac22796c9d5a61fc66a25055f701dd218d26e055df6",
                "20210324213146753fa108f26c6cf21459aca6e96eb75301f165486b2a07376",
                "202103152245309515f817cd0f30922dc67ea0aa065c0501788c9f2bda1e4ea",
                "2021032922591345f812c704d0e5a5cfb69cdbf3db2c2d018a9bbd9b475eac9",
                "20210310215250590b2c5313d02f673c519c62d549b3790134973d4a193683g",
                "20210323235604ff56c091bc604e8512b170d0b6469cbe017a083bfc133ab68",
                "20210327120617e90877f0268e240b91fdda9695936ad701d766a72b7817fb2",
                "202104010023118d06194f3dbb960667c305febe30835a01078b8e6eb7471d",
                "20210329152749eca940d0ca06f46ab3f5d5668e43b82f010d25f24c155b40d",
                "2021031400105053a8d482a470e558531ba2d5fbd84aec01e9c0184cc060022",
                "20210111004034c746d119e8c417b279ef7d1eff14650d019b4ddb8c89cfcff",
                "202103131235266f73432cc3b465422d19b99f6229220901d0f24f4071fe3f4",
                "202104081600216522b9d365897c623ae23662c59ab25c018977b67bbb710be",
                "2021031312293896e18fe051c76ceae5552fff905c7b4d01c3f945c1a4a1532",
                "20190521232257731cbf622d7c4f6536a3de438289bfeb01eb9df8fcd056e6",
                "202009262121275aaaf520796c13ad6ca5a813a99499140139ea8b4239c858",
                "2021012220223357e5f0e5e4fd33234a24c829d95655f001f51e4c672e41b1i",
                "202103131231093fb8f1c920d67398021ab26d92467d2601dffff65e9d084e5",
                "202103301500137c87f48f4189810b2bcc6385d6172e7901c05c437e95f46c6",
                "20210401215320c737e21eeec52a5954a5b84bd5d0a1a2015e0f1eadd5e71b",
                "202103301156408ae03dda3a04c956fddd8368b327278d01176776f1934dc6f",
                "20210314002559dc1eba08360a3908284142909a0ae3a601d56ba3b0a99918d",
                "202103140024467b1521256cc7d83c40cfa0ca0da551d101c1f76591703961c",
                "2021031523103851cebff619569e4be51a57042441e5740117e3e0586b90946",
                "2021031312293785e38c6439349694a7fa41b82e4088a201a7dfe3fd3427c67",
                "202103291852026b189105cd7873d8ab2dcc4e90c722320144ebb592a9f0ccf",
                "20210310201411d177e1c23acdfe1b78ac8e43207c371401d97868ba4391eaa",
                "2021031312305174e2f86ceab7721b4678af75f3009f850152da5364861f0a3",
                "20210331155259426d91c689a62c89d29d3cc80443054901b89b29505487e80",
                "20210122202253ab263f7832725864bd1d132a5938822e01ef762434fdfe8dg",
                "202101262003454fe5c554880418958acaec152e31717901d8d01cb9c7e2ceu",
                "20210310215321731cc78ccaa5f9021a3f2fa80f5f8054015c8524655e186ev",
                "2021060917051918e010baa90b4f0cdf8c585f5d425fa40181188225f78870",
                "20210329225851a999e2e328ae00dfe665f5fb78b18a7b01d66c518d44895cb",
                "2021040918163395f0698b693e774063a74cf5e19427150109ea8694d87d6c5",
                "20210713084731d67691671b59574af4f5e8cceb1d1a8c01101f54dcddc432",
                "2021031220175817794deb27a9b17ed153cb74082450260152a1d160ec91dbd",
                "202103131233340e8736140ebc09657eb0550697ddff2601d86db34d1fdc687",
                "2021031220175853441becbc64b8ad24323ddb4bdd420a010808032ce81a8fd",
                "2021031312293785e38c6439349694a7fa41b82e4088a201a7dfe3fd3427c63",
                "20210325144859e5b8c14bebbb92748190e355bf7330ab015c5b6df04ca6672",
                "20210619112457efcf56aed13bb0341701d5cc7cbcc1d701a724383272850a2",
                "20210625161547c0978677c3e6a748123c3eb5a13827a501549dab561ade42f",
                "202103131230230e093bcb578c06df0acaae7a0d8d7a8301672ac82dfb07516",
                "20210312191514dad8779869ea42879a605e4a8228a488019a9a4047eafae0a",
                "202101262002550efe67e6919c87b1d4b12782de051ef10126f60c7c10f0a5l",
                "20210330145950850c7c8974de40c4c100308b5f30084101a0564d50e3895d7",
                "202107021539108639594cda7720b35f86564178e437690160b1886398268a",
                "2021031523103851cebff619569e4be51a57042441e5740117e3e0586b90941",
                "202107131957107158b9fd94fd17746e7a97fadcf6a4490145993b0eeef3b1",
                "20210331155344b74b5615eaab668eae6c2e74238bafe701b8bb75818505404",
                "2021013101371672c8bf2ac19d7fc2b0e9d054c4c008cc01b41139ef0304210",
                "20210713100058b1f59ff9a70157121181875cb910c1ab017ae8b4c64bf542",
                "202103232041486dfcaa7d1042dd149be3b8b7baeae6be01ea9ff9d408af816",
                "20210310215320928c145e82f9a1fa68e5721c15d8909c010578624983f081v",
                "20210713212303e1ee7e4bc4dc1b49d18b45d2916a1aa2016754c9c6bd5bcc",
                "20210327182806c34140d6084a08de2d23d79c294e6c2001e75f07e8ccbd1ea",
                "20210314123432b8180313e1b91de6ff58b19687560e1b01b583d7c773a90e4",
                "20210314122710d912c59184a6b3b1cad9181e0bc3b9720192769f5ea423b10",
                "20210123232433c51754c8906d7cb333e818407e56d73901aac62bfd5ddf9eb",
                "2021031021544443a7215517c089ff53167cf689b8fbd4010d5ed4faae7be3m",
                "20210314124636564f08f75c2e1038121ccfe689431cb201a45131d527cf336",
                "20210314122952f28650716ecb29690cede39100a2b5a90171a7dd67e8bd4af",
                "202103252025565e40fa17e908f2272683bed753522e3501c2d97811474f6c9",
                "202103291528031ea47963e1c38c4911e28041f367480901a4c935e1d753e33",
                "20210329154311c42aa035893b0883caeda3cfd2ca410100c0bd2076c2f7f6",
                "20210310215251591700fd5372c1a395f7023f49502425018a5d79e88ae196g",
                "20210414210556257b2b69664b4e7e74c87bf0088757880140daae061fd213e",
                "20210703133557f32ab0a23ed03845128a73faac110c7201b0e992c04c70a4f",
                "20210313123031ccbb4ed7d19e7df59caab8112cb6145e01754aee84a98b510",
                "20210622211813f7232b79886b61daeb6af4af050ddd570147adb49eafd47c",
                "20210314004229ef8419e213ce59f0a4ea09c389bcd258015d69ae7b363e9a",
                "2021033012443884f63027108689c1df7d90ba23e421b001c1e676dc121842c",
                "2020120612122861c9e5af687e1509b9bf0b09c8aa009a01d3404d5380460f",
                "202103141233046eb2f9b0d8ab33f3ea81f8956a7380aa01c1274b800b8c4ck",
                "20210331155259426d91c689a62c89d29d3cc80443054901b89b29505487e80",
                "202103141234571bc79cf6b88dbd6b6841446b742d332e012a463d40786cb7i",
                "20210314124518c22aa8f77a91529d16f42dcf3b1bfa2301795012312a8a3e2",
                "2021031400114494e94abb33c7f670b9f351afdd73aec401d73024d10a16028",
                "202104051351565cc8617434c14e241892b86c400de95350c0a7873d39092d4",
                "202103141246456f7eb331a538e151e98e4772bca52f7801fcfd2c6fa811e95",
                "202103241754374c973c87392c7e86c3e1095cbbfb1ac001000c74cb59183cp",
                "2021031412371555260fd98518fe1009ec7a5358038fe901b2db9fe5af9bac2",
                "20210314001102fbe16caf6b4f4c6db6dc76d5b001378c019d99ab6841e79f7",
                "20210313123228e5ce8be5eb0a5599b91b7a20773cab060119d805d72854d05",
                "20210310215239b5eda6650af7a9a489481930ddab6539013a0ce357e06a11k",
                "20210327120612ae28f39afb6612da8b2c83c38949b56b0113026723268edf0",
                "20210330201221677495302fa22ff14e0529ab497d396f01ac84fd3943afc0",
                "20210310215406c8c8e1a82129414570093ef0be421dc3017f1e4e16ee9658i",
                "20210428132849d7126449a71adfd18b30c72dcf09d88e0122c22790c54841",
                "20210329225851a999e2e328ae00dfe665f5fb78b18a7b01d66c518d44895cb",
                "2021013102553879fde1bbfeaa5172b990870bd396275b017b0d680522ea9c9",
                "20210323204036900cc9ce01190a99dc59c663f6812e8301ed6da13929bbd16",
                "20210328171730d227064ac5a73a4b193f318c3e0fa6bd0188bf437fa37f7ed",
                "202103141246459ac78b5a6fa644fea2e63f5ad620eb4101b48d1c8179da776",
                "20210301213424a09e2b3f37816cc59c4b3bd360a60fa10150580d9ae6fb88",
                "202103131230513130af04435f9d9ab222fe8176051eef01c8118e4f7bac446",
                "202103112001047c24be5a7a7088a6a734184dc0394cd301bffc1f6fe47b4bk",
                "20210331155315c5d15e1971abd527a317a2d81129de2c01a7177e33051b4a6",
                "202103281717103dd67894ba6ee317f9d023bb271b2e3b0121c1c9654a1405a",
                "20210329225817c8ab4a96ba2f311dcd249a9a729c32c0015e766d738b2f5ec",
                "20210511144645ae1eadd6404ee898eea2534630e4233c015e814d468cce89",
                "202106191501445f82870836f9635c9dd1d6f0076a709a01c124a49f79c813",
                "20210313123448b4d609524d810411b1ad4452752629be01354fe0595f54b07",
                "20210310200424132ae082db8240914c190878d089d9710165267bacd2926dr",
                "202107120605327b3203717d731f337f54cfca98d498e401d93f372bf7227b",
                "20210713215907aab52943a2d395259c1b87bf8e86dbe90146166b8a2b47a5",
                "202103131140082c36fc8e877ec56e04f1fd76ed7b25f401d535256796e15da",
                "20210321190611eac233bbbd1c323412d6763d8b503899010f6ef0dedcbebbe",
                "20210406165935feb7d77b7e975b8aaf98756de0ba8ed737e0e46d18482d1e2",
                "20210327120617470c0f15e1e84382a7433b9e730f505501c9b5fc43540d282",
                "20210313123449c467e1ce985db691257fb4d623e1a8b801f25ac2faa8ed995",
                "20210510152834fb19b71b6ad5206f2c42eb0e31156b260139113f6b0bd3e1",
                "202101192035188a6905fc81e7c58aecef8911b0408a770143de63d5a3507ca",
                "20210330145953f7d8d1cb1af4ecd61c926472bd6777c60188b5450ece40423",
                "2021012323245029c888ecec6ba25ac50770920198db0c010c388a4dd91d1db",
                "20210314123715abdd3e633161f7625ad97af98ede4cca011a33df831c1a4b4",
                "202103102153038a392840c710457020466b9dc815534e01c167ebb9a70a2ag",
                "202103121526406587c54cd82b33c194b23bd874d48a72019aefbbfdec7498",
                "20210313123140dcf609d1830836d0cbdf64f5a9c58ce101a9d946e37d176f5",
                "202103140023586a396b7298ef60a03bf1473c170ffa7601a2310458b3b6c02",
                "2021071311104070985672b52087dca9518efa3b1750c601c3c52c1102b524",
                "202104141745570b252b6a1fee90ae6bc584542e45219a014fdacfdd2b0b93",
                "2021031021531446d040b4b876b8dcb2e9a464abadb0ee012ab2b4fa51ba67v",
                "20210329225828022319941380fae25be9c6939a5a3b35015882cb8ef0c231c",
                "2021031312334944d719c9dfbe72d0d3a9cc647fb3c98301b0920ba20c24b12",
                "20210509134803e46b8365173e82fef7296f4de4985ad801aa0b31e9366f84",
                "202103311552264cead29fcd73a37a0eeeedbff6ce6abc013881bc7aa1c171b",
                "20210122202054993a3c3465162bcad7e1b69af87c5ab701ecb645b1239fb4s",
                "20210122201611b2ff8ae5ac77a915e2f947035b0058e101dcd44a96e54336z",
                "20210409152304dcabe89e0654a2b5751b644cefa06d77011f3023db97bb624",
                "202103141227303a981484e58c57ca5efdc6c0387242b301f71a369c0ef40f4",
                "20210313122825b92f745e32b34055db244eb583cd48ec0184cc4f1e8b44613",
                "20210331155253957d40f8ff41a9476012e36ad535c8e001c30a1980efc69cf",
                "20210330150042d992329b654c6f522a53d377dfa8d44b015103955cdb59279",
                "202101222021138852e82fd21d0872a2ad0e3d234b726c016f6784b380a9f5f",
                "2021013101371672c8bf2ac19d7fc2b0e9d054c4c008cc01b41139ef0304219",
                "20210310214105a727abdba721ff21bb67783709d4225c012bf4efe0c6d1c0k",
                "202103140013426be49359cfc11a87a59768d9a06a86b3014e66f97c26f4174",
                "20210122141501a287f24cc29405e1201a28dc9a1217c10182a79907441249b",
                "20210122202322ccc51b24bb1338febafce50b46d510d9019b87fc064577faf",
                "20210314123035e85cb648eb96f1f4a2cdf26effd1d009018189832b36bd58f",
                "20210316211543f4489f4598975e0cd04961d01519764d01cc7e67c9b61858a",
                "2021031412453399c6164748f4f667c5b9eab14d9c319201ec30fa8dd58fcb7",
                "2021033115525904b40e6476805ff0ea7d124a3cf8a196015a4922d02fb5603",
                "20210324175443243f07acfd037ab0c50bb6e844ec367c014c77b1563d8d34o",
                "2021012620025878b411a2993302ac7ce1dd6001b1671701e0f236a21656ead",
                "2021012220222829ad800d9f703de08311a60cbdfd467e01af3d0eb2229d51k",
                "2021031312293896e18fe051c76ceae5552fff905c7b4d01c3f945c1a4a1531",
                "202104061620081f7a99691ff39755bb56205c8e57e61dab7cfe90a7043e003",
                "20210122202104afa1051c6f52f11db8dcb62f2a35024c0187480af2646b35m",
                "2021031400111138c7246a448109635add2e8244c5a64f01c48f2faa618a3c6",
                "202103311553287ea71d21e51c0f0455c274c599fb0a20017a5f0b22b455078",
                "20210313123349f1218244a788a999f2cfa8ba303cc8f101cc1ed5c2ee05353",
                "20210313122825b92f745e32b34055db244eb583cd48ec0184cc4f1e8b44613",
                "2021032915274374dba64d1edb345c3896735bb78c9126012dd0c22a2814274",
                "20210329185204fc816c0c06fc1c1523ea75bf90c998aa0137d46c50981323c",
                "202103311553050d1b4bc0c57b654a57a73c9b513215d001ed9ce6d89b94525",
                "20210331110522c3bdd27af45fe15eded6ebdbc760868801aa3ffce37c996d",
                "202003251414568e06386d216cb01439792a23bb01db77015206b2abe8bbb6",
                "202103131230236ba4907dba81443c0f6dcb076c24f1b301ef618f1dadee4b5",
                "20210314002541e722a892968e4862427a32d80d30828101f04c4e8763fbe1b",
                "2021013101371672c8bf2ac19d7fc2b0e9d054c4c008cc01b41139ef0304219",
                "2021040918163395f0698b693e774063a74cf5e19427150109ea8694d87d6c3",
                "2021012220211855cb7f739c2d63572fe85e32f35aca07011d96b8cd243b96z",
                "20210310215222a9b748d8147e3dfd4ab9dc8dd8499b4a01389d6bae4cc54ca",
                "20210313123550103f179bca65ac22af10e188dab3fac40115529c7de2a07e5",
                "2021032712061547c8b3ce0b70d364ce4f45c5e9ad5eb9018078e605f6cccf7",
                "2021033115540399ab3808edba77e1006215dad0d6c61f0129f1651b99c897",
                "2021031400111997bc6d31084534a7eda8bf784254584e01ff054088d9f9cf5",
                "201903041734233e1b71d7447e1458beddbacf71b00d0b013f339f22cc789d",
                "20210313122809bf137bbad6ce355454d0a7bf15365fca01fd899db2ba96c31",
                "202101232324363b0ca70109f794928233eb87d1ed079401f54998de7bcca8o",
                "20210122201650e583f04d2b1b701465970639e1d66313014b14a528002222l",
                "202101191622461102972808270c06ee94e91a31f42e50012a0ab1aace3f28c",
                "2021031412272413b8d8c7ac8bb06e5b12d3b94aa83e9a016e8d7960b971e26",
                "202101221822546e2eb7ab02c0185a9e73eefd3139fc0501710747cc5d7500o",
                "20201123164409bed3eb464c4f9a50e528ce21fd47de4f015eb33ac5d2846c",
                "2021032718195786ebe04eafa598f8b6a1e344ce5b182a019f277f8b10cc65d",
                "20210330234157b1e7346d3bb34d83c2d308b8e99926a501e6ea6d07b03d483",
                "2021031021535854385b07cbf5d82d89809eef068446bd01bb936c4a260db6i",
                "2021031312305174e2f86ceab7721b4678af75f3009f850152da5364861f0a6",
                "2021031412342386479ae3588b013b5f4fd725ebeca55801595cecec92d6033",
                "20210301213424a09e2b3f37816cc59c4b3bd360a60fa10150580d9ae6fb88",
                "20210330201232773ea773f4e45fea8c06c957d33250540151f879520cf8b8a",
                "202106090409457db07c12b1f9acbbb18bda226d90ea1101236b178fe2f0d3e",
                "2021030620572367c61d23641b816b4b27c8897410e8c7010ad534015a6980k",
                "20210122202258fe39eaa5a75faaca4633dc6454ce674a019feb43bac53ccbf",
                "202101222021138852e82fd21d0872a2ad0e3d234b726c016f6784b380a9f5d",
                "20210313123228e5ce8be5eb0a5599b91b7a20773cab060119d805d72854d04",
                "20210329225819020e901e97537690a77b4c20df57c70f018ba1deadb567ced",
                "20210330234217ec52d46ced22360135f8a561b5f855280132b578e52648583",
                "2021070117165286a0b43056e4c1fce61f3bbc312bb30001da3335ad46654ea",
                "2021032712061381e11cfbf54908000cd2157ef75fbe0701a80e2bd976e34c8",
                "2021032719413426d8cc5d70a1cabd86d9d415592aadd901009082b746603da",
                "2021012620035914d10f871b2a841bbf5533630f5d4d390134bfbf9c808a88u",
                "20210313123354733000c9863f1699dd1c7900525d0eb401df70f4f0ea4dce5",
                "202103302012323deb9cf25e0e11859cf72d5462018fc80159ef5b9926981dd",
                "202105091354331db5328e4034a518d1b948fc2e77a0a20199fe838a1c22e6",
                "20210310215423b472777dd6a3a4bc369d31386fb8b220018b07b3a598130fj",
                "20210314140613ac9dbe0046688ea0fe9ab0dcb506d9a60103c5282e0037e4j",
                "20210330234214207bde58a89678f86ae71d81bed7b78501592d1df7a3285d7",
                "20210331110522c3bdd27af45fe15eded6ebdbc760868801aa3ffce37c996d",
                "2021040616140749c1fc014f5e180dd9aa1ba37c203620b897a2b98957151b2",
                "20210313123218a49b3daad02f05b7f753824fe5bb2c190142a794967bcf8b6",
                "20210621133252eae050ac952a9d768c80c7209fe6b3fd0113b76f6e56a7da4",
                "202103141202408df1769c08fb92b62d000cd2769103560175d3fc595e54b7e",
                "202103102153202463198f2ff9383146fd65daca50259f01250576eaa22b0cv",
                "2021050420580379d28610e353308c70c73a54a4cecf3c01b12cfdcbc05276",
                "202101262003043cde0e568d39a28b64b8ba19ecc3e1f1017d1a8038fde643v",
                "20210126200324cfb38c2b4909d602c6778c7ea3fe786c01f8ca60ac549cd9t",
                "20210313123425cc8e15ee7f739436d33fac3ed88a6245013f7462862a32ff6",
                "20210310215405e4c589876e0bcb618df782d60018399d015c6634e841ae43i");
//        deviceIdList = Arrays.asList("20211021153611f72b48c4acea2f8ecd28b05887d70100007de93c77535d81");

//        int totalEmpty = 0;
//        for (int i = 0; i < deviceIdList.size(); i++) {
//            String deviceId = deviceIdList.get(i);
//            String response = queryDeviceRawData(deviceId);
//            if ("{}".equals(response)) {
//                totalEmpty++;
//            } else {
//                System.err.println("deviceId:" + deviceId + " ,,, " + response);
//
//            }
//        }
//        System.err.println(totalEmpty);
//
//        System.err.println(URLDecoder.decode("Xiaomi MI+8+Lite", "UTF-8"));
//        System.err.println(InetAddress.getLoopbackAddress().getHostAddress());

        System.err.println(new Date());
        Future<HttpResponse> f = queryAsync("2021040422364168c2204420d03ccb90e6f9c4d478b723016e70bd9f3f2da9",
                createHttpClient());
        System.err.println(parseAsyncResponse(f.get()));;
        System.err.println(new Date());

    }
}
