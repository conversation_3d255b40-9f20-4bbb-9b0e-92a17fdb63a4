package com.yupaopao.risk.insight.flink.bean.portrait;

import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;
import java.util.TreeSet;

@Data
public class PortraitBean implements Serializable {
    private static final long serialVersionUID = -6370570279168524394L;
    private Long uid;
    private Long mobile;
    private String nikeName;
    private String birthday;
    private String createTime;
    private String gender;
    private String profession;
    private String freezeState;
    private String showNo;
    private String wechatId;
    private String qqId;

    private String iTokenIdFirstActiveTimestamp;
    private String ismIdFirstActiveTimestamp;
    private Integer iTokenIdActiveDays7d;
    private Integer ismIdActiveDays7d;
    private Integer iTokenIdActiveDays4w;
    private Integer ismIdActiveDays4w;

    private Integer iTokenIdLoginCnt1d;
    private Integer iTokenIdLoginCnt7d;
    private Integer ismIdRegisterCnt1d;
    private Integer ismIdRegisterCnt7d;
    private Integer ismIdLoginCnt1d;
    private Integer ismIdLoginCnt7d;


    private Integer ismIdRelateTokenIdCnt1d;
    private Integer ismIdRelateTokenIdCnt7d;
    private Integer ismIdRelateIpCityCnt1d;
    private Integer ismIdRelateIpCityCnt7d;
    private Integer iTokenIdRelateSmIdCnt1d;
    private Integer iTokenIdRelateSmIdCnt7d;

    private Integer iTokenIdRelateIpCityCnt1d;
    private Integer iTokenIdRelateIpCityCnt7d;
    private Boolean bTokenIdCommonSmId4w = Boolean.FALSE;
    private Boolean bTokenIdCommonIpCity4w = Boolean.FALSE;
    private String currentCity;
    private String currentIsmId;
    private Integer deviceTotal;
    private Integer relateCityTotal;
    private Long timestamp;
    private Double score;

    private String tab;
    private JSONArray sTokenIdRelateIpCityInfoMap4w;
    private JSONArray sTokenIdRelateSmIdInfoMap4w;

    private TreeSet<PortraitFirstTag> riskTags = new TreeSet<>();
}
