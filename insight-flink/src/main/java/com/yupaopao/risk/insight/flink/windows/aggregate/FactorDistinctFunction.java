package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.yupaopao.risk.insight.common.property.FactorTimeProperties;
import com.yupaopao.risk.insight.flink.utils.FactorUtil;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.AggregateFunction;

import java.util.HashSet;
import java.util.Set;

@Slf4j
public class FactorDistinctFunction implements AggregateFunction<FactorCalDetail, AggregateResult, AggregateResult> {

    @Override public AggregateResult createAccumulator() {
        FactorTimeProperties.getInstance().initConfig();
        AggregateResult result = new AggregateResult();
        result.setDistinct(new HashSet<>());
        result.setResult(-1.0);
        return result;
    }

    @Override public AggregateResult add(FactorCalDetail value, AggregateResult accumulator) {
        accumulator.setKey(value.getGroupKey());
        accumulator.setTimeSpan(FactorUtil.getRemainTime(value));
        accumulator.setPurge(value.isPurge());

        Set<String> distinct = accumulator.getDistinct();
        if (distinct.size() > 35) {
            return accumulator;
        }
        distinct.add(value.getData());
        return accumulator;
    }

    @Override public AggregateResult getResult(AggregateResult accumulator) {
        return accumulator;
    }

    @Override public AggregateResult merge(AggregateResult a, AggregateResult b) {
        if (a.getKey().equals(b.getKey()) && a.getResult().equals(b.getResult())) {
            a.setCount(a.getCount() + b.getCount());
        }
        return a;
    }
}
