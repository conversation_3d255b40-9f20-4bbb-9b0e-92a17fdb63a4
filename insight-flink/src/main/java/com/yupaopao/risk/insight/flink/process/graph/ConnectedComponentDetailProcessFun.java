package com.yupaopao.risk.insight.flink.process.graph;

import com.alibaba.fastjson.JSON;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.bean.graph.CCAnalysisParams;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.job.processor.graph.service.ConnectedComponentDetailService;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.*;

import java.util.Arrays;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-23 10:44
 * 连通部件明细处理
 ***/

@Slf4j
public class ConnectedComponentDetailProcessFun extends ProcessFunction<String, String> {

    private transient HTable currentTable;
    private transient Connection hbaseConn;
    private transient ClickHouseConnection ckConn;

    private CCAnalysisParams analysisParams;
    private String runDay;

    public ConnectedComponentDetailProcessFun(CCAnalysisParams analysisParams, String runDay) {
        this.analysisParams = analysisParams;
        this.runDay = runDay;

    }


    public void open(Configuration parameters) throws Exception {
        HBaseProperties hBaseProperties = HBaseProperties.getProperties(PropertyType.HBASE);
        ClickHouseProperties clickHouseProperties = ClickHouseUtil.getLongTimeoutProperties();
        hbaseConn = HBaseUtil.createConnection(hBaseProperties);
        currentTable = HBaseUtil.getTable("risk_device_tag", hbaseConn);
        ckConn = ClickHouseUtil.createConnection(clickHouseProperties);
    }

    /***
     *
     * @param value 连通部件标签
     * @param ctx
     * @param out
     * @throws Exception
     */
    @Override
    public void processElement(String value, Context ctx, Collector<String> out) throws Exception {
        String label = JSON.parseObject(value).getString("connectedLabel");
        log.info("start cc: {}", label);
        Long startTime = System.currentTimeMillis();
        String connectedLabel = label;


        //查询一个label下的所有数据
        ConnectedComponentDetailService service = new ConnectedComponentDetailService(analysisParams, runDay);
        if(!service.isFinishData(ckConn,connectedLabel, Arrays.asList("userTopEvent","deviceTopEvent"))){
            service.updateSingleCCTopData(ckConn, connectedLabel);
        }

//        if(!service.isFinishData(ckConn,connectedLabel, Arrays.asList("userLatestEvent","deviceLatestEvent"))){
//            service.getUserLatestEventMetrics(ckConn, connectedLabel).stream().forEach(elem -> out.collect(elem));
//            service.getDeviceLatestEventMetrics(ckConn, currentTable, connectedLabel).stream().forEach(elem -> out.collect(elem));
//        }


        log.info("finished cc: {} process ... cost: {} ms", connectedLabel, (System.currentTimeMillis() - startTime));
    }

    @Override
    public void close() throws Exception {
        if (currentTable != null) {
            currentTable.close();
        }
        if (hbaseConn != null) {
            hbaseConn.close();
        }
        if (ckConn != null) {
            ckConn.close();
        }
    }


}
