package com.yupaopao.risk.insight.flink.job.processor.graph.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.lindorm.client.core.utils.Bytes;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.clickhouse.jdbc.ClickHouseStatement;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.graph.CCAnalysisParams;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Row;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-23 10:39
 * 连通部件各顶点详情处理
 ***/

@Slf4j
public class ConnectedComponentDetailService {

    private CCAnalysisParams analysisParams;
    private String runDay;

    public ConnectedComponentDetailService(CCAnalysisParams params, String runDay) {
        this.analysisParams = params;
        this.runDay = runDay;

    }

//    public void updateTopEvents(ClickHouseConnection conn) {
//        //step one : 连通图节点大于50的社区
//        String topCCSqlTemplate = "\n" +
//                "select connectedLabel,count() ccCount from cc_detection_result where runDay = '%s' group by " +
//                "connectedLabel\n" +
//                "having ccCount> %d\n" +
//                "order by ccCount desc;";
//        String topCCSql = String.format(topCCSqlTemplate, runDay, analysisParams.getCcVertexCountLimit());
//
//        log.info("step one: fetch top connected component: {}", topCCSql);
//
//        List<Map<String, String>> largeCCList = ClickHouseUtil.executeQuery(conn, topCCSql);
//
//        if (CollectionUtils.isEmpty(largeCCList)) {
//            return;
//        }
//
//
//        //step two: 计算连通社区活跃节点top10活跃事件
//        //step three: 统计每个用户top10拒绝事件
//        log.info("step two: start to fetch top events");
//
//        Map<String, Object> sqlParams = new HashMap<>();
//        sqlParams.put("maxAnalysisSizePerCC", analysisParams.getMaxAnalysisSizePerCC());
//        sqlParams.put("maxEventSizePerUser", analysisParams.getMaxEventSizePerUser());
//        sqlParams.put("runDay", runDay);
//        fetchEachConnectedComponentData(conn, largeCCList, sqlParams);
//    }


    public List<String> getUserLatestEventMetrics(ClickHouseConnection conn, String connectedLabel) {
        Map<String, Object> sqlParams = new HashMap<>();
        sqlParams.put("maxAnalysisSizePerCC", analysisParams.getMaxAnalysisSizePerCC());
        sqlParams.put("latestEventPerUser", analysisParams.getMaxEventSizePerUser());
        sqlParams.put("runDay", runDay);
        sqlParams.put("connectedLabel", connectedLabel);

        StrSubstitutor sub = new StrSubstitutor(sqlParams);
        List<Map<String, String>> userEvents = ClickHouseUtil.executeQuery(conn,
                sub.replace(userLatestEventSqlTemplate));
        if (CollectionUtils.isEmpty(userEvents)) {
            return new ArrayList<>();
        }
        log.info("user eventCount {} for cc： {}", userEvents.size(), connectedLabel);
        //每个用户多条记录分组
        Map<String, List<Tuple2<String, String>>> userDetailMap = userEvents.stream().map(elem -> {
            String userId = elem.get("userId");
            //allMsg
            String allMsg = elem.get("allMsg");

            String[] columns = allMsg.split("#######");
            Map<String, String> rowMap = new HashMap<>();
            rowMap.put("level", columns[0]);
            rowMap.put("createdAt", columns[1]);
            if ("empty".equals(columns[2])) {
                rowMap.put("id", "");
            } else {
                rowMap.put("id", columns[2]);
            }
            rowMap.put("eventCode", columns[3]);
            if (columns.length == 4) {
                rowMap.put("content", "");
            } else {
                rowMap.put("content", columns[4]);
            }

            return new Tuple2<String, String>(userId, JSON.toJSONString(rowMap));
        }).collect(Collectors.groupingBy(ele -> ele.getField(0), toList()));

        //转为显示的明细指标
        List<String> outputList = new ArrayList<>();
        for (List<Tuple2<String, String>> list : userDetailMap.values()) {
            String userId = list.get(0).getField(0);
            List<String> jsonList =
                    list.stream().map(elem -> (String) elem.getField(1)).collect(toList());
            Map<String, String> rowMap = new HashMap<>();
            rowMap.put("metricValue", JSON.toJSONString(jsonList));
            rowMap.put("id", userId);
            rowMap.put("metricType", "userLatestEvent");
            rowMap.put("runDay", runDay);
            outputList.add(JSON.toJSONString(rowMap));
        }

        log.info("finish user event for cc： {}", connectedLabel);
        return outputList;

    }

    public List<String> getDeviceLatestEventMetrics(ClickHouseConnection conn, HTable currentTable, String connectedLabel) {
        Map<String, Object> sqlParams = new HashMap<>();
        sqlParams.put("maxAnalysisSizePerCC", analysisParams.getMaxAnalysisSizePerCC());
        sqlParams.put("latestEventPerUser", analysisParams.getLatestEventPerUser());
        sqlParams.put("runDay", runDay);
        sqlParams.put("connectedLabel", connectedLabel);
        StrSubstitutor sub = new StrSubstitutor(sqlParams);
        List<Map<String, String>> deviceEvents = ClickHouseUtil.executeQuery(conn,
                sub.replace(deviceLatestEventSqlTemplate));
        if (CollectionUtils.isEmpty(deviceEvents)) {
            return new ArrayList<>();
        }
        log.info("device eventCount {} for cc： {}", deviceEvents.size(), connectedLabel);
        //写入detail表
        Map<String, List<Tuple2<String, String>>> deviceDetailMap =
                deviceEvents.stream().map(elem -> {
                    String userId = elem.get("deviceId");
                    //allMsg
                    String allMsg = elem.get("allMsg");
                    String[] columns = allMsg.split("#######");
                    Map<String, String> rowMap = new HashMap<>();
                    rowMap.put("level", columns[0]);
                    rowMap.put("createdAt", columns[1]);
                    if ("empty".equals(columns[2])) {
                        rowMap.put("id", "");
                    } else {
                        rowMap.put("id", columns[2]);
                    }
                    rowMap.put("eventCode", columns[3]);
                    if (columns.length == 4) {
                        rowMap.put("content", "");
                    } else {
                        rowMap.put("content", columns[4]);
                    }
                    return new Tuple2<String, String>(userId, JSON.toJSONString(rowMap));
                }).collect(Collectors.groupingBy(ele -> ele.getField(0), toList()));

        List<String> outputList = new ArrayList<>();
        for (List<Tuple2<String, String>> list : deviceDetailMap.values()) {
            String deviceId = list.get(0).getField(0);
            List<String> jsonList =
                    list.stream().map(elem -> (String) elem.getField(1)).collect(toList());
            Map<String, String> rowMap = new HashMap<>();
            rowMap.put("metricValue", JSON.toJSONString(jsonList));
            rowMap.put("id", deviceId);
            rowMap.put("metricType", "deviceLatestEvent");
            rowMap.put("runDay", runDay);
            outputList.add(JSON.toJSONString(rowMap));
        }
        log.info("finish device event for cc： {}", connectedLabel);
        outputList.addAll(getDeviceTags(currentTable, runDay, deviceDetailMap));
        return outputList;
    }

    private List<String> getDeviceTags(HTable currentTable, String runDay, Map<String, List<Tuple2<String, String>>> deviceDetailMap) {
        List<String> outputList = new ArrayList<>();
        try {
            //处理设备标签，hbase scan
            List<Row> batch = new ArrayList<Row>();
            for (String key : deviceDetailMap.keySet()) {
                Get get = new Get(Bytes.toBytes(key));
                Arrays.stream(deviceTag).forEach(elem -> {
                    get.addColumn(HBaseConstants.HBASE_FAMILY_KEY, Bytes.toBytes(elem));
                });
                batch.add(get);
            }
            Object[] results = new Object[batch.size()];
            currentTable.batch(batch, results);
            for (Object o : results) {
                //output tag
                Result result = (Result) o;
                if (o == null || result.getRow() == null) {
                    continue;
                }
                //读取所有为true的标签
                Map<String, Object> tagData = new HashMap<>();
                Arrays.stream(deviceTag).forEach(elem -> {
                    byte[] byteValue = result.getValue(HBaseConstants.HBASE_FAMILY_KEY,
                            Bytes.toBytes(elem));
                    if (byteValue == null) {
                        return;
                    }
                    if (Bytes.toBoolean(byteValue)) {
                        tagData.put(elem, true);
                    }
                });
                Map<String, String> rowMap = new HashMap<>();
                rowMap.put("metricValue", JSON.toJSONString(tagData));
                rowMap.put("id", Bytes.toString(result.getRow()));
                rowMap.put("metricType", "deviceTag");
                rowMap.put("runDay", runDay);
                outputList.add(JSON.toJSONString(rowMap));
            }
        } catch (Exception e) {
            log.error("get deviceTag error: ", e);
        }
        return outputList;
    }

//    /***
//     * sql方式写入数据
//     * @param largeCCList
//     * @param sqlParams
//     */
//    private void fetchEachConnectedComponentData(ClickHouseConnection conn, List<Map<String, String>> largeCCList,
//                                                 Map<String, Object> sqlParams) {
//        try (ClickHouseStatement stmt = conn.createStatement();) {
//            for (Map<String, String> ccElement : largeCCList) {
//                Long start = System.currentTimeMillis();
//                String connectedLabel = ccElement.get("connectedLabel");
//                sqlParams.put("connectedLabel", connectedLabel);
//                StrSubstitutor sub = new StrSubstitutor(sqlParams);
//                sqlTemplates.forEach(elem -> {
//                    String sql = sub.replace(elem);
//                    try {
//                        stmt.executeUpdate(sql);
//                    } catch (SQLException e) {
//                        log.error("execute sql error, sql=" + sql, e);
//                    }
//                });
//                log.info("process cc: {} for  recent 7days top events, cost={} ms", connectedLabel,
//                        (System.currentTimeMillis() - start));
//            }
//        } catch (Exception e) {
//            log.error("fetch cc data with sql error: ", e);
//        }
//
//    }

    public void updateSingleCCTopData(ClickHouseConnection ckConn, String connectedLabel) {
        Long start = System.currentTimeMillis();
        Map<String, Object> sqlParams = new HashMap<>();
        sqlParams.put("maxAnalysisSizePerCC", analysisParams.getMaxAnalysisSizePerCC());
        sqlParams.put("maxEventSizePerUser", analysisParams.getMaxEventSizePerUser());
        sqlParams.put("runDay", runDay);
        sqlParams.put("connectedLabel", connectedLabel);
        Date before7Days = DateUtils.addDays(InsightDateUtils.getDateFromString(runDay,
                InsightDateUtils.DATE_FORMAT_yyyy_MM_dd), -7);
        String startDate = InsightDateUtils.getDateStr(before7Days, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
        String endDay = runDay + " 00:00:00";
        sqlParams.put("startDay", startDate);
        sqlParams.put("endDay", endDay);


        try (ClickHouseStatement stmt = ckConn.createStatement();) {
            //cc topN信息
            List<String> ids = getTopNIds(new StrSubstitutor(sqlParams).replace(ccTopNIdsSql), stmt);
            List<String> userIds = ids.stream().filter(elem -> elem.length() != 62).collect(toList());
            List<String> deviceIds = ids.stream().filter(elem -> elem.length() == 62).collect(toList());
            if (CollectionUtils.isNotEmpty(userIds)) {
                String inUserIds = ClickHouseUtil.sqlInParamsJoin(userIds);
                sqlParams.put("inUserIds", inUserIds);
                StrSubstitutor sub = new StrSubstitutor(sqlParams);
                sqlTopUserEvents.forEach(elem -> {
                    String sql = sub.replace(elem);
                    executeUpdateWithRetry(stmt, sql);
                });
            }
            if (CollectionUtils.isNotEmpty(deviceIds)) {
                String inDeviceIds = ClickHouseUtil.sqlInParamsJoin(deviceIds);
                sqlParams.put("inDeviceIds", inDeviceIds);
                StrSubstitutor sub = new StrSubstitutor(sqlParams);
                sqlTopDeviceEvents.forEach(elem -> {
                    String sql = sub.replace(elem);
                    executeUpdateWithRetry(stmt, sql);
                });
            }

        } catch (Exception e) {
            log.error("fetch cc data with sql error: cc=" + connectedLabel, e);
        }
        log.info("process cc: {} for  recent 7days top events, cost={} ms", connectedLabel,
                (System.currentTimeMillis() - start));
    }

    private List<String> getTopNIds(String sql, ClickHouseStatement stmt) {
        List<String> resultIds = new ArrayList<>();
        try (ResultSet rs = stmt.executeQuery(sql);) {

            while (rs.next()) {
                resultIds.add(rs.getString(1));
            }
            return resultIds;
        } catch (SQLException e) {
            return new ArrayList<>();
        }
    }

    private void executeUpdateWithRetry(ClickHouseStatement stmt, String sql) {
        try {
            stmt.executeUpdate(sql);
        } catch (SQLException e) {
            log.error("execute sql error, sql=" + sql, e);
            try {
                stmt.executeUpdate(sql);
            } catch (SQLException ex) {
                log.error("retry execute sql error, sql=" + sql, e);
            }
        }
    }


    public boolean isFinishData(ClickHouseConnection ckConn, String connectedLabel, List<String> metricTypeList) {
        Map<String, Object> sqlParams = new HashMap<>();
        sqlParams.put("runDay", runDay);
        sqlParams.put("connectedLabel", connectedLabel);
        sqlParams.put("metrics", "'" + StringUtils.join(metricTypeList, "','") + "'");
        StrSubstitutor sub = new StrSubstitutor(sqlParams);

        String checkSql = "\n" +
                "select count(1) events from cc_detection_result_detail where id global in (\n" +
                "    select id from cc_detection_result where connectedLabel = '${connectedLabel}' and runDay = " +
                "'${runDay}'\n" +
                "    ) and runDay = '${runDay}' and metricType in (${metrics})";

        List<Map<String, String>> eventCount = ClickHouseUtil.executeQuery(ckConn, sub.replace(checkSql));
        if (CollectionUtils.isNotEmpty(eventCount) && Integer.valueOf(eventCount.get(0).get("events")) > 0) {
            return true;
        }
        return false;
    }

    public static void main(String[] args) {
        System.err.println("insert into cc_detection_result_detail(id,metricValue,metricType,runDay) " +
                "select userId, groupArray(${maxEventSizePerUser})(concat(eventCode, '#', toString(eventCount)))" +
                " " +
                "topEventStatistics, 'userTopEvent',today() \n" +
                "from (\n" +
                "\n" +
                "         select a.userId, a.eventCode, count(1) eventCount\n" +
                "         from risk_hit_log a\n" +
                "         where a.createdAt between '${startDay}' and '${endDay}'\n" +
                "           and userId global in (select id\n" +
                "                                 from cc_detection_result a\n" +
                "                                 where a.connectedLabel = '${connectedLabel}'\n" +
                "                                 and a.runDay = '${runDay}'\n" +
                "                                 order by degree desc,id desc\n" +
                "                                 limit ${maxAnalysisSizePerCC})\n" +
                "         group by userId, eventCode\n" +
                "         order by eventCount desc\n" +
                "         ) aa\n" +
                "group by userId;");
    }


    private final static String ccTopNIdsSql = "select id\n" +
            "                                 from cc_detection_result a\n" +
            "                                 where a.connectedLabel = '${connectedLabel}'\n" +
            "                                 and a.runDay = '${runDay}'\n" +
            "                                 order by degree desc,id desc\n" +
            "                                 limit ${maxAnalysisSizePerCC}";
    private final static List<String> sqlTopUserEvents = Arrays.asList(
            //每个cc用户topN事件
            "insert into cc_detection_result_detail(id,metricValue,metricType,runDay) " +
                    "select userId, groupArray(${maxEventSizePerUser})(concat(eventCode, '#', toString(eventCount)))" +
                    " " +
                    "topEventStatistics, 'userTopEvent',today() \n" +
                    "from (\n" +
                    "\n" +
                    "         select a.userId, a.eventCode, count(1) eventCount\n" +
                    "         from risk_hit_log a\n" +
                    "         where a.createdAt between '${startDay}' and '${endDay}'\n" +
                    "           and userId in (${inUserIds})\n" +
                    "         group by userId, eventCode\n" +
                    "         order by eventCount desc\n" +
                    "         ) aa\n" +
                    "group by userId;",
            //每个cc用户拒绝事件topN
            "insert into cc_detection_result_detail(id,metricValue,metricType," +
                    "runDay) " +
                    "select userId, groupArray(${maxEventSizePerUser})(concat(eventCode, '#', toString(eventCount)))" +
                    " " +
                    "topEventStatistics, 'userTopRejectEvent',today() \n" +
                    "from (\n" +
                    "\n" +
                    "         select a.userId, a.eventCode, count(1) eventCount\n" +
                    "         from risk_hit_log a\n" +
                    "         where a.createdAt between '${startDay}' and '${endDay}'\n" +
                    "           and userId  in (${inUserIds})\n" +
                    "          and level='REJECT'\n" +
                    "         group by userId, eventCode\n" +
                    "         order by eventCount desc\n" +
                    "         ) aa\n" +
                    "group by userId;"


    );

    private final static List<String> sqlTopDeviceEvents = Arrays.asList(
            //每个cc设备topN事件
            "insert into cc_detection_result_detail(id,metricValue,metricType,runDay) " +
                    "select deviceId, groupArray(${maxEventSizePerUser})(concat(eventCode, '#', toString" +
                    "(eventCount)" +
                    "))" +
                    " " +
                    "topEventStatistics, 'deviceTopEvent',today() \n" +
                    "from (\n" +
                    "\n" +
                    "         select a.deviceId, a.eventCode, count(1) eventCount\n" +
                    "         from risk_hit_log a\n" +
                    "         where a.createdAt between '${startDay}' and '${endDay}'\n" +
                    "           and deviceId  in (${inDeviceIds})\n" +
                    "         group by deviceId, eventCode\n" +
                    "         order by eventCount desc\n" +
                    "         ) aa\n" +
                    "group by deviceId;",


            //每个cc设备拒绝事件topN
            "insert into cc_detection_result_detail(id,metricValue,metricType,runDay) " +
                    "select deviceId, groupArray(${maxEventSizePerUser})(concat(eventCode, '#', toString" +
                    "(eventCount)" +
                    "))" +
                    " " +
                    "topEventStatistics, 'deviceTopRejectEvent',today() \n" +
                    "from (\n" +
                    "\n" +
                    "         select a.deviceId, a.eventCode, count(1) eventCount\n" +
                    "         from risk_hit_log a\n" +
                    "         where a.createdAt between '${startDay}' and '${endDay}'\n" +
                    "           and deviceId  in (${inDeviceIds})\n" +
                    "          and level='REJECT'\n" +
                    "         group by deviceId, eventCode\n" +
                    "         order by eventCount desc\n" +
                    "         ) aa\n" +
                    "group by deviceId;"
    );


    private final static String userLatestEventSqlTemplate =
            //每个cc用户近20条明细
            "select userId, arrayJoin(groupArray(${latestEventPerUser})(content)) allMsg\n" +
                    "                        from (\n" +
                    "                                select userId,\n" +
                    "                                concat(level, '#######', toString(createdAt), '#######', if(deviceId=='','empty',deviceId), '#######',\n" +
                    "                                        eventCode, '#######',\n" +
                    "                                        concat(data_body, data_content)) as content\n" +
                    "                                FROM risk_hit_log a\n" +
                    "                                where a.createdAt between\n" +
                    "                                addDays(now(), -7) and now()\n" +
                    "                                and userId global in (\n" +
                    "                                        select id\n" +
                    "                                        from cc_detection_result a\n" +
                    "                                        where a.connectedLabel = '${connectedLabel}'\n" +
                    "                                        and a.runDay = '${runDay}'\n" +
                    "                                        order by degree desc, id desc\n" +
                    "                                        limit ${maxAnalysisSizePerCC}\n" +
                    "                                )\n" +
                    "                                order by createdAt desc\n" +
                    "                        )\n" +
                    "                        group by userId;";

    //每个cc设备近20条明细
    private final static String deviceLatestEventSqlTemplate = "select deviceId, arrayJoin(groupArray(${latestEventPerUser})(content))" +
            " allMsg\n" +
            "                        from (\n" +
            "                                select deviceId,\n" +
            "                                concat(level, '#######', toString(createdAt), '#######', if" +
            "(userId=='','empty',userId), " +
            "'#######',\n" +
            "                                        eventCode, '#######',\n" +
            "                                        concat(data_body, data_content)) as content\n" +
            "                                FROM risk_hit_log a\n" +
            "                                where a.createdAt between\n" +
            "                                addDays(now(), -7) and now()\n" +
            "                                and deviceId global in (\n" +
            "                                        select id\n" +
            "                                        from cc_detection_result a\n" +
            "                                        where a.connectedLabel = '${connectedLabel}'\n" +
            "                                        and a.runDay = '${runDay}'\n" +
            "                                        order by degree desc, id desc\n" +
            "                                        limit ${maxAnalysisSizePerCC}\n" +
            "                                )\n" +
            "                                order by createdAt desc\n" +
            "                        )\n" +
            "                        group by deviceId;";


    private final String[] deviceTag = "bPcEmulator#bCloudDevice#bAltered#bMultiBoxing#bFaker#bFarmer#bOfferwall#bRoot#bSim#bDebuggable#bVpn#bMonkeyApps#bAcc#bMultiBoxingApps#bHook".split("#");

}
