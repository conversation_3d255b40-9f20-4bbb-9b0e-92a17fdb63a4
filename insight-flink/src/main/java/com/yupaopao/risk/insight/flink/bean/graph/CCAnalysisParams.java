package com.yupaopao.risk.insight.flink.bean.graph;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-08-26 11:57
 *
 ***/

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CCAnalysisParams implements Serializable {

    private Integer ccVertexCountLimit = 10;// 选取大于ccVertexCountLimit节点的社区[50->10]
    private Integer maxAnalysisSizePerCC = 100;//每个社区分析的最大节点数
    private Integer maxEventSizePerUser = 10; //每个用户设备取的top事件量
    private Integer latestEventPerUser = 20; //每个用户设备最近20条记录信息

    private String runDay;

    public static void main(String[] args) throws Exception {
        Map<String,String> argMap = new HashMap<>() ;
        argMap.put("ccVertexCountLimit","10");
        argMap.put("maxAnalysisSizePerCC","100");
        argMap.put("maxEventSizePerUser","10");
        argMap.put("latestEventPerUser","10");
        argMap.put("runDay","2020-09-21");

        System.err.println(URLEncoder.encode(JSON.toJSONString(argMap),"UTF-8"));



    }
}
