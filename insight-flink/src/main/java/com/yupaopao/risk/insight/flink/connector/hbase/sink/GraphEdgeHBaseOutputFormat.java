package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.yupaopao.risk.insight.common.beans.graph.EdgeInfo;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-08 18:43
 *
 ***/

@Slf4j
public class GraphEdgeHBaseOutputFormat extends CommonHBaseOutputFormat {

    private transient Table readTable;

    public GraphEdgeHBaseOutputFormat(String tableName, HBaseProperties hBaseProperties) {
        super(tableName, hBaseProperties);
    }

    @Override
    public void open(int taskNumber, int numTasks) throws IOException {
        super.open(taskNumber, numTasks);
        this.readTable = this.getConn().getTable(TableName.valueOf(HBaseConstants.NAMESPACE,
                this.getTableName()));
    }

    @Override
    public String getRowKey(Map<String, Object> flattenMap) {
        return (String) flattenMap.remove("id");
    }

    public boolean addColumns(Map<String, Object> flattenMap, Put put) {
        //如果边已经存在过则无需记录create时间
        Get g = new Get(put.getRow());
        try {
            Result result = readTable.get(g);
            if (result != null && result.getRow() != null) {
                flattenMap.remove(EdgeInfo.EDGE_PROP_CREATE_TIME);
                byte[] weightWeight = result.getValue(HBaseConstants.HBASE_FAMILY_KEY, Bytes.toBytes("weight"));
                if (weightWeight != null) {
                    BigDecimal bigDecimalWeight = Bytes.toBigDecimal(weightWeight);
                    bigDecimalWeight = bigDecimalWeight.add(BigDecimal.ONE);
                    flattenMap.put("weight", bigDecimalWeight);
                } else {
                    flattenMap.put("weight", BigDecimal.ONE);
                }
            } else {
                flattenMap.put("weight", BigDecimal.ONE);
            }
        } catch (IOException e) {
            log.warn("get row error: ");
        }
        return super.addColumns(flattenMap, put);
    }
}
