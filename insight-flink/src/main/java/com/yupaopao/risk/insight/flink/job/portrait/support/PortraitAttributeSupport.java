package com.yupaopao.risk.insight.flink.job.portrait.support;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitBean;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @description 构建 画像 属性
 *
 * <AUTHOR>
 */
public class PortraitAttributeSupport {



    public static void buildAttribute(PortraitBean portraitBean, JSONObject resPortraitJSON, JSONObject riskAction) {

        JSONObject tokenLabels = resPortraitJSON.getJSONObject("tokenLabels");
        if (CommonTagSupport.isEmpty(tokenLabels)) {
            return;
        }
        parseAccountActiveInfo(portraitBean, tokenLabels);

        parseAccountFreqInfo(portraitBean, tokenLabels);

        parseAccountRelateInfo(portraitBean, tokenLabels);

        parseAccountCommonInfo(portraitBean, riskAction, tokenLabels);

        JSONObject deviceActiveInfo = resPortraitJSON.getJSONObject("device_active_info");
        if (CommonTagSupport.isEmpty(deviceActiveInfo)){
            return;
        }

        Long iSmIdFirstActiveTimestamp = deviceActiveInfo.getLong("i_smid_first_active_timestamp");
        portraitBean.setIsmIdFirstActiveTimestamp(InsightDateUtils.getDateStr(iSmIdFirstActiveTimestamp, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));

    }

    private static void parseAccountCommonInfo(PortraitBean portraitBean, JSONObject riskAction, JSONObject tokenLabels) {
        JSONObject accountInfo = tokenLabels.getJSONObject("account_common_info");
        // 常用设备汇聚 按配置取前 n 个 使用天数较多的，且统计出 总共关联的设备数量 以及 判断当前设备是否是 常用设备
        if (!CommonTagSupport.isEmpty(accountInfo)) {

            //近28天设备使用天数集合
            JSONArray sTokenIdRelateSmIdInfoMap4w = accountInfo.getJSONArray("s_tokenid_relate_smid_info_map_4w");
            if (null != sTokenIdRelateSmIdInfoMap4w && !sTokenIdRelateSmIdInfoMap4w.isEmpty()){
                //新用户 或 长时间未使用的用户 是否应该拥有常用设备
                Integer threshold = Integer.valueOf(PortraitApolloProperties.getConfigByKey("portrait.active.device.days.threshold", "7"));
                String currentDeviceId = riskAction.getString("deviceId");
                portraitBean.setDeviceTotal(sTokenIdRelateSmIdInfoMap4w.size());
                for (Object deviceObj : sTokenIdRelateSmIdInfoMap4w) {
                    JSONObject deviceJSON = JSONObject.parseObject(JSONObject.toJSONString(deviceObj));
                    String deviceId = deviceJSON.getString("smid");
                    Integer days = deviceJSON.getInteger("days");
                    if (StringUtils.isNotEmpty(currentDeviceId) && currentDeviceId.equals(deviceId) && days>=threshold) {
                        portraitBean.setBTokenIdCommonSmId4w(Boolean.TRUE);
                    }
                }
            }
            portraitBean.setSTokenIdRelateSmIdInfoMap4w(sTokenIdRelateSmIdInfoMap4w);
            //当前IP归属城市
            String currentCity = accountInfo.getString("s_ip_city");
            // TODO 城市关联总数 也计算出来
            //近28天账户关联城市集合
            JSONArray sTokenIdRelateIpCityInfoMap4w = accountInfo.getJSONArray("s_tokenid_relate_ip_city_info_map_4w");
            portraitBean.setSTokenIdRelateIpCityInfoMap4w(sTokenIdRelateIpCityInfoMap4w);
            if (null!=sTokenIdRelateIpCityInfoMap4w && !sTokenIdRelateIpCityInfoMap4w.isEmpty()) {
                Integer threshold = Integer.valueOf(PortraitApolloProperties.getConfigByKey("portrait.active.city.days.threshold", "7"));
                for (Object cityObj : sTokenIdRelateIpCityInfoMap4w) {
                    JSONObject cityJSON = JSONObject.parseObject(JSONObject.toJSONString(cityObj));
                    String city = cityJSON.getString("city");
                    Integer days = cityJSON.getInteger("days");
                    if (StringUtils.isNotEmpty(currentCity) && currentCity.equals(city) && days>=threshold) {
                        portraitBean.setBTokenIdCommonIpCity4w(Boolean.TRUE);
                    }
                }
            }

        }
    }

    private static void parseAccountRelateInfo(PortraitBean portraitBean, JSONObject tokenLabels) {
        JSONObject accountRelateInfo = tokenLabels.getJSONObject("account_relate_info");
//        if (!CommonTagSupport.isEmpty(accountRelateInfo)) {
            portraitBean.setIsmIdRelateTokenIdCnt1d(CommonTagSupport.getJSONValue(accountRelateInfo, "i_smid_relate_tokenid_cnt_1d", 0));
            portraitBean.setIsmIdRelateTokenIdCnt7d(CommonTagSupport.getJSONValue(accountRelateInfo, "i_smid_relate_tokenid_cnt_7d", 0));

            portraitBean.setIsmIdRelateIpCityCnt1d(CommonTagSupport.getJSONValue(accountRelateInfo, "i_smid_relate_ip_city_cnt_1d", 0));
            portraitBean.setIsmIdRelateIpCityCnt7d(CommonTagSupport.getJSONValue(accountRelateInfo, "i_smid_relate_ip_city_cnt_7d", 0));

            portraitBean.setITokenIdRelateSmIdCnt1d(CommonTagSupport.getJSONValue(accountRelateInfo, "i_tokenid_relate_smid_cnt_1d", 0));
            portraitBean.setITokenIdRelateSmIdCnt7d(CommonTagSupport.getJSONValue(accountRelateInfo, "i_tokenid_relate_smid_cnt_7d", 0));

            portraitBean.setITokenIdRelateIpCityCnt1d(CommonTagSupport.getJSONValue(accountRelateInfo, "i_tokenid_relate_ip_city_cnt_1d", 0));
            portraitBean.setITokenIdRelateIpCityCnt7d(CommonTagSupport.getJSONValue(accountRelateInfo, "i_tokenid_relate_ip_city_cnt_7d", 0));
//        }
    }

    private static void parseAccountFreqInfo(PortraitBean portraitBean, JSONObject tokenLabels) {
        JSONObject accountFreqInfo = tokenLabels.getJSONObject("account_freq_info");
//        if (!CommonTagSupport.isEmpty(accountFreqInfo)) {
            portraitBean.setITokenIdLoginCnt1d(CommonTagSupport.getJSONValue(accountFreqInfo, "i_tokenid_login_cnt_1d", 0));
            portraitBean.setITokenIdLoginCnt7d(CommonTagSupport.getJSONValue(accountFreqInfo,"i_tokenid_login_cnt_7d", 0));
            portraitBean.setIsmIdRegisterCnt1d(CommonTagSupport.getJSONValue(accountFreqInfo,"i_smid_register_cnt_1d", 0));
            portraitBean.setIsmIdRegisterCnt7d(CommonTagSupport.getJSONValue(accountFreqInfo,"i_smid_register_cnt_7d", 0));
            portraitBean.setIsmIdLoginCnt1d(CommonTagSupport.getJSONValue(accountFreqInfo, "i_smid_login_cnt_1d", 0));
            portraitBean.setIsmIdLoginCnt7d(CommonTagSupport.getJSONValue(accountFreqInfo, "i_smid_login_cnt_7d", 0));
//        }
    }

    private static void parseAccountActiveInfo(PortraitBean portraitBean, JSONObject tokenLabels) {
        JSONObject accountActiveInfo = tokenLabels.getJSONObject("account_active_info");
        if (!CommonTagSupport.isEmpty(accountActiveInfo)) {
            Long accountFirstActiveTime = accountActiveInfo.getLong("i_tokenid_first_active_timestamp");
            Long deviceActiveTime = accountActiveInfo.getLong("i_smid_first_active_timestamp");
            if (null !=accountFirstActiveTime) {
                //首次在数美平台活跃时间
                Date accountActiveDate = new Date(accountFirstActiveTime);
                String accountActiveTimeStr = InsightDateUtils.getDateStr(accountActiveDate, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
                portraitBean.setITokenIdFirstActiveTimestamp(accountActiveTimeStr);
            }
            if (null != deviceActiveTime) {
                Date deviceActiveDate = new Date(deviceActiveTime);
                String deviceActiveTimeStr = InsightDateUtils.getDateStr(deviceActiveDate, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
                portraitBean.setIsmIdFirstActiveTimestamp(deviceActiveTimeStr);
            }
        }
        portraitBean.setITokenIdActiveDays7d(CommonTagSupport.getJSONValue(accountActiveInfo, "i_tokenid_active_days_7d", 0));
        portraitBean.setIsmIdActiveDays7d(CommonTagSupport.getJSONValue(accountActiveInfo, "i_smid_active_days_7d", 0));
        portraitBean.setITokenIdActiveDays4w(CommonTagSupport.getJSONValue(accountActiveInfo, "i_tokenid_active_days_4w", 0));
        portraitBean.setIsmIdActiveDays4w(CommonTagSupport.getJSONValue(accountActiveInfo, "i_smid_active_days_4w", 0));
    }

}
