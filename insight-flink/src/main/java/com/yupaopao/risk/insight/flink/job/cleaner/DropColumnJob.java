package com.yupaopao.risk.insight.flink.job.cleaner;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.io.PrintingOutputFormat;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.net.URLDecoder;
import java.util.*;

/****
 * zengxiangcai
 * 2023/6/13 15:49
 ***/

@Slf4j
public class DropColumnJob {
    public static void main(String[] args) throws Exception {
//        if(true){
//            System.err.println(STRING_TYPE_CHECK_SQL);
//            return;
//        }

        log.info("start to drop columns for risk_hit_log");
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        String batchSize = "50";
//        if (args != null && args.length > 0) {
//            String inputParam = args[0];
//            if (StringUtils.isNotEmpty(inputParam)) {
//                inputParam = URLDecoder.decode(inputParam, "UTF-8");
//                log.info("input params : {}", inputParam);
//                Map<String,String> obj = JSON.parseObject(inputParam, Map.class);
//                if(obj.containsKey("batchSize")){
//                    batchSize = obj.get("batchSize");
//                }
//            }
//        }
        env.fromElements(batchSize)
                .flatMap(new DropColumnFlatMap())
                .output(new PrintingOutputFormat<>());
        env.execute("drop columns for risk_hit_log");
    }

    public static class DropColumnFlatMap extends RichFlatMapFunction<String, String> {

        private transient ClickHouseConnection conn;

        @Override
        public void open(Configuration parameters) throws Exception {
            super.open(parameters);
            ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
            ckProperties.setHost("cc-bp14di2b9y6g6qbxz.clickhouse.ads.aliyuncs.com:8123");
            ckProperties.setPassword("1TpzoV9E8bWcYN2F");
            ckProperties.setUsername("risk_rw");
            conn = ClickHouseUtil.createConnection(ckProperties);
        }

        @Override
        public void flatMap(String value, Collector<String> out) throws Exception {
            log.info("batchSize: {}", value);

            //1、根据已经已知的空字段data_worldId分析空字段，每次取10个，按照非压缩数据量升序
            //2、对每个字段查询最近100天是否有数据，没有数据则进行drop，如果碰到字段有非空数据，则任务退出
            String checkSql = INT32_TYPE_CHECK_SQL + value;
            List<Map<String, String>> candidateColumns = ClickHouseUtil.executeQuery(conn, checkSql);
            if (candidateColumns == null || candidateColumns.isEmpty()) {
                return;
            }
            //检查字段的时间区间
            int checkPeriodDays[] = new int[]{120, 90, 60, 30, 0};

            List<String> canDropColumns = new ArrayList<>();
            for (Map<String, String> columnInfo : candidateColumns) {
                log.info("prepare to drop column: {}", JSON.toJSONString(columnInfo));
                //防止新增字段数据过少提前过滤
                if (Long.valueOf(columnInfo.get("rows_cnt")) < 20000000) {
                    continue;
                }
                String columnName = columnInfo.get("column");
                boolean canDrop = false;
                for (int i = 0; i < 4; i++) {
                    String sql = String.format(INT32_TYPE_COUNT_CHECK_SQL, columnName, checkPeriodDays[i],
                            checkPeriodDays[i + 1], columnName);

                    List<Map<String, String>> countResult = ClickHouseUtil.executeQuery(conn, sql);
                    if (countResult == null) {
                        canDrop = false;
                        break;
                    }
                    String distinctCount = countResult.get(0).get("distinctCount");
                    String nullEmptyCount = countResult.get(0).get("nullEmptyCount");
                    log.info("column distinct countCheck sql: {}, result: {}", sql, JSON.toJSONString(countResult));
                    if (Integer.valueOf(distinctCount) == 0 && Integer.valueOf(nullEmptyCount) == 0) {
                        canDrop = true;
                    } else {
                        canDrop = false;
                        break;
                    }
                }
                if (canDrop) {
                    canDropColumns.add(columnName);
                }
//                else {
//                    //因为筛选出来的字段是按照大小升序的，如果有一个字段不能删除，后续的字段就不需要考虑了
//                    break;
//                }
            }

            if (canDropColumns.isEmpty()) {
                return;
            }

            List<String> dropLog = new ArrayList<>();
            //删除字段：
            try {
                boolean flushResult = ClickHouseUtil.flushDistributedTable("risk_hit_log", conn);
                if (!flushResult) {
                    return;
                }
                String alertTable = "risk_hit_log";
                String distributeSql = getSql(canDropColumns, alertTable);
                boolean distributeDrop = ClickHouseUtil.dropColumn(distributeSql, conn);
                log.info("distributeDrop : {}", distributeDrop);
                Map<String, String> distributeRow = new HashMap<>();
                distributeRow.put("tableName", alertTable);
                distributeRow.put("sql", distributeSql);
                distributeRow.put("result", String.valueOf(distributeDrop));
                distributeRow.put("createdAt",
                        InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));
                dropLog.add(JSON.toJSONString(distributeRow));
                if (!distributeDrop) {
                    return;
                }
                //防止有未处理完的local表sql: sleep
                Thread.sleep(60 * 1000);
                alertTable += "_local";
                String localSql = getSql(canDropColumns, alertTable);
                boolean localDrop = ClickHouseUtil.dropColumn(localSql, conn);
                log.info("localDrop : {}", localDrop);
                Map<String, String> localRow = new HashMap<>();
                localRow.put("tableName", alertTable);
                localRow.put("sql", localSql);
                localRow.put("result", String.valueOf(localDrop));
                localRow.put("createdAt",
                        InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));
                dropLog.add(JSON.toJSONString(localRow));
                if (!localDrop) {
                    return;
                }
            } catch (Exception e) {
                log.error("drop columns error, msg: " + e.getMessage(), e);
            } finally {
                //写入表中做记录，方便后面分析字段空的情况
                String insertSql = "INSERT INTO column_drop_log FORMAT JSONEachRow\n" + String.join("\n", dropLog);
                ;
                ClickHouseUtil.executeUpdate(conn, insertSql);
            }

        }

        private String getSql(List<String> columns, String tableName) {
            StringBuilder distributeSql = new StringBuilder("ALTER TABLE ").append(tableName).append(" ON " +
                    "CLUSTER default");
            columns.forEach(col -> {
                distributeSql.append(" drop column IF  EXISTS ")
                        .append(col)
                        .append(",");
            });
            String alterSql = distributeSql.deleteCharAt(distributeSql.length() - 1).toString();
            return alterSql;
        }

        @Override
        public void close() throws Exception {
            super.close();
            if (conn != null) {
                conn.close();
            }
        }
    }

    private static String STRING_TYPE_CHECK_SQL = "with  (\n" +
            "    SELECT\n" +
            "        tuple(sum(rows),sum(column_data_uncompressed_bytes))\n" +
            "    FROM system.parts_columns\n" +
            "    WHERE (active = 1) AND (database LIKE '%') AND table='risk_hit_log_local' and column='data_wordId'\n" +
            ")  as totalS\n" +
            "SELECT\n" +
            "    database,\n" +
            "    table,\n" +
            "    column,\n" +
            "    sum(column_data_compressed_bytes) AS size,\n" +
            "    formatReadableSize(size) AS compressed,\n" +
            "    sum(column_data_uncompressed_bytes) AS usize,\n" +
            "    formatReadableSize(usize) AS uncompressed,\n" +
            "    sum(column_data_compressed_bytes),\n" +
            "    round(usize / size, 2) AS compr_ratio,\n" +
            "    sum(rows) rows_cnt,\n" +
            "    round(usize / rows_cnt, 2) avg_row_size\n" +
            "FROM system.parts_columns\n" +
            "WHERE (active = 1) AND (database LIKE '%') AND table='risk_hit_log_local' and column!='data_wordId' " +
            " and type='String'\n" +
            "GROUP BY\n" +
            "    database,\n" +
            "    table,\n" +
            "    column\n" +
            "having rows_cnt = tupleElement(totalS,1) and avg_row_size=1 and usize = tupleElement(totalS,2)\n" +
            "ORDER BY usize asc,column asc limit ";

    private static String INT32_TYPE_CHECK_SQL = "with  (\n" +
            "    SELECT\n" +
            "        tuple(sum(rows),sum(column_data_uncompressed_bytes),sum(column_data_compressed_bytes))\n" +
            "    FROM system.parts_columns\n" +
            "    WHERE (active = 1) AND (database LIKE '%') AND table='risk_hit_log_local' and column='data_loginCheck_detail_deviceLabels_fake_device_b_faker'\n" +
            ")  as totalS\n" +
            "SELECT\n" +
            "    database,\n" +
            "    table,\n" +
            "    column,\n" +
            "    sum(column_data_compressed_bytes) AS size,\n" +
            "    formatReadableSize(size) AS compressed,\n" +
            "    sum(column_data_uncompressed_bytes) AS usize,\n" +
            "    formatReadableSize(usize) AS uncompressed,\n" +
            "    sum(column_data_compressed_bytes),\n" +
            "    round(usize / size, 2) AS compr_ratio,\n" +
            "    sum(rows) rows_cnt,\n" +
            "    round(usize / rows_cnt, 2) avg_row_size\n" +
            "FROM  system.parts_columns\n" +
            "WHERE (active = 1) AND (database LIKE '%') AND table='risk_hit_log_local' and column!='data_loginCheck_detail_deviceLabels_fake_device_b_faker'  and type='Int32'\n" +
            "GROUP BY\n" +
            "    database,\n" +
            "    table,\n" +
            "    column\n" +
            "having rows_cnt = tupleElement(totalS,1) and avg_row_size=4 and usize = tupleElement(totalS,2) and size=tupleElement(totalS,3)\n" +
            "ORDER BY usize asc,column asc limit ";

    private static String INT32_TYPE_COUNT_CHECK_SQL = "select count(distinct %s) distinctCount,count(1) " +
            "nullEmptyCount\n" +
            " from risk_hit_log where createdAt between addDays(now(),-%s) and addDays(now(),-%s)\n" +
            "and %s!=0";


    private static String STRING_TYPE_COUNT_CHECK_SQL = "select count(distinct %s) distinctCount,count(1) " +
            "nullEmptyCount\n" +
            " from risk_hit_log where createdAt between addDays(now(),-%s) and addDays(now(),-%s)\n" +
            "and %s!=''";
}
