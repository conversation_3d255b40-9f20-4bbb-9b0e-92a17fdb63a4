package com.yupaopao.risk.insight.flink.job.wangan.processor;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.connector.kafka.sink.serialization.KafkaObjSerializationSchema;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.WangAnDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.flink.job.wangan.constant.TopicConstant;
import com.yupaopao.risk.insight.flink.job.wangan.function.WangAnFlatMapFunction;
import com.yupaopao.risk.insight.flink.utils.KafkaUtils;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;

import java.util.Arrays;
import java.util.Map;
import java.util.Properties;

public class WangAnETLProcessor implements FlinkJobBuilder.MainProcessor {
    @Override
    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {


        FlinkKafkaConsumer consumer = KafkaUtils.getKafkaConsumer(Arrays.asList(TopicConstant.LOGIN_RECODE,TopicConstant.ACCOUNT,
                        TopicConstant.T_LOGIN_DEVICE, TopicConstant.T_CHATROOM_BEHAVIOR_LOG,TopicConstant.ACCOUNT_ASSOCIATE,TopicConstant.T_COMMENT
                ,TopicConstant.CONTENT_ANCHOR, TopicConstant.CONTENT_MAIN, TopicConstant.CONTENT_POSITION,TopicConstant.T_PAYMENT_TRADE,
                        TopicConstant.PAYMENT_RECHARGE_ORDER, TopicConstant.PAYMENT_THIRD_PAY, TopicConstant.PAYMENT_THIRD_PAY_ATTACH,TopicConstant.RELATION_DOMAIN,
                        TopicConstant.T_USER_AUTH,TopicConstant.T_USER_INFO, TopicConstant.T_USER_LOCATION),
                PropertyType.MASERATI_KAFKA, FlinkConstants.KAFKA_GROUP_ID_ETL_WANGAN, "risk-insight-flink",new WangAnDeserializationSchemaWrapper(new SimpleStringSchema()));


        String parallelism = PortraitApolloProperties.getConfigByKey("wangan.stream.parallelism", "3");
        DataStream<String> stream = env.addSource(consumer).setParallelism(Integer.valueOf(parallelism)).name("网安数据上报");
        FlinkKafkaConsumer bigDataConsumer = KafkaUtils.getKafkaConsumer(Arrays.asList(TopicConstant.GROUP_CHAT_BINLOG), PropertyType.KAFKA_BIGDATA,
                FlinkConstants.KAFKA_GROUP_ID_ETL_WANGAN, "risk-insight-flink",new WangAnDeserializationSchemaWrapper(new SimpleStringSchema()));
        DataStream<String> bigDataStream = env.addSource(bigDataConsumer).setParallelism(1).name("群组聊天消费");
        stream.join(bigDataStream);
        String sinkParallelism = PortraitApolloProperties.getConfigByKey("wangan.sink.parallelism", "1");
        stream.flatMap(new WangAnFlatMapFunction())
                .setParallelism(Integer.valueOf(parallelism))
                .addSink(KafkaUtils.getKafkaProducer(FlinkConstants.WANGAN_KAFKA,PropertyType.KAFKA_BIGDATA)).name("网安数据写出")
                .setParallelism(Integer.valueOf(sinkParallelism));
        KafkaProperties.getProperties(PropertyType.KAFKA_BIGDATA);
    }
}
