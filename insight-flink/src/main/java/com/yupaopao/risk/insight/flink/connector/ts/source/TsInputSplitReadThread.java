//package com.yupaopao.risk.insight.flink.connector.ts.source;
//
//import com.alicloud.openservices.tablestore.SyncClient;
//import com.alicloud.openservices.tablestore.model.*;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsClientFactory;
//import com.yupaopao.risk.insight.flink.constants.TsConstants;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//
//import java.util.LinkedList;
//import java.util.List;
//import java.util.concurrent.LinkedBlockingQueue;
//
//import static com.yupaopao.risk.insight.flink.utils.TsPrimaryKeyTools.buildRiskUserDefinedDataEndPk;
//import static com.yupaopao.risk.insight.flink.utils.TsPrimaryKeyTools.buildRiskUserDefinedDataStartPk;
//
///****
// *
// * @Author: zengxiangcai
// * @Date: 2019-12-21 17:37
// *
// ***/
//
//@Slf4j
//public class TsInputSplitReadThread extends Thread{
//    private final TsInputSplit.SplitBucket split;
//    private TsClientFactory clientFactory;
//    private LinkedBlockingQueue<LinkedList<Row>> rows;
//    private final String otsTableName;
//    private String tableId;
//    private String tableType;
//    private String[] columns;
//    protected volatile boolean exit = false;
//
//    private Integer totalReadCount = 0;
//    private long startTime = 0;
//
//    public TsInputSplitReadThread(TsInputSplit.SplitBucket split,
//                                  TsClientFactory clientProvider,
//                                  LinkedBlockingQueue<LinkedList<Row>> rows,
//                                  String otsTableName,
//                                  String tableId,
//                                  String tableType,
//                                  String[] columns,
//                                  String threadName) {
//        super(threadName);
//        this.split = split;
//        this.clientFactory = clientProvider;
//        this.rows = rows;
//        this.columns = columns;
//        this.tableId = tableId;
//        this.tableType = tableType;
//        if (TsConstants.TABLE_TYPE_TEMP.equalsIgnoreCase(this.tableType)) {
//            this.otsTableName = TsConstants.TABLE_RISK_USER_DEFINE_DATA;
//        } else {
//            this.otsTableName = otsTableName;
//        }
//        log.info("fetch rows for table: {}, tableId: {}, threadName: {}", this.otsTableName, tableId,
//                this.getName());
//        startTime = System.currentTimeMillis();
//    }
//
//    @Override
//    public void run() {
//        PrimaryKey start = split.getLowerBound();
//        PrimaryKey end = split.getUpperBound();
//        if (TsConstants.TABLE_RISK_USER_DEFINE_DATA.equalsIgnoreCase(otsTableName)) {
//            log.info("user define data fetch, tableId: {}", tableId);
//            //用户自定义表查询的数据是：table定义的数据,startEnd都不一样
//            start = buildRiskUserDefinedDataStartPk(tableId);
//            end = buildRiskUserDefinedDataEndPk(tableId);
//        }
//        while (!Thread.interrupted()) {
//            //范围读tablestore
//            GetRangeResponse res = getRange(clientFactory.getClient(), start, end);
//            if (res == null) {
//                log.warn("get range from ts is null");
//                continue;
//            }
//            List<Row> temp = res.getRows();
//            totalReadCount += (temp != null ? temp.size() : 0);
//
//            //读取结果放入queue中
//            try {
//                if (CollectionUtils.isEmpty(temp)) {
//                    log.info("empty row, nextKey: {}", res.getNextStartPrimaryKey());
//                } else {
//                    rows.put(new LinkedList<>(temp));
//                }
//
//            } catch (InterruptedException e) {
//                log.error("put range response batchFetchedList to queue error", e);
//            }
//            //任然有数据继续读， getRows可能只返回部分
//            if (res.getNextStartPrimaryKey() != null) {
//                start = res.getNextStartPrimaryKey();
//            } else {
//                log.info("thread: {} exit flag set, totalReadCount: {}, cost = {} ms", this.getName(), totalReadCount
//                        ,(System.currentTimeMillis() - startTime));
//                exit = true;
//                break;
//            }
//        }
//    }
//
//    /***
//     * 范围读取
//     * @param client
//     * @param lowerBound
//     * @param upperBound
//     * @return
//     */
//    private GetRangeResponse getRange(SyncClient client, PrimaryKey lowerBound, PrimaryKey upperBound) {
//        try {
//            long start = System.currentTimeMillis();
//            GetRangeRequest grr = new GetRangeRequest();
//            RangeRowQueryCriteria rec = new RangeRowQueryCriteria(otsTableName);
//            rec.setDirection(Direction.FORWARD);
//            rec.setInclusiveStartPrimaryKey(lowerBound);
//            rec.setExclusiveEndPrimaryKey(upperBound);
//            rec.setMaxVersions(1);
//            if (columns != null && columns.length > 0) {
//                rec.addColumnsToGet(columns);
//            }
//            grr.setRangeRowQueryCriteria(rec);
//
//            GetRangeResponse response = client.getRange(grr);
//            long end = System.currentTimeMillis();
//            log.info("read: {} with {} ms",response.getRows().size(),(end-start));
//            return response;
//        } catch (Exception e) {
//            log.error("getRange error: tableName: " + otsTableName + ", lowerBound: " + lowerBound + ", upperBound: " + upperBound
//                    , e);
//        }
//        return null;
//    }
//}
