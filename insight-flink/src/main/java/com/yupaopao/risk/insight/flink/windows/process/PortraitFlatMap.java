package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.flink.bean.portrait.OpenAiOutPutVO;
import com.yupaopao.risk.insight.flink.bean.portrait.OutputMess;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitBean;
import com.yupaopao.risk.insight.flink.bean.portrait.ResultVO;
import com.yupaopao.risk.insight.flink.connector.redis.sink.client.RedisClient;
import com.yupaopao.risk.insight.flink.job.portrait.process.PortraitProcess;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.co.RichCoFlatMapFunction;
import org.apache.flink.util.Collector;
import redis.clients.jedis.JedisPool;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class PortraitFlatMap extends RichCoFlatMapFunction<String, String, Tuple2<Long, PortraitBean>> {

    private JedisPool jedisPool;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        jedisPool = RedisClient.getClient();
    }

    @Override
    public void flatMap1(String value, Collector<Tuple2<Long, PortraitBean>> out) throws Exception {
        PortraitBean portraitBean = PortraitProcess.process(value ,jedisPool);
        if (!Objects.isNull(portraitBean) && !Objects.isNull(portraitBean.getUid())){
            out.collect(new Tuple2<>(portraitBean.getUid(), portraitBean));
        }
    }

    @Override
    public void flatMap2(String value, Collector<Tuple2<Long, PortraitBean>> out) throws Exception {
        JSONObject riskLog = JSON.parseObject(value);
        if (riskLog.containsKey("objectId") && "3000".equals(riskLog.get("bizType"))){
            Cat.logMetricForCount("risk.insight.flink.openAi.count");
            JSONObject outputMessJSON = riskLog.getJSONObject("outputMess");
            if (Objects.nonNull(outputMessJSON) && !outputMessJSON.isEmpty()){
                String result = outputMessJSON.getString("result");
                if (StringUtils.isNotEmpty(result)){
                    List<JSONObject> resultVO = JSONObject.parseObject(result, List.class);
                    List<ResultVO> newResultVO = new ArrayList();
                    for (JSONObject vo : resultVO) {
                        if (Objects.nonNull(vo) && !vo.isEmpty()){
                            newResultVO.add(JSONObject.parseObject(vo.toJSONString(), ResultVO.class));
                        }
                    }
                    OpenAiOutPutVO outputVO = new OpenAiOutPutVO();
                    outputVO.setUid(Long.valueOf(riskLog.getString("objectId")));
                    OutputMess outputMess = new OutputMess();
                    outputMess.setResult(newResultVO);
                    outputVO.setOutputMess(outputMess);
                    outputVO.setBizType(riskLog.getInteger("bizType"));
                    outputVO.setEventType(riskLog.getString("eventType"));
                    outputVO.setTimestamp(riskLog.getLong("timestamp"));
                    outputVO.setTime(riskLog.getLong("time"));
                    out.collect(new Tuple2<>(outputVO.getUid(), outputVO));
                }
            }
        }
    }

    @Override
    public void close() throws Exception {
        RedisClient.closePool(jedisPool);
        PortraitProcess.close();
    }
}
