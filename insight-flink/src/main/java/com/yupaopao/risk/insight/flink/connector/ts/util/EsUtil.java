/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yupaopao.risk.insight.flink.connector.ts.util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.util.TypeUtils;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.utils.HTTPUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Credentials;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.admin.indices.mapping.put.PutMappingRequest;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;

import java.util.*;

@Slf4j
public class EsUtil {

    public static RestHighLevelClient getClient(EsProperties esProperties) {
        RestHighLevelClient client = new RestHighLevelClient(getClientBuilder(esProperties));
        return client;
    }

    public static RestClientBuilder getClientBuilder(EsProperties esProperties) {
        List<HttpHost> httpHostList = parseHostList(esProperties);
        RestClientBuilder builder = RestClient.builder(httpHostList.toArray(new HttpHost[httpHostList.size()]));
        if (StringUtils.isNotBlank(esProperties.getUsername()) && StringUtils.isNotBlank(esProperties.getPassword())) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(esProperties.getUsername(), esProperties.getPassword()));
            builder.setHttpClientConfigCallback(httpClient -> httpClient.setDefaultCredentialsProvider(credentialsProvider));
        }
        //大批量读取时可能会出现超时，需要http超时设置
        builder.setRequestConfigCallback(new RestClientBuilder.RequestConfigCallback(){

            @Override
            public RequestConfig.Builder customizeRequestConfig(RequestConfig.Builder param) {
                return RequestConfig.custom()
                        .setConnectTimeout(1000*10)
                        .setSocketTimeout(1000*60*3) // 3min,默认是30s
                        .setConnectionRequestTimeout(5000);
            }
        });

        return builder;
    }

    public static List<HttpHost> parseHostList(EsProperties esProperties) {
        List<HttpHost> httpHostList = new ArrayList<>();
        String[] addr = esProperties.getHosts().split(",");
        for (String add : addr) {
            String[] pair = add.split(":");
            httpHostList.add(new HttpHost(pair[0], Integer.valueOf(pair[1]), "http"));
        }
        return httpHostList;
    }



    public static Map<String, String> fetchColumns(EsProperties esProperties, String indexName) {
        OkHttpClient okHttpClient = new OkHttpClient();
        Map<String, String> notExitColumns = new HashMap<>();
        try {
            Request.Builder builder = new Request.Builder();
            if (StringUtils.isNotEmpty(esProperties.getUsername()) && StringUtils.isNotEmpty(esProperties.getPassword())) {
                String credential = Credentials.basic(esProperties.getUsername(), esProperties.getPassword());
                builder.addHeader("Authorization", credential);
            }
            Request request = builder
                    .url("http:" + esProperties.getHosts() + "/" + indexName + "/_mappings")
                    .get().build();
            Response response = okHttpClient.newCall(request).execute();
            String responseStr = response.body().string();
            JSONObject jsonObject = read(responseStr, "$." + indexName + ".mappings.V1.properties", JSONObject.class);
            jsonObject.remove("full_text");
            if (indexName.startsWith("risk_hit_log")) {
                jsonObject.remove("result");
            }


            Map<String, Object> esColumn = JsonFlatterUtils.toMap(jsonObject.toJSONString(), FlattenMode.KEEP_ARRAYS
                    , '.');

            esColumn.forEach((key, value) -> {
                String columnKey = key.replace(".", "_").replace("_properties", "").replaceAll("_type$", "");
                String valueType = esTypeToCkType(value.toString());
                //es不支持中间下划线，所以如果key包含-，需要转为_
                if(columnKey.contains("-")){
                    columnKey = columnKey.replace("-","_");
                }
                if (StringUtils.isNotEmpty(valueType)) {
                    notExitColumns.put(columnKey, valueType);
                }
            });
        } catch (Exception e) {
            log.error("fetch ES index fields occurs error: ", e);
        } finally {
            HTTPUtil.releaseResource(okHttpClient);
        }
        notExitColumns.put("createdAt", "TIMESTAMP");
        log.info("notExitColumns: {}", JSONObject.toJSONString(notExitColumns));
        return notExitColumns;
    }


    private static <T> T read(Object obj, String expr, Class<T> clazz) {
        Object result = JSONPath.eval(obj, expr);
        return TypeUtils.castToJavaBean(result, clazz);
    }

    private static String esTypeToCkType(String type) {
        String ckType = "";
        switch (type.toLowerCase()) {
            case "keyword":
            case "text":
            case "ip":
                ckType = "STRING";
                break;
            case "byte":
                ckType = "BOOLEAN";
                break;
            case "short":
                ckType = "SMALLINT";
                break;
            case "integer":
                ckType = "INT";
                break;
            case "long":
                ckType = "BIGINT";
                break;
            case "boolean":
                ckType = "BOOLEAN";
                break;
            case "float":
                ckType = "FLOAT";
                break;
            case "double":
                ckType = "DOUBLE";
                break;
            case "date":
                ckType = "TIMESTAMP";
                break;
            default:
                log.info("not support for this type: {}", type);
        }
        return ckType;
    }


}
