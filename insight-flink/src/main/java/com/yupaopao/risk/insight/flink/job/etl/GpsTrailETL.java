package com.yupaopao.risk.insight.flink.job.etl;

import com.alibaba.lindorm.client.core.utils.Bytes;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.connector.hbase.sink.CommonHBaseOutputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.sink.HBaseStreamSink;
import com.yupaopao.risk.insight.flink.connector.redis.sink.GpsRedisSink;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.OriginalStringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.job.base.CheckpointSetting;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.apache.hadoop.hbase.client.Put;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.KAFKA_GROUP_ID_KAFKA_CK_SYNC;
import static com.yupaopao.risk.insight.common.constants.HBaseConstants.COLUMN_FAMILY;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc GpsTrailETL
 * @date 2020/7/6
 */
@Slf4j
public class GpsTrailETL {

    public static void main(String[] args) throws Exception {
        new FlinkJobBuilder()
            .withJobName("gps-trail")
            .withCheckpointSetting(new CheckpointSetting(60000, 30000, true))
            .withMainProcessor(new GpsTrailProcessor())
            .start(args);
    }

    private static OutputTag<String> redisSink = new OutputTag<String>("redisSink") {
        private static final long serialVersionUID = -8959934865445224944L;
    };
    private static OutputTag<String> hbaseSink = new OutputTag<String>("hbaseSink") {
        private static final long serialVersionUID = -7996309722027790365L;
    };

    private static class GpsTrailProcessor implements FlinkJobBuilder.MainProcessor {

        @Override public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
            ClickHouseProperties clickHouseProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
            RedisProperties redisProperties = RedisProperties.getProperties(PropertyType.REDIS);

            SingleOutputStreamOperator<String> process = env.addSource(getKafkaConsumer("USER_LNG_LAT_REPORT"))
                .process(new sinkOutputProcess());

            process.addSink(new ClickHouseStreamSink(clickHouseProperties, "user_gps_trail"))
                .name("gps clickhouse sink");

            process.getSideOutput(redisSink)
                .addSink(new GpsRedisSink(redisProperties))
                .name("gps redis sink");

            process.getSideOutput(hbaseSink)
                .addSink(getHBaseOutputFormat())
                .name("gps hbase sink");
        }
    }

    private static HBaseStreamSink getHBaseOutputFormat() {
        HBaseProperties hBProperties = HBaseProperties.getProperties(PropertyType.HBASE);
        CommonHBaseOutputFormat outputFormat = new CommonHBaseOutputFormat("risk_gps_info", hBProperties) {
            private static final long serialVersionUID = 5417397480621084853L;

            @Override public String getRowKey(Map<String, Object> flattenMap) {
                return flattenMap.get("uid").toString();
            }

            @Override
            public boolean addColumns(Map<String, Object> flattenMap, Put put) {
                byte[] columnFamily = Bytes.toBytes(COLUMN_FAMILY);
                int columnCount = 0;
                for (Map.Entry<String, Object> entry : flattenMap.entrySet()) {
                    byte[] columnValue = HBaseUtil.jsonTypeToHBaseColumn(entry.getValue().toString());
                    if (columnValue == null) {
                        continue;
                    }
                    columnCount++;
                    put.addColumn(columnFamily, Bytes.toBytes(entry.getKey()), columnValue);
                }
                return columnCount != 0;
            }
        };
        return new HBaseStreamSink(outputFormat);
    }

    private static FlinkKafkaConsumer<String> getKafkaConsumer(String... topic) {
        KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA);
        Properties etlProperties = kafkaProperties.getProperties();
        etlProperties.put("group.id", KAFKA_GROUP_ID_KAFKA_CK_SYNC);

        List<String> topics = Arrays.asList(topic);

        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics,
            new OriginalStringKafkaDeserializationSchemaWrapper(new SimpleStringSchema()),
            kafkaProperties.getProperties());
        // 设定项目启动读取时间，不影响从checkpoint 恢复
        consumer.setStartFromTimestamp(System.currentTimeMillis() - 1000);
        return consumer;
    }

    private static class sinkOutputProcess extends ProcessFunction<String, String> {
        private static final long serialVersionUID = 736301995521703785L;

        @Override public void processElement(String value, Context ctx, Collector<String> out) throws Exception {
            ctx.output(redisSink, value);
            ctx.output(hbaseSink, value);
            out.collect(value);
        }
    }

}
