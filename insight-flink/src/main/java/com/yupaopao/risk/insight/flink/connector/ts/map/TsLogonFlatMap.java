package com.yupaopao.risk.insight.flink.connector.ts.map;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.constants.TsConstants;
import com.yupaopao.risk.insight.common.support.InsightFlinkUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;

import java.util.*;
import java.util.stream.Collectors;

public class TsLogonFlatMap extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {
    private static List<String> REGISTER_CODE = Arrays.asList("user-register", "user-login");

    @Override public void flatMap(Map<String, Object> value, Collector<Map<String, Object>> out) throws Exception {
        if (REGISTER_CODE.contains(value.get("eventCode"))) {
            Map<String, Object> result = new HashMap<>(32);
            result.put("pkDatePartition", value.get("pkDatePartition"));
            result.put(TsConstants.RISK_USER_DEFINE_DATA_UUID, InsightFlinkUtils.getUUID());
            result.put("traceId", value.get("traceId"));
            result.put("createdAt", value.get("createdAt"));
            result.put("uid", value.get("userId"));
            result.put("mobile", value.get("data_Mobile"));
            result.put("deviceId", value.get("data_DeviceId"));
            result.put("ip", value.get("data_ClientIp"));
            result.put("country", value.get("data_clientIpDetail_country"));
            result.put("province", value.get("data_clientIpDetail_province"));
            result.put("city", value.get("data_clientIpDetail_city"));
            result.put("isp", value.get("data_clientIpDetail_isp"));
            result.put("platform", value.get("data_Platform"));
            result.put("level", value.get("level"));
            result.put("reply", value.get("reply"));
            result.put("eventCode", value.get("eventCode"));

            String hits;
            if ("user-register".equals(value.get("eventCode"))) {
                result.put("checkModel", value.get("data_registerCheck_detail_model"));
                result.put("checkDescription", value.get("data_registerCheck_detail_description"));
                result.put("checkDescriptionV2", value.get("data_registerCheck_detail_descriptionV2"));
                hits = (String) value.get("data_registerCheck_detail_hits");
            } else {
                result.put("checkModel", value.get("data_loginCheck_detail_model"));
                result.put("checkDescription", value.get("data_loginCheck_detail_description"));
                result.put("checkDescriptionV2", value.get("data_loginCheck_detail_descriptionV2"));
                hits = (String) value.get("data_loginCheck_detail_hits");
            }

            List<JSONObject> hitArray = JSONObject.parseArray(hits, JSONObject.class);

            if (null == hitArray) {
                out.collect(result.keySet().stream().filter(p -> null != result.get(p)).collect(Collectors.toMap(p -> p, result::get)));
            } else {
                for (JSONObject hitObject : hitArray) {
                    result.put("hitModel", hitObject.getString("model"));
                    result.put("hitScore", hitObject.getString("score"));
                    result.put("hitRiskLevel", hitObject.getString("riskLevel"));
                    result.put("hitDescription", hitObject.getString("description"));
                    result.put("hitDescriptionV2", hitObject.getString("descriptionV2"));
                    out.collect(result.keySet().stream().filter(p -> null != result.get(p)).collect(Collectors.toMap(p -> p, result::get)));
                }
            }
        }
    }
}
