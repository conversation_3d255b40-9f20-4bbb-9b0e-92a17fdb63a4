package com.yupaopao.risk.insight.flink.connector.mysql.sink;

import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.configuration.Configuration;

import java.io.IOException;
import java.sql.*;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public class MysqlBaseOutputFormat extends RichOutputFormat<String> {

    private Connection connection = null;
    private PreparedStatement ps = null;
    private String tableName;
    private DBProperties dbProperties;
    private final long BUFFER_FLUSH_COUNString = 10;
    private transient AtomicLong numPendingRequests;

    public MysqlBaseOutputFormat(String tableName, DBProperties dbProperties) {
        this.tableName = tableName;
        this.dbProperties = dbProperties;
    }

    @Override public void configure(Configuration parameters) {

    }

    @Override public void open(int taskNumber, int numStringasks) throws IOException {
        connection = getConnection();
        judgeIsExit();
        this.numPendingRequests = new AtomicLong(0);
    }

    @Override public void writeRecord(String record) throws IOException {
        try {
            ps.addBatch(record);
            flush();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    @Override public void close() throws IOException {
        try {
            ps.cancel();
            connection.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public boolean canFlush() {
        return numPendingRequests.incrementAndGet() <= BUFFER_FLUSH_COUNString;
    }

    public void flush() throws IOException {
        try {
            if (canFlush()) {
                ps.executeBatch();
            }
            numPendingRequests.set(0);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    private void judgeIsExit() {
        try {
            ps = connection.prepareStatement(String.format("select * from information_schema.tables where table_name =%s", tableName));
            ResultSet resultSet = ps.executeQuery();
            if (!resultSet.next()) {
                log.info("{} 不存在", tableName);
                throw new RuntimeException("table is not exist");
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }

    }

    private Connection getConnection() {
        try {
            Class.forName(dbProperties.getDriver());
            connection = DriverManager.getConnection(dbProperties.getUrl(), dbProperties.getUsename(), dbProperties.getPassword());
        } catch (Exception e) {
            log.info("connect to mysql error : ", e);
        }
        return connection;
    }
}
