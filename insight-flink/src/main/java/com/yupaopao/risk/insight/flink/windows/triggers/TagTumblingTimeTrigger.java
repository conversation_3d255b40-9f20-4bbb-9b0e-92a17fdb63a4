package com.yupaopao.risk.insight.flink.windows.triggers;

import com.yupaopao.risk.insight.flink.windows.AggTagDetail;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;

public class TagTumblingTimeTrigger extends Trigger<AggTagDetail, TimeWindow> {
    @Override public TriggerResult onElement(AggTagDetail element, long timestamp, TimeWindow window, TriggerContext ctx) throws Exception {
        if (window.maxTimestamp() <= ctx.getCurrentWatermark()) {
            // 过期数据直接丢弃
            return TriggerResult.CONTINUE;
        }
        if (element.isPurge()) {
            return TriggerResult.PURGE;
        }

        ctx.registerEventTimeTimer(window.maxTimestamp());
        timestamp = ctx.getCurrentProcessingTime();
        if ((window.getEnd() - timestamp) <= 24 * 60 * 60 * 1000) {
            return TriggerResult.FIRE;
        } else {
            return TriggerResult.CONTINUE;
        }
    }

    @Override public TriggerResult onProcessingTime(long time, TimeWindow window, TriggerContext ctx) throws Exception {
        return TriggerResult.CONTINUE;
    }

    @Override public TriggerResult onEventTime(long time, TimeWindow window, TriggerContext ctx) throws Exception {
        return TriggerResult.CONTINUE;
    }

    @Override public void clear(TimeWindow window, TriggerContext ctx) throws Exception {
        ctx.deleteProcessingTimeTimer(window.maxTimestamp());
    }

    @Override
    public boolean canMerge() {
        return true;
    }

    @Override
    public void onMerge(TimeWindow window,
        OnMergeContext ctx) {
        // only register a timer if the time is not yet past the end of the merged window
        // this is in line with the logic in onElement(). If the time is past the end of
        // the window onElement() will fire and setting a timer here would fire the window twice.
        long windowMaxTimestamp = window.maxTimestamp();
        if (windowMaxTimestamp > ctx.getCurrentProcessingTime()) {
            ctx.registerProcessingTimeTimer(windowMaxTimestamp);
        }
    }

    public static TagTumblingTimeTrigger create() {
        return new TagTumblingTimeTrigger();
    }
}
