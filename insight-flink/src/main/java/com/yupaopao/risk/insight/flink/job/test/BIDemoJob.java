package com.yupaopao.risk.insight.flink.job.test;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.StringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.connector.ts.util.EsUtil;
import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.Data;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.java.tuple.Tuple;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.windowing.WindowFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.connectors.elasticsearch.ElasticsearchSinkFunction;
import org.apache.flink.streaming.connectors.elasticsearch.RequestIndexer;
import org.apache.flink.streaming.connectors.elasticsearch6.ElasticsearchSink;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.Requests;
import org.elasticsearch.common.xcontent.XContentType;

import java.util.Collections;
import java.util.List;
import java.util.Properties;

/**
 * Created by Avalon on 2020/2/25 14:36
 */
public class BIDemoJob {

    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env =
                StreamExecutionEnvironment.getExecutionEnvironment();

        //load property from apollo
        KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA);
        Properties etlProperties = kafkaProperties.getProperties();
        etlProperties.put("group.id", "risk-insight-risk_hit_log_etl_es_demo");

        EsProperties esProperties = EsProperties.getProperties(PropertyType.ES);

        List<String> topics = Collections.singletonList("RISK-ONLINE-RESULT-LOG");
        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics,
                new StringKafkaDeserializationSchemaWrapper(new SimpleStringSchema()),
                etlProperties);


        ElasticsearchSink.Builder<LevelDetail> esSinkBuilder = new ElasticsearchSink.Builder<>(
                EsUtil.parseHostList(esProperties),
                new ElasticsearchSinkFunction<LevelDetail>() {
                    @Override
                    public void process(LevelDetail element, RuntimeContext ctx, RequestIndexer indexer) {
                        indexer.add(createIndexRequest(element));
                    }

                    public IndexRequest createIndexRequest(LevelDetail element) {

                        return Requests.indexRequest()
                                .index("bi_demo")
                                .type("V1")
                                .source(JSONObject.toJSONString(element), XContentType.JSON);
                    }
                }
        );

        esSinkBuilder.setBulkFlushMaxActions(1);

        esSinkBuilder.setRestClientFactory(builder -> EsUtil.getClientBuilder(esProperties));


        env.addSource(consumer)
                .map((MapFunction<String, LevelDetail>) value -> {
                    JSONObject jsonObject = JSONObject.parseObject(value);
                    LevelDetail detail = new LevelDetail();
                    detail.setRiskLevel(jsonObject.getString("level"));
                    detail.setCount(1);
                    return detail;
                })
                .keyBy("riskLevel")
                .window(TumblingProcessingTimeWindows.of(Time.seconds(30)))
                .apply(new WindowFunction<LevelDetail, LevelDetail, Tuple, TimeWindow>() {
                    @Override
                    public void apply(Tuple tuple, TimeWindow window, Iterable<LevelDetail> input, Collector<LevelDetail> out) throws Exception {
                        int count = 0;
                        String riskLevel = "";
                        for (LevelDetail detail : input) {
                            count += detail.getCount();
                            riskLevel = detail.getRiskLevel();
                        }
                        LevelDetail detail = new LevelDetail();
                        detail.setCreatedAt(window.maxTimestamp());
                        detail.setRiskLevel(riskLevel);
                        detail.setCount(count);
                        out.collect(detail);
                    }
                })
                .addSink(esSinkBuilder.build())
//                .print()
                .name("BI Demo Job")
                .setParallelism(1);

        env.execute("BI Demo");


    }

    @Data
    public static class LevelDetail {

        private long createdAt;

        private String riskLevel;

        private Integer count;
    }
}
