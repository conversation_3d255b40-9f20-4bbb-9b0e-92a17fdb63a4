package com.yupaopao.risk.insight.flink.bean.portrait;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OpenAiInputVO implements Serializable {
    private static final long serialVersionUID = -4173280817918668942L;

    private Integer bizType;
    private String ObjectId;
    private List<InputMess> inputMess;
    private String eventType;
    private Long time;
    private String extInfo;
}
