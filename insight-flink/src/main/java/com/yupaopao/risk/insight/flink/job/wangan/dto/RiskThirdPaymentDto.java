package com.yupaopao.risk.insight.flink.job.wangan.dto;

import lombok.Data;

import java.util.Date;

@Data
public class RiskThirdPaymentDto {

    private String payUser;
    private String userId;
    private String productName;
    private String productAmount;
    private String orderNo;
    private String payNo;
    private String orderPayChannel;
    private String receivePayChannel;
    private String merchantId;
    private String payAmount;
    private String payTime;
    private String clientIp;
    private String deviceId;
    private String terminalType;
    private String terminalVersion;
    private String terminalOsType;
    private String terminalOsVersion;


}
