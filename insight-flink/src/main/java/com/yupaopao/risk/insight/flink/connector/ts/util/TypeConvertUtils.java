package com.yupaopao.risk.insight.flink.connector.ts.util;

//import com.alibaba.fastjson.JSON;
//import com.alicloud.openservices.tablestore.model.*;
//import com.github.wnameless.json.flattener.FlattenMode;
//import com.yupaopao.risk.insight.flink.meta.FlinkMetaInfo;
//import com.yupaopao.risk.insight.flink.utils.TsFlinkTypeUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.flink.table.types.DataType;
//
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
//@Slf4j
//public class TypeConvertUtils {

//    /***
//     * 查询的flink结果表数据转为tableStore数据
//     * @param record
//     * @return
//     */
//    public static Row transFlinkRowToTsRowIgnorePk(org.apache.flink.types.Row record, FlinkMetaInfo schema) {
//        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        primaryKeyBuilder.addPrimaryKeyColumn("NoOP", PrimaryKeyValue.fromString("NoOP"));
//        PrimaryKey key = primaryKeyBuilder.build();
//        List<Column> columns = new ArrayList<>();
//        int columnSize = record.getArity();
//        List<String> names = new ArrayList<>(columnSize);
//        String[] fieldNames = schema.getFieldNames();
//        for (int i = 0; i < fieldNames.length; i++) {
//            DataType dataType = schema.getFieldDataType(i);
//            ColumnValue columnValue = TsFlinkTypeUtils.getColumnValueFromFlink(record.getField(i), dataType);
//            if(columnValue==null){
//                continue;
//            }
//            columns.add(new Column(fieldNames[i], columnValue));
//        }
//        Row row = new Row(key, columns);
//        return row;
//    }
//
//
//    /***
//     * json串转为 tablestore 行
//     * @param json
//     * @return
//     */
//    public static Row transJsonToTsRow(String json) {
//        //json扁平化处理
//        Map<String, Object> flattenMap = JsonFlatterUtils.toMap(json, FlattenMode.KEEP_ARRAYS);
//        //为构建row用，实际在相应的format中处理
//        PrimaryKeyBuilder primaryKeyBuilder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        primaryKeyBuilder.addPrimaryKeyColumn("NoOP", PrimaryKeyValue.fromString("NoOP"));
//        PrimaryKey key = primaryKeyBuilder.build();
//
//        List<Column> columns = new ArrayList<>();
//        for (Map.Entry<String, Object> entry : flattenMap.entrySet()) {
//            ColumnValue columnValue = jsonTypeToColumnValue(entry.getValue());
//            if (columnValue == null) {
//                continue;
//            }
//            columns.add(new Column(entry.getKey(), columnValue));
//        }
//        Row row = new Row(key, columns);
//        return row;
//    }
//
//    /***
//     * the json type see {@link com.github.wnameless.json.flattener.JsonFlattener#jsonVal2Obj}
//     * @param valueObj
//     * @return
//     */
//    private static ColumnValue jsonTypeToColumnValue(Object valueObj) {
//        if (null == valueObj) {
//            return null;
//        }
//        //ts 支持的类型：
//        if (valueObj instanceof List) {
//            return ColumnValue.fromString(JSON.toJSONString(valueObj));
//        } else if (valueObj instanceof String) {
//            return ColumnValue.fromString((String) valueObj);
//        } else if (valueObj instanceof Boolean) {
//            return ColumnValue.fromBoolean((Boolean) valueObj);
//        } else if (valueObj instanceof BigDecimal) {
//            BigDecimal value = (BigDecimal) valueObj;
//            boolean isDouble = value.toPlainString().contains("\\.");
//            if (isDouble) {
//                return ColumnValue.fromDouble(value.doubleValue());
//            } else {
//                return ColumnValue.fromLong(value.longValue());
//            }
//        } else {
//            if (valueObj instanceof Map && ((Map) valueObj).isEmpty()) {
//                return null;
//            }
//            log.info("not support type: {}, data= {}", valueObj.getClass(), valueObj);
//        }
//        return null;
//    }
//
//
//    /***
//     * 把 tableStore中的数据按照flink table 的类型读出来
//     * 需要配置本次的查询类型
//     * @param record
//     * @param tableSchema
//     * @return
//     */
//    public static org.apache.flink.types.Row extractNextRecord(Row record, FlinkMetaInfo tableSchema) {
//        org.apache.flink.types.Row resultRow = new org.apache.flink.types.Row(tableSchema.getFieldNames().length);
//        // clear row
//        for (int i = 0; i < resultRow.getArity(); i++) {
//            resultRow.setField(i, null);
//        }
//
//        //table store 原表所有列,排除主键
//        Map<String, Column> recordColumnMap = TsUtils.recordMap(record);
//
//        //提取flink需要的列
//        //table store row to flink row
//        //String columnNames[] = tableSchema.getFieldNames();
//        for (int i = 0; i < tableSchema.getFieldNames().length; i++) {
//            DataType columnType = tableSchema.getFieldDataType(i);
//            String columnName = tableSchema.getFieldNames()[i];
//            PrimaryKeyColumn primaryKeyColumn = record.getPrimaryKey().getPrimaryKeyColumn(columnName);
//            if (null != primaryKeyColumn) {
//                Object obj = TsFlinkTypeUtils.getValueFromPkValue(primaryKeyColumn.getValue(), columnType);
//                resultRow.setField(i, obj);
//                continue;
//            }
//            Column column = recordColumnMap.get(columnName);
//            if (column != null) {
//                Object obj = TsFlinkTypeUtils.getValueFromColumnValue(column.getValue(), columnType);
//                resultRow.setField(i, obj);
//            }
//        }
//        return resultRow;
//    }


//}
