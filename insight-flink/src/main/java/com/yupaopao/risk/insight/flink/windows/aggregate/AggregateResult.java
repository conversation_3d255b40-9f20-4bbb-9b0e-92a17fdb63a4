package com.yupaopao.risk.insight.flink.windows.aggregate;

import lombok.Getter;
import lombok.Setter;
import org.roaringbitmap.longlong.Roaring64NavigableMap;

import java.io.Serializable;
import java.util.Set;

@Getter
@Setter
public class AggregateResult implements Serializable {
    /**
     * 累计因子key
     */
    private String key;
    /**
     * 累计因子数值
     */
    private Double result = 0.0;
    /**
     * 数量，均值使用
     */
    private int count = 0;
    /**
     * distinct 存储，distinct 使用
     */
    private Set<String> distinct;
    /**
     * distinct bitmap 存储，distinct count 使用
     */
    private Roaring64NavigableMap bitMap;
    /**
     * 累计因子时间跨度
     */
    private Long timeSpan;
    /**
     * 是否清理该因子
     */
    private boolean purge;
//    /**
//     * 是否首次计算，distinct count 使用
//     */
//    private boolean isFirst = true;
//    /**
//     * 最新数据，distinct count 使用，减少 redis 操作
//     */
//    private String lastValue;
}
