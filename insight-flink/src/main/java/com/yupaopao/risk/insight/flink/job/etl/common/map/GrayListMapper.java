package com.yupaopao.risk.insight.flink.job.etl.common.map;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.support.InsightFlinkUtils;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.dubbo.core.DubboConfigConstants;
import com.yupaopao.risk.insight.flink.job.etl.common.options.MapperConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.qos.server.Server;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.dubbo.rpc.service.GenericService;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.util.*;

/****
 * zengxiangcai
 * 2023/5/11 12:00
 ***/

@Slf4j
public class GrayListMapper extends BaseMapFunction {

    private static final String DIMENSION_GROUPS_KEY = "dimensionGroups";
    private static final String DIMENSION_FIELD_MAPPING_KEY = "dimensionFieldMapping";

    private ReferenceConfig<GenericService> reference;

    private GenericService genericService;

    LongCounter readerCounter;

    LongCounter writerCounter;

    private String dimensionGroups;
    private String dimensionFieldMapping;

    private Map<String, String> dimensionGroupMaps;

    private Map<String, String> dimensionFieldMaps;

    public GrayListMapper(MapperConfig config) throws Exception {
        super(config);
        dimensionGroups = config.getStringVal(DIMENSION_GROUPS_KEY); //各维度过滤的名单组
        dimensionFieldMapping = config.getStringVal(DIMENSION_FIELD_MAPPING_KEY); //各维度对应的字段

    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        reference = new ReferenceConfig<>();
        reference.setInterface("com.yupaopao.risk.shoot.api.RiskListService");

        reference.setVersion(FlinkConstants.getDubboVersion());
        reference.setProtocol(DubboConfigConstants.PROTOCOL);
        reference.setApplication(new ApplicationConfig(DubboConfigConstants.APPLICATION));
        reference.setTimeout(30000);
        Server.getInstance().stop();

        RegistryConfig registryConfig = new RegistryConfig();
        registryConfig.setAddress(ApolloProperties.getConfigStr("zkUrl"));
        reference.setRegistry(registryConfig);

        reference.setGeneric("true");
        genericService = reference.get();

        readerCounter = context.getLongCounter("reader_count_" + id);
        writerCounter = context.getLongCounter("writer_count_" + id);

        mapperMetric.addMetric("reader_count_" + id, readerCounter);
        mapperMetric.addMetric("writer_count_" + id, writerCounter);

        dimensionGroupMaps = JSON.parseObject(dimensionGroups, Map.class);
        dimensionFieldMaps = JSON.parseObject(dimensionFieldMapping, Map.class);
        log.info("dimensionGroupMaps: {} , dimensionFieldMaps: {}", JSON.toJSONString(dimensionGroupMaps),
                JSON.toJSONString(dimensionFieldMaps));
    }

    @Override
    public void flatMap(Map<String, Object> value, Collector<Map<String, Object>> out) throws Exception {
        super.flatMap(value, out);
        readerCounter.add(1);

        String[] argTypes = new String[]{"java.util.Set", "com.yupaopao.risk.common.enums.GrayListType", "com.yupaopao" + ".risk.common.enums.Dimensions", "java.lang.String"};
        Object[] data = new Object[4];
        //如果过滤多维度的白名单，只要有一个维度为true就不输出
        for (Map.Entry<String, String> entry : dimensionGroupMaps.entrySet()) {
            try {
                String dimension = entry.getKey();
                String groups = entry.getValue();
                Set<Long> groupSet = new HashSet<>();
                Arrays.stream(groups.split("#")).forEach(groupId -> groupSet.add(Long.valueOf(groupId)));
                data[0] = groupSet;
                data[1] = "WHITE";
                data[2] = dimension;
                //维度对应的字段
                String filed = dimensionFieldMaps.get(dimension);
                if (StringUtils.isEmpty(filed)) {
                    continue;
                }
                Object dataValue = value.get(filed.trim());
                if (dataValue == null) {
                    continue;
                }
                data[3] = String.valueOf(dataValue);
                RpcContext.getContext().setAttachment("application", DubboConfigConstants.APPLICATION);
                RpcContext.getContext().setAttachment("dubboApplication", DubboConfigConstants.APPLICATION);
                Object dubboResultObj = genericService.$invoke("isHit", argTypes, data);
                if (dubboResultObj != null && dubboResultObj instanceof Boolean && (Boolean) dubboResultObj) {
                    //白名单
                    log.info("ignore white data: " + dataValue);
                    return;
                }
            } catch (Exception e) {
                log.info("check grayList error,param=" + value, e);
                return;
            }
        }

        out.collect(value);
        writerCounter.add(1);
    }

}
