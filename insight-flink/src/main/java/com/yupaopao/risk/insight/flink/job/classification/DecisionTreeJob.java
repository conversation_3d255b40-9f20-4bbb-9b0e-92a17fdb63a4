package com.yupaopao.risk.insight.flink.job.classification;

import com.alibaba.alink.common.MLEnvironment;
import com.alibaba.alink.common.utils.DataSetConversionUtil;
import com.alibaba.alink.common.utils.DataSetUtil;
import com.alibaba.alink.operator.batch.BatchOperator;
import com.alibaba.alink.operator.common.tree.TreeModelInfo;
import com.alibaba.alink.params.shared.tree.HasIndividualTreeType;
import com.alibaba.alink.pipeline.classification.DecisionTreeClassifier;
import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.job.base.ALinkJobBuilder;
import com.yupaopao.risk.insight.flink.utils.InnerFlinkJobInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.java.tuple.Tuple18;
import org.apache.flink.core.fs.FileSystem;
import org.apache.flink.table.api.Table;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/****
 * zengxiangcai
 * 2023/10/19 17:56
 * decision-tree-analysis.jar
 ***/

@Slf4j
public class DecisionTreeJob {

    public static void main(String[] args) throws Exception {
        new ALinkJobBuilder()
                .withJobName("decision tree")
                .withProcessor(new DecisionTreeProcessor())
                .start(args)
//                .start(new String[]{testParam()});

        ;
    }
    private static String testParam() throws Exception {
        Map<String, String> argMap = new HashMap<>();
        argMap.put("taskId", "30004");
        argMap.put("activeDate", "2024-01-11");
        argMap.put("batchId", "bad5ed9648984ad99ca9d27189ec8947");
        argMap.put("runId", UUID.randomUUID().toString().replaceAll("-",""));
//        argMap.put("excludeFeatures","devicePortrait,userIdBehaviorIpClusteringReward,mobileRegisterCnt1d," +
//                "mobileRegisterCnt7d,deviceIdCpuAnomaly,deviceIdSignFail,deviceIdSignFailDetail," +
//                "deviceRegisterSuccessCnt1d,userRelateProvinceAggLevel,userIdRelateIpCityCnt1d,nobilityLevel," +
//                "deviceLoginFreqAbnormalLevel,cardRiskTagV2,deviceLoginFreqAbnormalLevel,userIdRelateIpCityCnt3d," +
//                "userBeComplainCount7d,fun_latestActiveTime,fun_nation,userBeComplainAdoptCount7d,bRoot,fun_registerTime,userRelateCityAggLevel");
//        argMap.put("excludeFeatures", "userBeComplainCount7d,fun_latestActiveTime,fun_nation,userBeComplainAdoptCount7d,bRoot," +
//                "fun_rawdata_isRoot," +
//                "userIdRelateIpCityCnt3d");
        String jsonParam = URLEncoder.encode(JSON.toJSONString(argMap), "UTF-8");
        System.err.println("runId: "+argMap.get("runId"));
        System.err.println(jsonParam);
        return jsonParam;

    }

    public static class DecisionTreeProcessor implements ALinkJobBuilder.MainProcessor {


        @Override
        public void internalProcess(MLEnvironment mlEnv, Long mlSessionId, Map<String, String> argMap) throws Exception {

            mlEnv.getExecutionEnvironment().setParallelism(1);

            String taskId = argMap.get("taskId"); //本次运行的taskId
            String activeDate = argMap.get("activeDate"); //样本活跃时间yyyy-MM-dd
            String batchId = argMap.get("batchId"); //样本batchId
            String excludeFeatures = argMap.get("excludeFeatures");//逗号隔开的本次排除特征
            String runId = argMap.get("runId");
            String treeDepth = argMap.get("treeDepth");
            if (StringUtils.isEmpty(treeDepth)) {
                treeDepth = "6";
            }

            Set<String> excludeSet = new HashSet<>();
            if (StringUtils.isNotEmpty(excludeFeatures)) {
                Arrays.stream(excludeFeatures.trim().split(",")).forEach(elem -> excludeSet.add(elem));
            }

            String currentSituation = FeatureExtractor.getTaskSituation(taskId);
            argMap.put("currentSituation",currentSituation);

            List<FeatureExtractor.FlinkFeatureCfg> cfgList =
                    FeatureExtractor.getTaskAvailableFeatures(currentSituation).stream().filter(elem -> !excludeSet.contains(elem.getFeatureCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(cfgList)) {
                return;
            }

            Table dataTable = FeatureExtractor.getInputTable(mlEnv.getExecutionEnvironment(), mlSessionId, argMap, cfgList);


//            if (true) {
//                String path = "/Users/<USER>/Documents/决策树分析demo/v2/checkFeatures.csv";
//                DataSetConversionUtil.fromTable(mlSessionId, dataTable).writeAsText(path);
//                return;
//            }
            BatchOperator trainData = BatchOperator.fromTable(dataTable);
            // 所有特征
            List<String> features = cfgList.stream().map(elem -> elem.getFeatureCode()).collect(Collectors.toList()); //
            // 分类特征
            List<String> categories =
                    cfgList.stream().filter(elem -> elem.getFeatureType().equals("0")).map(elem -> elem.getFeatureCode()).collect(Collectors.toList());

            BatchOperator<?> opModel = new DecisionTreeClassifier()
                    .setPredictionDetailCol("pred_detail")
                    .setPredictionCol("pred")
                    .setMaxDepth(Integer.valueOf(treeDepth))
                    .setMaxLeaves(50)
                    .setMinSamplesPerLeaf(8)
//                    .setTreeType(HasIndividualTreeType.TreeType.GINI)
                    .setLabelCol("label")
                    .setWeightCol("weight")
                    .setFeatureCols(features.toArray(new String[0]))
                    .setCategoricalCols(categories.toArray(new String[0]))
//                    .enableLazyPrintModelInfo()
                    .fit(trainData)
                    .getModelData();


            //由于flink1.13之后无法使用collect后的数数据启动任务，打算线存储数据,然后读取构建策略树
            ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
            CKOutputFormat ckOutputFormat = new CKOutputFormat(ckProperties, "risk_model_info") {
                @Override
                public void finalizeGlobal(int parallelism) throws IOException {
                    //调用写入模型后处理数据
                    String jarName = "decision-tree-constructor.jar";
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("batchId", batchId);
                    paramMap.put("taskId", taskId);
                    paramMap.put("runId", runId);
                    String jsonParam = URLEncoder.encode(JSON.toJSONString(paramMap), "UTF-8");
                    InnerFlinkJobInvoker.invokeJob(jarName, jsonParam);
                }
            };
            opModel.getDataSet().map(elem -> {
                Map<String, Object> row = new HashMap<>();
                Object model_id = elem.getField(0);
                Object model_info = elem.getField(1);
                Object label_value = elem.getField(2);
                if (model_id != null) {
                    row.put("model_id", model_id);
                }
                if (model_info != null) {
                    row.put("model_info", model_info);
                }
                if (label_value != null) {
                    row.put("label_value", label_value);
                }
                row.put("runId", runId);
                row.put("batchId", batchId);
                row.put("taskId", taskId);
                return JSON.toJSONString(row);
            }).output(ckOutputFormat);

        }

    }


}
