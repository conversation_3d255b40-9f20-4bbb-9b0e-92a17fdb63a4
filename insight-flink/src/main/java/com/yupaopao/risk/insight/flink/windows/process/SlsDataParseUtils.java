package com.yupaopao.risk.insight.flink.windows.process;

/****
 * zengxiangcai
 * 2023/12/21 11:11
 ***/
public class SlsDataParseUtils {

    public static void main(String[] args) {
        String data = "{x-forwarded-for=***********, **************, ************, " +
                "x-sign=e7758ac2bc8ed5ebee0924b7eea50892, x-forwarded-cluster=waf,gf,, eagleeye-rpcid=0.1.1, x-true-ip=**************, remoteip=************, x-sinfo=on, x-equipment=windows, x-network=, content-length=121, x-authentication=133e098d2765ccc9df17a2901fab8d94, x-udid=6261c9c479379262a29ad806458117ff, x-client-time=1703126816000, host=api.hibixin.com, x-forwarded-proto=https, accept-language=zh-CN,en,*, user-agent=Mozilla/5.0, x-device=windows, eagleeye-traceid=ac11000117031268170807115eb6c5, accept-encoding=gzip, deflate, x-bundleid=pc.yupaopao.bixin, connection=close, x-real-ip=***********, content-type=application/json; charset=utf-8, cookie=aliyungf_tc=408396edfa04e0357dc52f6ae68709ba5aa8be2ca905b6f0b86dceaa92dc7bd6; acw_tc=ac11000117031267959311053e02e16afb70e8b6519c850724ff4ff1eeec99, wl-proxy-client-ip=**************, x-user-agent=mapi/1.0 (windows 10;pc.yupaopao.bixin *******;windows;qt-fusion) pc/1.0 (11th Gen Intel(R) Core(TM) i7-11370H @ 3.30GHz processors:8;physic:15G virtual:131071G), x5-uuid=5e960bd8e1f5cc2d78332b1089fdc733, x-client-ip=***********, x-vnum=*******, x-channel=qt-fusion, web-server-type=nginx}";
        parseHeader(data);
    }

    private static String parseHeader(String header){
        String headers = header.trim();
        String substring = headers.substring(1, headers.length() - 1);
        String[] split = substring.split(",");
        for(String s: split){

            if(!s.contains("x-user-agent")){
                continue;
            }
            int index = s.indexOf("pc/1.0");
            String subData = s.substring(index+7);
            subData.split(";");
            int processorIdx = subData.indexOf("processors");
            int physicIdx = subData.indexOf("physic");
            int virtualIdx = subData.indexOf("virtual");
            System.err.println(subData.substring(0,processorIdx));
            System.err.println("processor:"+subData.substring(processorIdx,physicIdx));;
            System.err.println("physic: "+subData.substring(physicIdx,virtualIdx));

        }
        return null;
    }
}
