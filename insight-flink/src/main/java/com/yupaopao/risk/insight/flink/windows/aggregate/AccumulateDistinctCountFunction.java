package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.yupaopao.risk.insight.common.beans.aggregate.AccumulateResult;
import com.yupaopao.risk.insight.common.property.FactorTimeProperties;
import com.yupaopao.risk.insight.flink.windows.AccumulateCalDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.roaringbitmap.longlong.Roaring64NavigableMap;

import java.util.HashSet;
import java.util.Set;

@Slf4j
public class AccumulateDistinctCountFunction implements AggregateFunction<AccumulateCalDetail, AccumulateResult, AccumulateResult> {

    @Override public AccumulateResult createAccumulator() {
        AccumulateResult result = new AccumulateResult();
        result.setDistinct(new HashSet<>());
        result.setResult(0.0);
        return result;
    }

    @Override public AccumulateResult add(AccumulateCalDetail value, AccumulateResult accumulator) {
        accumulator.setId(value.getId());
        accumulator.setKey(value.getGroupKey());
        accumulator.setTimeSpan(value.getTimeSpan());
        accumulator.setPurge(value.isPurge());
        accumulator.setThresholds(value.getThresholds());
        accumulator.setUid(value.getUid());
        accumulator.setDeviceId(value.getDeviceId());
        accumulator.setIp(value.getIp());
        accumulator.setAppId(value.getAppId());
        accumulator.setAppVersion(value.getAppVersion());

        try {
            long data = Long.parseLong(value.getData());
            Roaring64NavigableMap bitMap = accumulator.getBitMap();
            if (null == bitMap) {
                bitMap = new Roaring64NavigableMap();
            }
            if (!bitMap.contains(data)) {
                bitMap.add(data);
                accumulator.setResult(accumulator.getResult() + 1);
            }
            accumulator.setBitMap(bitMap);
        } catch (NumberFormatException e) {
            Set<String> distinct = accumulator.getDistinct();
            distinct.add(value.getData());
            accumulator.setResult((double) distinct.size());
        }
        return accumulator;
    }

    @Override public AccumulateResult getResult(AccumulateResult accumulator) {
        return accumulator;
    }

    @Override public AccumulateResult merge(AccumulateResult a, AccumulateResult b) {
        if (a.getKey().equals(b.getKey()) && a.getResult().equals(b.getResult())) {
            a.setResult(a.getResult() + b.getResult());
        }
        return a;
    }
}
