package com.yupaopao.risk.insight.flink.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-08-18 14:16
 * 从jobmanager启动flink job[根据jar文件]
 ***/

@Slf4j
public class InnerFlinkJobInvoker {

    public static void invokeJob(String jarName, String argJson) {
        log.info("start to execute jar: {}, params: {}", jarName, argJson);
        String flinkWebUrl = "http://localhost:8081";
        invoke(flinkWebUrl, jarName, argJson);
    }

    public static void invokeJobFromTaskManager(String jarName, String argJson) {
        log.info("start to execute jar from taskManager : {}", jarName);
        //"http://************:8081"
        String flinkWebUrl = ApolloProperties.getConfigStr("application","flinkWebUrlByIp");
        invoke(flinkWebUrl, jarName, argJson);
    }


    private static void invoke(String flinkWebUrl, String jarName, String argJson) {
        //完成调用另外一个任务
        OkHttpClient httpClient = new OkHttpClient.Builder()
                .connectTimeout(60, TimeUnit.SECONDS)//设置连接超时时间
                .readTimeout(60, TimeUnit.SECONDS)//设置读取超时时间
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
        Request request = new Request.Builder()
                .url(flinkWebUrl + "/jars")
                .get().build();
        Response response = null;
        String jarId = null;
        try {
            response = httpClient.newCall(request).execute();
            JSONObject json = JSONObject.parseObject(response.body().string());
            jarId = FastJsonUtils.read(json, "$.files[name = '" + jarName + "'][0].id", String.class);

            if (StringUtils.isEmpty(jarId)) {
                log.error("get empty jarId fro jarName:{}, ", jarName);
                return;
            }
            Map<String, Object> jsonParams = new HashMap<>();
            jsonParams.put("parallelism", null);
            jsonParams.put("programArgs", argJson);
            jsonParams.put("savepointPath", null);
            jsonParams.put("allowNonRestoredState", null);
            RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(jsonParams));

            Request executeJar = new Request.Builder()
                    .url(flinkWebUrl + "/jars/" + jarId + "/run")
                    .post(body).build();
            Response executeJarResponse = httpClient.newCall(executeJar).execute();
            String responseStr = executeJarResponse.body().string();
            log.info("the run result is: {}, for jarId: {}", responseStr, jarId);
        } catch (Exception e) {
            log.error("execute job: " + jarName + " error ", e);
            httpClient.connectionPool().evictAll();
            httpClient.dispatcher().executorService().shutdown();
        }

    }
}
