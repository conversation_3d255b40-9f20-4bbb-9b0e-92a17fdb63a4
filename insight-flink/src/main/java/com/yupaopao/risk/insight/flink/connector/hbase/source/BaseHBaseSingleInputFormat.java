package com.yupaopao.risk.insight.flink.connector.hbase.source;

import com.alibaba.lindorm.client.core.utils.Bytes;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.io.DefaultInputSplitAssigner;
import org.apache.flink.api.common.io.RichInputFormat;
import org.apache.flink.api.common.io.statistics.BaseStatistics;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.InputSplitAssigner;
import org.apache.hadoop.hbase.client.*;

import java.io.IOException;

@Slf4j
@Getter
@Setter
public abstract class BaseHBaseSingleInputFormat<T> extends RichInputFormat<T, HBaseSplit> {
    private static final long serialVersionUID = 2644705004653840023L;
    private HBaseProperties hBaseProperties;
    private transient Connection conn;
    private TsTableInfo tsTableInfo;
    private LongCounter readerCounter = new LongCounter();

    private boolean isFinish = false;

    protected ResultScanner resultScanner = null;
    protected transient Scan scan = null;
    protected byte[] currentRow;

    public BaseHBaseSingleInputFormat(TsTableInfo tsTableInfo, HBaseProperties hBaseProperties) {
        this.tsTableInfo = tsTableInfo;
        this.hBaseProperties = hBaseProperties;
    }

    @Override public BaseStatistics getStatistics(BaseStatistics cachedStatistics) throws IOException {
        return null;
    }

    @Override public InputSplitAssigner getInputSplitAssigner(HBaseSplit[] inputSplits) {
        return new DefaultInputSplitAssigner(inputSplits);
    }


    @Override
    public void open(HBaseSplit split) throws IOException {
        try {
            createConnection();
            this.scan = new Scan();
            scan.setCaching(500);

            byte[] family = Bytes.toBytes(HBaseConstants.COLUMN_FAMILY);
            if (tsTableInfo.getColumnNames() != null) {
                for (String column : tsTableInfo.getColumnNames()) {
                    scan.addColumn(family, Bytes.toBytes(column));
                }
            }

            if (null != currentRow) {
                log.info("init scan start row: {}", Bytes.toString(currentRow));
                scan.withStartRow(currentRow);
            }

            HTable table = HBaseUtil.getTable(tsTableInfo.getTableName(), createConnection());
            resultScanner = table.getScanner(scan);
            if (resultScanner == null) {
                log.warn("create scanner error");
            }

            if (getRuntimeContext().getAccumulator("hbaseReadRecords") == null) {
                getRuntimeContext().addAccumulator("hbaseReadRecords", this.readerCounter);
            } else {
                this.readerCounter = getRuntimeContext().getLongCounter("hbaseReadRecords");
            }
        } catch (Exception e) {
            log.error("open input format error: ", e);
            throw e;
        }
    }

    @Override public boolean reachedEnd() throws IOException {
        return isFinish;
    }

    @Override public T nextRecord(T reuse) throws IOException {
        Result next = resultScanner.next();
        T result = null;
        if (null != next) {
            readerCounter.add(1);
            result = parseResult(next);
            currentRow = next.getRow();
        } else {
            isFinish = true;
        }
        return result;
    }

    /**
     * 处理 hbase 结果
     * @param rowResult result
     * @return 处理后结果
     */
    public abstract T parseResult(Result rowResult);

    @Override
    public void closeInputFormat() throws IOException {
        HBaseUtil.closeResource(conn);
    }

    @Override public void close() throws IOException {
    }

    @Override public void configure(Configuration parameters) {
    }

    private Connection createConnection() throws IOException {
        if (conn != null) {
            return conn;
        }
        conn = HBaseUtil.createConnection(hBaseProperties);
        return conn;
    }
}
