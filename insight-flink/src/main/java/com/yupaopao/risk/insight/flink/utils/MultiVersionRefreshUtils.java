package com.yupaopao.risk.insight.flink.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.lindorm.client.core.utils.Bytes;
import com.google.common.collect.Maps;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.common.groovy.GroovyCommonExecutor;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseSupport;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Put;

import java.util.HashMap;
import java.util.Map;

import static com.yupaopao.risk.insight.common.constants.HBaseConstants.COLUMN_FAMILY;

/****
 * zengxiangcai
 * 2023/12/28 14:15
 * 多维标签刷新处理
 ***/

@Slf4j
public class MultiVersionRefreshUtils {
    public static final String ZOMBIE_USER = "zombieUser";
    public static final String USER_RISK_LEVEL = "userRiskLevel";

    public static byte[] columnFamily = Bytes.toBytes(COLUMN_FAMILY);
    private static HTable userTagTable = null;

    static {
        try {
            userTagTable = HBaseUtil.getTable("risk_user_tag",
                    HBaseUtil.createConnection(HBaseProperties.getProperties(PropertyType.HBASE)));
        } catch (Exception e) {
            log.error("create htable error", e);
        }
    }


    /***
     * 当前主要针对僵尸用户更新后需要处理userRiskLevel(不处理容易引起审核测客户投诉)
     * @param currentTag
     * @param columnValue
     * @param put
     * @return
     */
    public static void refreshUserRiskLevelForZombieUser(String currentTag, byte[] columnValue, Put put) {
        String rowKey = Bytes.toString(put.getRow());
        //僵尸用户标签特殊处理
        if (ZOMBIE_USER.equalsIgnoreCase(currentTag)) {
            Map<String, Object> existParams = new HashMap<>();
            existParams.put(ZOMBIE_USER, Bytes.toBoolean(columnValue) ? 1 : 0);

            byte[] userRiskLevelBytes = buildUserRiskLevel(existParams, rowKey);
            if (userRiskLevelBytes != null) {
                log.info("refresh userRiskLevel for : {} , newValue: {}",rowKey, Bytes.toLong(userRiskLevelBytes));
                put.addColumn(columnFamily, Bytes.toBytes(USER_RISK_LEVEL), userRiskLevelBytes);
            }
        }
    }


    private static byte[] buildUserRiskLevel(Map<String, Object> existParams, String userId) {
        TagInfoBO tagInfoBO = TagCacheSupport.getTagInfoByCode(USER_RISK_LEVEL);
        if (tagInfoBO == null || StringUtils.isEmpty(tagInfoBO.getDataContent())) {
            return null;
        }
        JSONObject dataContent = JSON.parseObject(tagInfoBO.getDataContent());

        String eventCode = dataContent.getString("eventCode");
        String condition = dataContent.getString("condition");
        String[] dependentTags = eventCode.split(",");

        //hbase查询其他依赖字段
        Map<String, Object> hbaseParams = null;
        try {
            hbaseParams = HBaseSupport.getRow(userId, userTagTable);
        } catch (Exception e) {
            log.error("buildUserRiskLevel get hbase row error,userId=" + userId, e);
        }
        for (String field : dependentTags) {
            if (existParams.containsKey(field)) {
                continue;
            }
            //如果field是map或者set类型，因为计算比较复杂，暂时不支持
            TagInfoBO tmpDependent = TagCacheSupport.getTagInfoByCode(field);
            if (tmpDependent == null || "Map".equalsIgnoreCase(tmpDependent.getValueType()) || "Set".equalsIgnoreCase(tmpDependent.getValueType())) {
                return null;
            }
            Object fieldValue = hbaseParams != null ? hbaseParams.get(field) : null;
            existParams.put(field, fieldValue);

        }
        //计算userRiskLevel
        try {
            Object userRiskLevelValue = GroovyCommonExecutor.getResult(USER_RISK_LEVEL + condition, condition, existParams);
            if (userRiskLevelValue == null) {
                return null;
            } else {
                long v = Long.valueOf(userRiskLevelValue.toString());
                return Bytes.toBytes(v);
            }
        } catch (Exception e) {
            log.error("execute user riskLevel error, userId = " + userId + ", params=" + JSON.toJSONString(existParams), e);
            return null;
        }


    }

    public static void main(String[] args) {
        Map<String, Object> existParams = new HashMap<>();
        existParams.put(ZOMBIE_USER, true ? 1 : 0);

        String eventCode = "userRiskLevel,newUsers,mediaUsers,governmentUsers,zombieUser,userIdRechargeAmount30d,userNobilityLevel,auditPornographyRiskUserId30d,hitHighRiskRuleUserBase,comlplainedByMultiUserAndPunishedBase,complainedByMultiRiskAndPunishedBase,overseasIPRegisteredUserId,specialRiskUserId,muteForeverUser,suspectUnderAgeUserPunishedBySelfExposeLive,userPunishedBySelfExposeUnderAgeLive,userPunishedBySelfExposeUnderAgeBase,punishedBySheZhengLive,tempUnderAgeDegrade,advertiseGang";
        String[] dependentTags = eventCode.split(",");
        Map<String, Object> hbaseParams = Maps.newHashMap();
        ;


        String condition = "if((advertiseGang!=null && advertiseGang==1)||(mediaUsers!=null && mediaUsers==1)||(governmentUsers!=null && governmentUsers==1)||(auditPornographyRiskUserId30d!=null && auditPornographyRiskUserId30d==1)|| (hitHighRiskRuleUserBase!=null && hitHighRiskRuleUserBase==1)|| (comlplainedByMultiUserAndPunishedBase!=null && comlplainedByMultiUserAndPunishedBase==1)|| (complainedByMultiRiskAndPunishedBase!=null && complainedByMultiRiskAndPunishedBase==1) || (muteForeverUser!=null && muteForeverUser==1) || (suspectUnderAgeUserPunishedBySelfExposeLive!=null && suspectUnderAgeUserPunishedBySelfExposeLive==1 && tempUnderAgeDegrade!=null && tempUnderAgeDegrade!=1) || (userPunishedBySelfExposeUnderAgeLive!=null && userPunishedBySelfExposeUnderAgeLive==1 && tempUnderAgeDegrade!=null && tempUnderAgeDegrade!=1) || (userPunishedBySelfExposeUnderAgeBase!=null && userPunishedBySelfExposeUnderAgeBase==1 && tempUnderAgeDegrade!=null && tempUnderAgeDegrade!=1) || (punishedBySheZhengLive!=null && punishedBySheZhengLive==1))\n" +
                "{\n" +
                "return 400\n" +
                "}else if((userIdRechargeAmount30d!=null && userIdRechargeAmount30d>=1000) || (userNobilityLevel!=null && userNobilityLevel>=2)){\n" +
                "    return 0\n" +
                "}else if(newUsers!=null && newUsers==1){\n" +
                "    return 200\n" +
                "}else if ((overseasIPRegisteredUserId!=null && overseasIPRegisteredUserId==1) || (specialRiskUserId!=null && specialRiskUserId==1) || (zombieUser!=null && zombieUser==1)) {\n" +
                "    return 300\n" +
                "}\n" +
                "return 100";
        for (String field : dependentTags) {
            if (existParams.containsKey(field)) {
                continue;
            }
            Object fieldValue = hbaseParams != null ? hbaseParams.get(field) : null;
            existParams.put(field, fieldValue);
        }
        //计算userRiskLevel
        try {
            Object userRiskLevelValue = GroovyCommonExecutor.getResult(USER_RISK_LEVEL + condition, condition, existParams);
            if (userRiskLevelValue == null) {
                System.err.println("null");
            } else {
                long v = Long.valueOf(userRiskLevelValue.toString());
                System.err.println(Bytes.toBytes(v).length);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
