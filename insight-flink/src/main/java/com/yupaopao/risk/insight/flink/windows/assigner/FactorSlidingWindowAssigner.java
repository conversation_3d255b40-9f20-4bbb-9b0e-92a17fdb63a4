package com.yupaopao.risk.insight.flink.windows.assigner;

import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import com.yupaopao.risk.insight.flink.windows.triggers.FactorSlidingTimeTrigger;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.WindowAssigner;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.joda.time.DateTime;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class FactorSlidingWindowAssigner extends WindowAssigner<FactorCalDetail, TimeWindow> {
    private long slideAvgNum;

    public FactorSlidingWindowAssigner() {
        String slideAvg = PortraitApolloProperties.getConfigByKey("factor.slide.avg.count", "10");
        slideAvgNum = Long.parseLong(slideAvg);
    }

    @Override public Collection<TimeWindow> assignWindows(FactorCalDetail element, long timestamp, WindowAssignerContext context) {
        long size = element.getTimeSpan() * 60 * 1000;
        long slide = size / slideAvgNum;
        long currentTime = context.getCurrentProcessingTime();
//        long allowLateMs = 30 * 1000L; //允许数据延迟30秒
        timestamp = element.getRequestTime();

        List<TimeWindow> windows = new ArrayList<>((int) (size / slide));
        if (element.getWindowType() == 1) {
            long lastStart = TimeWindow.getWindowStartWithOffset(timestamp, 16 * 60 * 60 * 1000, slide);
            for (long start = lastStart; start > timestamp - size; start -= slide) {
                windows.add(new TimeWindow(start, start + size));
            }
        } else {
            long start = getWindowStart(timestamp, element.getTimeSpan());
            windows.add(new TimeWindow(start, start + size));
        }
        return windows.stream().filter(w -> w.maxTimestamp() >= currentTime).collect(Collectors.toList());

//        return windows;
    }

    @Override public Trigger<FactorCalDetail, TimeWindow> getDefaultTrigger(StreamExecutionEnvironment env) {
        return FactorSlidingTimeTrigger.create(slideAvgNum);
    }

    @Override public TypeSerializer<TimeWindow> getWindowSerializer(ExecutionConfig executionConfig) {
        return new TimeWindow.Serializer();
    }

    @Override public boolean isEventTime() {
        return false;
    }

    private long getWindowStart(long now, long timeSpan) {
        long start;
        DateTime dateTime = new DateTime(now);
        if (timeSpan < 60) {
            // 分钟级别，使用自带窗口对齐
            return TimeWindow.getWindowStartWithOffset(now, 0, timeSpan * 60 * 1000);
        } else if (timeSpan < 720) {
            // 小时级别，窗口从当前小时0分钟开始
            dateTime = dateTime.withSecondOfMinute(0).withMillisOfSecond(0).withMinuteOfHour(0);
        } else if (timeSpan < 1440) {
            // 半天级别，窗口从当前小时0分钟开始
            dateTime = dateTime.withSecondOfMinute(0).withMillisOfSecond(0).withMinuteOfHour(0);
            dateTime = dateTime.getHourOfDay() < 12 ? dateTime.withHourOfDay(0) : dateTime.withHourOfDay(12);
        } else if (timeSpan < 10080) {
            // 天级，窗口从当天0点开始
            dateTime = dateTime.withMillisOfDay(0);
        } else if (timeSpan < 40320) {
            // 周级，窗口从周一0点开始
            dateTime = dateTime.withDayOfWeek(1).withMillisOfDay(0);
        } else if (timeSpan < 518400) {
            // 月级，窗口从1号0点开始
            dateTime = dateTime.withDayOfMonth(1).withMillisOfDay(0);
        } else {
            // 年级，窗口从1月1日0点开始
            dateTime = dateTime.withMonthOfYear(1).withDayOfMonth(1).withMillisOfDay(0);
        }

        start = dateTime.getMillis();
        return start;
    }

}
