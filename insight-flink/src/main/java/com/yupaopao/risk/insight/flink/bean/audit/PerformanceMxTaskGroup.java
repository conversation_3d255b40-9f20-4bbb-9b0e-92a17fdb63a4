package com.yupaopao.risk.insight.flink.bean.audit;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.beanutils.PropertyUtils;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-04 14:29
 *
 ***/

@Getter
@Setter
public class PerformanceMxTaskGroup extends MxTaskGroup {

    public PerformanceMxTaskGroup(MxTaskGroup task) {
        try {
            PropertyUtils.copyProperties(this, task);
        } catch (Exception e) {
        }
        this.processCost = 0;
        this.wholeProcessCost = 0;
        this.flinkHandleTime = System.currentTimeMillis();
    }

    //人效指标,耗时秒数
    private long processCost;
    private long wholeProcessCost;

    private long flinkHandleTime; //flink处理时间

    private String flinkRemark;

}
