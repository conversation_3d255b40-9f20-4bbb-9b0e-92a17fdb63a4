package com.yupaopao.risk.insight.flink.job.wangan.process;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.yupaopao.platform.common.utils.EnDecryptUtil;
import com.yupaopao.risk.insight.flink.job.wangan.constant.TopicConstant;
import com.yupaopao.risk.insight.flink.job.wangan.dto.RiskLoginLogInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.util.Collector;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class LoginSendSupport {


    private WangAnHbaseTableSupport wangAnHbaseTableSupport;
    private LoginSendSupport(){}

    public LoginSendSupport(WangAnHbaseTableSupport wangAnHbaseTableSupport){
        this.wangAnHbaseTableSupport = wangAnHbaseTableSupport;
    }


    private  String passWord = "ICp#ffufJ#lMOA2n22";

    public void send(String topic, JSONObject messsage, Collector<Map> collector) throws Exception{
        try {
            if (!pass(topic)) {
                return;
            }
            log.debug("登陆消费:{}", messsage);
            RiskLoginLogInfo loginLogInfo = getLoginInfo(getCoverJSON(messsage));

            if (
                    StringUtils.isNotBlank(loginLogInfo.getAccount())
                            && StringUtils.isNotBlank(loginLogInfo.getUid())
                            && StringUtils.isNotBlank(loginLogInfo.getLoginTime())
                            && StringUtils.isNotBlank(loginLogInfo.getActionType())
                            && StringUtils.isNotBlank(loginLogInfo.getLoginIp())
                            && StringUtils.isNotBlank(loginLogInfo.getLoginSource())
                            && StringUtils.isNotBlank(loginLogInfo.getLoginMobile())
                            && StringUtils.isNotBlank(loginLogInfo.getTerminalType())
                            && StringUtils.isNotBlank(loginLogInfo.getTerminalVersion())
                            && StringUtils.isNotBlank(loginLogInfo.getTerminalOsType())
                            && StringUtils.isNotBlank(loginLogInfo.getTerminalOsVersion())
            ) {
                Map<String, String> map = new HashMap<>();
                map.put("module", "login");
                map.put("newJob", "true");
                map.put("riskLoginLogInfo", EnDecryptUtil.encrypt(JSONObject.toJSONString(loginLogInfo),passWord));
                Cat.logMetricForCount("wangan."+this.getClass().getName());
                log.debug("网安上报登录信息:{}", map);
                collector.collect(map);
            }
        } catch (Exception e) {
            log.error("上报聊天室信息错误:{},{}",topic, messsage, e);
        }
    }

    private boolean pass(String topic){
        if (TopicConstant.ACCOUNT.equals(topic)||TopicConstant.LOGIN_RECODE.equals(topic)||
                TopicConstant.T_LOGIN_DEVICE.equals(topic)) {
            return true;
        }
        return false;
    }

    private JSONObject getCoverJSON(JSONObject dataJSON){
       return WangAnHbaseSupport.covertData(dataJSON.getJSONArray("data"));
    }

    public JSONObject putHbase(JSONObject dataJSON, String topic){
        if (TopicConstant.LOGIN_RECODE.equals(topic)||
                TopicConstant.T_LOGIN_DEVICE.equals(topic)) {
            return null;
        }
        JSONObject jsonObject = WangAnHbaseSupport.covertData(dataJSON.getJSONArray("data"));
        String rowKey = jsonObject.getString("uid");
        if (!StringUtils.isEmpty(rowKey)){
            wangAnHbaseTableSupport.putRow(rowKey, topic, jsonObject);
        }
        return jsonObject;
    }

    private RiskLoginLogInfo getLoginInfo(JSONObject messsage) {
        RiskLoginLogInfo loginLogInfo = new RiskLoginLogInfo();
        loginLogInfo.setUid(messsage.getString("uid"));
        Map<String, Object> accountInfo = null;
        Map<String, Object> loginDevice = null;
        Map<String, Object> loginRecord = null;
        try {
            accountInfo = wangAnHbaseTableSupport.getRow(TopicConstant.ACCOUNT,loginLogInfo.getUid(),
                    Arrays.asList("uid", "mobile"));
            loginDevice = wangAnHbaseTableSupport.getRow(TopicConstant.T_LOGIN_DEVICE,loginLogInfo.getUid(),
                    Arrays.asList("uid", "os", "version", "equipment"));
            loginRecord = wangAnHbaseTableSupport.getRow(TopicConstant.LOGIN_RECODE,loginLogInfo.getUid(),
                    Arrays.asList("ip","create_time", "login_type","type"));
        } catch (Exception e) {
            log.warn("登录查询hbase错误:{}", messsage, e);
        }


        if (!CollectionUtils.isEmpty(accountInfo) && accountInfo.containsKey("mobile")) {
            loginLogInfo.setAccount(accountInfo.get("mobile")+"");
            loginLogInfo.setLoginMobile(accountInfo.get("mobile")+"");
        }
        if (!CollectionUtils.isEmpty(loginRecord)) {
            loginLogInfo.setLoginIp(loginRecord.containsKey("ip")?loginRecord.get("ip")+"":"");
            loginLogInfo.setLoginTime(loginRecord.containsKey("create_time")?loginRecord.get("create_time")+"":"");
            loginLogInfo.setLoginSource(loginRecord.containsKey("login_type")?loginRecord.get("login_type")+"":"");
            loginLogInfo.setActionType(loginRecord.containsKey("type")?loginRecord.get("type")+"":"");
        }
        if (!CollectionUtils.isEmpty(loginDevice)) {
            loginLogInfo.setTerminalVersion(loginDevice.containsKey("equipment")?loginDevice.get("equipment")+"":"");
            loginLogInfo.setTerminalOsType(loginDevice.containsKey("os")?loginDevice.get("os")+"":"");
            loginLogInfo.setTerminalOsVersion(loginDevice.containsKey("version")?loginDevice.get("version")+"":"");
        }
        loginLogInfo.setTerminalType("0");
        log.debug("登陆补全信息：{},{},{},{}", accountInfo,loginRecord,loginDevice,loginLogInfo);
        return loginLogInfo;
    }
}
