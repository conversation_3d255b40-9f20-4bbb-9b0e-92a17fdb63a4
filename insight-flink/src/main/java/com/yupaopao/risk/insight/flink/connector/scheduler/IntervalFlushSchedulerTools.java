package com.yupaopao.risk.insight.flink.connector.scheduler;

import com.yupaopao.risk.insight.common.thread.InsightFlinkThreadFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-07 20:35
 * 刷新scheduler
 ***/

@Slf4j
public class IntervalFlushSchedulerTools {

    public static ScheduledThreadPoolExecutor createScheduler(String threadPrefix, Integer intervalInMillis,
                                                              Runnable task) {
        ScheduledThreadPoolExecutor scheduler = (ScheduledThreadPoolExecutor) Executors.newScheduledThreadPool(1,
                new InsightFlinkThreadFactory(threadPrefix));
        scheduler.setExecuteExistingDelayedTasksAfterShutdownPolicy(false);
        scheduler.setContinueExistingPeriodicTasksAfterShutdownPolicy(false);

        scheduler.scheduleWithFixedDelay(task, 1000 * 60,
                intervalInMillis, TimeUnit.MILLISECONDS);
        return scheduler;
    }

    public static ScheduledThreadPoolExecutor createScheduler(String threadPrefix, Long initialDelay, Long intervalInMillis,
        Runnable task) {
        ScheduledThreadPoolExecutor scheduler = (ScheduledThreadPoolExecutor) Executors.newScheduledThreadPool(1,
            new InsightFlinkThreadFactory(threadPrefix));
        scheduler.setExecuteExistingDelayedTasksAfterShutdownPolicy(false);
        scheduler.setContinueExistingPeriodicTasksAfterShutdownPolicy(false);

        scheduler.scheduleWithFixedDelay(task, initialDelay,
            intervalInMillis, TimeUnit.MILLISECONDS);
        return scheduler;
    }

    public static void closeScheduler(ScheduledThreadPoolExecutor scheduler) {
        if (scheduler == null) {
            return;
        }
        try {
            scheduler.shutdown();
            scheduler.awaitTermination(1000 * 10, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            log.warn("shutdown scheduler error ", e);
        } finally {
            scheduler.shutdownNow();
        }
    }
}
