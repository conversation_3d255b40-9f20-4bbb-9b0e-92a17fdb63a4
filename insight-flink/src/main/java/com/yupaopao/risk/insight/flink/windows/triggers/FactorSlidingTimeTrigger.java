package com.yupaopao.risk.insight.flink.windows.triggers;

import com.yupaopao.risk.insight.flink.windows.FactorCaches;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;


@Slf4j
public class FactorSlidingTimeTrigger extends Trigger<FactorCalDetail, TimeWindow> {
    private long slideAvgNum;

    public FactorSlidingTimeTrigger() {
    }

    public FactorSlidingTimeTrigger(long slideAvgNum) {
        this.slideAvgNum = slideAvgNum;
    }

    @Override
    public TriggerResult onElement(FactorCalDetail element, long timestamp, TimeWindow window, TriggerContext ctx) throws Exception {
        ctx.registerProcessingTimeTimer(window.maxTimestamp());
        timestamp = ctx.getCurrentProcessingTime();

        if (element.isPurge()) {
            return TriggerResult.FIRE_AND_PURGE;
        }
        if (element.getWindowType() == 1) {
            //按照事件请求事件计算时，可能当前所有window的end都小于当前事件（基于windowAssigner，currentTime和element时间不会超过30s）
            long size = window.getEnd() - window.getStart();
            if (window.getEnd() - timestamp <= size / slideAvgNum) {
                return TriggerResult.FIRE;
            } else {
                return TriggerResult.CONTINUE;
            }
        } else {
            return TriggerResult.FIRE;
        }
    }


    @Override
    public TriggerResult onProcessingTime(long time, TimeWindow window, TriggerContext ctx) throws Exception {
        //窗口结束输出数据，避免数据流没有数据的时候无法感知到最新的窗口结束时状态
        Integer windowType = getFactorWindowType(ctx);
        if (windowType != null && windowType == 1) {
            if (isSlidingWindowInAllowedDelay(window)) {
                return TriggerResult.FIRE;
            }
        } else if (windowType == null) {
            log.warn("windowType is not set");
        }
        return TriggerResult.CONTINUE;
    }

    private boolean isSlidingWindowInAllowedDelay(TimeWindow window) {
        //滑动窗口是否在允许输出的时间范围内，暂时不优化处理
//        long windowPeriod = window.getEnd() - window.getStart();
//        if (System.currentTimeMillis() - window.getEnd() > windowPeriod / slideAvgNum) {
//            //如果当前时间已经大于滑动窗口的滑动时间，则窗口结束时输出数据没有必要(已经有新窗口的结果可以输出了，如果是最后一个窗口用户不活跃了，也只是用户提前进行窗口失效)
//            return false;
//        }
        return true;
    }

    @Override
    public TriggerResult onEventTime(long time, TimeWindow window, TriggerContext ctx) throws Exception {
        return TriggerResult.CONTINUE;
    }

    @Override
    public void clear(TimeWindow window, TriggerContext ctx) throws Exception {
        ctx.deleteProcessingTimeTimer(window.maxTimestamp());
    }

    @Override
    public boolean canMerge() {
        return true;
    }

    @Override
    public void onMerge(TimeWindow window,
                        OnMergeContext ctx) {
        // only register a timer if the time is not yet past the end of the merged window
        // this is in line with the logic in onElement(). If the time is past the end of
        // the window onElement() will fire and setting a timer here would fire the window twice.
        long windowMaxTimestamp = window.maxTimestamp();
        if (windowMaxTimestamp > ctx.getCurrentProcessingTime()) {
            ctx.registerProcessingTimeTimer(windowMaxTimestamp);
        }
    }

    public static FactorSlidingTimeTrigger create() {
        return new FactorSlidingTimeTrigger();
    }

    public static FactorSlidingTimeTrigger create(long slideAvgNum) {
        return new FactorSlidingTimeTrigger(slideAvgNum);
    }


    private Integer getFactorWindowType(TriggerContext ctx) {
        //WindowOperator.Context
        //toString: return "Context{" + "key=" + key + ", window=" + window + '}';
        if (ctx == null) {
            return null;
        }
        String ctxString = ctx.toString();
        int startIndex = ctxString.indexOf("key=") + "key=".length();
        int endIndex = ctxString.indexOf(",");
        String key = ctxString.substring(startIndex, endIndex);
        if (StringUtils.isEmpty(key)) {
            log.warn("current window key is empty");
            return null;
        }
        String[] groupKeys = key.split("#");
        String factorId = null;
        if (groupKeys.length >= 2) {
            factorId = groupKeys[1];
        }
        if (StringUtils.isEmpty(factorId)) {
            log.warn("current factorId is empty");
            return null;
        }
        Integer windowType = FactorCaches.getWindowType(factorId);
        return windowType;
    }

}
