package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.beans.tag.TagMessage;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.java.tuple.Tuple2;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-08 17:27
 * 将窗口内某个用户或设备的所有标签聚合
 ***/
public class TagAggregateFunction implements AggregateFunction<TagMessage, Tuple2<String, Map<String, Object>>,
        Map<String, Object>> {



    @Override
    public Tuple2<String, Map<String, Object>> createAccumulator() {
        return new Tuple2<>("", new HashMap<>());
    }

    @Override
    public Tuple2<String, Map<String, Object>> add(TagMessage tagInfo, Tuple2<String, Map<String, Object>> accumulator) {
        Map<String, Object> tagRow = accumulator.getField(1);
        String valueType = tagInfo.getValueType();
        String code = tagInfo.getCode();
        Object tagValue = tagInfo.getValue();
        //特殊类型处理
        if (Arrays.asList("set", "map").contains(valueType.toLowerCase())) {
            tagValue = JSON.toJSONString(tagValue);
        }
        //tag值放入map
        tagRow.put(code, tagValue);
        //tag类型
        tagRow.put(code + FlinkConstants.TAG_STORE_VALUE_TYPE, valueType);
        tagRow.put(TagMessage.TAG_TYPE, tagInfo.getGroupKey());
        return new Tuple2<>(tagInfo.getGroupValue(), tagRow);
    }

    @Override
    public Map<String, Object> getResult(Tuple2<String, Map<String, Object>> accumulator) {
        if (StringUtils.isEmpty(accumulator.getField(0))) {
            return null;
        }
        Map<String, Object> tagMap = accumulator.getField(1);
        tagMap.put(TagMessage.HBASE_ROW_KEY, accumulator.getField(0));
        return tagMap;
    }

    @Override
    public Tuple2<String, Map<String, Object>> merge(Tuple2<String, Map<String, Object>> a, Tuple2<String, Map<String, Object>> b) {
        String rowKey = a.getField(0);
        if (StringUtils.isEmpty(rowKey)) {
            rowKey = b.getField(0);
        }
        Map<String, Object> tagData = new HashMap<>();
        if (a.getField(1) != null) {
            tagData.putAll(a.getField(1));
        }
        if (b.getField(1) != null) {
            tagData.putAll(a.getField(1));
        }
        return new Tuple2<>(rowKey, tagData);
    }
}
