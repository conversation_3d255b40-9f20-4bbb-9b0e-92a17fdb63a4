package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSONObject;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.property.AccumulateProperties;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.bean.AccumulateInfo;
import com.yupaopao.risk.insight.flink.constants.FactorConstants;
import com.yupaopao.risk.insight.flink.job.portrait.support.AccumulateCacheSupport;
import com.yupaopao.risk.insight.flink.windows.AccumulateCalDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc TagAggTransferProcess
 * @date 2020/5/21
 */
@Slf4j
public class AccumulateAggTransferProcess extends ProcessFunction<String, AccumulateCalDetail> {

    private static final long serialVersionUID = 1976743924552668336L;

    public static OutputTag<JSONObject> verify = new OutputTag<JSONObject>("verify") {
        private static final long serialVersionUID = 6639120748949744432L;
    };

    @Override public void processElement(String valueStr, Context ctx, Collector<AccumulateCalDetail> out) throws Exception {
        JSONObject value = JSONObject.parseObject(valueStr);
        if (value == null) {
            return;
        }
        JSONObject message;
        try {
            message = value.getJSONObject("message");
            if (message == null || message.size() == 0) {
                return;
            }
        } catch (Exception e) {
            log.error("参数转换错误：{}", valueStr, e);
            return;
        }
        String headers = message.getString("src_headers");
        if (StringUtils.isNotBlank(headers)) {
            ctx.output(verify, message);
        }
        List<AccumulateInfo> accumulateInfos = AccumulateCacheSupport.getAccumulateInfo();
        if (accumulateInfos.size() <= 0) {
            return;
        }
        String uri = message.getString("uri");
        if (StringUtils.isBlank(uri) || uri.contains("config") || uri.contains("system")) {
            return;
        }
        String ip = message.getString("ip");
        if (StringUtils.isBlank(ip) || AccumulateProperties.getInstance().getWhiteIp().contains(ip)) {
            log.info("{} 为白名单，跳过该条数据", ip);
            return;
        }
        parseSpecialKey(message);
        Long serverTime = message.getLong("serverTime");
        accumulateInfos.forEach(accumulateInfo -> {
            if (checkCondition(message, accumulateInfo)) {
                String groupKey = getGroupKey(accumulateInfo, message);
                String aggValue = getAggValue(accumulateInfo, message);

                if ((StringUtils.isNotEmpty(aggValue) || "COUNT".equals(accumulateInfo.getFunction())) && StringUtils.isNotEmpty(groupKey)) {
                    AccumulateCalDetail result = new AccumulateCalDetail();
                    result.setId(accumulateInfo.getId());
                    result.setGroupKey(String.format(FactorConstants.ACCUMULATE_DATA_PREFIX, accumulateInfo.getId(), groupKey));
                    result.setData(aggValue);
                    result.setFunction(accumulateInfo.getFunction());
                    result.setTimeSpan(accumulateInfo.getTimeSpan());
                    result.setTimeStamp(serverTime != null ? serverTime: System.currentTimeMillis());
                    result.setThresholds(getThresholds(accumulateInfo.getThresholds()));
                    result.setUid(message.getString("uid"));
                    result.setDeviceId(message.getString("did"));
                    result.setIp(message.getString("ip"));
                    result.setAppId(message.getString("product"));
                    result.setAppVersion(message.getString("appVersion"));
                    out.collect(result);
                }
            }
        });
    }

    private boolean checkCondition(JSONObject param, AccumulateInfo accumulateInfo) {
        if (StringUtils.isEmpty(accumulateInfo.getCondition())) {
            return true;
        }
        try {
            Object result = ExpressionExecutor.execute(accumulateInfo.getCondition(), param);
            log.debug("前置表达式执行结果:{} - {} - {}", result, accumulateInfo.getCondition(), param);
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else {
                return result != null;
            }
        } catch (Throwable e) {
            log.error("前置条件执行表达式出错: " + accumulateInfo.getCondition() + param.toJSONString(), e);
        }
        return false;
    }

    private String getGroupKey(AccumulateInfo accumulateInfo, JSONObject data){
        List<String> groupValues = new LinkedList<>();
        for (String groupKey : accumulateInfo.getGroupKey().split(",")) {
            String groupValue;

            if (groupKey.contains(".")) {
                Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                groupValue = dataMap.getOrDefault(groupKey, "").toString();
            } else {
                groupValue = data.getString(groupKey);
            }
            // 缺失任意groupKeyValue则放弃
            if (StringUtils.isBlank(groupValue)) {
                return "";
            }
            groupValues.add(groupValue);
        }
        return String.join("$", groupValues);
    }

    private String getAggValue(AccumulateInfo accumulateInfo, JSONObject data){
        if(StringUtils.isEmpty(accumulateInfo.getAggKey())){
            return "";
        } else {
            List<String> aggValues = new LinkedList<>();
            boolean hasValue = false;
            for (String aggKey : accumulateInfo.getAggKey().split(",")) {
                String aggValue;

                if (aggKey.contains(".")) {
                    Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                    aggValue = dataMap.getOrDefault(aggKey, "").toString();
                } else {
                    aggValue = data.getOrDefault(aggKey, "").toString();
                }
                if (StringUtils.isNotBlank(aggValue)) {
                    hasValue = true;
                }
                aggValues.add(aggValue);
            }
            if (hasValue) {
                return String.join("$", aggValues);
            } else {
                return "";
            }
        }
    }

    private long getThresholds(String thresholds) {
        long result = -1;
        try {
            result = Long.parseLong(thresholds);
        } catch (NumberFormatException e) {
            log.error("频控信息转换失败");
        }
        return result;
    }

    private void parseSpecialKey(JSONObject value) {
        String uri = value.getString("uri");
        int index = -1;
        if (StringUtils.isNotBlank(uri) && (index = uri.indexOf("?")) > -1) {
            value.put("uri", uri.substring(0, index));
        }
    }

    @Override public void close() throws Exception {
        super.close();
        AccumulateCacheSupport.cleanResource(AccumulateAggTransferProcess.class.getClassLoader());
    }
}
