package com.yupaopao.risk.insight.flink.job.portrait;

import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.AlgorithmModelInitProcessor;
import com.yupaopao.risk.insight.flink.job.processor.AlgorithmModelProcessor;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-16 16:15
 *  异常情况下清洗
 ***/
public class AlgorithmModelCleanJob {
    public static void main(String[] args) throws Exception {
        new FlinkJobBuilder()
                .withJobName("algorithm-job-clean")
                .withMainProcessor(new AlgorithmModelInitProcessor())
                .isBatchJob(true)
                .start(args);
    }
}
