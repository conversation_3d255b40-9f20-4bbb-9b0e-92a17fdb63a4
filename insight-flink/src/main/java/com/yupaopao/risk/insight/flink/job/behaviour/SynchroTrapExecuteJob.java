package com.yupaopao.risk.insight.flink.job.behaviour;

import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.behavior.SynchroTrapProcessor;

/****
 * zengxiangcai
 * 2021/5/14 4:40 下午
 ***/
public class SynchroTrapExecuteJob {

    public static void main(String[] args) throws Exception {
        new BatchJobBuilder()
                .withJobName("synchroTrap-execute")
                .withDefaultExecute(true)
                .withProcessor(new SynchroTrapProcessor())
                .start(args);
    }
}
