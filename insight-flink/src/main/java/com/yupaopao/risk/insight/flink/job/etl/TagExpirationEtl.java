package com.yupaopao.risk.insight.flink.job.etl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import com.yupaopao.risk.insight.common.support.TagUtil;
import com.yupaopao.risk.insight.flink.connector.hbase.sink.HBaseStreamSink;
import com.yupaopao.risk.insight.flink.connector.hbase.sink.TagHBaseOutputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseFullTableInputFormat;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.base.CheckpointSetting;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.TagCkSyncProcessor;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.Result;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc TagExpirationDayEtl
 * @date 2020/5/21
 */
@Slf4j
public class TagExpirationEtl {
    public static void main(String[] args) throws Exception {
        String tableName = "";
        if (args != null && args.length > 0) {
            String inputParam = args[0];
            if (StringUtils.isNotEmpty(inputParam)) {
                inputParam = URLDecoder.decode(inputParam, "UTF-8");
                tableName = JSON.parseObject(inputParam, Map.class).get("tableName").toString();
            }
        }
        new FlinkJobBuilder()
            .withJobName("tag-expiration-day-etl [" + tableName +"]")
            .withCheckpointSetting(new CheckpointSetting(3 * 60 * 1000, 2 * 60 * 1000, true))
            .withMainProcessor(new TagExpirationEtl.TagExpirationDayProcessor())
            .start(args);
    }

    public static class TagExpirationDayProcessor implements FlinkJobBuilder.MainProcessor {
        @Override public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
            String tableName = argMap.get("tableName");
            if (StringUtils.isEmpty(tableName)) {
                throw new IllegalArgumentException("tableName must be configured");
            }

            //hbase全量读取数据同步
            TsTableInfo tsInTable = new TsTableInfo()
                .withTableName(tableName)
                .withBucketPerSplit(1)
                .withTableType("SYSTEM");
            LinkedBlockingQueue<Result> cache = new LinkedBlockingQueue<>(1024 * 10);
            HBaseProperties hBaseProperties = HBaseProperties.getProperties(PropertyType.HBASE);

            env.createInput(new HBaseFullTableInputFormat(tsInTable, hBaseProperties, cache) {
                private static final long serialVersionUID = 1980763333393813331L;

                @Override
                public String parseHBaseResult(Result rowResult) {
                    return TagUtil.transHBaseRowToJsonForAI(rowResult);
                }

                @Override public void close() throws IOException {
                    super.close();
                    TagCacheSupport.cleanResource(this.getClass().getClassLoader());
                }
            }).name("read_hbase[" + tableName + "]")
                .filter(elem -> !HBaseUtil.emptyResultJson(elem))
                .flatMap(new FlatMapFunction<String, String>() {
                    @Override public void flatMap(String value, Collector<String> out) throws Exception {
                        JSONArray columnArray = JSONArray.parseArray(value);
                        if (columnArray.size() > 0) {
                            Map<String, Object> parseMap = new HashMap<>();
                            String rowKey = "";
                            for (int i = 0; i < columnArray.size(); i++) {
                                JSONObject column = columnArray.getJSONObject(i);
                                String columnName = column.getString("columnName");
                                Long timestamp = column.getLong("timestamp");
                                if (!"rowKey".equals(columnName)) {
                                    TagInfoBO valueType = TagCacheSupport.getTagInfoByCode(columnName);
                                    if (valueType == null || StringUtils.isEmpty(valueType.getValueType()) || StringUtils.isEmpty(valueType.getDataContent()) ||
                                        TagUtil.isInitValue(column.get("columnValue"), valueType.getValueType())) {
                                        continue;
                                    }
                                    JSONObject dataContent = JSONObject.parseObject(valueType.getDataContent());
                                    Long expirationDay = dataContent.getLong("expirationDay");
                                    if (null != expirationDay && timestamp > 0 && expirationDay > 0 && timestamp < System.currentTimeMillis() - expirationDay * 24 * 60 * 60 * 1000 ) {
                                        Object parseValue = TagUtil.getInitValue(valueType.getValueType());
                                        if (parseValue == null) {
                                            continue;
                                        }
                                        parseMap.put(columnName, parseValue);
                                        parseMap.put(columnName + FlinkConstants.TAG_STORE_VALUE_TYPE, valueType.getValueType());
                                    }
                                } else {
                                    rowKey = column.getString("columnValue");
                                }
                            }
                            if (parseMap.size() > 0) {
                                parseMap.put("groupValue", rowKey);
                                String clean = JSONObject.toJSONString(parseMap);
//                                log.info("准备重置过期标签：{}", clean);
                                out.collect(clean);
                            }
                        }
                    }
                })
                .addSink(new HBaseStreamSink(new TagHBaseOutputFormat(tableName,
                    hBaseProperties)))
                .name("update [" + tableName + "] expiration data" ).setParallelism(1);
        }
    }
}
