package com.yupaopao.risk.insight.flink.job.cleaner;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-28 15:20
 * tablestore 数据清洗任务, 针对ts表中可能很多数据需要重新清洗的情况(数据异常吗，数据格式重新处理等)
 ***/
@Deprecated
public class DataCleaner {
    public static void main(String[] args) throws Exception {
//        final ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
//
//        TsProperties tsProperties = (TsProperties) PropertiesFactory.loadProperty(PropertyType.TABLE_STORE);
//        TsTableInfo tsInTable = new TsTableInfo().withTableName("risk_hit_log")
//                .withTsProperties(tsProperties)
//                .withPrimaryKeyType(ApolloProperties.getTsPrimaryKeyTypes("risk_hit_log"))
//               // .withColumnNameType(flinkTable.getStrColumns())
//               // .withTableQueryPeriod(period)
//               // .withBucketPerSplit(jobParams.getBucketPerSplit())
//                .withTableId("1")
//                .withTableType("SYSTEM");
//        TsCleanerInputFormat inputFormat = new TsCleanerInputFormat(tsInTable);
//
//        DataSet<Row> dataSet = env.createInput(inputFormat);
//
//        TsCleanerOutputFormat outputFormat = new TsCleanerOutputFormat(tsInTable);
//        dataSet.output(outputFormat);
//
//        env.execute("cleaner");
    }
}
