package com.yupaopao.risk.insight.flink.job.processor.graph.service;

import com.alibaba.fastjson.JSON;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.utils.DBUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.flink.api.java.tuple.Tuple2;

import java.sql.Connection;
import java.util.*;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-22 15:56
 * 社区合并
 ***/

@Slf4j
public class CommunityCombineService {

    private DBProperties dbProperties;

    private ClickHouseProperties clickHouseProperties;

    private String runDay;

    private Connection conn;

    private ClickHouseConnection ccConn;


    public CommunityCombineService(String runDay) {
        this.runDay = runDay;
        this.clickHouseProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        clickHouseProperties.setDataTransferTimeout(60000 * 20); //20min
        clickHouseProperties.setSocketTimeout(60000 * 20); //20min
        clickHouseProperties.setConnectionTimeout(60000 * 1); //1min
        clickHouseProperties.setKeepAliveTimeout(60000 * 5); //5min
        this.dbProperties = DBProperties.getProperties(PropertyType.DB_INSIGHT);
        this.conn = DBUtil.getConnection(dbProperties);
        this.ccConn = ClickHouseUtil.createConnection(clickHouseProperties);
    }

    public Connection getConnection() {
        return conn;
    }

    public ClickHouseConnection getCKConnection() {
        return ccConn;
    }


    /***
     * 合并所有社区
     */
    public void combineAll(double ratio) {
        String sql = "SELECT community_label AS communityLabel,\n" +
                "       count(DISTINCT vertex_id) vCount\n" +
                "FROM t_community_detail\n" +
                "GROUP BY community_label\n" +
                "ORDER BY create_time ASC ";
        List<Map<String, Object>> allCcList = DBUtil.queryList(getConnection(), sql,
                null);
        List<String> ccList = new ArrayList<>(allCcList.size());
        Map<String, Integer> allCCMap = new HashMap<>();
        allCcList.stream().forEachOrdered(elem -> {
            allCCMap.put(elem.get("communityLabel").toString(),
                    Integer.valueOf(elem.get(
                            "vCount").toString()));
            ccList.add(elem.get("communityLabel").toString());
        });


        Map<String, String> combinedMap = new HashMap<>();

        String candidateSql = "SELECT community_label as communityLabel,\n" +
                "       count(DISTINCT vertex_id) vCount\n" +
                "FROM t_community_detail t\n" +
                "WHERE t.vertex_id IN\n" +
                "    ( SELECT vertex_id\n" +
                "     FROM t_community_detail dd\n" +
                "     WHERE dd.community_label = ? )" +
                " and community_label not in (?)\n" +
                "GROUP BY community_label\n" +
                "HAVING vCount>=?\n" +
                "ORDER BY vCount DESC";
        for (int i = 0; i < ccList.size(); i++) {

            String currentLabel = ccList.get(i);
            if (combinedMap.containsKey(currentLabel)) {
                //处理过
                continue;
            }

            //可能合并社区交集节点数
            Map<String, Integer> intersection = new HashMap<>();

            List<Map<String, Object>> ccCountList = DBUtil.queryList(getConnection(), candidateSql,
                    Arrays.asList(currentLabel, currentLabel,50*ratio));

            ccCountList.stream().forEach(elem -> intersection.put(elem.get("communityLabel").toString(), Integer.valueOf(elem.get(
                    "vCount").toString())));

            //查询可能社区的社区大小

            Integer currentCount = allCCMap.get(currentLabel);

            for (Map.Entry<String, Integer> entry : intersection.entrySet()) {
                Integer minCount = Math.min(allCCMap.get(entry.getKey()), currentCount);
                boolean greatThanHalf = minCount * ratio >= entry.getValue();
                if (greatThanHalf) {
                    if (combinedMap.containsKey(currentLabel)) {
                        combinedMap.put(entry.getKey(), combinedMap.get(currentLabel));
                    } else {
                        combinedMap.put(entry.getKey(), currentLabel);
                    }
                }
            }
            if (!combinedMap.containsKey(currentLabel)) {
                combinedMap.put(currentLabel, currentLabel);
            }
            log.info("finished cc: {}", ccList.get(i));
        }

        //更新标签
        for(Map.Entry<String, String> combinedEntry: combinedMap.entrySet()){
            if(combinedEntry.getKey().equals(combinedEntry.getValue())){
                continue;
            }
            DBUtil.executeUpdate(getConnection(),"update t_community_detail set community_label = ? where " +
                    "community_label=?",Arrays.asList(combinedEntry.getValue(),combinedEntry.getKey()));
        }

        log.info("latest result: {},\n finished...", JSON.toJSONString(combinedMap));
    }

    /***
     * 合并connectedComponent
     * @param largeCCList
     */
    public void combine(List<Map<String, String>> largeCCList) {

        if (CollectionUtils.isEmpty(largeCCList)) {
            return;
        }

        //同一天数据可能重新跑
        DBUtil.executeUpdate(getConnection(), "delete from t_community where run_day = ? ", Arrays.asList(runDay));
        DBUtil.executeUpdate(getConnection(), "delete from t_community_detail where run_day = ? ",
                Arrays.asList(runDay));

        log.info("insert into t_community_combine_log...");
        addCommunityCombineLog(largeCCList);

        log.info("insert into t_community_detail ... ");

        addCommunityDetail(largeCCList);

        log.info("update communityLabel ...");

        updateCommunityLabel(largeCCList);

        log.info("insert into t_community(add today increase) ...");
        addNewCommunity();

        log.info("finished mysql db cc process. ");
    }

    private boolean addNewCommunity() {
        String newCommunitySql = "insert into t_community(name,community_label,run_day,created_time,update_time)" +
                " select community_label,community_label,run_day,now(),now() from t_community_combine_log t1 where " +
                "run_day = ? and original_label='' and not exists(" +
                " select 1 from t_community t2 where t2.community_label = t1.community_label )";
        DBUtil.executeUpdate(getConnection(), newCommunitySql, Arrays.asList(runDay));

        //更新t_community 节点数
        String updateVertexCount = "update t_community t set t.vertex_count = (select count(distinct vertex_id) from t_community_detail t1 where t1\n" +
                "    .community_label = t.community_label) where 1=1";
        DBUtil.executeUpdate(getConnection(), updateVertexCount, null);
        return true;
    }

    private boolean updateCommunityLabel(List<Map<String, String>> largeCCList) {

        //合并cc，当前每一个cc在昨天的cc中顶点数，超过50%重合则认为是同一个
        Map<String, String> combinedLabelMap = getCoincidenceCommunities(largeCCList);
        if (combinedLabelMap == null || combinedLabelMap.isEmpty()) {
            return false;
        }

        //更新标签
        String sql = "update t_community_combine_log set community_label = ?, name = ?, original_label = ? where community_label " +
                "= ? and run_day = ?";
        String sqlDetail = "update t_community_detail set community_label = ? where " +
                "community_label = ? and run_day = ?";
        for (Map.Entry<String, String> entry : combinedLabelMap.entrySet()) {
            String curLabel = entry.getKey();
            String oldLabel = entry.getValue();
            DBUtil.executeUpdate(getConnection(), sql, Arrays.asList(oldLabel, oldLabel, curLabel, curLabel, runDay));
            DBUtil.executeUpdate(getConnection(), sqlDetail, Arrays.asList(oldLabel, curLabel, runDay));

        }
        return true;
    }

    private Map<String, String> getCoincidenceCommunities(List<Map<String, String>> largeCCList) {
        Map<String, Integer> todayLabelCountMap = new HashMap<>();
        largeCCList.stream().forEach(elem -> {
            String connectedLabel = elem.get("connectedLabel");
            String ccCount = elem.get("ccCount");
            todayLabelCountMap.put(connectedLabel, Integer.parseInt(ccCount));
        });

        //今日标签和昨日合并的集合（key: today, value: before day）
        Map<String, String> combinedLabelMap = new HashMap<>();

        //前一天数据
        String beforeDay = getBeforeDay();
        Map<String, Integer> beforeDayLabelCount = getBeforeDayPerCommunityCount(beforeDay);
        if (beforeDayLabelCount.isEmpty()) {
            return null;
        }

        for (Map.Entry<String, Integer> entry : todayLabelCountMap.entrySet()) {
            Integer labelCount = entry.getValue(); //当前社区大小
            String connectedLabel = entry.getKey();
            Tuple2<String, Integer> beforeDayMax = getMaxCoincidenceInBeforeDay(connectedLabel, runDay, beforeDay);
            if (beforeDayMax == null) {
                continue;
            }
            boolean greatThanHalf =
                    beforeDayMax.f1 > Math.min(labelCount, beforeDayLabelCount.get(beforeDayMax.f0)) * 0.5;
            if (greatThanHalf) {
                combinedLabelMap.put(connectedLabel, beforeDayMax.f0);
            }
        }
        if (combinedLabelMap.isEmpty()) {
            return null;
        }
        return combinedLabelMap;
    }

    private String getBeforeDay() {
        Date today = InsightDateUtils.getDateFromString(runDay, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        return InsightDateUtils.getDateStr(DateUtils.addDays(today, -1),
                InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
    }

    private void addCommunityDetail(List<Map<String, String>> largeCCList) {
        String ccVertexSql =
                "select id, degree from cc_detection_result where connectedLabel='%s' and runDay = '" + runDay + "'";
        for (Map<String, String> entry : largeCCList) {
            String connectedLabel = entry.get("connectedLabel");
            //明细数据
            String querySql = String.format(ccVertexSql, connectedLabel);
            List<Map<String, String>> vertexList = ClickHouseUtil.executeQuery(getCKConnection(), querySql);
            List<List<String>> detailRowList = vertexList.stream().map(elem -> {
                String vertexId = elem.get("id");
                String vertexDegree = elem.get("degree");

                List<String> row = new ArrayList<>();
                row.add(connectedLabel);
                row.add(vertexId);
                row.add(vertexDegree);
                row.add(runDay);
                return row;
            }).collect(Collectors.toList());

            DBUtil.insertBatch(conn, "t_community_detail", Arrays.asList("community_label", "vertex_id",
                    "degree", "run_day"), detailRowList);
        }
    }

    private void addCommunityCombineLog(List<Map<String, String>> largeCCList) {
        List<List<String>> insertRows = largeCCList.stream().map(elem -> {
            String connectedLabel = elem.get("connectedLabel");
            String ccCount = elem.get("ccCount");
            List<String> row = new ArrayList<>();
            row.add(connectedLabel);
            row.add(connectedLabel);
            row.add(ccCount);
            row.add(runDay);
            return row;
        }).collect(Collectors.toList());

        DBUtil.insertBatch(getConnection(), "t_community_combine_log", Arrays.asList("name", "community_label", "vertex_count",
                "run_day"), insertRows);
    }


    private Map<String, Integer> getBeforeDayPerCommunityCount(String beforeDay) {
        //label昨日量：
        Map<String, Integer> beforeDayCountMap = new HashMap<>();
        String beforeDaySql = "select community_label, vertex_count vCount from t_community_combine_log t where " +
                "run_day = ? ";
        List<Map<String, Object>> list = DBUtil.queryList(getConnection(), beforeDaySql, Arrays.asList(beforeDay));
        list.stream().forEach(elem -> {
            String label = elem.get("community_label").toString();
            Integer count = Integer.parseInt(elem.get("vCount").toString());
            beforeDayCountMap.put(label, count);
        });
        return beforeDayCountMap;
    }

    /**
     * @param connectedLabel
     * @param runDay
     * @param beforeDay
     * @return
     */
    private Tuple2<String, Integer> getMaxCoincidenceInBeforeDay(String connectedLabel, String runDay,
                                                                 String beforeDay) {
        //当前社区的节点在前一天各社区的分布(重合情况)
        String sql = "  select community_label,count(1) vCount from t_community_detail t\n" +
                "           where t.vertex_id in (\n" +
                "                        select vertex_id from t_community_detail dd where dd.community_label = ?\n" +
                "                        and dd.run_day=?\n" +
                "                      )\n" +
                "             and t.run_day=?\n" +
                "             group by community_label order by vCount desc limit 1";

        Map<String, Object> maxCoincidence = DBUtil.queryObject(getConnection(), sql, Arrays.asList(connectedLabel, runDay,
                beforeDay));

        if (!maxCoincidence.isEmpty()) {
            return new Tuple2(maxCoincidence.get("community_label").toString(), Integer.parseInt(maxCoincidence.get(
                    "vCount").toString()));
        }
        return null;
    }


    public enum CombineStrategy {
        COMBINE_WITH_BEFORE_DAY, //超过50%合并
        ;
    }

}
