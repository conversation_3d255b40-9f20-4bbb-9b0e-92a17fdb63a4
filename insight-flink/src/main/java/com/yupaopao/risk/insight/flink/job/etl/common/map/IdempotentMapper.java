package com.yupaopao.risk.insight.flink.job.etl.common.map;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.flink.connector.redis.sink.client.RedisClient;
import com.yupaopao.risk.insight.flink.job.etl.common.options.MapperConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yupaopao.risk.insight.flink.job.etl.common.options.MapperConfig.KEY_CACHE;
import static com.yupaopao.risk.insight.flink.job.etl.common.options.MapperConfig.KEY_IDEMPOTENT;

/**
 * Copyright (C), 2021, jimmy
 *
 * <AUTHOR>
 * @desc IdempotentMapper
 * @date 2021/02/03
 */
@Slf4j
public class IdempotentMapper extends BaseMapFunction {
    private static final long serialVersionUID = 7079346476096367541L;
    private JedisPool jedisPool;
    private Jedis jedis;
    private Map<String, List<String>> idempotentKeyMap = new HashMap<>();
    private int cacheTime = 0;

    public IdempotentMapper(MapperConfig config) throws Exception {
        super(config);
        String idempotentKey = config.getStringVal(KEY_IDEMPOTENT);
        if (StringUtils.isNotBlank(idempotentKey)) {
            JSONObject idempotent = JSONObject.parseObject(idempotentKey);
            for (String key : idempotent.keySet()) {
                List<String> keyList = Arrays
                    .stream(idempotent.getString(key).split("#")).collect(Collectors.toList());
                idempotentKeyMap.put(key, keyList);
            }
        }

        cacheTime = config.getIntVal(KEY_CACHE,60);

        if (cacheTime == 60) {
            //default value ,config with apollo
            String ckIdempotentCacheTime = ApolloProperties.getConfigStr("ck.idempotent.cacheTime");
            if (StringUtils.isNotEmpty(ckIdempotentCacheTime)) {
                cacheTime = Integer.valueOf(ckIdempotentCacheTime);
            }
        }
    }

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        jedisPool = RedisClient.getClient();
        jedis = jedisPool.getResource();
    }

    @Override public void flatMap(Map<String, Object> value, Collector<Map<String, Object>> out) throws Exception {
        super.flatMap(value, out);
        if (isDebug && value.containsKey(KEY_DEBUG)) {
            return;
        }
        if (idempotentKeyMap.size() == 0) {
            out.collect(value);
            return;
        }
        String source;
        if (value.containsKey("data_source")) {
            source = value.get("data_source").toString();
        } else {
            source = value.get("source").toString();
        }
        if (StringUtils.isBlank(source) || !idempotentKeyMap.containsKey(source)) {
            out.collect(value);
            return;
        }
        StringBuilder cacheValueBuilder = new StringBuilder("Idempotent#");
        boolean flag = false;

        for (String key : idempotentKeyMap.get(source)) {
            String[] parts = key.split("\\.");

            Map<String, Object> current = value;
            for(int j = 0; j < parts.length - 1; ++j) {
                if (current == null) {
                    return;
                }
                current = (Map<String, Object>) current.get(parts[j]);
            }
            Object curValue = current.get(parts[parts.length - 1]);
            if (null != curValue) {
                flag = true;
                cacheValueBuilder.append(curValue);
            }
            cacheValueBuilder.append("#");
        }
        if (flag) {
            String cacheValue = cacheValueBuilder.toString();
            if (StringUtils.isBlank(jedis.get(cacheValue))) {
                jedis.setex(cacheValue, cacheTime + 20, "true");
                out.collect(value);
            }
        }
    }

    @Override public void close() throws Exception {
        super.close();
        RedisClient.closeJedis(jedis);
        RedisClient.closePool(jedisPool);
    }
}
