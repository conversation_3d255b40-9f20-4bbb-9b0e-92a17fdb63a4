package com.yupaopao.risk.insight.flink.job.cal;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.connector.mysql.model.Factor;
import com.yupaopao.risk.insight.flink.connector.mysql.source.HugMysqlSource;
import com.yupaopao.risk.insight.flink.connector.mysql.source.MysqlSource;
import com.yupaopao.risk.insight.flink.connector.redis.sink.RedisCustomizeSink;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.KafkaMQDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.StringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import com.yupaopao.risk.insight.flink.windows.aggregate.*;
import com.yupaopao.risk.insight.flink.windows.assigner.FactorSlidingWindowAssigner;
import com.yupaopao.risk.insight.flink.windows.process.FactorBroadcastProcess;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.contrib.streaming.state.EmbeddedRocksDBStateBackend;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.util.*;

import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.KAFKA_GROUP_ID_ETL_RISK_HIT_FACTOR_CAL;
import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.REALTIME_RISK;

public class FactorCalHugging {

    public static void main(String[] args) throws Exception {

        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        env.enableCheckpointing(3 * 60 * 1000);
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        env.getCheckpointConfig().enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        env.getCheckpointConfig().setCheckpointTimeout(30 * 60 * 1000);
        EmbeddedRocksDBStateBackend rocksDBStateBackend = new EmbeddedRocksDBStateBackend(true);
        rocksDBStateBackend.setRocksDBOptions(new FlinkJobBuilder.CustomRocksdbOptionsFactory());
        env.setStateBackend(rocksDBStateBackend);

        env.getCheckpointConfig().setCheckpointStorage(FlinkConstants.getRocksdbPathWithNFS());

        Config config = ConfigService.getAppConfig();
        int parallelism = config.getIntProperty("factor.hug.parallelism", 1);

        DBProperties dbProperties = DBProperties.getProperties(PropertyType.DB_RISK_HUG);
        // 广播流
        BroadcastStream<List<Factor>> broadcast = env.addSource(new HugMysqlSource(dbProperties, REALTIME_RISK)).setParallelism(1).name("mysql source")
                .broadcast(FactorBroadcastProcess.CONFIG_DESCRIPTOR);

        KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA_HUG);
        Properties etlProperties = kafkaProperties.getProperties();
        etlProperties.put("group.id", KAFKA_GROUP_ID_ETL_RISK_HIT_FACTOR_CAL);

        List<String> topics = Collections.singletonList("RISK-ONLINE-RESULT-LOG");

        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics,
                new StringKafkaDeserializationSchemaWrapper(new SimpleStringSchema()),
                kafkaProperties.getProperties());
        // 设定项目启动读取时间，不影响从checkpoint 恢复
        consumer.setStartFromTimestamp(System.currentTimeMillis() - 1000);

        DataStream<String> stream = env.addSource(consumer).setParallelism(Math.min(parallelism, 20)).name("kafka source");

        SingleOutputStreamOperator<FactorCalDetail> process = stream.connect(broadcast)
                .process(new FactorBroadcastProcess()).setParallelism(Math.min(parallelism, 20))
                .process(new ProcessFunction<FactorCalDetail, FactorCalDetail>() {
                    @Override public void processElement(FactorCalDetail value, Context ctx, Collector<FactorCalDetail> out) throws Exception {
                        FactorEnum enumData = FactorEnum.getEnumData(value.getFunction());
                        if (null != enumData) {
                            ctx.output(enumData.getOutputTag(), value);
                        }
                    }
                }).setParallelism(Math.min(parallelism, 20))
                ;

        RedisProperties redisProperties = RedisProperties.getProperties(PropertyType.REDIS_RISK_HUG);

        for (FactorEnum factorEnum : FactorEnum.NOT_OFTEN_FACTOR) {
            process.getSideOutput(factorEnum.getOutputTag())
                    .keyBy(FactorCalDetail::getGroupKey)
                    .window(new FactorSlidingWindowAssigner())
                    .aggregate(factorEnum.getAggregateFunction()).setParallelism(1)
                    .addSink(new RedisCustomizeSink(redisProperties)).name(factorEnum.getKey()).setParallelism(1)
            ;
        }

        process.getSideOutput(FactorEnum.COUNT.getOutputTag())
                .keyBy(FactorCalDetail::getGroupKey)
                .window(new FactorSlidingWindowAssigner())
                .aggregate(FactorEnum.COUNT.getAggregateFunction()).setParallelism(parallelism)
                .addSink(new RedisCustomizeSink(redisProperties)).name(FactorEnum.COUNT.getKey()).setParallelism(parallelism)
        ;

        process.getSideOutput(FactorEnum.COUNT_DISTINCT.getOutputTag())
                .keyBy(FactorCalDetail::getGroupKey)
                .window(new FactorSlidingWindowAssigner())
                .aggregate(FactorEnum.COUNT_DISTINCT.getAggregateFunction()).setParallelism(parallelism)
                .addSink(new RedisCustomizeSink(redisProperties)).name(FactorEnum.COUNT_DISTINCT.getKey()).setParallelism(parallelism)
        ;

        process.getSideOutput(FactorEnum.DISTINCT.getOutputTag())
            .keyBy(FactorCalDetail::getGroupKey)
            .window(new FactorSlidingWindowAssigner())
            .aggregate(FactorEnum.DISTINCT.getAggregateFunction()).setParallelism(parallelism)
            .addSink(new RedisCustomizeSink(redisProperties)).name(FactorEnum.DISTINCT.getKey()).setParallelism(parallelism)
        ;

        env.execute("factor cal hug");
    }

    public enum FactorEnum {
        /**
         * OutputTag与AggregateFunction enum
         */
        COUNT("COUNT", new OutputTag<FactorCalDetail>("count") {}, new FactorCountFunction()),
        SUM("SUM", new OutputTag<FactorCalDetail>("sum") {}, new FactorSumFunction()),
        COUNT_DISTINCT("COUNT_DISTINCT", new OutputTag<FactorCalDetail>("count_distinct") {}, new FactorDistinctCountFunction()),
        DISTINCT("DISTINCT", new OutputTag<FactorCalDetail>("distinct") {}, new FactorDistinctFunction()),
        AVG("AVG", new OutputTag<FactorCalDetail>("avg") {}, new FactorAvgFunction()),
        MAX("MAX", new OutputTag<FactorCalDetail>("max") {}, new FactorMaxFunction()),
        MIN("MIN", new OutputTag<FactorCalDetail>("min") {}, new FactorMinFunction()),
        ;

        static List<FactorEnum> NOT_OFTEN_FACTOR = new ArrayList<>();
        static {
            NOT_OFTEN_FACTOR.add(SUM);
            NOT_OFTEN_FACTOR.add(AVG);
            NOT_OFTEN_FACTOR.add(MAX);
            NOT_OFTEN_FACTOR.add(MIN);
        }

        FactorEnum(String key, OutputTag<FactorCalDetail> outputTag, AggregateFunction<FactorCalDetail, AggregateResult, AggregateResult> aggregateFunction) {
            this.key = key;
            this.outputTag = outputTag;
            this.aggregateFunction = aggregateFunction;
        }

        private String key;
        private OutputTag<FactorCalDetail> outputTag;
        private AggregateFunction<FactorCalDetail, AggregateResult, AggregateResult> aggregateFunction;

        public String getKey() {
            return key;
        }

        public OutputTag<FactorCalDetail> getOutputTag() {
            return outputTag;
        }

        public AggregateFunction<FactorCalDetail, AggregateResult, AggregateResult> getAggregateFunction() {
            return aggregateFunction;
        }

        public static FactorEnum getEnumData(String key) {
            return Arrays.stream(FactorEnum.values()).filter(p -> p.key.equals(key)).findFirst().orElse(null);
        }
    }
}
