package com.yupaopao.risk.insight.flink.job.wangan.function;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.job.wangan.constant.TopicConstant;
import com.yupaopao.risk.insight.flink.job.wangan.process.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.flink.util.StringUtils;
import org.apache.hadoop.hbase.client.HTable;

import java.io.IOException;
import java.util.Map;

@Slf4j
public class WangAnFlatMapFunction extends RichFlatMapFunction<String, Map> {


    private WangAnHbaseTableSupport wangAnHbaseTableSupport;

    private ChatRoomSendSupport chatRoomSendSupport;

    private FriendsZoneSupport friendsZoneSupport;

    private GroupChatSendSupport groupChatSendSupport;

    private LoginSendSupport loginSendSupport;

    private UserInfoSupport userInfoSupport;

    private ThirdPaymentSupport thirdPaymentSupport;

    private UserAssociateSupport userAssociateSupport;

    private UserBehaviourSupport userBehaviourSupport;
    @Override
    public void open(Configuration parameters) throws Exception {
        wangAnHbaseTableSupport = new WangAnHbaseTableSupport();
        chatRoomSendSupport = new ChatRoomSendSupport(wangAnHbaseTableSupport);
        groupChatSendSupport = new GroupChatSendSupport(wangAnHbaseTableSupport);
        loginSendSupport = new LoginSendSupport(wangAnHbaseTableSupport);
        userInfoSupport = new UserInfoSupport(wangAnHbaseTableSupport);
        thirdPaymentSupport = new ThirdPaymentSupport(wangAnHbaseTableSupport);
        userAssociateSupport = new UserAssociateSupport(wangAnHbaseTableSupport);
        userBehaviourSupport = new UserBehaviourSupport(wangAnHbaseTableSupport);
        friendsZoneSupport = new FriendsZoneSupport(wangAnHbaseTableSupport);
        super.open(parameters);
    }

    @Override
    public void flatMap(String value, Collector<Map> collector) throws Exception {
        try {
            if (StringUtils.isNullOrWhitespaceOnly(value)) {
                return;
            }
            JSONObject dataJson = JSON.parseObject(value);

            String kafkaTopic = dataJson.getString("kafkaTopic");
            userInfoSupport.putHBase(dataJson, kafkaTopic);
            userAssociateSupport.putHbase(dataJson, kafkaTopic);
            loginSendSupport.putHbase(dataJson, kafkaTopic);

            userInfoSupport.send(kafkaTopic, dataJson, collector);
            loginSendSupport.send(kafkaTopic, dataJson, collector);
            userAssociateSupport.send(kafkaTopic, dataJson, collector);
            userBehaviourSupport.send(kafkaTopic, dataJson, collector);
            chatRoomSendSupport.send(kafkaTopic, dataJson, collector);
            friendsZoneSupport.send(kafkaTopic, dataJson, collector);
            thirdPaymentSupport.send(kafkaTopic, dataJson, collector);
            groupChatSendSupport.send(kafkaTopic, dataJson, collector);
        } catch (Exception e) {
            log.error("网安数据上报错误:{}", value, e);
        }

    }



    @Override
    public void close() throws Exception {
        wangAnHbaseTableSupport.close();
        super.close();
    }
}
