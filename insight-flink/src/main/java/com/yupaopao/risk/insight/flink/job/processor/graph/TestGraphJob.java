package com.yupaopao.risk.insight.flink.job.processor.graph;

import org.apache.flink.api.java.DataSet;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.core.fs.FileSystem;
import org.apache.flink.graph.Edge;

/****
 * zengxiangcai
 * 2021/3/11 5:12 下午
 ***/
public class TestGraphJob {
    public static void main(String[] args) throws Exception {
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        DataSet<Edge<String, Integer>> ds = env.fromElements(new Edge<>("1", "2", 1),
                new Edge<>("2", "3", 1),
                new Edge<>("4", "5", 1),
                new Edge<>("2", "6", 1)
        );
        DataSet<Edge<String, Integer>> weightEdge = env.fromElements(
                new Edge<>("2", "6", 10),
                new Edge<>("1", "2", 10)
        );
        DataSet<Edge<String, Long>> resultEdges = ds.join(weightEdge)
                .where("f0", "f1").equalTo("f0", "f1")
                .project(0);
//        resultEdges.writeAsCsv("/Users/<USER>/test.csv", FileSystem.WriteMode.OVERWRITE);

        resultEdges.printToErr();
//        env.execute("111");
    }
}
