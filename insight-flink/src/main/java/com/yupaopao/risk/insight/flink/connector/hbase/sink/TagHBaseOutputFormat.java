package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.alibaba.lindorm.client.core.utils.Bytes;
import com.yupaopao.risk.insight.common.beans.tag.TagMessage;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.flink.utils.MultiVersionRefreshUtils;
import org.apache.hadoop.hbase.client.Put;

import java.util.HashMap;
import java.util.Map;

import static com.yupaopao.risk.insight.common.constants.HBaseConstants.COLUMN_FAMILY;
import static com.yupaopao.risk.insight.common.support.TagUtil.transTagValueToBytes;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-07 17:24
 *
 ***/
public class TagHBaseOutputFormat extends CommonHBaseOutputFormat {

    public TagHBaseOutputFormat(String tableName, HBaseProperties hBaseProperties) {
        super(tableName, hBaseProperties);
    }

    @Override
    public String getRowKey(Map<String, Object> flattenMap) {
        String rowKey = (String) flattenMap.get(TagMessage.HBASE_ROW_KEY);
        return rowKey;
    }


    public boolean addColumns(Map<String, Object> flattenMap, Put put) {
        //remove rowKey
        String rowKey = (String) flattenMap.remove(TagMessage.HBASE_ROW_KEY);
        byte[] columnFamily = Bytes.toBytes(COLUMN_FAMILY);
        int columnCount = 0;
        for (Map.Entry<String, Object> entry : flattenMap.entrySet()) {
            if (entry.getKey().endsWith(FlinkConstants.TAG_STORE_VALUE_TYPE)) {
                continue;
            }

            String valueType = (String) flattenMap.get(entry.getKey() +
                    FlinkConstants.TAG_STORE_VALUE_TYPE);
            if(entry.getKey().equals("updateTime")){
                valueType = "String";
            }
            byte[] columnValue = transTagValueToBytes(entry.getValue(), valueType);
            if (columnValue == null) {
                continue;
            }
            columnCount++;
            put.addColumn(columnFamily, Bytes.toBytes(entry.getKey()), columnValue);
            MultiVersionRefreshUtils.refreshUserRiskLevelForZombieUser(entry.getKey(), columnValue, put);
        }
        return columnCount != 0;
    }


    public Map<String, Object> generateExtraColumn(Map<String, Object> flattenMap) {
        Map<String,Object> extraMap = new HashMap<>();
        extraMap.put("updateTime", InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));
        return extraMap;
    }
}
