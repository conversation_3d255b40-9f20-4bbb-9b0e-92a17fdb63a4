package com.yupaopao.risk.insight.flink.job.processor.graph.historyData;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.lindorm.client.core.utils.Bytes;
import com.yupaopao.risk.insight.common.beans.graph.EdgeInfo;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.bean.graph.EventGraphElement;
import com.yupaopao.risk.insight.flink.connector.gdb.sink.GDBOutputFormat;
import com.yupaopao.risk.insight.flink.connector.gdb.sink.GDBStreamSink;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseFullTableInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseInputSplitReadThread;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseSplit;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.GDBProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;

import static com.yupaopao.risk.insight.common.constants.HBaseConstants.HBASE_FAMILY_KEY;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-17 13:48
 * 写入边
 ***/

@Slf4j
public class GraphHbaseEdgeToGDBProcessor implements FlinkJobBuilder.MainProcessor {

    @Override
    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
        LinkedBlockingQueue<Result> cache = new LinkedBlockingQueue<>(1024 * 16);

        HBaseProperties hBaseProperties = HBaseProperties.getProperties(PropertyType.HBASE);
        GDBProperties gdbProperties = GDBProperties.getProperties(PropertyType.GDB);

        GDBStreamSink sink = new GDBStreamSink(new GDBOutputFormat(gdbProperties) {
            @Override
            public boolean directInsert() {
                return true;
            }
        });

        TsTableInfo tsInTable = new TsTableInfo()
                .withTableName("g_edge")
                .withBucketPerSplit(2)
                .withTableType("SYSTEM");


        env.createInput(new HBaseFullTableInputFormat(tsInTable, hBaseProperties, cache) {

            @Override
            public String parseHBaseResult(Result rowResult) {
                getReaderCounter().add(1);
                if (rowResult == null) {
                    return "{}";
                }
                try {
                    String edgeId = Bytes.toString(rowResult.getRow());
                    String label = getHBaseString(rowResult, "label");
                    String fromVertex = getHBaseString(rowResult, "fromVertex");
                    String toVertex = getHBaseString(rowResult, "toVertex");

                    Long createTime = getHBaseLong(rowResult, "createTime");
                    Long updateTime = getHBaseLong(rowResult, "updateTime");
                    Map<String, Object> properties = new HashMap<>(2);
                    properties.put(EdgeInfo.EDGE_PROP_CREATE_TIME, createTime);
                    properties.put(EdgeInfo.EDGE_PROP_UPDATE_TIME, updateTime);
                    EdgeInfo edgeInfo = new EdgeInfo(edgeId, label, properties, fromVertex, toVertex);
                    return JSON.toJSONString(edgeInfo, SerializerFeature.DisableCircularReferenceDetect);
                } catch (Exception e) {
                    log.warn("parse hbase data error, res: " + rowResult);
                }
                return "{}";
            }

            private String getHBaseString(Result rowResult, String column) {
                byte[] columnValue = rowResult.getValue(HBASE_FAMILY_KEY, Bytes.toBytes(column));
                if (columnValue == null) {
                    return null;
                }
                return Bytes.toString(columnValue);
            }

            private Long getHBaseLong(Result rowResult, String column) {
                byte[] columnValue = rowResult.getValue(HBASE_FAMILY_KEY, Bytes.toBytes(column));
                if (columnValue == null) {
                    return null;
                }
                BigDecimal decimal = Bytes.toBigDecimal(columnValue);
                return decimal.longValue();
            }

            private Boolean getHBaseBoolean(Result rowResult, String column) {
                byte[] columnValue = rowResult.getValue(HBASE_FAMILY_KEY, Bytes.toBytes(column));
                if (columnValue == null) {
                    return null;
                }
                return Bytes.toBoolean(columnValue);
            }

            @Override
            public HBaseInputSplitReadThread initReadThread(HBaseSplit.BucketSplit bucket, HTable currentTable, String hbaseTableName, String threadName, TsTableInfo tsTableInfo, LinkedBlockingQueue<Result> queue) {
                HBaseInputSplitReadThread readThread = new HBaseInputSplitReadThread(bucket,
                        currentTable,
                        queue,
                        hbaseTableName,
                        tsTableInfo.getTableId(),
                        tsTableInfo.getTableType(),
                        tsTableInfo.getColumnNames(),
                        threadName
                ) {
//                    public Scan initScan() {
//                        Scan scan = super.initScan();
//                        List<Filter> filters = new ArrayList();
//                        //过滤event
//                        SingleColumnValueFilter filterLogin =
//                                new SingleColumnValueFilter(HBaseConstants.HBASE_FAMILY_KEY,
//                                        org.apache.hadoop.hbase.util.Bytes.toBytes("eventCode"), CompareOperator.NOT_EQUAL,
//                                        new BinaryComparator(org.apache.hadoop.hbase.util.Bytes.toBytes("user-login")));
//                        filters.add(filterLogin);
//
//                        FilterList filterList = new FilterList(filters);
//                        scan.setFilter(filterList);
//                        return scan;
//                    }

//                    @Override
//                    public void sleepPerRow() {
//                        this.hbaseReadLimiter.acquire();
//                    }
//                    public void initRate(){
//                        this.hbaseReadLimiter = RateLimiter.create(rate);
//                    }
                };
                return readThread;
            }
        }).name("read_hbase[g_edge]").setParallelism(1)
//                        .setParallelism(1)
                .flatMap(new FlatMapFunction<String, EventGraphElement>() {
                    @Override
                    public void flatMap(String value, Collector<EventGraphElement> out) throws Exception {
                        if (!HBaseUtil.emptyResultJson(value)) {
//                                    log.info(value);
                            EventGraphElement graphElement = new EventGraphElement();
                            graphElement.setEdges(Arrays.asList(JSON.parseObject(value, EdgeInfo.class)));
                            out.collect(graphElement);
//                            Cat.logMetricForCount("history.event.out");
                        }
                    }
                }).addSink(sink).setParallelism(4);
    }
}
