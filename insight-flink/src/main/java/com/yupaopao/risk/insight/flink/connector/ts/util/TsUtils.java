//package com.yupaopao.risk.insight.flink.connector.ts.util;
//
//import com.alicloud.openservices.tablestore.model.*;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.*;
//
//@Slf4j
//public class TsUtils {
//
//    public static Map<String, Column> recordMap(Row record) {
//        Map<String, Column> columnMap = new HashMap<>(record.getColumns().length);
//        for (Column column : record.getColumns()) {
//            columnMap.put(column.getName(), column);
//        }
//        return columnMap;
//    }
//
//    /**
//     * 转换记录为map
//     * @param record 结果记录
//     * @return 转换结果
//     */
//    public static Map<String, Object> parseToMap(StreamRecord record) {
//        Map<String, Object> columnMap = new HashMap<>(record.getColumns().size());
//
//        for (RecordColumn column : record.getColumns()) {
//            Object obj = columnValueToInnerValue(column.getColumn().getValue());
//            columnMap.put(column.getColumn().getName(), obj);
//        }
//        for (PrimaryKeyColumn primaryKeyColumn : record.getPrimaryKey().getPrimaryKeyColumns()) {
//            Object obj = pkValueToInnerValue(primaryKeyColumn.getValue());
//            columnMap.put(primaryKeyColumn.getName(), obj);
//        }
//        return columnMap;
//    }
//
//    private static Object pkValueToInnerValue(PrimaryKeyValue value) {
//        switch (value.getType()) {
//            case STRING:
//                return value.asString();
//            case INTEGER:
//                return (int) value.asLong();
//            case BINARY:
//                return value.asBinary();
//            default:
//                throw new IllegalArgumentException("not support the type : " + value.getType());
//        }
//    }
//
//    private static Object columnValueToInnerValue(ColumnValue value) {
//        switch (value.getType()) {
//            case BINARY:
//                return value.asBinary();
//            case STRING:
//                return value.asString();
//            case INTEGER:
//                return (int) value.asLong();
//            case DOUBLE:
//                return value.asDouble();
//            case BOOLEAN:
//                return value.asBoolean();
//            default:
//                throw new IllegalArgumentException("not support the type :" + value.getType());
//        }
//    }
//
//}
