package com.yupaopao.risk.insight.flink.connector.redis.source;

import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.common.support.RedisPoolCreator;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.io.Serializable;
import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-24 16:33
 *
 ***/

public abstract class BaseRedisSource<OUT> extends RichSourceFunction<OUT> implements Serializable{

    private RedisProperties redisProperties;

    private transient JedisPool jedisPool;

    public BaseRedisSource(RedisProperties redisProperties) {
        this.redisProperties = redisProperties;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        jedisPool = RedisPoolCreator.createJedisPool(redisProperties);

    }

    @Override
    public void close() throws Exception {
        if (jedisPool == null) {
            return;
        }
        jedisPool.close();
    }

    public String getKey(String key){
        try(Jedis jedis = jedisPool.getResource();){
           return jedis.get(key);
        }
    }

    public List<String> getList(String key){
        try(Jedis jedis = jedisPool.getResource();){
            Long size  = jedis.llen(key);
            return jedis.lrange(key,0,size);
        }
    }

    public JedisPool getJedisPool() {
        if (jedisPool == null) {
            synchronized (this) {
                jedisPool = RedisPoolCreator.createJedisPool(redisProperties);
            }
        }
        return jedisPool;
    }
}
