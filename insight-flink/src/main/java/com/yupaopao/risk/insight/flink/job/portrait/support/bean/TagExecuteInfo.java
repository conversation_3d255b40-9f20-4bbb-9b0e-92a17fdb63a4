package com.yupaopao.risk.insight.flink.job.portrait.support.bean;

import groovy.lang.Script;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TagExecuteInfo implements Serializable {
    private static final long serialVersionUID = 8379123183913221880L;
    private Script script;
    private String condition;
    private String dependent;
    private String name;
    private String code;
    private String valueType;
    private String eventCode;
    private String groupKey;
    private String alias;
    private List<String> topicList;

}
