package com.yupaopao.risk.insight.flink.job.processor.graph.historyData;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.beans.graph.GdbDSLParam;
import com.yupaopao.risk.insight.common.support.GDBUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.tinkerpop.gremlin.driver.Client;

import java.util.concurrent.Callable;

/****
 *
 * @Author: zengxia<PERSON><PERSON><PERSON>
 * @Date: 2020-07-19 21:47
 *
 ***/

@Slf4j
public class DeleteTask implements Callable<Integer> {

    private transient Client client;
    private GdbDSLParam params;

    public DeleteTask() {

    }

    public DeleteTask(Client client, GdbDSLParam params) {
        this.client = client;
        this.params = params;
    }

    @Override
    public Integer call() throws Exception {
        try {
            GDBUtil.sendRequest(client, params);
            return params.getParameter().size();
        } catch (Exception e) {
            if (e.getMessage() != null && e.getMessage().contains("GraphDB id")) {
                //id可能并行移除
            } else if (e.getMessage() != null && e.getMessage().contains("Operation timed out")) {

            } else {
                log.warn("delete error: " + JSON.toJSONString(params), e);
            }
            return 0;
        }
    }
}
