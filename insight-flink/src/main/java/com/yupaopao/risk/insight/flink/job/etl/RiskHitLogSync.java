package com.yupaopao.risk.insight.flink.job.etl;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.connector.clickhouse.map.DataFlatMap;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CkColumnRepairMapFunction;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.connector.es.sink.SinkCreator;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.StringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.process.logon.LoginFlatMap;
import com.yupaopao.risk.insight.flink.process.logon.LogonFilter;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;

import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.*;

/****
 * zengxiangcai
 * 2023/3/28 23:33
 ***/
public class RiskHitLogSync {
    public static void main(String[] args) throws Exception {
        //get env
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        //checkpoint setting

        env.enableCheckpointing(2 * 60 * 1000);
        env.getCheckpointConfig().setMinPauseBetweenCheckpoints(50 * 1000);
        // allow only one checkpoint to be in progress at the same time
        env.getCheckpointConfig().setMaxConcurrentCheckpoints(1);
        env.getCheckpointConfig().enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);


        //load property from apollo
        KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA_RISK_ALI);
        ClickHouseProperties clickHouseProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        Properties etlProperties = kafkaProperties.getProperties();
        etlProperties.put("group.id", KAFKA_GROUP_ID_ETL_RISK_HIT_LOG_HBASE + "_newCk");
        etlProperties.put(FlinkKafkaConsumerBase.KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS, "30000");
        EsProperties esProperties = EsProperties.getProperties(PropertyType.ES_FLINK_AUDIT);

//        List<String> topics = Arrays.asList("RISK-HIT-RESULT-LOG");
        List<String> topics = Arrays.asList(KAFKA_TOPIC_RISK_ONLINE_RESULT_LOG,
                KAFKA_TOPIC_PAYMENT_RISK_ONLINE_RESULT_LOG);
        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics,
                new StringKafkaDeserializationSchemaWrapper(new SimpleStringSchema()),
                etlProperties);

        DataStream<String> stream = env
                .addSource(consumer)
                .name("read_kafka_risk_online_result_log")
                .setParallelism(3);
//        consumer.setStartFromEarliest(); //切换集群
        consumer.setStartFromGroupOffsets();
//        consumer.setStartFromTimestamp(1680277800000L);// 切换到新集群 2023-03-31 23:50:00

        OutputTag<String> register = new OutputTag<String>("register") {
        };
        OutputTag<String> login = new OutputTag<String>("login") {
        };
        OutputTag<String> logon = new OutputTag<String>("logon") {
        };
        OutputTag<String> ck = new OutputTag<String>("ck") {
        };
        SingleOutputStreamOperator<String> process = stream.process(new ProcessFunction<String, String>() {
            @Override
            public void processElement(String value, Context ctx, Collector<String> out) throws Exception {
                JSONObject valueObj = JSONObject.parseObject(value);
                String eventCode = valueObj.getString("eventCode");
                String createdAt = valueObj.getString("createdAt");
                if (createdAt != null && createdAt.compareTo("2023-04-01 00:00:00") < 0) {
                    //0401前的数据已经有了
                    return;
                }
                if ("user-register".equals(eventCode) || "user-register-post".equals(eventCode)) {
                    ctx.output(register, value);
                } else if ("user-login".equals(eventCode) || "user-login-post".equals(eventCode)) {
                    ctx.output(login, value);
                }
                // 注册登录前置存储
                if ("user-register".equals(eventCode) || "user-login".equals(eventCode)) {
                    ctx.output(logon, value);
                }
                if (clickHouseProperties.isCanFlush()) {
                    ctx.output(ck, value);
                }
                out.collect(value);
            }
        }).setParallelism(3);


        clickHouseProperties.setInsertDistributedSync(true);
        // create sink
        ClickHouseStreamSink ckSink = new ClickHouseStreamSink(clickHouseProperties, "risk_hit_log", 1000 * 60 * 1,
                10000, true);
        ckSink.setUniqueKey("traceId");

        /*
        处理注册登录信息
         */
        process.getSideOutput(register)
                .flatMap(new RiskHitLogETL.IdFlatMap("Mobile"))
                .filter(new LogonFilter("user-register"))
                .flatMap(new LoginFlatMap("user-register"))
                .addSink(SinkCreator.createLogonSink(esProperties, "risk_register_log_data"))
                .name("write to es[risk_register_log]");

        process.getSideOutput(login)
                .flatMap(new RiskHitLogETL.IdFlatMap("UserId"))
                .filter(new LogonFilter("user-login"))
                .flatMap(new LoginFlatMap("user-login"))
                .addSink(SinkCreator.createLogonSink(esProperties, "risk_login_log_data"))
                .name("write to es[risk_login_log]");

        /*
        处理登录注册前置存储
         */
        process.getSideOutput(logon)
                .addSink(SinkCreator.createLogonSinkWithoutId(esProperties, "risk_logon_log"))
                .name("write to es[risk_logon_log]");

        process.getSideOutput(ck)
                .flatMap(new DataFlatMap()).setParallelism(3)
                //ck字段修复
                .map(new CkColumnRepairMapFunction("risk_hit_log", clickHouseProperties)).setParallelism(3)
                .addSink(ckSink).setParallelism(1)
                .name("write to CK[risk_hit_log]");

        env.execute("SYNC(risk-hit-log:kafka ---> ck)");
    }
}
