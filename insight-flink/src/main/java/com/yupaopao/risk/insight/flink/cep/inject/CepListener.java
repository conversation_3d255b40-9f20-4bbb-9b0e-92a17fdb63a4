package com.yupaopao.risk.insight.flink.cep.inject;

import com.yupaopao.risk.insight.common.cep.beans.CepRule;
import org.apache.flink.cep.pattern.Pattern;

import java.io.Serializable;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-11 20:37
 *
 ***/

public interface CepListener<T> extends Serializable {

    /***
     * 初始化
     * @throws Exception
     */
    void init() throws Exception;

    /***
     * 获取最新的period
     * @return
     * @throws Exception
     */
    Map<String,Pattern<T, ?>> getPatterns() throws Exception;

    long getPeriod();

   /**
    * nfa id
    * @return
    */
    NfaKeySelector<T, String> getNfaKeySelector();

    Integer getChangedVersion();

    Map<String, CepRule> getRuleMap();

    void close();

    String getDataSource();
}
