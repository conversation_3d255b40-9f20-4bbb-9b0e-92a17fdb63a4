package com.yupaopao.risk.insight.flink.job.behaviour;

import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.behavior.CustomSynchroTrapProcessor;

/****
 * zengxiangcai
 * 2022/5/12 17:45
 ***/
public class CustomSynchroTrapExecuteJob {
  public static void main(String[] args) throws Exception {
    new BatchJobBuilder()
            .withJobName("custom-synchroTrap-execute")
            .withDefaultExecute(true)
            .withProcessor(new CustomSynchroTrapProcessor())
            .start(args);
  }
}
