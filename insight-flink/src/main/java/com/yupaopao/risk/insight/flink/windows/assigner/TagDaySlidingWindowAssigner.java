package com.yupaopao.risk.insight.flink.windows.assigner;

import com.yupaopao.risk.insight.flink.windows.AggTagDetail;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.WindowAssigner;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class TagDaySlidingWindowAssigner extends WindowAssigner<AggTagDetail, TimeWindow> {

    @Override public Collection<TimeWindow> assignWindows(AggTagDetail element, long timestamp, WindowAssignerContext context) {
        long slide = 24 * 60 * 60 * 1000;
        long size = element.getTimeSpan() * 24 * 60 * 60 * 1000;
        long slideAvgNum = size / slide;
        List<TimeWindow> windows = new ArrayList<>((int) (slideAvgNum));
        timestamp = null != element.getTimeStamp() ? element.getTimeStamp() : context.getCurrentProcessingTime();
//        timestamp = context.getCurrentProcessingTime();
        long lastStart = TimeWindow.getWindowStartWithOffset(timestamp, 16 * 60 * 60 * 1000, slide);
        for (long start = lastStart; start > timestamp - size; start -= slide) {
            windows.add(new TimeWindow(start, start + size));
        }
        return windows;
    }

    @Override public Trigger<AggTagDetail, TimeWindow> getDefaultTrigger(StreamExecutionEnvironment env) {
        return null;
    }

    @Override public TypeSerializer<TimeWindow> getWindowSerializer(ExecutionConfig executionConfig) {
        return new TimeWindow.Serializer();
    }

    @Override public boolean isEventTime() {
        return false;
    }
}
