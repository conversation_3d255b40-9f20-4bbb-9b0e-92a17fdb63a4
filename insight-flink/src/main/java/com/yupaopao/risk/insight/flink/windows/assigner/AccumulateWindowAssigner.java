package com.yupaopao.risk.insight.flink.windows.assigner;

import com.yupaopao.risk.insight.flink.windows.AccumulateCalDetail;
import com.yupaopao.risk.insight.flink.windows.triggers.AccumulateProcessTimeTrigger;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.WindowAssigner;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

public class AccumulateWindowAssigner extends WindowAssigner<AccumulateCalDetail, TimeWindow> {
    private long slideAvgNum = 10;

    @Override public Collection<TimeWindow> assignWindows(AccumulateCalDetail element, long timestamp, WindowAssignerContext context) {
        long size = element.getTimeSpan() * 60 * 1000;
        timestamp = context.getCurrentProcessingTime();

        long start = TimeWindow.getWindowStartWithOffset(timestamp, 16 * 60 * 60 * 1000, size);
        return Collections.singletonList(new TimeWindow(start, start + size));
//        List<TimeWindow> windows = new ArrayList<>((int) (size / slide));
//        long lastStart = TimeWindow.getWindowStartWithOffset(timestamp, 16 * 60 * 60 * 1000, slide);
//        if (element.getTimeSpan() >= 10) {
//            for (long start = lastStart; start > timestamp - size; start -= slide) {
//                windows.add(new TimeWindow(start, start + size));
//            }
//        } else {
//            windows.add(new TimeWindow(lastStart, lastStart + size));
//        }
    }

    @Override public Trigger<AccumulateCalDetail, TimeWindow> getDefaultTrigger(StreamExecutionEnvironment env) {
        return AccumulateProcessTimeTrigger.create(slideAvgNum);
    }

    @Override public TypeSerializer<TimeWindow> getWindowSerializer(ExecutionConfig executionConfig) {
        return new TimeWindow.Serializer();
    }

    @Override public boolean isEventTime() {
        return false;
    }
}
