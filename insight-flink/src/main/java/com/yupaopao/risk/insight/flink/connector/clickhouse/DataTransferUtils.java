package com.yupaopao.risk.insight.flink.connector.clickhouse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/****
 * zengxiangcai
 * 2022/10/25 16:31
 ***/
public class DataTransferUtils {
    public static boolean isEmptyObject(Object objValue) {
        if (objValue == null) {
            return true;
        }
        if (objValue instanceof String) {
            return StringUtils.isEmpty((String) objValue);
        }
        if (objValue instanceof List) {
            return CollectionUtils.isEmpty((List) objValue);
        }
        if (objValue instanceof Map) {
            Map m = (Map) objValue;
            return m.isEmpty();
        }
        return false;
    }

}
