package com.yupaopao.risk.insight.flink.job.outlier;

import com.alibaba.alink.common.MLEnvironment;
import com.alibaba.alink.operator.batch.source.DataSetWrapperBatchOp;
import com.alibaba.alink.params.shared.clustering.HasFastMetric;
import com.alibaba.alink.pipeline.Pipeline;
import com.alibaba.alink.pipeline.dataproc.MinMaxScaler;
import com.alibaba.alink.pipeline.dataproc.format.ColumnsToVector;
import com.alibaba.alink.pipeline.similarity.VectorNearestNeighbor;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.lindorm.client.core.utils.Bytes;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.connector.clickhouse.source.CKInputFormat;
import com.yupaopao.risk.insight.flink.job.base.ALinkJobBuilder;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.DataSet;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.io.PrintingOutputFormat;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Put;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yupaopao.risk.insight.common.constants.HBaseConstants.COLUMN_FAMILY;

/****
 * zengxiangcai
 * 2022/3/11 2:22 PM
 ***/
public class KnnAnomalyDetectionJob {
    public static void main(String[] args) throws Exception {


        new ALinkJobBuilder()
                .withJobName("knn anomaly")
                .withProcessor(new KnnAnomalyDetectionProcessor())
                .start(args);
//
//        Map<String,String> argMap = new HashMap<>();
//        argMap.put("runDay","2022-03-21");
//        System.err.println(URLEncoder.encode(JSON.toJSONString(argMap),"UTF-8"));
//
//    List<Row> columns = Arrays.asList(Row.of("userId","1.1", "2.2", "3.3", "4.4", "5.5"));
//
//    ColumnsToVector c2v = new ColumnsToVector().setVectorCol("vec").setVectorSize(5).setSelectedCols("f0","f1","f2",
//            "f3","f4").setReservedCols("f");
//
//    BatchOperator<?> data = new MemSourceBatchOp(columns,
//            "f,f0,f1,f2,f3,f4".split(","));
//
//    c2v.transform(data).getDataSet().map(elem->{
//      System.err.println(elem);
//      return elem;
//    }).print();
//
//    String[] selectedColumns = new String[]{"userId","mobileNo"};
//    String[] hourColumns = new String[]{"h1","h2","h3"};
//    String[] vecColumns = new String[selectedColumns.length+hourColumns.length];
//    System.arraycopy(selectedColumns,0,vecColumns,0,selectedColumns.length);
//    System.arraycopy(hourColumns,0,vecColumns,selectedColumns.length,hourColumns.length);
    }

    private static void writeHbase() throws Exception {

        List<String> deviceList = Arrays.asList("");

        final ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        env.fromElements("start").map(new RichMapFunction<String, String>() {
            private transient HTable table;

            @Override
            public String map(String value) throws Exception {
                List<Put> puts = new ArrayList<>();
                for (String deviceId : deviceList) {

                    Put put = new Put(Bytes.toBytes(deviceId));
                    byte[] columnFamily = Bytes.toBytes(COLUMN_FAMILY);
                    put.addColumn(columnFamily, Bytes.toBytes("bFarmer"), Bytes.toBytes(false));
                    puts.add(put);
                }
                table.put(puts);

                return "finish";
            }

            @Override
            public void open(Configuration parameters) throws Exception {

                super.open(parameters);
                HBaseProperties hBProperties = HBaseProperties.getProperties(PropertyType.HBASE);
                Connection conn = HBaseUtil.createConnection(hBProperties);
                table = HBaseUtil.getTable("risk_device_tag", conn);
            }

            @Override
            public void close() throws Exception {
                if (table != null) {
                    table.close();
                }
                super.close();
            }
        }).output(new PrintingOutputFormat<>());

        env.execute("write hbase");
    }


    public static class KnnAnomalyDetectionProcessor implements ALinkJobBuilder.MainProcessor {

        @Override
        public void internalProcess(MLEnvironment mlEnv, Long mlSessionId, Map<String, String> argMap) throws Exception {
//      //处理类型
//      String type = argMap.get("type");
//      String sql =


            String runDay;
            String dataDay;
            if (StringUtils.isEmpty(argMap.get("runDay"))) {
                dataDay = InsightDateUtils.getBeforeDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
                runDay = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
            } else {
                runDay = argMap.get("runDay");
                dataDay = runDay;
            }

            String columns[] = ("userId,mobileNo,regDeviceCount,regIpCount,ipV6Count,ipV4Count,appDeviceCount," +
                    "notAppDeviceCount,cityCount,provinceCount,equipmentCount,relatedDeviceRegCount,relatedDeviceIpCount," +
                    "relatedDeviceMobileCount,relatedDeviceCityCount,relatedDeviceProvinceCount").split(",");
            ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
            CKInputFormat ckInputFormat = new CKInputFormat(ckProperties, String.format(querySql,dataDay,dataDay,dataDay
                    ,dataDay),
                    null);
            DataSet<Row> ds =
                    mlEnv.getExecutionEnvironment().createInput(ckInputFormat).filter(elem -> !HBaseUtil.emptyResultJson(elem)).setParallelism(1).map(elem -> {
                        JSONObject row = JSON.parseObject(elem);
//                String userId = row.getString("userId");
//                String mobileNo = row.getString("mobileNo");
                        String registerHour = row.remove("registerHour").toString();
                        Row rowInfo = Row.withNames();
                        for (String col : columns) {
                            if ("userId".equals(col) || "mobileNo".equals(col)) {
                                rowInfo.setField(col, row.getString(col));
                            } else {
                                rowInfo.setField(col, row.getInteger(col));
                            }

                        }
                        String[] hours = registerHour.substring(1, registerHour.length() - 1).split(",");
                        for (int i = 0; i < 24; i++) {
                            rowInfo.setField("h_" + i, 0);
                        }
                        for (String h : hours) {
                            rowInfo.setField("h_" + h, 1);
                        }
                        return rowInfo;

                    });


            String[] hourColumns = new String[24];
            for (int i = 0; i < 24; i++) {
                hourColumns[i] = "h_" + i;
            }

            List<String> allColumns = new ArrayList<>();
            allColumns.addAll(Arrays.asList(columns));
            allColumns.addAll(Arrays.asList(hourColumns));

            String[] COL_NAMES = allColumns.toArray(new String[0]);

            String[] selectedColumns =
                    Stream.of(columns).filter(elem -> !elem.equals("userId") && !elem.equals("mobileNo")).collect(Collectors.toList()).toArray(new String[0]);


            TypeInformation[] colTypes = new TypeInformation[COL_NAMES.length];
            for (int i = 0; i < COL_NAMES.length; i++) {
                if ("userId".equals(COL_NAMES[i]) || "mobileNo".equals(COL_NAMES[i])) {
                    colTypes[i] = TypeInformation.of(String.class);
                } else {
                    colTypes[i] = TypeInformation.of(Integer.class);
                }

            }
            DataSetWrapperBatchOp source = new DataSetWrapperBatchOp(ds, COL_NAMES, colTypes);

            MinMaxScaler minMaxScaler = new MinMaxScaler()
                    .setSelectedCols(selectedColumns);

            String[] vecColumns = new String[selectedColumns.length + 24];
            System.arraycopy(selectedColumns, 0, vecColumns, 0, selectedColumns.length);
            System.arraycopy(hourColumns, 0, vecColumns, selectedColumns.length, hourColumns.length);

            ColumnsToVector c2v = new ColumnsToVector()
                    .setVectorCol("vec")
                    .setVectorSize(vecColumns.length)
                    .setSelectedCols(vecColumns).setReservedCols("userId", "mobileNo");
            VectorNearestNeighbor vnn = new VectorNearestNeighbor()
                    .setIdCol("mobileNo")
                    .setReservedCols("userId", "mobileNo")
                    .setSelectedCol("vec")
                    .setMetric(HasFastMetric.Metric.EUCLIDEAN)
                    .setOutputCol("out_cols")
                    .setTopN(10);
            Pipeline pipeline = new Pipeline().add(minMaxScaler).add(c2v).add(vnn);

            CKOutputFormat ckOutputFormat = new CKOutputFormat(ckProperties, "knn_graph");
            pipeline.fit(source).transform(source).select("mobileNo,userId,out_cols").getDataSet().flatMap(new RichFlatMapFunction<Row, String>() {
                @Override
                public void flatMap(Row elem, Collector<String> out) throws Exception {

                    System.err.println(elem);
                    String mobileNo = elem.getField(0).toString();
                    String userId = elem.getField(1).toString();
                    JSONObject metrics = JSON.parseObject(elem.getField(2).toString());
                    JSONArray metric = metrics.getJSONArray("METRIC");
                    JSONArray ids = metrics.getJSONArray("ID");
                    Map<String, Object> row = new HashMap<>();
                    for (int i = 0; i < ids.size(); i++) {
                        String toId = ids.getString(i);
                        if (toId.equals(mobileNo)) {
                            continue;
                        }
                        row.put("id", mobileNo);
                        row.put("userId", userId);
                        row.put("toId", toId);
                        row.put("weight", metric.getBigDecimal(i));
                        row.put("runDay", runDay);
                        out.collect(JSON.toJSONString(row));
                    }

                }
            }).output(ckOutputFormat);


        }


        public static String querySql = "select  mobileNo,userId,regDeviceCount,regIpCount,ipV6Count,ipV4Count,appDeviceCount,notAppDeviceCount,cityCount,provinceCount,equipmentCount,registerHour,\n" +
                "length(arrayDistinct(groupArray(traceList))) relatedDeviceRegCount,\n" +
                "length(arrayDistinct(groupArray(deviceIpList))) relatedDeviceIpCount,\n" +
                "length(arrayDistinct(groupArray(deviceMobileList))) relatedDeviceMobileCount,\n" +
                "length(arrayDistinct(groupArray(deviceCityList))) relatedDeviceCityCount,\n" +
                "length(arrayDistinct(groupArray(deviceProvinceList))) relatedDeviceProvinceCount\n" +
                "from (\n" +
                "select mobileNo,userId,\n" +
                "count(distinct deviceId) regDeviceCount,\n" +
                "count(distinct clientIp) regIpCount,\n" +
                "countIf(length(clientIp)>15) ipV6Count,\n" +
                "countIf(length(clientIp)<=15) ipV4Count,\n" +
                "countIf(length(deviceId)==62) appDeviceCount,\n" +
                "countIf(length(deviceId)!=62) notAppDeviceCount,\n" +
                "count(distinct data_clientIpDetail_city) cityCount,\n" +
                "count(distinct data_clientIpDetail_province) provinceCount,\n" +
                "count(distinct data_clientDetail_equipment) equipmentCount,\n" +
                "arrayDistinct(groupArray(toHour(createdAt))) registerHour,\n" +
                "arrayJoin(arrayDistinct(groupArray(deviceId))) deviceList\n" +
                "from risk_hit_log where createdAt between '%s 00:00:00' and '%s 23:59:59' and eventCode = " +
                "'user-register-post'\n" +
                "and userId global not in (\n" +
                "                  select valueData from risk_gray_list_sync where syncDate =today()\n" +
                "               ) \n" +
                "               and clientIp global not in (\n" +
                "                  select valueData from risk_gray_list_sync where syncDate =today()\n" +
                "               ) \n" +
                "               and deviceId global not in (\n" +
                "                  select valueData from risk_gray_list_sync where syncDate =today()\n" +
                "               ) \n" +
                "group by mobileNo,userId\n" +
                ") a global all inner join (\n" +
                "select deviceId,\n" +
                "arrayJoin(arrayDistinct(groupArray(traceId))) traceList,\n" +
                "arrayJoin(arrayDistinct(groupArray(mobileNo))) deviceMobileList,\n" +
                "arrayJoin(arrayDistinct(groupArray(clientIp))) deviceIpList,\n" +
                "arrayJoin(arrayDistinct(groupArray(data_clientIpDetail_city))) deviceCityList,\n" +
                "arrayJoin(arrayDistinct(groupArray(data_clientIpDetail_province))) deviceProvinceList\n" +
                "from risk_hit_log where createdAt between '%s 00:00:00' and '%s 23:59:59' and eventCode in " +
                "('user-register')\n" +
                "group by deviceId\n" +
                ") b on a.deviceList = b.deviceId\n" +
                "group by \n" +
                "mobileNo,userId,regDeviceCount,regIpCount,ipV6Count,ipV4Count,appDeviceCount,notAppDeviceCount," +
                "cityCount,provinceCount,equipmentCount,registerHour\n";
    }

}
