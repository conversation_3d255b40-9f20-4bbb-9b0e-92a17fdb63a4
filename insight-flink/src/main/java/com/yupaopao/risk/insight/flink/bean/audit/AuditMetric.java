package com.yupaopao.risk.insight.flink.bean.audit;

import lombok.Data;

import java.io.Serializable;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc AuditMetric
 * @date 2020/7/2
 */
@Data
public class AuditMetric implements Serializable {
    private static final long serialVersionUID = 5926027922213323177L;
    private String createdAt;
    private String bu;
    private String group;
    private String team;
    private String auditor;
    private String channel;
    private String phase;
    private String result;
    private String level;
    private String reason;
    private String metric;
    private Double value;
    private String startTime;
    private String endTime;
    private String source;

    private String groupKey;

    public AuditMetric cloneData(AuditMetric auditMetric) {
        this.createdAt = auditMetric.getCreatedAt();
        this.bu = auditMetric.getBu();
        this.group = auditMetric.getGroup();
        this.team = auditMetric.getTeam();
        this.auditor = auditMetric.getAuditor();
        this.channel = auditMetric.getChannel();
        this.phase = auditMetric.getPhase();
        this.result = auditMetric.getResult();
        this.level = auditMetric.getLevel();
        this.reason = auditMetric.getReason();
        this.metric = auditMetric.getMetric();
        this.value = auditMetric.getValue();
        this.startTime = auditMetric.getStartTime();
        this.endTime = auditMetric.getEndTime();
        this.groupKey = auditMetric.getGroupKey();
        this.source = auditMetric.getSource();
        return this;
    }
}


