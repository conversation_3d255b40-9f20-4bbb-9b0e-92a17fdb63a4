package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.flink.windows.AggTagDetail;
import org.apache.flink.api.common.functions.AggregateFunction;

import java.util.HashSet;

public class TagSetFunction implements AggregateFunction<AggTagDetail, TagAggregateResult, TagAggregateResult> {

    @Override public TagAggregateResult createAccumulator() {
        TagAggregateResult result = new TagAggregateResult();
        result.setDistinct(new HashSet<>());
        return result;
    }

    @Override public TagAggregateResult add(AggTagDetail value, TagAggregateResult accumulator) {
        accumulator.setId(value.getId());
        accumulator.setGroupKey(value.getGroupKey());
        accumulator.getDistinct().add(value.getData());
        accumulator.setType(value.getType());
        Cat.logMetricForCount("tag.set");
        return accumulator;
    }

    @Override public TagAggregateResult getResult(TagAggregateResult accumulator) {
        return accumulator;
    }

    @Override public TagAggregateResult merge(TagAggregateResult a, TagAggregateResult b) {
        return a;
    }
}
