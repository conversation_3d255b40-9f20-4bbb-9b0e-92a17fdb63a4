package com.yupaopao.risk.insight.flink.job.etl.common.connect.odps.reader;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.security.UserGroupInformation;
import sun.security.krb5.Config;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/****
 * zengxiangcai
 * 2023/6/1 20:19
 ***/

@Slf4j
public class SQLUtil {
    private final String HIVE_URL = "***********************************************,bixin-bigdata005.yppagent.com:2182,bixin-bigdata006.yppagent.com:2182/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=hiveserver2;principal=hive/<EMAIL>";
    private final String SPARK_URL = "***********************************************,bixin-bigdata005.yppagent.com:2182,bixin-bigdata006.yppagent.com:2182/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=kyuubi-cluster;principal=hive/<EMAIL>";
    private final String IMPALA_URL = "***********************************************,bixin-bigdata005.yppagent.com:2182,bixin-bigdata006.yppagent.com:2182/;serviceDiscoveryMode=zooKeeper;zooKeeperNamespace=impala-weblog-ha;principal=impala/<EMAIL>";
    private final String DRIVER_NAME = "org.apache.hive.jdbc.HiveDriver";
    private String KRB5_CONF_DIR = null;
    private String KEYTAB_DIR = null;
    private final String PRINCIPAL = "public_data/<EMAIL>";
    private ArrayList<String> header;


    public SQLUtil(String confDir, String tabDir) {
        this.KRB5_CONF_DIR = confDir;
        this.KEYTAB_DIR = tabDir;
    }

    public SQLUtil checkKrbAuth() {
        Configuration conf = new Configuration();
        conf.set("hadoop.security.authentication", "Kerberos");
        conf.set("keytab.file", this.KEYTAB_DIR);
        conf.set("kerberos.principal", PRINCIPAL);
        System.setProperty("java.security.krb5.conf", this.KRB5_CONF_DIR);
        try {
            Config.refresh();
            UserGroupInformation.setConfiguration(conf);
            UserGroupInformation.loginUserFromKeytab(PRINCIPAL, this.KEYTAB_DIR);
            log.info("{}, userGroup afterLogin: {}", new java.util.Date(), UserGroupInformation.getLoginUser());
        } catch (Exception e) {
            log.error("error for kerberos login,error=" + e.getMessage(), e);
        }
        return this;

    }

    private Connection getImpalaConnection() {
        try {
            Class.forName(DRIVER_NAME);
            return DriverManager.getConnection(IMPALA_URL);
        } catch (Exception var2) {
            log.error("get impala conn error," + var2.getMessage(), var2);
            return null;
        }
    }

    public Connection getSparkConnection() {
        try {
            Class.forName(DRIVER_NAME);
            return DriverManager.getConnection(SPARK_URL);
        } catch (Exception var2) {
            log.error("get spark conn error," + var2.getMessage(), var2);
            return null;
        }
    }

    public Connection getHiveConnection() {
        try {
            Class.forName(DRIVER_NAME);
            return DriverManager.getConnection(HIVE_URL);
        } catch (Exception var2) {
            log.error("get hive conn error," + var2.getMessage(), var2);
            return null;
        }
    }

    public JSONArray executeQuery(String sqlStr, String engineType) throws SQLException {
        this.checkKrbAuth();
        Connection conn = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            switch (engineType) {
                case "spark":
                    conn = this.getSparkConnection();
                    break;
                case "impala":
                    conn = this.getImpalaConnection();
                    break;
                default:
                    conn = this.getHiveConnection();
            }
            if (conn == null) {
                return new JSONArray();
            }
            statement = conn.createStatement();
            resultSet = statement.executeQuery(sqlStr);
            int columns = resultSet.getMetaData().getColumnCount();
            ArrayList<String> header = new ArrayList();
            ArrayList<Map<String, String>> resultData = new ArrayList();

            for (int i = 1; i <= columns; ++i) {
                String columnName = resultSet.getMetaData().getColumnName(i);
                if (columnName.contains(".")) {
                    header.add(columnName.split("\\.")[1]);
                } else {
                    header.add(resultSet.getMetaData().getColumnName(i));
                }
            }

            if (header.size() == 0) {
                return null;
            } else {
                this.header = header;

                while (resultSet.next()) {
                    HashMap<String, String> map = new HashMap();

                    for (int i = 1; i <= columns; ++i) {
                        map.put(header.get(i - 1), resultSet.getString(i));
                    }

                    resultData.add(map);
                }

                resultSet.close();
                statement.close();
                conn.close();
                return JSONObject.parseArray(JSON.toJSONString(resultData));
            }
        } catch (SQLException var11) {
            log.error("get executeSql error," + var11.getMessage(), var11);
            return null;
        }
    }

    public List<String> getHeader() {
        return this.header;
    }
}
