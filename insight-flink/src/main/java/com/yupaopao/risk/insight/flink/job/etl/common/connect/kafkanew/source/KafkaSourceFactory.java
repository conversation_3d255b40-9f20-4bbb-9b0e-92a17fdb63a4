/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.yupaopao.risk.insight.flink.job.etl.common.connect.kafkanew.source;

import com.dtstack.flinkx.reader.MetaColumn;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.SourceFactory;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.kafka.core.KafkaBaseInputFormat;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.kafka.core.KafkaConfigKeys;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.kafka.core.enums.StartupMode;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.kafka.core.util.KafkaUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.kafkanew.core.serialization.RowDeserializationSchema;
import com.yupaopao.risk.insight.flink.job.etl.common.options.DataTransferConfig;
import com.yupaopao.risk.insight.flink.job.etl.common.options.ReaderConfig;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;


public class KafkaSourceFactory extends SourceFactory {

    protected String topic;
    protected String groupId;
    protected String codec;
    protected boolean blankIgnore;
    protected String encoding;
    protected String mode;
    protected String offset;
    protected Long timestamp;
    protected Map<String, String> consumerSettings;
    protected List<MetaColumn> metaColumns;

    @SuppressWarnings("unchecked")
    public KafkaSourceFactory(DataTransferConfig config, StreamExecutionEnvironment env) {
        super(config, env);
        ReaderConfig readerConfig = config.getJob().getContent().get(0).getReader();

        topic = readerConfig.getParameter().getStringVal(KafkaConfigKeys.KEY_TOPIC);
        groupId = readerConfig.getParameter().getStringVal(KafkaConfigKeys.KEY_GROUP_ID, "default");
        codec = readerConfig.getParameter().getStringVal(KafkaConfigKeys.KEY_CODEC, "json");
        blankIgnore = readerConfig.getParameter().getBooleanVal(KafkaConfigKeys.KEY_BLANK_IGNORE, false);
        encoding = readerConfig.getParameter().getStringVal(KafkaConfigKeys.KEY_ENCODING, StandardCharsets.UTF_8.name());
        mode = readerConfig.getParameter().getStringVal(KafkaConfigKeys.KEY_MODE, StartupMode.GROUP_OFFSETS.name);
        offset = readerConfig.getParameter().getStringVal(KafkaConfigKeys.KEY_OFFSET, "");
        timestamp = readerConfig.getParameter().getLongVal(KafkaConfigKeys.KEY_TIMESTAMP, -1L);
        consumerSettings = (Map<String, String>) readerConfig.getParameter().getVal(KafkaConfigKeys.KEY_CONSUMER_SETTINGS);
        metaColumns = MetaColumn.getMetaColumns(readerConfig.getParameter().getColumn());
    }

    @Override
    public DataStream<Map<String, Object>> createSource() {
        Properties props = new Properties();
        props.put("group.id", groupId);
        props.put("flink.partition-discovery.interval-millis", 10000);
        props.putAll(consumerSettings);
        KafkaConsumer consumer = new KafkaConsumer(
                Arrays.asList(topic.split(",")),
                new RowDeserializationSchema(this.codec, this.encoding),
                props);
        switch (StartupMode.getFromName(mode)) {
            case EARLIEST:
                consumer.setStartFromEarliest();
                break;
            case LATEST:
                consumer.setStartFromTimestamp(System.currentTimeMillis());
                break;
            case TIMESTAMP:
                consumer.setStartFromTimestamp(timestamp);
                break;
            case SPECIFIC_OFFSETS:
                consumer.setStartFromSpecificOffsets(KafkaUtil.parseSpecificOffsetsStringNew(topic, offset));
                break;
            default:
                consumer.setStartFromGroupOffsets();
                break;
        }
        consumer.setCommitOffsetsOnCheckpoints(groupId != null);
        TypeInformation<Map<String, Object>> typeInformation = consumer.getProducedType();
        return env.addSource(consumer, this.getClass().getName(), typeInformation);
    }

    public KafkaBaseInputFormat getFormat(){
        return new KafkaBaseInputFormat();
    }
}
