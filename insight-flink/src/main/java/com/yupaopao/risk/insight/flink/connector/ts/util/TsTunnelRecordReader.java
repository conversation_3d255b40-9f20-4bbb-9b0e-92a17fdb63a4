//package com.yupaopao.risk.insight.flink.connector.ts.util;
//
//import com.alicloud.openservices.tablestore.model.StreamRecord;
//import com.alicloud.openservices.tablestore.model.tunnel.internal.ReadRecordsResponse;
//import com.yupaopao.risk.insight.flink.property.connection.TsProperties;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import java.io.Serializable;
//import java.util.*;
//
//public class TsTunnelRecordReader implements Serializable {
//
//    static final String OTS_FINISHED = "finished";
//    private static final long FETCH_DATA_SIZE_BAR = 900 * 1024;
//    private static final long FETCH_DATA_ROW_BAR = 500;
//    private static final int HEARTBEAT_GAP = 200 * 1000;
//
//    private static Logger LOG = LoggerFactory.getLogger(TsTunnelRecordReader.class);
//    private TsTunnelApi tunnelApi;
//    private List<TsTunnelInputSplit> inputSplits;
//    private String nextToken;
//    private long lastHeartbeatTime;
//
//    /**
//     * 上次获取数据时间
//     */
//    private long lastFetchDataTime = 0;
//    /**
//     * 上次获取数据的字节大小
//     */
//    private long lastFetchRawSize = Long.MAX_VALUE;
//    /**
//     * 上次获取数据条数
//     */
//    private long lastFetchRowCount = Long.MAX_VALUE;
//
//    private volatile boolean interrupted = false;
//
//    public TsTunnelRecordReader(TsProperties properties, String tableName, String tunnelName) {
//        this.tunnelApi = new TsTunnelApi(properties, tableName, tunnelName);
//        this.tunnelApi.connectTunnel();
//        this.inputSplits = this.tunnelApi.getScheduledSplits(1, 0);
//        inputSplits.forEach(p -> p.setInitToken(tunnelApi.getCheckpoint(p.getSplitId())));
//        lastHeartbeatTime = System.currentTimeMillis();
//    }
//
//    /**
//     * 是否获取增量数据
//     *
//     * @return 是否获取数据
//     */
//    public boolean shouldFetchOts() {
//        boolean shouldFetch;
//        // 休眠策略（数据量、增量条数小于阈值而且两次获取时间小于1s）
//        if (lastFetchRawSize < FETCH_DATA_SIZE_BAR && lastFetchRowCount < FETCH_DATA_ROW_BAR) {
//            shouldFetch = (System.currentTimeMillis() - lastFetchDataTime > 1000);
//        } else {
//            shouldFetch = true;
//        }
//        return shouldFetch;
//    }
//
//    /**
//     * 获取数据
//     *
//     * @return 数据处理结果
//     */
//    public List<Map<String, Object>> getData() {
//        List<Map<String, Object>> result = new ArrayList<>();
//
//        heartBeat();
//        boolean isTunnelRefresh = false;
//        for (TsTunnelInputSplit split : inputSplits) {
//            if (OTS_FINISHED.equals(split.getInitToken())) {
//                LOG.info("初始化返回finished 标志，跳过该channel，待后续刷新");
//                continue;
//            }
//
//            List<StreamRecord> records = fetchOts(split.getInitToken(), split.getSplitId());
//
//            for (StreamRecord record : records) {
//                Map<String, Object> recordMap = TsUtils.parseToMap(record);
//                if (recordMap.size() > 0) {
//                    result.add(recordMap);
//                }
//            }
//
//            if (OTS_FINISHED.equals(nextToken)) {
//                LOG.info("读取到数据结束标志，刷新tunnel");
//                isTunnelRefresh = true;
//            } else {
//                try {
//                    tunnelApi.checkpoint(split.getSplitId(), split.getInitToken());
//                } catch (Exception e) {
//                    LOG.error(String.format("Update CheckPoint Error and Ignore it %s", e));
//                }
//            }
//            split.setInitToken(this.nextToken);
//        }
//        if (result.size() > 0) {
//            LOG.info("读取到数据：{} 条", result.size());
//        }
//
//        if (isTunnelRefresh){
//            refreshTunnel();
//        }
//        return result;
//    }
//
//    private void retryCheckoutPoint(String spiltId) {
//        int retry = 0;
//        while (true) {
//            LOG.error("尝试重新获取checkpoint，重试次数: {}", retry++);
//            String checkpoint = tunnelApi.getCheckpoint(spiltId);
//            if (StringUtils.isNotEmpty(checkpoint) && !OTS_FINISHED.equals(checkpoint)) {
//                nextToken = checkpoint;
//                break;
//            }
//            tunnelApi.refreshTunnelClient();
//            try {
//                Thread.sleep(100 * retry);
//            } catch (InterruptedException e) {
//                LOG.error("sleep 异常", e);
//            }
//        }
//    }
//
//    private void heartBeat() {
//        Long now = System.currentTimeMillis();
//        // 保持心跳，刷新channel
//        if (now - lastHeartbeatTime > HEARTBEAT_GAP) {
//            try {
//                inputSplits = tunnelApi.heartbeat();
//                inputSplits.forEach(p -> p.setInitToken(tunnelApi.getCheckpoint(p.getSplitId())));
//                lastHeartbeatTime = now;
//            } catch (Exception e) {
//                LOG.info("刷新失败，client过期，重新连接: {}", e.getMessage());
//                tunnelApi.refreshTunnelClient();
//                tunnelApi.connectTunnel();
//            }
//        }
//    }
//
//    private void refreshTunnel() {
//        tunnelApi.refreshTunnel();
//        this.tunnelApi.connectTunnel();
//        this.inputSplits = this.tunnelApi.getScheduledSplits(1, 0);
//        inputSplits.forEach(p -> p.setInitToken(tunnelApi.getCheckpoint(p.getSplitId())));
//    }
//
//    /**
//     * 关闭通道
//     */
//    public void close() {
//        tunnelApi.close();
//    }
//
//    /**
//     * 读取数据
//     *
//     * @param startToken token
//     * @return 增量数据
//     */
//    private List<StreamRecord> fetchOts(String startToken, String splitId) {
//        ReadRecordsResponse resp = tunnelApi.readRecords(splitId, startToken);
//        List<StreamRecord> records = resp.getRecords();
//        lastFetchDataTime = System.currentTimeMillis();
//        lastFetchRowCount = records.size();
//        lastFetchRawSize = resp.getMemoizedSerializedSize();
//        nextToken = resp.getNextToken();
//        return records;
//    }
//
//    /**
//     * 修改数据获取状态
//     */
//    public void interrupt() {
//        interrupted = true;
//    }
//
//    public boolean isCancel() {
//        return !interrupted;
//    }
//}
