package com.yupaopao.risk.insight.flink.job.etl;

import com.clickhouse.jdbc.ClickHouseConnection;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.io.PrintingOutputFormat;
import org.apache.flink.util.Collector;

import java.util.List;
import java.util.Map;

/****
 * zengxiangcai
 * 2023/3/28 10:41
 ***/


@Slf4j
public class SQLTestJob {
    public static void main(String[] args) throws Exception {
//        if(true){// "举报总量"
//            String sql = "\n" +
//                    "select count(DISTINCT data_attachment_complainId) as \"举报总量\" from audit_mx_spot_complain\n" +
//                    "where data_business = 'COMPLAIN'\n" +
//                    "  and data_attachment_status == 0\n" +
//                    "  and data_attachment_complainPerson == 0\n" +
//                    "  and toDate(toDateTime((`default`.`audit_mx_spot_complain`.`data_attachment_complainTime` / 1000))) = '2023-03-24'\n" +
//                    ""
////                    "    FORMAT TabSeparatedWithNamesAndTypes;"
//                    ;
//            ClickHouseProperties oldProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
//            try (
//                    ClickHouseConnection oldConn = ClickHouseUtil.createConnection(oldProperties);
//            ) {
//                List<Map<String, String>> queryList = ClickHouseUtil.executeQuery(oldConn, sql);
//            }
//            return;
//        }
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        env.fromElements("1").name("default source")
                .flatMap(new FlatMapFunction<String, String>() {

                    @Override
                    public void flatMap(String elem, Collector<String> out) throws Exception {

                        ClickHouseProperties oldProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
                        ClickHouseProperties newProperties = ClickHouseProperties.getProperties(PropertyType.CLICKHOUSE_RISK);
                        try (
                                ClickHouseConnection oldConn = ClickHouseUtil.createConnection(oldProperties);
                                ClickHouseConnection newConn = ClickHouseUtil.createConnection(newProperties);
                        ) {
                            String querySql = "\n" +
                                    "select  replaceAll(trim(query),'_local WHERE',' WHERE') as querySql,arrayJoin(groupArray(1)(query_id)) qId\n" +
                                    "from remote('10.111.203.167:3003,10.111.203.168:3003,10.111.203.169:3003,10.111.203.170:3003', system.query_log)\n" +
                                    "where event_time between '2023-03-21 00:00:00'  and '2023-03-28 00:00:00' and exception='' and query not like '%INSERT INTO%' \n" +
                                    "and http_user_agent!='Apache-HttpClient/4.5.13 (Java/11.0.16)'\n" +
                                    "and query not like 'desc%'\n" +
                                    "and query not like 'DESC%'\n" +
                                    "and (\n" +
                                    " querySql  like 'SELECT%' or \n" +
                                    " querySql  like 'select%'\n" +
                                    ")\n" +
                                    "and querySql not like '%KILL%'\n" +
                                    "and querySql not like 'CREATE%'\n" +
                                    "and querySql not like 'ALTER%'\n" +
                                    "and querySql not like '%CREATE TABLE%'\n" +
                                    "and querySql not like '%drop table%'\n" +
                                    "and querySql not like '%subquery%'\n" +
                                    "and querySql not like '/%'\n" +
                                    "and querySql not like 'SYSTEM FLUSH%'\n" +
                                    "group by querySql\n" +
                                    "";
                            List<Map<String, String>> queryList = ClickHouseUtil.executeQuery(oldConn, querySql);

                            if (queryList == null) {
                                log.info("result is empty");
                                return;
                            }

                            for (int i = 0; i < queryList.size(); i++) {
                                Map<String, String> res = queryList.get(i);
                                String queryRes = res.get("querySql");
                                if (StringUtils.isEmpty(queryRes)) {
                                    return;
                                }
                                if (!StringUtils.trim(queryRes.toLowerCase()).startsWith("select")) {
                                    return;
                                }
                                if (queryRes.endsWith("FORMAT TabSeparatedWithNamesAndTypes;")) {
                                    queryRes = queryRes.replace("FORMAT TabSeparatedWithNamesAndTypes;", "");
                                }
                                String qId = res.get("qId");
                                log.info("start to execute : {}", qId);
                                List<Map<String, String>> responseData = ClickHouseUtil.executeQuery(newConn, queryRes);
                                if (responseData == null) {
                                    log.info("finished test ck sql is : {}, query_id: {}", queryRes, qId);
                                    return;
                                }
                                log.info("finished to collect, qId: {}", qId);
                                out.collect(res.get("qId"));
                            }

                        }

                    }

                }).returns(String.class).output(new PrintingOutputFormat<>());
        env.execute("ck sql test");

    }
}
