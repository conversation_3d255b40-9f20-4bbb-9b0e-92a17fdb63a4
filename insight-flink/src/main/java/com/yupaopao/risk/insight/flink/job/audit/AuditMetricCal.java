package com.yupaopao.risk.insight.flink.job.audit;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.bean.audit.AuditMetric;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.OriginalStringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.StringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.job.base.CheckpointSetting;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.windows.aggregate.AuditMetricSumFunction;
import com.yupaopao.risk.insight.flink.windows.process.AuditMetricCalFlatMap;
import com.yupaopao.risk.insight.flink.windows.process.AuditMetricFunctionFlatMap;
import com.yupaopao.risk.insight.flink.windows.process.AuditMetricWindowProcess;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.joda.time.DateTime;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.KAFKA_GROUP_ID_AUDIT_METRIC;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc AuditMetricCal
 * @date 2020/7/2
 */
public class AuditMetricCal  {
    public static void main(String[] args) throws Exception {

        new FlinkJobBuilder()
            .withJobName("audit-metric-cal")
            .withCheckpointSetting(new CheckpointSetting(3 * 60000, 2 * 60000, true))
            .withMainProcessor(new AuditMetricCal.AuditMetricCalProcessor())
            .withJobDesc("审核指标计算")
            .start(args);
    }

    private static OutputTag<AuditMetric> calMetric = new OutputTag<AuditMetric>("calMetric") {
        private static final long serialVersionUID = 6248021963350975098L;
    };
    private static OutputTag<AuditMetric> functionMetric = new OutputTag<AuditMetric>("functionMetric") {
        private static final long serialVersionUID = 6248021963350975098L;
    };

    private static class AuditMetricCalProcessor implements FlinkJobBuilder.MainProcessor {

        ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);

        @Override public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
            SingleOutputStreamOperator<String> process =
                env.addSource(getKafkaConsumer(new OriginalStringKafkaDeserializationSchemaWrapper(new SimpleStringSchema()), "AUDIT_METRIC_SPOT"))
                    .process(new CalOutputProcess());
            process.addSink(new ClickHouseStreamSink(ckProperties, "dwd_mx_business_metric_log"))
                .name("sink minute metric");

            SingleOutputStreamOperator<String> aggProcess =
                process.getSideOutput(calMetric)
                    .flatMap(new AuditMetricCalFlatMap())
                    .keyBy(AuditMetric::getGroupKey)
                    .window(TumblingProcessingTimeWindows.of(Time.hours(1)))
                    .aggregate(new AuditMetricSumFunction())
                    .process(new FunctionOutputProcess());

            aggProcess.addSink(new ClickHouseStreamSink(ckProperties, "dwd_hourly_mx_business_metric_log"))
                .name("sink hour cal metric");
            aggProcess.getSideOutput(functionMetric)
                .flatMap(new AuditMetricFunctionFlatMap())
                .keyBy(AuditMetric::getGroupKey)
                .window(TumblingProcessingTimeWindows.of(Time.minutes(5)))
                .process(new AuditMetricWindowProcess())
                .addSink(new ClickHouseStreamSink(ckProperties, "dwd_hourly_mx_business_metric_log"))
                .name("sink hour function metric")
            ;
        }
    }

    private static FlinkKafkaConsumer<String> getKafkaConsumer(StringKafkaDeserializationSchemaWrapper wrapper, String... topic) {
        KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA_RISK_ALI);
        Properties etlProperties = kafkaProperties.getProperties();
        etlProperties.put("group.id", KAFKA_GROUP_ID_AUDIT_METRIC);

        List<String> topics = Arrays.asList(topic);

        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics,
            wrapper,
            kafkaProperties.getProperties());
        // 设定项目启动读取时间，不影响从checkpoint 恢复
        consumer.setStartFromTimestamp(System.currentTimeMillis() - 1000);
        return consumer;
    }

    private static class CalOutputProcess extends ProcessFunction<String, String> {

        private static final long serialVersionUID = 880264712369611126L;

        @Override public void processElement(String value, Context ctx, Collector<String> out) throws Exception {
            AuditMetric auditMetric = JSONObject.parseObject(value, AuditMetric.class);
            ctx.output(calMetric, auditMetric);
            out.collect(JSONObject.toJSONString(auditMetric));
        }
    }

    private static class FunctionOutputProcess extends ProcessFunction<AuditMetric, String> {
        private static final long serialVersionUID = 1868188705548709626L;

        @Override public void processElement(AuditMetric value, Context ctx, Collector<String> out) throws Exception {
            DateTime createTime = new DateTime();
            DateTime endTime = createTime.withMillisOfSecond(0);
            value.setCreatedAt(createTime.toString("yyyy-MM-dd HH:mm:ss"));
            value.setStartTime(endTime.minusHours(1).toString("yyyy-MM-dd HH:mm:ss"));
            value.setEndTime(endTime.toString("yyyy-MM-dd HH:mm:ss"));
            out.collect(JSONObject.toJSONString(value));
            ctx.output(functionMetric, value);
        }
    }

}
