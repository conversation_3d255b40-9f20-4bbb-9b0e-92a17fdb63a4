package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.flink.windows.AggTagDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.AggregateFunction;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class TagMapFunction implements AggregateFunction<AggTagDetail, TagAggregateResult, TagAggregateResult> {

    @Override public TagAggregateResult createAccumulator() {
        TagAggregateResult result = new TagAggregateResult();
        result.setMap(new HashMap<>());
        return result;
    }

    @Override public TagAggregateResult add(AggTagDetail value, TagAggregateResult accumulator) {
        Map<String, Integer> map = accumulator.getMap();
        if (map.size() > 500) {
            log.info("large map: {}, value: {}", accumulator.getId(), accumulator.getGroupKey());
            return accumulator;
        }
        accumulator.setId(value.getId());
        accumulator.setGroupKey(value.getGroupKey());
        map.put(value.getData(), map.getOrDefault(value.getData(), 0) + 1);
        accumulator.setMap(map);
        accumulator.setType(value.getType());
        Cat.logMetricForCount("tag.map");

        return accumulator;
    }

    @Override public TagAggregateResult getResult(TagAggregateResult accumulator) {
        return accumulator;
    }

    @Override public TagAggregateResult merge(TagAggregateResult a, TagAggregateResult b) {
        return a;
    }
}
