package com.yupaopao.risk.insight.flink.connector.kafka.sink;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.connector.kafka.sink.serialization.KafkaObjSerializationSchema;
import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.property.connection.KafkaTransProperties;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.sinks.AppendStreamTableSink;
import org.apache.flink.table.sinks.TableSink;
import org.apache.flink.types.Row;

import java.util.Properties;

public class KafkaTableStreamSink implements AppendStreamTableSink<Row> {
    private KafkaTransProperties kafkaProperties;

    private FlinkMetaInfo flinkMetaInfo;

    public KafkaTableStreamSink(KafkaTransProperties kafkaProperties, FlinkMetaInfo flinkMetaInfo) {
        this.kafkaProperties = kafkaProperties;
        this.flinkMetaInfo = flinkMetaInfo;
    }


    @Override public DataStreamSink<?> consumeDataStream(DataStream<Row> dataStream) {
        Properties properties = new Properties();
        properties.put("bootstrap.servers", kafkaProperties.getServers());

        KafkaObjSerializationSchema<JSONObject> wrapper = new KafkaObjSerializationSchema<>(kafkaProperties.getTopic());

        FlinkKafkaProducer<JSONObject> product = new FlinkKafkaProducer<>(kafkaProperties.getTopic(), wrapper, properties, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE);

        String[] fieldNames = flinkMetaInfo.getFieldNames();
        return dataStream.map((MapFunction<Row, JSONObject>) row -> {
            int rowSize = row.getArity();
            JSONObject result = new JSONObject();
            for (int i = 0; i < rowSize; i++) {
                result.put(fieldNames[i], row.getField(i));
            }
            return result;
        }).addSink(product).name("kafka sink");
    }

    @Override
    public TableSink<Row> configure(String[] fieldNames, TypeInformation<?>[] fieldTypes) {
        return new KafkaTableStreamSink(kafkaProperties, flinkMetaInfo);
    }

    @Override
    public TableSchema getTableSchema() {
        return flinkMetaInfo.toTableSchema();
    }

    @Override
    public TypeInformation<Row> getOutputType() {
        return flinkMetaInfo.toTableSchema().toRowType();
    }

}
