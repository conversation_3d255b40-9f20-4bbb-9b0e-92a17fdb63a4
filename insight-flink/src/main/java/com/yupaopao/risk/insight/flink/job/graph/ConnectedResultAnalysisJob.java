package com.yupaopao.risk.insight.flink.job.graph;

import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.graph.CCAnalysisResultProcessor;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-19 19:40
 *
 ***/
public class ConnectedResultAnalysisJob {
    public static void main(String[] args) throws Exception {
        new FlinkJobBuilder()
                .withJobName("connected-result-analysis")
                .isBatchJob(true)
                .withMainProcessor(new CCAnalysisResultProcessor())
                .start(args);
    }
}
