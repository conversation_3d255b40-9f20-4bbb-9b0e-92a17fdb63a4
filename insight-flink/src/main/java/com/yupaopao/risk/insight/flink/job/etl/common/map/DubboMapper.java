package com.yupaopao.risk.insight.flink.job.etl.common.map;

import com.yupaopao.risk.insight.common.groovy.GroovyCommonExecutor;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.dubbo.core.DubboConfigConstants;
import com.yupaopao.risk.insight.flink.job.etl.common.options.MapperConfig;
import com.yupaopao.risk.insight.flink.utils.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.Version;
import org.apache.dubbo.config.ApplicationConfig;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.qos.server.Server;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.dubbo.rpc.service.GenericService;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc DefaultMapFunction
 * @date 2020/10/15
 */
@Slf4j
public class DubboMapper extends BaseMapFunction {

    protected final Logger LOG = LoggerFactory.getLogger(getClass());

    private static final String KEY_OBJECT = "object";

    private String interfaceName;

    protected String method;

    protected String version;

    private String zkAddress;

    private String[] argTypes;

    private List<String> argKeys = new ArrayList<>();

    private List<String> emitKeys = new ArrayList<>();

    private String outType;

    private String beforeRequestGroovy;

    private String afterRequestGroovy;

    private ReferenceConfig<GenericService> reference;

    private GenericService genericService;

    private Integer timeout;

    LongCounter readerCounter;

    LongCounter writerCounter;


    public DubboMapper(MapperConfig config) throws Exception {
        super(config);
        interfaceName = config.getStringVal(DubboConfigConstants.KEY_INTERFACE);
        method = config.getStringVal(DubboConfigConstants.KEY_METHOD);
        zkAddress = config.getStringVal(DubboConfigConstants.KEY_ZK_ADDRESS);
        version = config.getStringVal(DubboConfigConstants.KEY_VERSION);
        argTypes = config.getStringVal(DubboConfigConstants.KEY_ARG_TYPES).split(",");
        argKeys.addAll(Arrays.asList(config.getStringVal(DubboConfigConstants.KEY_ARG_KEYS).split(",")));

        outType = config.getStringVal("outType").toLowerCase();
        emitKeys.addAll(Arrays.asList(config.getStringVal("emitKeys").split(",")));
        beforeRequestGroovy = config.getStringVal("beforeRequestGroovy");
        afterRequestGroovy = config.getStringVal("afterRequestGroovy");
        String timeoutStr = config.getStringVal(DubboConfigConstants.KEY_TIMEOUT);
        timeout = StringUtils.isNotBlank(timeoutStr) ? Integer.parseInt(timeoutStr) : 1000;
    }

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        reference = new ReferenceConfig<>();
        reference.setInterface(interfaceName);
        reference.setVersion(version);
        reference.setProtocol(DubboConfigConstants.PROTOCOL);
        reference.setApplication(new ApplicationConfig(DubboConfigConstants.APPLICATION));
        reference.setTimeout(timeout);
        Server.getInstance().stop();

        RegistryConfig registryConfig = new RegistryConfig();
        registryConfig.setAddress(zkAddress);
        reference.setRegistry(registryConfig);

        reference.setGeneric("true");
        genericService = reference.get();
        readerCounter = context.getLongCounter("reader_count_" + id);
        writerCounter = context.getLongCounter("writer_count_" + id);

        mapperMetric.addMetric("reader_count_" + id, readerCounter);
        mapperMetric.addMetric("writer_count_" + id, writerCounter);
    }

    @Override public void flatMap(Map<String, Object> value, Collector<Map<String, Object>> out) throws Exception {
        super.flatMap(value, out);
        if (isDebug && value.containsKey(KEY_DEBUG)) {
            return;
        }
        readerCounter.add(1);
        if (StringUtils.isNotBlank(beforeRequestGroovy)) {
            Map<String, Object> params = new HashMap<>();
            params.put("params", value);
            Object groovyResult = GroovyCommonExecutor.getResult(id + "beforeRequest", beforeRequestGroovy, params);
            if (groovyResult instanceof Map) {
                value = (Map<String, Object>) groovyResult;
            } else {
                return;
            }
        }

        Object[] data = new Object[argKeys.size()];
        for (int i = 0; i < argKeys.size(); i++) {
            if ("params".equals(argKeys.get(i))) {
                data[i] = value;
            } else {
                data[i] = value.get(argKeys.get(i));
            }
        }
        for (Object o : data) {
            if (Objects.isNull(o)) {
                LOG.info("dubbo 请求参数存在空值，跳过");
                return;
            }
        }
        try {
            RpcContext.getContext().setAttachment("application", DubboConfigConstants.APPLICATION);
            RpcContext.getContext().setAttachment("dubboApplication", DubboConfigConstants.APPLICATION);

            Object dubboResultObj = genericService.$invoke(method, argTypes, data);
            Map<String, Object> dubboResult = new HashMap<>();
            if (KEY_OBJECT.equals(outType)) {
                dubboResult = MapUtil.objectToMap(dubboResultObj);
            } else {
                dubboResult.put("dubboResult", dubboResultObj);
            }

            Map<String, Object> result = new HashMap<>();
            for (String outKey : emitKeys) {
                if ("dubboResult".equals(outKey) && KEY_OBJECT.equals(outType)) {
                    result = dubboResult;
                } else if (outKey.contains("params")) {
                    String[] split = outKey.split("\\.");
                    if (split.length == 1) {
                        result.put(outKey, value);
                    } else {
                        result.put(outKey, value.get(split[1]));
                    }
                } else {
                    result.put(outKey, dubboResult.get(outKey));
                }
            }
            if (StringUtils.isNotBlank(afterRequestGroovy)) {
                Map<String, Object> params = new HashMap<>();
                params.put("params", value);
                params.put("dubboResult", dubboResult.containsKey("dubboResult") ? dubboResult.get("dubboResult") : dubboResult);
                Object groovyResult = GroovyCommonExecutor.getResult(id + "afterRequest",
                    afterRequestGroovy, params);
                if (groovyResult instanceof Map) {
                    writerCounter.add(1);
                    out.collect((Map<String, Object>) groovyResult);
                } else if (groovyResult instanceof List) {
                    ((List<?>) groovyResult).forEach(p -> {
                        writerCounter.add(1);
                        out.collect((Map<String, Object>) p);
                    });
                }
            } else {
                writerCounter.add(1);
                out.collect(result);
            }
        } catch (Exception e) {
            log.error("数据转换调用 dubbo 出错，{}", interfaceName + method);
        }
    }

    @Override public void close() throws Exception {
        super.close();
        reference.destroy();
    }
}
