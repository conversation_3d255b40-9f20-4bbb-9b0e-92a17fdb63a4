package com.yupaopao.risk.insight.flink.process.audit;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.flink.bean.audit.MxTask;
import com.yupaopao.risk.insight.flink.bean.audit.MxTaskGroup;
import com.yupaopao.risk.insight.flink.bean.audit.PerformanceMxTask;
import com.yupaopao.risk.insight.flink.bean.audit.PerformanceMxTaskGroup;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-16 17:45
 * 根据任务流转计算人效
 ***/

@Slf4j
public class PerformanceCalculator {

    public static final BigDecimal MILLIS_PER_SECONDS = new BigDecimal("1000");

    /***
     * 不考虑batch处理计算人效
     * assignCost: sum(assignUpdateTime-unAssignUpdateTime) , (已分配时间-未分配时间)数据对求和
     * processCost: (finishedUpdateTime - lastAssignTime), 完成时间-最后一次分配时间
     * reassignCost: sum(unAssignUpdateTime - assignUpdateTime) (未分配时间-已分配时间) 数据对求和
     * wholeProcessCost：(finishedUpdateTime - firstUnAssignUpdateTime) (完成事件-第一次未分配时间)
     * timeoutCount： reassignCost计算时出现的次数
     * @param allElements
     * @return
     * @throws Exception
     */
    public static PerformanceMxTask calculate(List<MxTask> allElements) throws Exception {
        //数据排序
        List<MxTask> sortedList =
                allElements.stream()
                        .sorted(Comparator.comparing(MxTask::getUpdateTime).thenComparing(MxTask::getMsgReceivedTime))
                        .collect(Collectors.toList());


        if (!isValidStatusSequence(sortedList)) {
            //任务状态不完整，可能是待分配直接到已完成等特殊情况,做特殊处理
            log.warn("task status sequence is not valid taskList: {}", JSON.toJSONString(sortedList));
            return calculateForInvalidStatus(sortedList);
        }
        MxTask firstTask = sortedList.get(0);
        MxTask finishedTask = sortedList.get(sortedList.size() - 1);
        PerformanceMxTask resultTask = new PerformanceMxTask(finishedTask);
        //分配耗时
        List<MxTask> assignSequence =
                sortedList.stream().filter(elem -> elem.unAssigned() || elem.assigned()).collect(Collectors.toList());
        long assignCost = 0;
        long reassignCost = 0;
        long timeoutCount = 0;
        for (int i = 1; i < assignSequence.size(); i = i + 1) {
            MxTask currentTask = assignSequence.get(i);
            MxTask beforeTask = assignSequence.get(i - 1);
            if (currentTask.assigned()) {
                //计算分配耗时
                if (beforeTask.unAssigned()) {
                    assignCost += diffSeconds(currentTask.getUpdateTime(), beforeTask.getUpdateTime());
                }
            } else {
                //计算重新分配耗时
                if (beforeTask.assigned()) {
                    reassignCost += diffSeconds(currentTask.getUpdateTime(), beforeTask.getUpdateTime());
                }
                timeoutCount++;
            }
        }

        //处理耗时：
        long processCost = -1;
        MxTask lastAssignedTask = sortedList.get(sortedList.size() - 2);
        if (finishedTask.finished() && lastAssignedTask.assigned()) {
            processCost = diffSeconds(finishedTask.getUpdateTime(), lastAssignedTask.getUpdateTime());
        }
        //整体延迟
        long wholeProcessCost = -1;
        if (finishedTask.finished()) {
            wholeProcessCost = diffSeconds(finishedTask.getUpdateTime(), firstTask.getUpdateTime());
        }

        resultTask.setAssignCost(assignCost);
        resultTask.setReassignCost(reassignCost);
        resultTask.setTimeoutCount(timeoutCount);
        resultTask.setProcessCost(processCost);
        resultTask.setWholeProcessCost(wholeProcessCost);
        return resultTask;
    }

    /***
     * 状态转换可能不是0,1,3都有
     * 目前有部分是从0直接管理员处理转为3
     * @param sortedList
     */
    public static PerformanceMxTask calculateForInvalidStatus(List<MxTask> sortedList) {
        MxTask finishedTask = sortedList.get(sortedList.size() - 1);
        MxTask firstTask = sortedList.get(0);
        PerformanceMxTask resultTask = new PerformanceMxTask(finishedTask);
        if (sortedList.size() == 2 && finishedTask.finished() && firstTask.unAssigned()) {
            resultTask.setProcessCost(0);
            resultTask.setWholeProcessCost(diffSeconds(finishedTask.getUpdateTime(), firstTask.getUpdateTime()));
            resultTask.setAssignCost(diffSeconds(finishedTask.getUpdateTime(), firstTask.getUpdateTime()));
            resultTask.setFlinkRemark("state incomplete");
        } else {
            resultTask.setFlinkRemark("state invalid");
        }
        return resultTask;

    }

    public static void main(String[] args) {
        String json = "[{\n" +
                "\t\t\"app_id\": \"10\",\n" +
                "\t\t\"auditor\": \"\",\n" +
                "\t\t\"auditor_group_id\": 1,\n" +
                "\t\t\"auditor_team_id\": 5,\n" +
                "\t\t\"batch_id\": \"\",\n" +
                "\t\t\"bu\": \"audit\",\n" +
                "\t\t\"bu_group\": \"base_audit\",\n" +
                "\t\t\"bu_id\": \"2966346012100281600\",\n" +
                "\t\t\"channel\": \"103\",\n" +
                "\t\t\"create_time\": \"2020-08-08 17:20:00\",\n" +
                "\t\t\"current_phase\": 100,\n" +
                "\t\t\"group_id\": \"\",\n" +
                "\t\t\"id\": 345245235000446,\n" +
                "\t\t\"kafkaTopic\": \"maserati_audit_service.t_channel_task\",\n" +
                "\t\t\"latest_level\": 0,\n" +
                "\t\t\"latest_result\": \"\",\n" +
                "\t\t\"msgReceivedTime\": 1596878400807,\n" +
                "\t\t\"op_version\": \"\",\n" +
                "\t\t\"remark\": \"\",\n" +
                "\t\t\"source\": \"0\",\n" +
                "\t\t\"state\": \"0\",\n" +
                "\t\t\"sub_channel\": \"\",\n" +
                "\t\t\"trace_id\": \"1ee0210f76b14feba4f2497dbfe3123d1\",\n" +
                "\t\t\"uid\": 1835212113200005,\n" +
                "\t\t\"update_time\": \"2020-08-08 17:20:00\",\n" +
                "\t\t\"user_risk_level\": 0\n" +
                "\t}, {\n" +
                "\t\t\"app_id\": \"10\",\n" +
                "\t\t\"auditor\": \"xiaowen\",\n" +
                "\t\t\"auditor_group_id\": 1,\n" +
                "\t\t\"auditor_team_id\": 5,\n" +
                "\t\t\"batch_id\": \"87849e1a85a843fb95e89a92cdaecd6e-3\",\n" +
                "\t\t\"bu\": \"audit\",\n" +
                "\t\t\"bu_group\": \"base_audit\",\n" +
                "\t\t\"bu_id\": \"2966346012100281600\",\n" +
                "\t\t\"channel\": \"103\",\n" +
                "\t\t\"create_time\": \"2020-08-08 17:20:00\",\n" +
                "\t\t\"current_phase\": 100,\n" +
                "\t\t\"group_id\": \"\",\n" +
                "\t\t\"id\": 345245235000446,\n" +
                "\t\t\"kafkaTopic\": \"maserati_audit_service.t_channel_task\",\n" +
                "\t\t\"latest_level\": 0,\n" +
                "\t\t\"latest_result\": \"PASS\",\n" +
                "\t\t\"msgReceivedTime\": 1596878411708,\n" +
                "\t\t\"op_version\": \"2b50e6f1-0c72-4b34-8bbb-d8e99a50945a\",\n" +
                "\t\t\"remark\": \"\",\n" +
                "\t\t\"source\": \"0\",\n" +
                "\t\t\"state\": \"3\",\n" +
                "\t\t\"sub_channel\": \"\",\n" +
                "\t\t\"trace_id\": \"1ee0210f76b14feba4f2497dbfe3123d1\",\n" +
                "\t\t\"uid\": 1835212113200005,\n" +
                "\t\t\"update_time\": \"2020-08-08 17:20:11\",\n" +
                "\t\t\"user_risk_level\": 0\n" +
                "\t}\n" +
                "]";
        List<MxTask> sortedList = JSON.parseArray(json,MxTask.class);
        calculateForInvalidStatus(sortedList);
    }

    /***
     * 检查数据完整新,可以往后走
     * @return
     * @throws Exception
     *
     */
    private static boolean isValidStatusSequence(List<MxTask> allStateElements) throws Exception {
        if (!allStateElements.get(0).unAssigned()) {
            return false;
        }
        //任务处理完成，且所有状态数据都包含了
        boolean hasUnAssignedState = true;
        boolean hasAssignedState = false;
        boolean hasFinishedState = false;
        int totalRecords = allStateElements.size();
        for (int i = 1; i < totalRecords; i++) {
            MxTask task = allStateElements.get(i);
            MxTask before = allStateElements.get(i - 1);
            if (!isValidDataOrder(task, before)) {
                return false;
            }
            if (task.unAssigned()) {
                hasUnAssignedState = true;
            } else if (task.assigned()) {
                hasAssignedState = true;
            } else if (task.finished()) {
                hasFinishedState = true;
            }
        }
        return hasUnAssignedState && hasAssignedState && hasFinishedState;
    }

    private static boolean isValidDataOrder(MxTask currentTask, MxTask beforeTask) {
        if (currentTask.assigned() && beforeTask.unAssigned()) {
            return true;
        }
        if (currentTask.unAssigned() && beforeTask.assigned()) {
            return true;
        }
        if (currentTask.finished() && beforeTask.assigned()) {
            return true;
        }
        return false;
    }


    /***
     * 批量任务计算人效
     * @param mapTasks
     * @return
     * @throws Exception
     */
    public static List<PerformanceMxTask> calculate(Map<Long, List<MxTask>> mapTasks) throws Exception {
        Map<String, PerformanceMxTask> resultMap = new HashMap<>();
        int batchSize = 0;
        long totalCost = 0;
        //单个任务耗时计算
        for (List<MxTask> taskList : mapTasks.values()) {
            PerformanceMxTask tempResult = PerformanceCalculator.calculate(taskList);
            resultMap.put(String.valueOf(tempResult.getId()), tempResult);
            if (!tempResult.illegalTask()) {
                //非异常数据处理
                batchSize++;
                totalCost += tempResult.getProcessCost();
            }
        }

        //处理耗时均值：
        long avgCost = (long) Math.ceil(1.0 * totalCost / batchSize);
        long processCost = (long) Math.ceil(1.0 * avgCost / batchSize);
        for (PerformanceMxTask res : resultMap.values()) {
            log.debug("totalCost: {}, batchSize: {},avgCost:{}, processCost: {}, batchId: {}, taskId: {}", totalCost,
                    batchSize, avgCost, processCost, res.getBatchId(), res.getId());
            if (res.illegalTask()) {
                continue;
            }
            res.setProcessCost(processCost);
            //整体耗时为：首次未分配到最后一次已分配+平均处理耗时
            long wholeCost = calculateWholeCostForBatchTask(processCost, mapTasks.get(res.getId()));
            res.setWholeProcessCost(wholeCost);
        }
        List<PerformanceMxTask> allResultList = new ArrayList<>(resultMap.values());
        return allResultList;
    }

    private static long calculateWholeCostForBatchTask(long avg, List<MxTask> taskList) {
        MxTask lastAssignedTask = taskList.get(taskList.size() - 2); //最后一次分配的任务
        //有些task直接0到3状态，没有中间的1
        if(!lastAssignedTask.assigned() && taskList.size()==2){
            //非assigned状态
            lastAssignedTask = taskList.get(taskList.size()-1);//直接用finished时间记录
        }
        MxTask firstTask = taskList.get(0);
        return diffSeconds(lastAssignedTask.getUpdateTime(), firstTask.getUpdateTime()) + avg;
    }


    private static long diffSeconds(Date after, Date before) {

        long diffMillis = after.getTime() - before.getTime();
        return new BigDecimal(String.valueOf(diffMillis)).divide(MILLIS_PER_SECONDS).setScale(0,
                RoundingMode.HALF_UP).longValue();
    }


    /***
     * 任务组指标计算
     * 第一次待到完成
     * 第一次已分配到完成
     * @param taskGroupList
     * @return
     */
    public static PerformanceMxTaskGroup calculateTaskGroup(List<MxTaskGroup> taskGroupList) {
        //数据排序
        List<MxTaskGroup> sortedList =
                taskGroupList.stream()
                        .sorted(Comparator.comparing(MxTaskGroup::getUpdateTime).thenComparing(MxTaskGroup::getMsgReceivedTime))
                        .collect(Collectors.toList());

        //第一次待分配时间到完成事件
        MxTaskGroup firstTask = sortedList.get(0);
        MxTaskGroup finishedTask = sortedList.get(sortedList.size() - 1);
        PerformanceMxTaskGroup resultTask = new PerformanceMxTaskGroup(finishedTask);
        if (!firstTask.unAssigned() && !finishedTask.finished()) {
            //状态信息不正确
            log.error("invalid state, params: " + JSON.toJSONString(sortedList));
            resultTask.setFlinkRemark("state invalid");
            return resultTask;
        }
        resultTask.setWholeProcessCost(diffSeconds(finishedTask.getUpdateTime(), firstTask.getUpdateTime()));

        //计算第一次已经分配到完成
        Optional<MxTaskGroup> firstAssigned = sortedList.stream().filter(elem -> elem.assigned()).findFirst();
        if (!firstAssigned.isPresent()) {
            log.error("invalid state, params: " + JSON.toJSONString(sortedList));
            resultTask.setFlinkRemark("state invalid: no assigned state");
            return resultTask;
        }
        resultTask.setProcessCost(diffSeconds(finishedTask.getUpdateTime(), firstAssigned.get().getUpdateTime()));
        return resultTask;
    }

}
