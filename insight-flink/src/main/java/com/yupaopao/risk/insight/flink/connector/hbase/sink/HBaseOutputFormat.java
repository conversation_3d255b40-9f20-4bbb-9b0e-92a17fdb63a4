package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.alibaba.lindorm.client.core.utils.Bytes;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.constants.TsConstants;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Put;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.yupaopao.risk.insight.common.constants.HBaseConstants.COLUMN_FAMILY;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-01-06 19:56
 *
 ***/

@Slf4j
public class HBaseOutputFormat extends CommonHBaseOutputFormat {

    public HBaseOutputFormat(String tableName, HBaseProperties hBaseProperties) {
        super(tableName, hBaseProperties);
    }

    @Override
    public void writeRecord(String record) throws IOException {
        if (StringUtils.isEmpty(record)) {
            return;
        }
        try {
            //json扁平化处理
            Map<String, Object> flattenMap = JsonFlatterUtils.toMap(record, FlattenMode.KEEP_ARRAYS);
            String dayPrefix = (String) flattenMap.remove(TsConstants.RISK_HIT_LOG_DATE_PARTITION);
            String traceId = (String) flattenMap.remove(TsConstants.RISK_HIT_LOG_TRACE_ID);
            String rowKey = dayPrefix + traceId;

            Put put = new Put(Bytes.toBytes(rowKey));
            byte[] columnFamily = Bytes.toBytes(COLUMN_FAMILY);
            put.addColumn(columnFamily, Bytes.toBytes(TsConstants.RISK_HIT_LOG_TRACE_ID), Bytes.toBytes(traceId));
            for (Map.Entry<String, Object> entry : flattenMap.entrySet()) {
                byte[] columnValue = HBaseUtil.jsonTypeToHBaseColumn(entry.getValue());
                if (columnValue == null) {
                    continue;
                }
                put.addColumn(columnFamily, Bytes.toBytes(entry.getKey()), columnValue);
            }
            this.getMutator().mutate(put);
            if (canFlush()) {
                flush();
            }
        } catch (Exception e) {
            log.error("write record error: ,record: " + record, e);
        }
    }

    @Override
    public String getRowKey(Map<String, Object> flattenMap) {
        String dayPrefix = (String) flattenMap.remove(TsConstants.RISK_HIT_LOG_DATE_PARTITION);
        String traceId = (String) flattenMap.get(TsConstants.RISK_HIT_LOG_TRACE_ID);
        String rowKey = dayPrefix + traceId;
        return rowKey;
    }

    @Override
    public Map<String, Object> generateExtraColumn(Map<String, Object> flattenMap) {
        String traceId = (String) flattenMap.get(TsConstants.RISK_HIT_LOG_TRACE_ID);
        Map<String, Object> newData = new HashMap<>();
        newData.put(TsConstants.RISK_HIT_LOG_TRACE_ID, traceId);
        return newData;
    }

}
