/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yupaopao.risk.insight.flink.job.etl.common.connect.kafkanew.converter;

import com.dtstack.flinkx.decoder.IDecode;
import com.dtstack.flinkx.decoder.TextDecoder;
import com.dtstack.flinkx.reader.MetaColumn;
import com.dtstack.flinkx.util.MapUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.kafkanew.core.decoder.JsonDecoder;
import com.yupaopao.risk.insight.flink.job.etl.common.converter.AbstractRowConverter;
import com.yupaopao.risk.insight.flink.job.etl.common.converter.IDeserializationConverter;
import com.yupaopao.risk.insight.flink.job.etl.common.element.column.BigDecimalColumn;
import com.yupaopao.risk.insight.flink.job.etl.common.element.column.BooleanColumn;
import com.yupaopao.risk.insight.flink.job.etl.common.element.column.StringColumn;
import com.yupaopao.risk.insight.flink.job.etl.common.element.column.TimestampColumn;
import com.yupaopao.risk.insight.flink.job.etl.common.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.util.CollectionUtil;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

public class KafkaColumnConverter extends AbstractRowConverter<String, Object, byte[], String> {

    /** source kafka msg decode */
    private final IDecode decode;
    /** kafka sink out fields */
    private final List<String> outList;

    public KafkaColumnConverter(String codec, List<String> keyTypeList, List<MetaColumn> columns) {
        this.outList = keyTypeList;
        if ("json".equals(codec)) {
            this.decode = new JsonDecoder();
        } else {
            this.decode = new TextDecoder();
        }

        // Only json need to extract the fields
        if (!CollectionUtils.isEmpty(columns) && "json".equals(codec)) {
            List<String> typeList = columns.stream()
                            .map(MetaColumn::getType)
                            .collect(Collectors.toList());
            this.toInternalConverters = new ArrayList<>();
            for (String s : typeList) {
                toInternalConverters.add(wrapIntoNullableInternalConverter(createInternalConverter(s)));
            }
        }
    }

    @Override
    public Map<String, Object> toInternal(String input) throws Exception {
        return decode.decode(input);
    }

    @Override
    public byte[] toExternal(Map<String, Object> rowData, byte[] output) throws Exception {
        // get partition key value
        if (!CollectionUtil.isNullOrEmpty(outList)) {
            Map<String, Object> keyPartitionMap = new LinkedHashMap<>(outList.size());
            for (Map.Entry<String, Object> entry : rowData.entrySet()) {
                if (outList.contains(entry.getKey())) {
                    keyPartitionMap.put(entry.getKey(), entry.getValue());
                }
            }
            rowData = keyPartitionMap;
        }

        return MapUtil.writeValueAsString(rowData).getBytes(StandardCharsets.UTF_8);
    }

    @Override
    protected IDeserializationConverter createInternalConverter(String type) {
        switch (type.toUpperCase(Locale.ENGLISH)) {
            case "INT":
            case "INTEGER":
                return val -> new BigDecimalColumn(Integer.parseInt(val.toString()));
            case "BOOLEAN":
                return val -> new BooleanColumn(Boolean.parseBoolean(val.toString()));
            case "TINYINT":
                return val -> new BigDecimalColumn(Byte.parseByte(val.toString()));
            case "CHAR":
            case "CHARACTER":
            case "STRING":
            case "VARCHAR":
            case "TEXT":
                return val -> new StringColumn(val.toString());
            case "SHORT":
                return val -> new BigDecimalColumn(Short.parseShort(val.toString()));
            case "LONG":
            case "BIGINT":
                return val -> new BigDecimalColumn(Long.parseLong(val.toString()));
            case "FLOAT":
                return val -> new BigDecimalColumn(Float.parseFloat(val.toString()));
            case "DOUBLE":
                return val -> new BigDecimalColumn(Double.parseDouble(val.toString()));
            case "DECIMAL":
                return val -> new BigDecimalColumn(new BigDecimal(val.toString()));
            case "DATE":
            case "TIME":
            case "DATETIME":
            case "TIMESTAMP":
                return val -> new TimestampColumn(DateUtil.getTimestampFromStr(val.toString()));
            default:
                throw new UnsupportedOperationException("Unsupported type:" + type);
        }
    }
}
