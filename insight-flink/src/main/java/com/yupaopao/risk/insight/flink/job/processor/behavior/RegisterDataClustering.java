package com.yupaopao.risk.insight.flink.job.processor.behavior;

import com.alibaba.alink.params.shared.clustering.HasKMeansDistanceType;
import com.alibaba.alink.pipeline.Pipeline;
import com.alibaba.alink.pipeline.PipelineModel;
import com.alibaba.alink.pipeline.clustering.KMeans;
import com.alibaba.alink.pipeline.nlp.Word2Vec;
import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.table.api.Table;

import java.util.Map;

/****
 * zengxiangcai
 * 2021/5/8 2:46 下午
 ***/
public class RegisterDataClustering  implements BatchJobBuilder.MainProcessor {

    @Override
    public void internalProcess(ExecutionEnvironment env, Map<String, String> argMap) throws Exception {

        Word2Vec word2Vec = new Word2Vec()
                .setSelectedCol("eventList")
                .setOutputCol("outputVector")
                .setVectorSize(50)
                .setMinCount(1)
                .setReservedCols("id")
                .setWindow(1);

        Word2Vec word2Vec2 = new Word2Vec()
                .setSelectedCol("eventList")
                .setOutputCol("outputVector")
                .setVectorSize(50)
                .setMinCount(1)
                .setReservedCols("id")
                .setWindow(1);

        KMeans kMeans = new KMeans()
                .setVectorCol("outputVector")
                .setPredictionCol("pred")
                .setPredictionDistanceCol("distance")
//                .setInitMode(InitMode.K_MEANS_PARALLEL)
                .setDistanceType(HasKMeansDistanceType.DistanceType.COSINE)
                .setK(500);

        Pipeline pipeline = new Pipeline().add(word2Vec).add(kMeans);
//        PipelineModel model = pipeline.fit(data);
//        Table resultTable = model.transform(data).select("pred,id");
    }
}
