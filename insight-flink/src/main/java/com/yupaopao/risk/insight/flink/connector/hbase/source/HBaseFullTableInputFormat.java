package com.yupaopao.risk.insight.flink.connector.hbase.source;

import com.google.gson.Gson;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-09 10:37
 * hbase读取全表
 *
 ***/

@Slf4j
public abstract class HBaseFullTableInputFormat extends HBaseCommonInputFormat<String> {

    public HBaseFullTableInputFormat(TsTableInfo tsTableInfo, HBaseProperties hBaseProperties, LinkedBlockingQueue<Result> cacheQueue) {
        super(tsTableInfo, hBaseProperties, cacheQueue);
    }

    @Override
    public HBaseSplit[] createInputSplits(int minNumSplits) throws IOException {
        List<HBaseSplit.BucketSplit> bucketList = new ArrayList<>();
        HBaseSplit.BucketSplit bucketSplit = new HBaseSplit.BucketSplit();
        bucketList.add(bucketSplit);
        HBaseSplit inputSplit = new HBaseSplit(1, new Gson().toJson(bucketList));
        return new HBaseSplit[]{inputSplit};
    }

    /***
     * attention: when inputFormat used in InputFormatSourceFunction , null data will break the job , so the default
     * return is "{}"
     * @param reuse
     * @return
     * @throws IOException
     */
    @Override
    public String nextRecord(String reuse) throws IOException {
        String resultJson = null;
        Result rowResult = null;
        try {
            rowResult = this.getCacheQueue().poll(20, TimeUnit.MILLISECONDS);
            if (rowResult != null) {
                //转为json
                resultJson = parseHBaseResult(rowResult);
            }
        } catch (Exception e) {
            log.warn("read hbase data from cache queue error: " + rowResult != null ? Bytes.toString(rowResult.getRow()) :
                    null, e);
        }
        if (StringUtils.isEmpty(resultJson)) {
            resultJson = "{}";
        }
        return resultJson;
    }

    public abstract String parseHBaseResult(Result rowResult);
}
