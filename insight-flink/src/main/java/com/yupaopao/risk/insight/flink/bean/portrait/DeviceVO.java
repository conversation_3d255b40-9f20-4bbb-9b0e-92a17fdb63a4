package com.yupaopao.risk.insight.flink.bean.portrait;

import com.yupaopao.risk.insight.flink.utils.ip2region.IPDetail;
import lombok.Data;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.ResultTypeQueryable;

import java.io.Serializable;

@Data
public class DeviceVO implements Comparable, Serializable, ResultTypeQueryable {
    private static final long serialVersionUID = -4634791759100408628L;
    private String id;
    private Integer num;
    private IPDetail ipDetail;

    public DeviceVO(String id, Integer num) {
        this.id = id;
        this.num = num;
    }

    @Override
    public int compareTo(Object o) {
        DeviceVO deviceVO = (DeviceVO) o;
        Integer otherNum = deviceVO.getNum();
        if (this.num.intValue() == otherNum.intValue()) {
            return this.id.compareTo(deviceVO.id);
        }
        return this.num.compareTo(otherNum) * -1;
    }

    @Override
    public TypeInformation getProducedType() {
        return TypeInformation.of(DeviceVO.class);
    }
}
