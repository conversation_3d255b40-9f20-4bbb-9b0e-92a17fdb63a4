package com.yupaopao.risk.insight.flink.connector.ts.businessmodel;

/**
 * 处理器
 *
 * <AUTHOR>
 * @date 2019/4/8 8:32 PM
 */
public enum Handler {

    RISK(0), AUDIT(1);

    private int code;

    Handler(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static Handler nameOf(String name) {
        for (Handler handler : values()) {
            if (handler.name().equalsIgnoreCase(name)) {
                return handler;
            }
        }
        return null;
    }

}
