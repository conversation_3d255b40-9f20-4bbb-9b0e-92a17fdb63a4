package com.yupaopao.risk.insight.flink.job.processor.behavior;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.yupaopao.risk.insight.common.algorithm.AlgorithmRunner;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.connector.clickhouse.source.CKInputFormat;
import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.GroupReduceFunction;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yupaopao.risk.insight.common.algorithm.AlgorithmRunner.ITEMSET_SEPARATOR;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-09-11 14:24
 *
 ***/

@Slf4j
public class PrefixSpanProcessor implements BatchJobBuilder.MainProcessor {


    @Override
    public void internalProcess(ExecutionEnvironment env, Map<String, String> argMap) throws Exception {
        String runDay = argMap.get("runDay");
        Integer idType = Integer.valueOf(argMap.get("idType"));

        String dataSource = argMap.get("dataSource");

        String inputTable = "behavior_cluster_daily";
        String outputTable = "behavior_cluster_sequence";
        String idSeqTable = "behavior_sequence_daily";
        if (StringUtils.isNotEmpty(dataSource)) {
            inputTable = dataSource + "_" + inputTable;
            outputTable = dataSource + "_" + outputTable;
            idSeqTable = dataSource + "_" + idSeqTable;
        }


        Map<String, Object> inputArgMap = new HashMap<>();
        inputArgMap.put("runDay", runDay);
        inputArgMap.put("idType", idType);
        inputArgMap.put("inputTable", inputTable);
        inputArgMap.put("idSeqTable", idSeqTable);




        ClickHouseProperties ckProperties = ClickHouseUtil.getLongTimeoutProperties();

        String allClusterIdSql = "select distinct clusterId from ${inputTable} a where a.runDay = " +
                "'${runDay}' and a" +
                ".idType=${idType} and id not in ('0','-1') ";
        List<Integer> clusterList;
        try (ClickHouseConnection ckConn = ClickHouseUtil.createConnection(ckProperties)) {
            clusterList =
                    ClickHouseUtil.executeQuery(ckConn, new StrSubstitutor(inputArgMap).replace(allClusterIdSql)).stream().map(elem -> Integer.parseInt(elem.get("clusterId"))).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("fetch all clusterId error: ", e);
            throw new RuntimeException("fetch clusterId error");
        }
        log.info("all cluster: {}", clusterList);

        String seqSql = "select t1.id, t1.eventList, t2.clusterId\n" +
                "from (\n" +
                "         select distinct id, eventList from ${idSeqTable} where runDay = '${runDay}'\n" +
                "         ) t1 global all\n" +
                "         inner join (\n" +
                "    select id, clusterId from ${inputTable} a where a.runDay = '${runDay}'\n" +
                "    ) t2 on t1.id = t2.id \n" +
                "order by id asc ";

        CKOutputFormat ckOutputFormat = new CKOutputFormat(ckProperties, outputTable);

        CKInputFormat inputFormat = new CKInputFormat(ckProperties, new StrSubstitutor(inputArgMap).replace(seqSql), "{}");

        env.createInput(inputFormat).setParallelism(1)
                .name("read_cluster_event_sequence")
                .filter(elem -> !HBaseUtil.emptyResultJson(elem))
                .map(elem -> {
                    JSONObject obj = JSON.parseObject(elem);
                    String eventList = obj.getString("eventList").trim();
                    return new Tuple2<Integer, String>(obj.getInteger("clusterId"), eventList.replace(" ", ITEMSET_SEPARATOR));
                }).returns(new TypeHint<Tuple2<Integer, String>>() {
            @Override
            public TypeInformation<Tuple2<Integer, String>> getTypeInfo() {
                return super.getTypeInfo();
            }
        })
                .groupBy(0).reduceGroup(new GroupReduceFunction<Tuple2<Integer, String>, String>() {


            @Override
            public void reduce(Iterable<Tuple2<Integer, String>> values, Collector<String> out) throws Exception {
                List<String> rows = new ArrayList<>();

                Integer firstValue = null;
                Integer totalCount = 0;
                for (Tuple2<Integer, String> value : values) {
                    totalCount++;
                    if (totalCount == 1) {
                        firstValue = value.f0;
                    }
                    rows.add(value.f1);
                }

                Integer clusterId = firstValue;

                log.info("process cluster: {},totalCount = {}", clusterId, totalCount);
                List<Map<String, Object>> patternList = AlgorithmRunner.runPrefixSpan(rows, 0.5, 5);

                if (CollectionUtils.isEmpty(patternList)) {
                    return;
                }
                patternList.forEach(elem -> {
                    elem.put("clusterId", clusterId);
                    elem.put("runDay", inputArgMap.get("runDay"));
                    String output = JSON.toJSONString(elem);
                    out.collect(output);
                });
            }
        }).output(ckOutputFormat).setParallelism(1).name("write_cluster_frequent_sequence");

    }

}
