package com.yupaopao.risk.insight.flink.job.etl.common;

import com.yupaopao.risk.insight.flink.job.etl.common.connect.odps.reader.WangYiOdpsQueryThread;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.io.PrintingOutputFormat;
import org.apache.flink.configuration.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/****
 * zengxiangcai
 * 2023/6/1 16:40
 ***/
public class OdpsTest {
    public static void main(String[] args) throws Exception {
//        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
//        env.fromElements("1").map(new RichMapFunction<String, String>() {
//            private transient LinkedBlockingQueue queue;
//            private  transient WangYiOdpsQueryThread t;
//            @Override
//            public void open(Configuration parameters) throws Exception {
//                queue = new LinkedBlockingQueue();
//                t = new WangYiOdpsQueryThread(queue,"select 1");
//                t.start();
//            }
//
//            @Override
//            public String map(String value) throws Exception {
//
//                try{
//                    queue.poll(10, TimeUnit.MILLISECONDS);
//                }catch (Exception e){
//                    e.getMessage();
//                }
//
//                return "null";
//            }
//
//            @Override
//            public void close() throws Exception {
//                super.close();
//                queue.clear();
//                queue = null;
//                t.interrupt();
//            }
//        }).returns(String.class).output(new PrintingOutputFormat<>());
//        env.execute("test odps");
    }
}
