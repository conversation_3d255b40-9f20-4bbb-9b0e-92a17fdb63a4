package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.bean.audit.AuditMetric;
import com.yupaopao.risk.insight.flink.bean.audit.MxMetric;
import com.yupaopao.risk.insight.flink.utils.FactorUtil;
import com.yupaopao.risk.insight.flink.utils.MxMetricCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;

import java.util.List;
import java.util.Objects;

@Slf4j
public class AuditMetricFunctionFlatMap implements FlatMapFunction<AuditMetric, AuditMetric> {

    @Override public void flatMap(AuditMetric value, Collector<AuditMetric> out) throws Exception {
        JSONObject param = (JSONObject) JSONObject.toJSON(value);

        List<MxMetric> function = MxMetricCacheUtils.getMetricByFunc("FUNCTION");
        if (Objects.isNull(function) || function.size() == 0) {
            return;
        }

        function.forEach(item -> {
            if (FactorUtil.checkCondition(item, param)) {
                String groupKey = FactorUtil.getGroupKey(item, param);
                if (StringUtils.isNotBlank(groupKey)) {
                    AuditMetric auditMetric = new AuditMetric();
                    auditMetric.cloneData(value);
                    auditMetric.setGroupKey(groupKey);
                    out.collect(auditMetric);
                }
            }
        });
    }
}
