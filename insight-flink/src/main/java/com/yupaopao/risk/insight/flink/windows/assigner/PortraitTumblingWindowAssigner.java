package com.yupaopao.risk.insight.flink.windows.assigner;

import com.yupaopao.risk.insight.flink.bean.portrait.PortraitBean;
import com.yupaopao.risk.insight.flink.windows.triggers.PortraitTagTumblingTimeTrigger;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.WindowAssigner;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.joda.time.DateTime;

import java.util.Collection;
import java.util.Collections;

public class PortraitTumblingWindowAssigner extends WindowAssigner<Tuple2<Long, PortraitBean>, TimeWindow> {

    @Override public Collection<TimeWindow> assignWindows(Tuple2<Long, PortraitBean> element, long timestamp, WindowAssignerContext context) {
        final long now = context.getCurrentProcessingTime();
        DateTime dateTime = new DateTime(now);
        long start = dateTime.withMillisOfDay(0).getMillis();
        return Collections.singletonList(new TimeWindow(start, start+24*60*60*1000 -1));
    }

    @Override public Trigger<Tuple2<Long, PortraitBean>, TimeWindow> getDefaultTrigger(StreamExecutionEnvironment env) {
        return PortraitTagTumblingTimeTrigger.create();
    }

    @Override public TypeSerializer<TimeWindow> getWindowSerializer(ExecutionConfig executionConfig) {
        return new TimeWindow.Serializer();
    }

    @Override public boolean isEventTime() {
        return false;
    }

}
