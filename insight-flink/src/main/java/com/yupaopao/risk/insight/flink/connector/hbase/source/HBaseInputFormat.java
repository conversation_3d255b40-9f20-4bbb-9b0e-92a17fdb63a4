package com.yupaopao.risk.insight.flink.connector.hbase.source;

import com.yupaopao.risk.insight.common.constants.TsConstants;
import com.yupaopao.risk.insight.common.enums.HBaseColumnTypeEnum;
import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.api.common.io.DefaultInputSplitAssigner;
import org.apache.flink.api.common.io.RichInputFormat;
import org.apache.flink.api.common.io.statistics.BaseStatistics;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.InputSplitAssigner;
import org.apache.flink.types.Row;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.filter.FilterList;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-01-07 15:05
 * 读取hbase数据转为flink row
 ***/

@Slf4j
public class HBaseInputFormat extends RichInputFormat<Row, HBaseSplit> {


    private TsTableInfo tsTableInfo;

    private HBaseProperties hBaseProperties;

    private FlinkMetaInfo flinkMetaInfo;

    private transient Connection conn;

    private HBaseColumnTypeEnum fromType;

    /**
     * hbase scan的数据先放入queue中，然后读取消费
     */
    private LinkedBlockingQueue<Result> fetchedCacheList = new LinkedBlockingQueue<>(1024 * 128);

    /**
     * 读取该InputFormat负责的splits的线程列表
     */
    private List<HBaseInputSplitReadThread> threadList = new ArrayList<>();

    public HBaseInputFormat(TsTableInfo tsTableInfo, HBaseProperties hBaseProperties, FlinkMetaInfo flinkMetaInfo) {
        this.tsTableInfo = tsTableInfo;
        this.hBaseProperties = hBaseProperties;
        this.flinkMetaInfo = flinkMetaInfo;
        if (TsConstants.TABLE_TYPE_TEMP.equalsIgnoreCase(tsTableInfo.getTableType())) {
            fromType = HBaseColumnTypeEnum.FROM_STRING;
        } else {
            fromType = HBaseColumnTypeEnum.FROM_JSON_FLATTER;
        }
    }


    @Override
    public void configure(Configuration parameters) {
    }

    @Override
    public BaseStatistics getStatistics(BaseStatistics cachedStatistics) throws IOException {
        return null;
    }

    @Override
    public HBaseSplit[] createInputSplits(int minNumSplits) throws IOException {
        if ("TEMP".equalsIgnoreCase(tsTableInfo.getTableType())) {
            log.info("user define data fetch, tableId: {}", tsTableInfo.getTableId());
            return new HBaseSplitBuilder(tsTableInfo, hBaseProperties).buildUserDefineDataSplits();
        } else {
            /***
             * 自定义方式分片0000_yyyyMMdd方式分为1024个分片 , risk_hit_log 表
             */
            return new HBaseSplitBuilder(tsTableInfo, hBaseProperties).buildRiskHitLogSplits();
        }
    }

    @Override
    public InputSplitAssigner getInputSplitAssigner(HBaseSplit[] inputSplits) {
        return new DefaultInputSplitAssigner(inputSplits);
    }

    @Override
    public void open(HBaseSplit split) throws IOException {
        try {
            if (split == null) {
                return;
            }
            //按split的bucket数量多线程读
            List<HBaseSplit.BucketSplit> bucketList = split.fetchBucketList();
            if (CollectionUtils.isEmpty(bucketList)) {
                return;
            }
            if (fetchedCacheList == null) {
                fetchedCacheList = new LinkedBlockingQueue<>(1024 * 16);
            }
            int bucketIndex = 0;
            for (HBaseSplit.BucketSplit bucket : bucketList) {
                String threadName =
                        "hbase-split-" + split.getSplitNumber() + "-bucket-" + bucketIndex++;
                String hbaseTableName = tsTableInfo.getTableName();
                if (TsConstants.TABLE_TYPE_TEMP.equalsIgnoreCase(tsTableInfo.getTableType())) {
                    hbaseTableName = TsConstants.TABLE_RISK_USER_DEFINE_DATA;
                }
                HTable currentTable = HBaseUtil.getTable(hbaseTableName, createConnection());
                HBaseInputSplitReadThread readThread = new HBaseInputSplitReadThread(bucket,
                        currentTable,
                        fetchedCacheList,
                        hbaseTableName,
                        tsTableInfo.getTableId(),
                        tsTableInfo.getTableType(),
                        tsTableInfo.getColumnNames(),
                        threadName,
                        getFilterList()
                );
                threadList.add(readThread);
                readThread.start();
                log.info("read split: {}, thread: {}", bucket, readThread.getName());
            }
        } catch (Exception e) {
            log.error("open input format error", e);
            throw e;
        }
    }


    public FilterList getFilterList(){
        return null;
    }

    @Override
    public boolean reachedEnd() throws IOException {
        threadList.removeIf(t -> t.exit);
        boolean reachEnd =
                CollectionUtils.isEmpty(threadList) && CollectionUtils.isEmpty(fetchedCacheList);
        return reachEnd;
    }

    @Override
    public Row nextRecord(Row reuse) throws IOException {
        try {
            if (fetchedCacheList == null) {
                return null;
            }
            Result rowResult = fetchedCacheList.poll(4, TimeUnit.MILLISECONDS);
            if (rowResult != null) {
                return HBaseUtil.parseResultToRow(rowResult, flinkMetaInfo, fromType);
            }
        } catch (Exception e) {
            log.warn("read next row error ", e);
        }
        return new Row(flinkMetaInfo.getFieldTypes().length);
    }

    @Override
    public void close() throws IOException {
        if(CollectionUtils.isEmpty(threadList)){
            return;
        }
        for (HBaseInputSplitReadThread t : threadList) {
            t.setExitFlag(true);
        }
    }

    @Override
    public void closeInputFormat() throws IOException {
        fetchedCacheList = null;
        HBaseUtil.closeResource(conn);
    }


    private Connection createConnection() throws IOException {
        if (conn != null) {
            return conn;
        }
        conn = HBaseUtil.createConnection(hBaseProperties);
        return conn;
    }


}
