/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yupaopao.risk.insight.flink.job.etl.common.connect.es.writer;

import com.yupaopao.risk.insight.flink.job.etl.common.connect.es.core.EsConfigKeys;
import com.yupaopao.risk.insight.flink.job.etl.common.options.DataTransferConfig;
import com.yupaopao.risk.insight.flink.job.etl.common.options.WriterConfig;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.SinkFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The writer plugin of ElasticSearch
 * <p>
 * Company: www.dtstack.com
 *
 * <AUTHOR>
 */
public class EsSinkFactory extends SinkFactory {

    public static final int DEFAULT_BULK_ACTION = 100;

    private String address;
    private String username;
    private String password;
    private String index;
    private String type;
    private int bulkAction;
    private Map<String, Object> clientConfig;
    private List<String> columnTypes;
    private List<String> columnNames;
    private List<String> idColumnNames;
    private List<String> idColumnTypes;
    private List<String> idColumnValues;

    public EsSinkFactory(DataTransferConfig config) throws Exception {
        super(config);
        WriterConfig writerConfig = config.getJob().getContent().get(0).getWriter();
        address = writerConfig.getParameter().getStringVal(EsConfigKeys.KEY_ADDRESS);
        username = writerConfig.getParameter().getStringVal(EsConfigKeys.KEY_USERNAME);
        password = writerConfig.getParameter().getStringVal(EsConfigKeys.KEY_PASSWORD);
        type = writerConfig.getParameter().getStringVal(EsConfigKeys.KEY_TYPE);
        index = writerConfig.getParameter().getStringVal(EsConfigKeys.KEY_INDEX);
        bulkAction = writerConfig.getParameter().getIntVal(EsConfigKeys.KEY_BULK_ACTION, DEFAULT_BULK_ACTION);

        clientConfig = new HashMap<>();
        clientConfig.put(EsConfigKeys.KEY_TIMEOUT, writerConfig.getParameter().getVal(EsConfigKeys.KEY_TIMEOUT));
        clientConfig.put(EsConfigKeys.KEY_PATH_PREFIX, writerConfig.getParameter().getVal(EsConfigKeys.KEY_PATH_PREFIX));

        List columns = writerConfig.getParameter().getColumn();
        if (CollectionUtils.isNotEmpty(columns)) {
            columnTypes = new ArrayList<>();
            columnNames = new ArrayList<>();
            for (Object column : columns) {
                Map sm = (Map) column;
                columnNames.add((String) sm.get(EsConfigKeys.KEY_COLUMN_NAME));
                columnTypes.add((String) sm.get(EsConfigKeys.KEY_COLUMN_TYPE));
            }
        }

        List idColumns = (List) writerConfig.getParameter().getVal(EsConfigKeys.KEY_ID_COLUMN);
        if (idColumns != null && idColumns.size() != 0) {
            idColumnNames = new ArrayList<>();
            idColumnTypes = new ArrayList<>();
            idColumnValues = new ArrayList<>();
            for (Object idColumn : idColumns) {
                Map<String, Object> sm = (Map) idColumn;
                idColumnNames.add((String) sm.get(EsConfigKeys.KEY_ID_COLUMN_NAME));
                idColumnTypes.add((String) sm.get(EsConfigKeys.KEY_ID_COLUMN_TYPE));
                idColumnValues.add((String) sm.get(EsConfigKeys.KEY_ID_COLUMN_VALUE));
            }
        }

    }

    @Override public DataStreamSink<?> createSink(DataStream<Map<String, Object>> dataSet) {
        EsOutputFormatBuilder builder = new EsOutputFormatBuilder();
        builder.setAddress(address);
        builder.setUsername(username);
        builder.setPassword(password);
        builder.setIndex(index);
        builder.setType(type);
        builder.setBatchInterval(bulkAction);
        builder.setClientConfig(clientConfig);
        builder.setColumnNames(columnNames);
        builder.setColumnTypes(columnTypes);
        builder.setIdColumnNames(idColumnNames);
        builder.setIdColumnTypes(idColumnTypes);
        builder.setIdColumnValues(idColumnValues);
        builder.setMonitorUrls(monitorUrls);
        builder.setErrors(errors);
        builder.setDirtyPath(dirtyPath);
        builder.setDirtyHadoopConfig(dirtyHadoopConfig);
        builder.setSrcCols(srcCols);

        return createOutput(dataSet, builder.finish());
    }
}
