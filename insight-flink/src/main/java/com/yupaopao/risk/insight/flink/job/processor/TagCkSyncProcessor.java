package com.yupaopao.risk.insight.flink.job.processor;

import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import com.yupaopao.risk.insight.common.support.TagUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseFullTableInputFormat;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.apache.hadoop.hbase.client.Result;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-08 21:10
 *
 ***/
public class TagCkSyncProcessor implements FlinkJobBuilder.MainProcessor {

    public static void main(String[] args) throws Exception {
        System.err.println(URLEncoder.encode("\t{\"tableName\":\"risk_user_tag\"}", "UTF-8"));
    }

    @Override
    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
        String tableName = argMap.get("tableName");
        if (StringUtils.isEmpty(tableName)) {
            throw new IllegalArgumentException("tableName must be configured");
        }


        //hbase全量读取数据同步
        TsTableInfo tsInTable = new TsTableInfo().withTableName(tableName).withBucketPerSplit(1).withTableType("SYSTEM");
        LinkedBlockingQueue<Result> cache = new LinkedBlockingQueue<>(1024 * 20);
        HBaseProperties hBaseProperties = HBaseProperties.getProperties(PropertyType.HBASE);


        ClickHouseProperties clickHouseProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        clickHouseProperties.setInsertDistributedSync(false);

        SingleOutputStreamOperator<String> process = env.createInput(new HBaseFullTableInputFormat(tsInTable, hBaseProperties, cache) {
                    @Override
                    public String parseHBaseResult(Result rowResult) {
                        this.getReaderCounter().add(1);
                        return TagUtil.transHBaseRowToTagJson(rowResult);
                    }

                    @Override
                    public void close() throws IOException {
                        super.close();
                        TagCacheSupport.cleanResource(this.getClass().getClassLoader());
                    }
                }).name("read_hbase[" + tableName + "]")
                .filter(elem -> !HBaseUtil.emptyResultJson(elem))
                .process(new ProcessFunction<String, String>() {
                    @Override
                    public void processElement(String value, Context context, Collector<String> collector) throws Exception {
                        collector.collect(value);
                    }
                });
        process.addSink(new ClickHouseStreamSink(clickHouseProperties, tableName, 2 * 60 * 1000, 8192*7)).name(
                "write_ck" +
                        "[" + tableName + "]").setParallelism(1);

    }
}
