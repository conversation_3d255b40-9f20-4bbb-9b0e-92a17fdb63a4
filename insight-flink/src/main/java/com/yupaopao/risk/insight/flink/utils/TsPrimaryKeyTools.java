package com.yupaopao.risk.insight.flink.utils;

//import com.alicloud.openservices.tablestore.model.PrimaryKey;
//import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
//import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.zip.CRC32;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-12-04 18:32
 *
 ***/

public class TsPrimaryKeyTools {

    public final static long RISK_HIT_LOG_BUCKET_PER_DAY = 1024L;

    public static void main(String[] args) {
        System.err.println(getRiskHitLogPk(1591272430727L, "a396abb3bd904716be0838e7146de33e"));
        System.err.println("0661_20200107ddffd086169d4ccea6c74bd83707f1e0".length());
    }

    /**
     * risk_hit_long 分区键的key构建
     * 将桶编号放在前面，可以保证最多数据分散到1024个分区(只要数据每行映射的桶足够分散)，将时间放前面可能导致一天的数据聚聚在一个分区，
     * 数据不够分散的情况下ts读取会比较慢
     *
     * @param receiveMsgTime
     * @param traceId
     * @return
     */
    public static String getRiskHitLogPk(long receiveMsgTime, String traceId) {
        String dayStr = DateFormatUtils.format(receiveMsgTime, "yyyyMMdd");
        String strBucketNum = getFixLengthBucketNum(traceId, RISK_HIT_LOG_BUCKET_PER_DAY);
        return strBucketNum + "_" + dayStr;
    }


    private static int getDefinedMaxBucketNumLength(long maxBucket) {
        int maxLength = String.valueOf(maxBucket).length();
        return maxLength;
    }

    /***
     * 根据key 计算crc32构建一个固定长度的字符串（长度由maxBucket的长度定，对maxBucket取模得到最后的值）
     * @param key
     * @param maxBucket
     * @return
     */
    private static String getFixLengthBucketNum(String key, long maxBucket) {
        CRC32 crc32 = new CRC32();
        crc32.update(key.getBytes());
        long bucketNum = crc32.getValue() % maxBucket;
        if (bucketNum < 0) {
            bucketNum = Math.abs(bucketNum);
        }
        int maxLength = getDefinedMaxBucketNumLength(maxBucket);
        String strBucketNum = StringUtils.leftPad(String.valueOf(bucketNum), maxLength, '0');
        return strBucketNum;
    }

//    public static PrimaryKey getRiskUserDefineDataPk(Integer userDefineTableId) {
//        String uuid = InsightFlinkUtils.getUUID();
//        PrimaryKeyBuilder builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        builder.addPrimaryKeyColumn(TsConstants.RISK_USER_DEFINE_DATA_TABLE_ID,
//                PrimaryKeyValue.fromLong(userDefineTableId));
//        builder.addPrimaryKeyColumn(TsConstants.RISK_USER_DEFINE_DATA_UUID, PrimaryKeyValue.fromString(uuid));
//        return builder.build();
//    }

//    public static PrimaryKey buildRiskHitLogPrimaryKeyUpperBound(String dayPrefix, int bucket) {
//        PrimaryKeyBuilder builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        String paddedBucketNum = StringUtils.leftPad(String.valueOf(bucket), getDefinedMaxBucketNumLength(RISK_HIT_LOG_BUCKET_PER_DAY), '0');
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_DATE_PARTITION,
//                PrimaryKeyValue.fromString(paddedBucketNum + "_" + dayPrefix));
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_TRACE_ID,
//                PrimaryKeyValue.INF_MAX);
//        return builder.build();
//    }

//    public static PrimaryKey buildRiskHitLogPrimaryKeyLowerBound(String dayPrefix, int bucket) {
//        PrimaryKeyBuilder builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        String paddedBucketNum = StringUtils.leftPad(String.valueOf(bucket), getDefinedMaxBucketNumLength(RISK_HIT_LOG_BUCKET_PER_DAY), '0');
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_DATE_PARTITION,
//                PrimaryKeyValue.fromString(paddedBucketNum + "_" + dayPrefix));
//        builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_TRACE_ID,
//                PrimaryKeyValue.INF_MIN);
//        return builder.build();
//    }
    public static String buildRiskHitLogStartKey(String dayPrefix, int bucket) {
        String paddedBucketNum = StringUtils.leftPad(String.valueOf(bucket), getDefinedMaxBucketNumLength(RISK_HIT_LOG_BUCKET_PER_DAY), '0');
        //! ascii中最小值（rowKey使用到的）
        return paddedBucketNum + "_" + dayPrefix+"!";
    }

    public static String buildRiskHitLogEndKey(String dayPrefix, int bucket) {
        String paddedBucketNum = StringUtils.leftPad(String.valueOf(bucket), getDefinedMaxBucketNumLength(RISK_HIT_LOG_BUCKET_PER_DAY), '0');
        //~ ascii中最小值（rowKey使用到的）
        return paddedBucketNum + "_" + dayPrefix + "~";
    }





//
//    public static PrimaryKey buildRiskUserDefinedDataStartPk(String tableId) {
//        PrimaryKeyBuilder builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        builder.addPrimaryKeyColumn(TsConstants.RISK_USER_DEFINE_DATA_TABLE_ID, PrimaryKeyValue.fromLong(Long.valueOf(tableId)));
//        builder.addPrimaryKeyColumn(TsConstants.RISK_USER_DEFINE_DATA_UUID, PrimaryKeyValue.INF_MIN);
//        return builder.build();
//    }
//
//    public static PrimaryKey buildRiskUserDefinedDataEndPk(String tableId) {
//        PrimaryKeyBuilder builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//        builder.addPrimaryKeyColumn(TsConstants.RISK_USER_DEFINE_DATA_TABLE_ID, PrimaryKeyValue.fromLong(Long.valueOf(tableId)));
//        builder.addPrimaryKeyColumn(TsConstants.RISK_USER_DEFINE_DATA_UUID, PrimaryKeyValue.INF_MAX);
//        return builder.build();
//    }


}
