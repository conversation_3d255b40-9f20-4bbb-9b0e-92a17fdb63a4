package com.yupaopao.risk.insight.flink.enums;

public enum TagRiskLevelEnum {

    SERIOUS("serious", "严重", 4),
    HIGH("high", "高危", 3),
    MIDDLE("middle", "中危", 2),
    SMALL("small", "低危", 1);

    TagRiskLevelEnum(String code, String name, Integer level){
        this.code = code;
        this.name = name;
        this.level = level;
    }
    private String code;
    private String name;
    private Integer level;

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getLevel() {
        return level;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
