package com.yupaopao.risk.insight.flink.connector.clickhouse;

import com.alibaba.fastjson.JSON;
import com.clickhouse.client.*;
import com.clickhouse.client.config.ClickHouseClientOption;
import com.clickhouse.client.config.ClickHouseDefaults;
import com.clickhouse.client.http.config.ClickHouseHttpOption;
import com.clickhouse.client.http.config.HttpConnectionProvider;
import com.clickhouse.data.ClickHouseDataConfig;
import com.clickhouse.data.ClickHouseFormat;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.clickhouse.jdbc.ClickHouseDataSource;
import com.clickhouse.jdbc.ClickHouseStatement;
import com.clickhouse.jdbc.JdbcConfig;
import com.yupaopao.risk.insight.common.groovy.GroovyCommonExecutor;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.clickhouse.core.ClickhouseUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.rdb.core.util.DbUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import ru.yandex.clickhouse.BalancedClickhouseDataSource;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc ClickhouseUtil
 * @date 2020/5/14
 */
@Slf4j
public class ClickHouseUtil {


    private static final Pattern COLUMN_NAME_PATTERN = Pattern.compile("^[A-Za-z_][A-Za-z0-9_]*$");


    public static ru.yandex.clickhouse.ClickHouseConnection createConnectionOld(ClickHouseProperties clickHouseProperties) {
        try {
            Properties connectionProperties = new Properties();
            connectionProperties.put(ClickHouseClientOption.CONNECTION_TIMEOUT.getKey(),
                    String.valueOf(clickHouseProperties.getConnectionTimeout()));
            connectionProperties.put(ClickHouseClientOption.SOCKET_TIMEOUT.getKey(),
                    String.valueOf(clickHouseProperties.getSocketTimeout()));
            BalancedClickhouseDataSource dataSource = new BalancedClickhouseDataSource(
                    "jdbc:clickhouse://" + clickHouseProperties.getHost(), connectionProperties);
            return dataSource.getConnection(clickHouseProperties.getUsername(), clickHouseProperties.getPassword());
        } catch (SQLException e) {
            log.error("create old clickhouse connection error",e);
        }
        return null;
    }

    public static ClickHouseConnection createConnection(ClickHouseProperties clickHouseProperties) {
        try {
            Properties connectionProperties = new Properties();
            connectionProperties.put(ClickHouseClientOption.CONNECTION_TIMEOUT.getKey(),
                    String.valueOf(clickHouseProperties.getConnectionTimeout()));
            connectionProperties.put(ClickHouseClientOption.SOCKET_TIMEOUT.getKey(),
                    String.valueOf(clickHouseProperties.getSocketTimeout()));
            connectionProperties.put(ClickHouseClientOption.WRITE_BUFFER_SIZE.getKey(), "65536");
            connectionProperties.put(ClickHouseClientOption.BUFFER_SIZE.getKey(), "65536");
            connectionProperties.put(JdbcConfig.PROP_WRAPPER_OBJ, "true");
            if (!clickHouseProperties.getHost().toLowerCase().contains("client_name")) {
                connectionProperties.put(ClickHouseClientOption.CLIENT_NAME.getKey(), "risk-insight-flink");
            }

            if (clickHouseProperties.getInsertDistributedSync() != null) {
                int insertSyncFlag = clickHouseProperties.getInsertDistributedSync() ? 1 : 0;
                connectionProperties.put(ClickHouseClientOption.CUSTOM_SETTINGS.getKey(),
                        "insert_distributed_sync=" + insertSyncFlag);
            }

            ClickHouseDataSource dataSource = new ClickHouseDataSource(
                    "jdbc:clickhouse://" + clickHouseProperties.getHost(), connectionProperties);
            return dataSource.getConnection(clickHouseProperties.getUsername(), clickHouseProperties.getPassword());
        } catch (SQLException e) {
            log.error("create clickhouse connection error", e);
        }
        return null;
    }


    public static ClickHouseClient getCkClient(ClickHouseProperties ckProperties) {

        String clientName = "";
        if (!ckProperties.getHost().toLowerCase().contains("client_name")) {
            clientName = "risk-insight-flink";
        }
        ClickHouseClientBuilder ckBuilder = ClickHouseClient.builder()
                .option(ClickHouseClientOption.CONNECTION_TIMEOUT, ckProperties.getConnectionTimeout())
                .option(ClickHouseClientOption.SOCKET_TIMEOUT, ckProperties.getTimeout())
                .option(ClickHouseClientOption.FORMAT, ClickHouseFormat.RowBinaryWithNamesAndTypes)
                .option(ClickHouseClientOption.WRITE_BUFFER_SIZE, 8192)
                .option(ClickHouseClientOption.LOAD_BALANCING_POLICY, "roundRobin")
                .option(ClickHouseDefaults.USER, ckProperties.getUsername())
                .option(ClickHouseDefaults.PASSWORD, ckProperties.getPassword())
                .defaultCredentials(ClickHouseCredentials.fromUserAndPassword(ckProperties.getUsername(), ckProperties.getPassword()))
                .nodeSelector(ClickHouseNodeSelector.of(ClickHouseProtocol.HTTP));

        if (StringUtils.isNotEmpty(clientName)) {
            ckBuilder.option(ClickHouseClientOption.CLIENT_NAME, clientName);
        }
        if (ckProperties.getInsertDistributedSync() != null) {
            int insertSyncFlag = ckProperties.getInsertDistributedSync() ? 1 : 0;
            ckBuilder.option(ClickHouseClientOption.CUSTOM_SETTINGS, "insert_distributed_sync=" + insertSyncFlag);
        }
        ClickHouseClient ckClient = ckBuilder.build();
        return ckClient;
    }

    public static ClickHouseNodes getServers(ClickHouseProperties ckProperties) {
        String protocal = "http://";
        if(StringUtils.isEmpty(ckProperties.getIpHosts())){
            ckProperties.setIpHosts(ckProperties.getHost());
        }
        Integer healthCheckInterval = ckProperties.getHealthCheckInterval();

        int indexOfStartQuery = ckProperties.getIpHosts().indexOf("?");
        String urlQuery = "";
        if (indexOfStartQuery > 0) {
            urlQuery = ckProperties.getIpHosts().substring(indexOfStartQuery + 1);
        }
        String urlWithPwd = Arrays.stream(ckProperties.getIpHosts().split(",")).map(elem -> ckProperties.getUsername() + ":" + ckProperties.getPassword() + "@" + elem).collect(Collectors.joining(","));
        String options = "?";
        if (StringUtils.isNotEmpty(urlQuery)) {
            options += urlQuery;
        }
        options = addOptions(options, "load_balancing_policy=roundRobin" +
                "&health_check_interval=" + healthCheckInterval);
        options = addOptions(options, ckProperties.getOtherOptions());

        ClickHouseNodes servers = CustomClickHouseNodes.of(protocal + urlWithPwd + options, protocal + ckProperties.getDefaultHost());
        return servers;
    }

    private static String addOptions(String originalOptional, String additionalOptions) {
        if (StringUtils.isEmpty(additionalOptions)) {
            return originalOptional;
        }
        if (originalOptional.endsWith("&")) {
            originalOptional += additionalOptions;
        } else {
            originalOptional += "&" + additionalOptions;
        }
        return originalOptional;
    }

    public static Map<String, String> fetchColumns(String tableName, ClickHouseConnection connection) {
        Map<String, String> columns = new HashMap<>();
        if (StringUtils.isEmpty(tableName)) {
            return columns;
        }
        String sql = "desc " + tableName;
        try (ClickHouseStatement stmt = connection.createStatement();) {
            ResultSet rs = stmt.executeQuery(sql);
            while (rs.next()) {
                String name = rs.getString("name");
                String type = rs.getString("type");
                columns.put(name, type);
            }
            return columns;
        } catch (Exception e) {
            log.error("fetch [{}] columns occurs error: ", tableName, e);
        }
        return columns;
    }

    /**
     * 是否为合法Clickhouse列名
     * @param columnName
     * @return
     */
    public static boolean isValidColumn(String columnName) {
        return COLUMN_NAME_PATTERN.matcher(columnName).matches();
    }

    public static boolean addColumn(String tableName, Map<String, String> newColumn, ClickHouseConnection connection) {
        Map<String, String> validColumns = newColumn.entrySet().stream()
                .filter(entry -> {
                    if (isValidColumn(entry.getKey())) {
                        log.warn("invalid column name: {} in table {} has ignored", entry.getKey(), tableName);
                        return false;
                    }
                    return true;
                }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        if (validColumns.isEmpty()) {
            return false;
        }

        StringBuilder sql = new StringBuilder(String.format("ALTER TABLE %s ON CLUSTER default", tableName));
        newColumn.forEach((key, value) -> sql.append(" ADD COLUMN IF NOT EXISTS ")
                .append(key)
                .append(" ")
                .append(value)
                .append(","));

        try (ClickHouseStatement stmt = connection.createStatement();) {
            String alterSql = sql.deleteCharAt(sql.length() - 1).toString();
            log.info("alter sql: {}", alterSql);
            stmt.executeUpdate(alterSql);
            return true;
        } catch (RuntimeException e) {
            // ru.yandex.clickhouse 暂不支持 alter 结果解析，会抛出 JsonParseException，字段会正常修改，忽视该异常
            log.info("alter 结果解析异常, 字段会正常修改");
            return true;
        } catch (Exception e) {
            log.error("sql execute error: ", e);
        }
        return false;
    }

    public static boolean flushDistributedTable(String tableName, ClickHouseConnection connection) {
        String sql = "SYSTEM FLUSH DISTRIBUTED  " + tableName + " on cluster default";
        try (ClickHouseStatement stmt = connection.createStatement();) {
            log.info("flush table: {}", tableName);
            stmt.execute(sql);
            return true;
        } catch (Exception e) {
            log.error("sql execute error: ", e);
        }
        return false;
    }

    public static boolean dropColumn(String alterSql, ClickHouseConnection connection) {

        try (ClickHouseStatement stmt = connection.createStatement();) {
            log.info("alter sql: {}", alterSql);
            stmt.executeUpdate(alterSql);
            return true;
        } catch (RuntimeException e) {
            // ru.yandex.clickhouse 暂不支持 alter 结果解析，会抛出 JsonParseException，字段会正常修改，忽视该异常
            log.info("alter 结果解析异常, 字段会正常修改");
            return true;
        } catch (Exception e) {
            log.error("sql execute error: ", e);
        }
        return false;
    }


    public static List<Map<String, String>> executeQuery(Connection ccConn, String sql) {
        List<Map<String, String>> resList = new ArrayList<>();
        try (Statement ccStmt = ccConn.createStatement();
             ResultSet rs = ccStmt.executeQuery(sql);
        ) {

            if (rs == null || rs.getMetaData() == null) {
                return resList;
            }
            Integer columnCount = rs.getMetaData().getColumnCount();
            if (columnCount == 0) {
                return resList;
            }

            while (rs.next()) {
                Map<String, String> row = new HashMap<>(columnCount);
                for (int i = 1; i <= columnCount; i++) {
                    //date类型会转为LocalDateTime需要处理
                    if (rs.getMetaData().getColumnTypeName(i).toUpperCase().contains("DATETIME")) {
                        Date datetime = rs.getDate(i);
                        row.put(rs.getMetaData().getColumnName(i), InsightDateUtils.getDateStr(datetime, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));
                    } else {
                        row.put(rs.getMetaData().getColumnName(i), rs.getString(i));
                    }
                }
                resList.add(row);
            }
            return resList;
        } catch (Exception e) {
            log.error("query from ck error, sql=" + sql, e);
            return null;
        }
    }

    public static void executeUpdate(ClickHouseConnection ccConn, String sql) {
        try (ClickHouseStatement ccStmt = ccConn.createStatement();) {
            ccStmt.executeUpdate(sql);
        } catch (Exception e) {
            log.error("query from ck error, sql=" + sql.substring(0, sql.length() > 1000 ? 1000 : sql.length()), e);
        }
    }

    /***
     * 针对运行时间较长的sql，设置相应的ckProperties
     * @return
     */
    public static ClickHouseProperties getLongTimeoutProperties() {
        ClickHouseProperties clickHouseProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        clickHouseProperties.setDataTransferTimeout(60000 * 20); //20min
        clickHouseProperties.setSocketTimeout(60000 * 20); //20min
        clickHouseProperties.setConnectionTimeout(60000 * 1); //1min
        return clickHouseProperties;
    }

    public static String sqlInParamsJoin(List<String> ids) {
        String joinIds = "'" + StringUtils.join(ids, "','") + "'";
        return joinIds;
    }


    /**
     * 考虑到risk_hit_log等大表字段比较多，按天分区如果凌晨写入的话大概率有些数据分在两个partition，这样的话势必至少要建两个parts目录
     * 消耗内存回double, 为减少内存使用过高导致内存超过限制错误，应用中对任务按天做切分(json解析也有效率损耗，就只考虑凌晨0点，1点内进行处理)
     */
    public static Map<String, List<String>> processBigTablesParts(List<String> originalDataList, String tableName) {
        Map<String, List<String>> batchParts = new HashMap<>(1);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        boolean isCheckPeriod = (0 == hour || 1 == hour);
        //需要检测凌晨分不同partition的大表，字段多，会占用很大的内存，如果一次插入两个分区的数据，则会产生更多的内存占用
        String bigTables = ApolloProperties.getConfigStr("partition.check.bigtables");
        List<String> checkTables = new ArrayList<>();
        if (StringUtils.isNotEmpty(bigTables)) {
            checkTables = Arrays.asList(bigTables.split(","));
        }
        if (checkTables.contains(tableName) && isCheckPeriod) {
            for (String row : originalDataList) {
                String createdAt = JSON.parseObject(row).getString("createdAt");

                //转为天yyyy-MM-dd
                if (createdAt != null && createdAt.length() >= 10) {
                    createdAt = createdAt.substring(0, 10);
                }
                List<String> dataList;
                if (batchParts.containsKey(createdAt)) {
                    dataList = batchParts.get(createdAt);
                } else {
                    dataList = new ArrayList<>();
                    batchParts.put(createdAt, dataList);
                }
                dataList.add(row);
            }
        } else {
            batchParts.put("1", originalDataList);
        }
        return batchParts;

    }


    public static List<String> getWriteToLocalTables(){
        String toLocalTablesCfg = ApolloProperties.getConfigStr("write.toLocal.tables");
        List<String> toLocalTables = new ArrayList<>();
        if (StringUtils.isNotEmpty(toLocalTablesCfg)) {
            toLocalTables = Arrays.asList(toLocalTablesCfg.split(","));
        }
        return toLocalTables;
    }

    public static Connection getCkConn(ClickHouseProperties ckProperties) {
        if (FlinkConstants.isProdProfile()) {
            return ClickHouseUtil.createConnection(ckProperties);
        } else {
            return ClickHouseUtil.createConnectionOld(ckProperties);
        }
    }


    public static void main(String[] args) {
        Map<String, String> newColumn = new HashMap<>();
        String columns = "{\"locale_rc\":\"String\",\"audio_music\":\"Float64\",\"ainfo_sys_props_ro.build.version.incremental\":\"String\",\"ainfo_sys_props_ro.miui.version.code_time\":\"String\",\"screenOn\":\"Float64\",\"appTime_lut\":\"Float64\",\"app\":\"String\",\"s_c_oces_sname\":\"String\",\"ainfo_sys_props_persist.sys.identifierid.supported\":\"String\",\"appTime_fit\":\"Float64\",\"hookJava_java/lang/reflect/Modifier\":\"Float64\",\"riskdir_cydia\":\"String\",\"s_c_fileExist_fname\":\"String\",\"s_c_dicWithFile_sname\":\"String\",\"locale_lan\":\"String\",\"ainfo_sys_props_service.adb.root\":\"String\",\"ainfo_stfSocket\":\"Float64\",\"s_c_processInfo_fname\":\"String\",\"props_ro.rpmb.board\":\"String\",\"s_c_exePath_fname\":\"String\",\"netInfos_wo\":\"Float64\",\"hookJava_java/lang/ProcessBuilder\":\"Float64\",\"netInfos_wi\":\"Float64\",\"overlayAuthorized\":\"String\",\"s_c_dicWithFile_opcode\":\"String\",\"ainfo_probeOpenCamera\":\"String\",\"ainfo_sys_props_ro.boot.securebootkeyhash\":\"String\",\"ainfo_arp2\":\"String\",\"ainfo_ports\":\"String\",\"ainfo_ipRS\":\"Float64\",\"pmClsName_proxy\":\"String\",\"dataDirReadable\":\"Float64\",\"s_c_oces_opcode\":\"String\",\"locale_cal\":\"String\",\"ainfo_appProcesses\":\"String\",\"audio_system\":\"Float64\",\"ainfo_sys_props_sys.boot.reason\":\"String\",\"sensorsData_acc_z\":\"Float64\",\"s_c_exePath_fbase\":\"String\",\"ainfo_sys_props_ro.build.characteristics\":\"String\",\"hookJava_android/location/LocationManager\":\"Float64\",\"ainfo_sys_props_init.svc.adbd\":\"String\",\"appUsedCount\":\"Float64\",\"ainfo_sysProps2_net_eth0_dns1\":\"String\",\"s_c_processInfo_fbase\":\"String\",\"ainfo_sysProps2_net_eth0_dns2\":\"String\",\"foundVDisplayOn\":\"Float64\",\"ainfo_probeSysProps_ret\":\"Float64\",\"caches_arc\":\"String\",\"launcherInfo_pkg\":\"String\",\"sensorsData_gyr_x\":\"Float64\",\"sensorsData_gyr_z\":\"Float64\",\"sensorsData_gyr_y\":\"Float64\",\"isIphoneXSeries\":\"String\",\"audio_ring\":\"Float64\",\"s_c_fileExist_saddr\":\"String\",\"ainfo_sys_props_ARGH\":\"String\",\"riskdir_apt\":\"String\",\"environment_MallocLargeCache\":\"String\",\"ainfo_arpInfo\":\"String\",\"riskapp_yyzkapp\":\"String\",\"launcherInfo_ver\":\"String\",\"props_ro.boot.securebootkeyhash\":\"String\",\"pmClsName_pm\":\"String\",\"s_c_exePath_opcode\":\"String\",\"screenRecord\":\"Float64\",\"ainfo_sys_props_qemu.sf.lcd_density\":\"String\",\"ainfo_artMethodsIns_m4\":\"String\",\"ainfo_artMethodsIns_m3\":\"String\",\"ainfo_artMethodsIns_m2\":\"String\",\"props_ro.ril.oem.psno\":\"String\",\"ainfo_artMethodsIns_m1\":\"String\",\"xpApp\":\"String\",\"ainfo_artMethodsIns_m6\":\"String\",\"ainfo_artMethodsIns_m5\":\"String\",\"s_c_ocelft_opcode\":\"String\",\"s_c_dicWithFile_saddr\":\"String\",\"ainfo_sys_props_gsm.serial\":\"String\",\"s_c_ocel_opcode\":\"String\",\"s_c_ocel_fname\":\"String\",\"ainfo_sys_props_ro.boot.bootdevice\":\"String\",\"ainfo_sys_props_ro.boot.bootreason\":\"String\",\"netInfos_ci\":\"Float64\",\"outerid\":\"String\",\"caches_pv\":\"String\",\"caches_pt\":\"String\",\"s_c_ocel_sname\":\"String\",\"netInfos_co\":\"Float64\",\"titleBarHeight\":\"String\",\"oi_rmn\":\"String\",\"ainfo_emuShareContent_xy\":\"String\",\"oi_ri\":\"String\",\"ainfo_sys_props_gsm.operator.isroaming\":\"String\",\"airplane\":\"Float64\",\"ainfo_sys_props_ro.bootloader\":\"String\",\"sensorsData_acc_x\":\"Float64\",\"ainfo_scDirPathState\":\"Float64\",\"sensorsData_acc_y\":\"Float64\",\"ainfo_sys_props_ro.build.version.emui\":\"String\",\"hookJava_android/telephony/TelephonyManager\":\"Float64\",\"translateLanguage\":\"String\",\"simCountryISO\":\"String\",\"ainfo_zteDimCls\":\"Float64\",\"ainfo_sys_props_qemu.sf.fake_camera\":\"String\",\"aliAppVerion\":\"String\",\"trafficBytes_tr\":\"Float64\",\"s_c_ocelft_saddr\":\"String\",\"launcherInfo_label\":\"String\",\"s_c_dicWithFile_fname\":\"String\",\"ainfo_gettimeofday\":\"String\",\"ainfo_sys_props_gsm.sim.state\":\"String\",\"trafficBytes_tt\":\"Float64\",\"s_c_ocel_fbase\":\"String\",\"apiLevel\":\"String\",\"microphoneEnabled\":\"String\",\"ainfo_sys_props_ro.ril.oem.psno\":\"String\",\"ainfo_sys_props_ro.miui.ui.version.code\":\"String\",\"currentBattery\":\"String\",\"caches_bv\":\"String\",\"s_c_exePath_sname\":\"String\",\"s_c_ocelft_fbase\":\"String\",\"s_c_fileExist_opcode\":\"String\",\"bootTime2_t1\":\"Float64\",\"bootTime2_t2\":\"Float64\",\"oi_cpuSerial\":\"String\",\"ainfo_stSocket\":\"Float64\",\"fakeLocExcept\":\"Int64\",\"ainfo_sys_props_gsm.sim.operator.numeric\":\"String\",\"hookJava_com/android/internal/telephony/PhoneProxy\":\"Float64\",\"bootCount\":\"Float64\",\"props_ro.ril.oem.sno\":\"String\",\"caches_rmn\":\"String\",\"taiJiBasePackage\":\"Float64\",\"asIpa2\":\"String\",\"ainfo_sys_props_ro.ril.oem.sno\":\"String\",\"riskdir_MobileSubstrate\":\"String\",\"sys_radioVersion\":\"String\",\"ainfo_sys_props_gsm.operator.numeric\":\"String\",\"networkCountryIso\":\"String\",\"audio_alarm\":\"Float64\",\"display\":\"String\",\"s_c_processInfo_sname\":\"String\",\"space_t\":\"Float64\",\"ainfo_sys_props_ro.boot.vbmeta.digest\":\"String\",\"ainfo_sys_props_ro.rpmb.board\":\"String\",\"cpuHw\":\"String\",\"audio_call\":\"Float64\",\"runningProcessCount\":\"Float64\",\"ainfo_sys_props_vmprop.androidid\":\"String\",\"space_f\":\"Float64\",\"s_c_ocel_saddr\":\"String\",\"ainfo_cid\":\"String\",\"ainfo_sys_props_gsm.operator.idpstring\":\"String\",\"props_sys.serialno\":\"String\",\"parentDirReadable\":\"Float64\",\"targetSdk\":\"Float64\",\"props_gsm.serial\":\"String\",\"s_c_oces_saddr\":\"String\",\"s_c_exePath_saddr\":\"String\",\"s_c_processInfo_saddr\":\"String\",\"ainfo_sys_props_gsm.sim.operator.alpha\":\"String\",\"caches_fv\":\"String\",\"ainfo_sys_props_sys.serialno\":\"String\",\"ainfo_sys_props_ro.build.version.opporom\":\"String\",\"ainfo_clock_gettime\":\"String\",\"ainfo_sys_props_ro.product.vendor.model\":\"String\",\"environment_PERFC_ENABLE_AGPC_CHECKS\":\"String\",\"ainfo_sys_props_gsm.operator.iso-country\":\"String\",\"s_c_ocelft_sname\":\"String\",\"adbEnabled\":\"Float64\",\"capture\":\"Float64\",\"s_c_dicWithFile_fbase\":\"String\",\"environment_PERFC_ENABLE_EXTENDED_DIAGNOSTIC_FORMAT\":\"String\",\"s_c_oces_fbase\":\"String\",\"extDisplays\":\"String\",\"hookJava_com/tencent/mapapi/service/LocationManager\":\"Float64\",\"sims2\":\"String\",\"hookJava_com/android/internal/telephony/gsm/GSMPhone\":\"Float64\",\"ainfo_mem_md5\":\"String\",\"ainfo_probeSysProps_b\":\"Float64\",\"uiMode\":\"Float64\",\"props_gsm.operator.alpha\":\"String\",\"ainfo_sys_props_ro.setupwizard.mode\":\"String\",\"ainfo_sys_props_ro.miui.ui.version.name\":\"String\",\"storage\":\"String\",\"s_c_oces_fname\":\"String\",\"appMode\":\"String\",\"riskapp_YOYapp\":\"String\",\"ainfo_sys_props_ro.vivo.os.version\":\"String\",\"ainfo_sys_props_gsm.operator.alpha\":\"String\",\"s_c_fileExist_fbase\":\"String\",\"hasMagisk\":\"Float64\",\"trafficBytes_mt\":\"Float64\",\"transparentTitle\":\"String\",\"s_c_fileExist_sname\":\"String\",\"performance\":\"String\",\"ainfo_sysProps2_net_eth0_gw\":\"String\",\"s_c_processInfo_opcode\":\"String\",\"audio_outs\":\"String\",\"sensorsData_mag_z\":\"Float64\",\"sensorsData_mag_y\":\"Float64\",\"ainfo_probePreviewCB\":\"String\",\"sensorsData_mag_x\":\"Float64\",\"s_c_ocelft_fname\":\"String\",\"account\":\"Float64\",\"hookJava_com/android/internal/telephony/PhoneSubInfo\":\"Float64\",\"trafficBytes_mr\":\"Float64\"}";
        newColumn = JSON.parseObject(columns, Map.class);
        StringBuilder sql = new StringBuilder("ALTER TABLE risk_hit_log ON CLUSTER default");
        newColumn.forEach((key, value) -> {
            sql.append(" ADD COLUMN IF NOT EXISTS ")
                    .append(key)
                    .append(" ")
                    .append(value)
                    .append(",");
        });

        String alterSql = sql.deleteCharAt(sql.length() - 1).toString();
        System.out.println(alterSql);

    }


}
