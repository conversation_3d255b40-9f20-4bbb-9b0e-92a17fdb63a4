package com.yupaopao.risk.insight.flink.job.etl.common.connect.odps.reader;

import com.yupaopao.risk.insight.flink.job.etl.common.connect.odps.core.OdpsConfigKeys;
import com.yupaopao.risk.insight.flink.job.etl.common.format.BaseRichInputFormatBuilder;

import java.util.Map;

/****
 * zengxiangcai
 * 2023/5/30 13:58
 ***/
public class WangYiOdpsInputFormatBuilder extends BaseRichInputFormatBuilder {

    private WangYiOdpsInputFormat format;


    public WangYiOdpsInputFormatBuilder() {
        format = new WangYiOdpsInputFormat();
        super.format = format;
    }

    public void setOdpsConfig(Map<String, String> odpsConfig) {
        format.odpsConfig = odpsConfig;
        format.projectName = odpsConfig.get(OdpsConfigKeys.KEY_PROJECT);
    }

    public void setCustomSql(String customSql) {
        format.sql = customSql;
    }

    @Override
    protected void checkFormat() {
        if (format.getRestoreConfig() != null && format.getRestoreConfig().isRestore()) {
            throw new UnsupportedOperationException("This plugin not support restore from failed state");
        }
    }
}
