package com.yupaopao.risk.insight.flink.job.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.support.InsightFlinkUtils;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.cep.DynamicCEP;
import com.yupaopao.risk.insight.flink.cep.PatternStream;
import com.yupaopao.risk.insight.common.cep.beans.CepRule;
import com.yupaopao.risk.insight.common.cep.beans.RiskEvent;
import com.yupaopao.risk.insight.common.cep.constants.CepConstants;
import com.yupaopao.risk.insight.flink.cep.inject.RuleChangeListener;
import com.yupaopao.risk.insight.flink.connector.redis.source.RedisRuleSourceFunction;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.StringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.cep.MatchResultHandler;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.common.typeutils.base.ListSerializer;
import org.apache.flink.api.common.typeutils.base.StringSerializer;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.cep.functions.PatternProcessFunction;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;
import org.apache.flink.streaming.api.datastream.BroadcastStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;

import java.util.*;

import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.KAFKA_GROUP_ID_RISK_INSIGHT_COMMON_CEP;
import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.TOPIC_FLINK_BIXIN_MAPI;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-08 13:27
 *
 ***/
@Slf4j
public class CepMAPIProcessor implements FlinkJobBuilder.MainProcessor {


    @Override
    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {

        TypeSerializer<CepRule> elemSerializer = TypeInformation.of(CepRule.class).createSerializer(env.getConfig());
        ListSerializer<CepRule> valueSerializer = new ListSerializer<>(elemSerializer);
        StringSerializer keySerializer = StringSerializer.INSTANCE;

        //ruleList
        MapStateDescriptor<String, List<CepRule>> ruleListState = new MapStateDescriptor<String,
                List<CepRule>>(
                "ruleListState",
                keySerializer,
                valueSerializer
        );


        //load property from apollo
        KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA);
        Properties kafkaProps = kafkaProperties.getProperties();
        kafkaProps.put("group.id", KAFKA_GROUP_ID_RISK_INSIGHT_COMMON_CEP);
        List<String> topics = Collections.singletonList(TOPIC_FLINK_BIXIN_MAPI);
        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics,
                new StringKafkaDeserializationSchemaWrapper(new SimpleStringSchema(), false),
                kafkaProps);
//        consumer.setStartFromEarliest();
        //add source stream
        DataStream<String> kafkaStream = env
                .addSource(consumer)
                .name("read_kafka_cep_job")
                .setParallelism(3);

        DataStream<RiskEvent> riskEventStream = kafkaStream.map(new MapFunction<String, RiskEvent>() {
            @Override
            public RiskEvent map(String s) throws Exception {
                if (StringUtils.isEmpty(s)) {
                    return null;
                }
                //json扁平化处理
                Map<String, Object> flattenMap = JsonFlatterUtils.toMap(s, FlattenMode.KEEP_ARRAYS);
                for (Map.Entry<String, Object> entry : flattenMap.entrySet()) {
                    if (entry.getValue() instanceof List) {
                        entry.setValue(JSONObject.toJSONString(entry.getValue()));
                    }
                }
                return new RiskEvent(flattenMap);
            }
        }).filter(elem -> elem != null).name("riskEvent");


        //get rules from redis
        RedisProperties redisProperties = RedisProperties.getProperties(PropertyType.REDIS);

        DataStream<List<CepRule>> ruleStream = env.addSource(new RedisRuleSourceFunction(redisProperties, TOPIC_FLINK_BIXIN_MAPI),
                "ruleEvent");
        //broad cast stream
        BroadcastStream<List<CepRule>> broadcastStream = ruleStream.broadcast(ruleListState);

        //connect non-broadcast stream to broadcastStream
        SingleOutputStreamOperator<RiskEvent> outputStream =
                riskEventStream.connect(broadcastStream).process(new BroadcastProcessFunction<RiskEvent,
                        List<CepRule>, RiskEvent>() {
                    @Override
                    public void processElement(RiskEvent riskEvent, ReadOnlyContext readOnlyContext, Collector<RiskEvent> collector) throws Exception {
                        //最新的规则
                        List<CepRule> latestRules = readOnlyContext.getBroadcastState(ruleListState).get("allRules");
                        if (CollectionUtils.isEmpty(latestRules)) {
                            log.info("latest rules is empty , maybe not initialized...");
                            return;
                        }

                        //output all riskEvent join rules
                        for (CepRule rule : latestRules) {
                            //riskEvent的topic和rule的dataSource相同
                            if (StringUtils.isNotEmpty(rule.getDataSource()) && riskEvent.getTopic().equals(rule.getDataSource())) {
                                riskEvent.addRuleId(rule.getRuleId());
                                riskEvent.addGroupByColumnName(rule.getGroupByColumns());
                                collector.collect(riskEvent);
                            }
                            if (StringUtils.isEmpty(rule.getDataSource()) && "RISK-ONLINE-RESULT-LOG".equals(riskEvent.getTopic())) {
                                //历史topic为RISK-ONLINE-RESULT-LOG
                                riskEvent.addRuleId(rule.getRuleId());
                                riskEvent.addGroupByColumnName(rule.getGroupByColumns());
                                collector.collect(riskEvent);
                            }
                        }


                    }

                    @Override
                    public void processBroadcastElement(List<CepRule> cepRules, Context context, Collector<RiskEvent> collector) throws Exception {
                        context.getBroadcastState(ruleListState).put("allRules", cepRules);
                    }
                });


//        outputStream = outputStream.assignTimestampsAndWatermarks(new AssignerWithPeriodicWatermarks<RiskEvent>() {
//            @Nullable
//            @Override
//            public Watermark getCurrentWatermark() {
//                //水印是当前时间减去10秒，也就是数据可以迟到十秒
//                return new Watermark(System.currentTimeMillis() - 10L * 1000);
//            }
//
//            @Override
//            public long extractTimestamp(RiskEvent riskEvent, long l) {
//                //事件时间使用createAt字段
//                Object msgTime = riskEvent.getData().get("kafkaMsgTime");
//                if (msgTime instanceof Long) {
//                    return (long) msgTime;
//                } else if (msgTime instanceof Integer) {
//                    return ((Integer) msgTime).intValue();
//                } else if (msgTime instanceof BigDecimal) {
//                    ((BigDecimal) msgTime).longValue();
//                }
//                return System.currentTimeMillis();
//            }
//        });


        KeyedStream<RiskEvent, String> ruleKeyedStream = outputStream
                .filter(elem -> {
                    if (elem == null || elem.getData() == null) {
                        return false;
                    }
                    String groupByName = elem.getGroupByColumnName();
                    if (StringUtils.isEmpty(groupByName)) {
                        return false;
                    }
                    String keyedColumnValue = elem.extractPartitionKey(groupByName);
                    if (StringUtils.isEmpty(keyedColumnValue)) {
                        return false;
                    }
                    return true;
                })
                .keyBy(new KeySelector<RiskEvent, String>() {
                    @Override
                    public String getKey(RiskEvent value) throws Exception {
                        try {
                            String keyedColumnValue = value.extractPartitionKey(value.getGroupByColumnName());

                            String topicInKey = ""; //兼容历史，原先的key去除掉
                            if ("RISK-ONLINE-RESULT-LOG".equals(value.getTopic())) {
                                return value.getRuleId() + CepConstants.RULE_KEY_SEPARATOR + keyedColumnValue;
                            } else {
                                topicInKey = value.getTopic();
                            }
                            String key =
                                    value.getRuleId() + CepConstants.RULE_KEY_SEPARATOR + topicInKey + ":" + keyedColumnValue;
                            return key;
                        } catch (Exception e) {
                            log.error("get key from riskEvent error: ", e);
                            throw e;
                        }
                    }
                });


        // 这个只是一个假设的测试模式,方便构建job,正式处理后这个模式会被替代
        Pattern defaultPattern = Pattern.<RiskEvent>begin("defaultPattern").where(new SimpleCondition<RiskEvent>() {
            @Override
            public boolean filter(RiskEvent value) throws Exception {
                return value != null && "cep-stater-pattern".equals(value.getData().get("userId"));
            }
        });


        PatternStream<RiskEvent> ps = DynamicCEP.pattern(ruleKeyedStream, defaultPattern, new RuleChangeListener() {
            @Override
            public String getDataSource() {
                return TOPIC_FLINK_BIXIN_MAPI;
            }
        });

        //匹配的模式结果处理
        DataStream<Map<String, Object>> result = ps.process(new PatternProcessFunction<RiskEvent, Map<String, Object>>() {
            @Override
            public void processMatch(Map<String, List<RiskEvent>> map, Context context, Collector<Map<String, Object>> collector) throws Exception {
                if (map == null) {
                    return;
                }
                String batchId = InsightFlinkUtils.getUUID();
                //方便某些时候一个模式只对分组值做处理(如黑名单),一个模式标记第一个
                int subBatchId = 0;
                Date currentTime = new Date();
                for (Map.Entry<String, List<RiskEvent>> entry : map.entrySet()) {
                    String currentRuleId = entry.getValue().get(0).getRuleId();
                    //每一个模式中匹配到的不同模式名
                    String currentSinglePatternName = entry.getKey();
                    for (RiskEvent r : entry.getValue()) {
                        String traceId = r.getStringValue("trace");
                        String groupByColumn = r.getGroupByColumnName();
                        String groupByColumnValue = r.extractPartitionKey(groupByColumn);
                        String rowKey = currentRuleId + "#" + currentSinglePatternName + "#" + traceId + "#" + System.currentTimeMillis();

                        Map<String, Object> resultMap = new HashMap<>();
                        resultMap.put("ruleId", r.getRuleId());
                        resultMap.put("sRuleName", currentSinglePatternName);
                        resultMap.put("groupByColumnValue", groupByColumnValue);
                        resultMap.put("groupByColumn", groupByColumn);
                        resultMap.put("userId", r.getData().get("uid"));
                        resultMap.put("traceId", r.getData().get("trace"));
                        resultMap.put("batchId", batchId); //标识某一个模式序列组
                        resultMap.put("subBatchId", String.valueOf(subBatchId++));
                        resultMap.put("createTime", currentTime.getTime());
                        String dataString = JSON.toJSONString(r.getData());
                        resultMap.put("detail", JsonFlatterUtils.toJson(dataString, FlattenMode.KEEP_ARRAYS));
                        collector.collect(resultMap);
                        log.info("matched, traceId: {}, patternId: {}, batchId: {}, srule: {}", traceId, r.getRuleId(),
                                batchId,currentSinglePatternName);
                    }
                }

                //先存入hbase做后续的处理
                //key: ruleId#patternName#traceId, groupByValue,
                // groupByColumn,traceId，userId，eventCode，其他字段统一当做json串放到一个字段
            }
        });


        //写入kafka，后续需要处理的直接消费kafka
//        result.addSink(MatchResultHandler.createDBSink()).name("write cep match");
        result.addSink(MatchResultHandler.createKafkaSink()).name("write cep match");
    }
}
