package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.types.Row;

@Slf4j
public class HBaseStreamRowSink extends RichSinkFunction<Row> implements CheckpointedFunction {

    final FlinkSQLOutputFormat outputFormat;

    public HBaseStreamRowSink(String tableName, FlinkMetaInfo flinkMetaInfo, HBaseProperties hBaseProperties) {
        outputFormat = new FlinkSQLOutputFormat(tableName, hBaseProperties, flinkMetaInfo);
    }

    @Override
    public void snapshotState(FunctionSnapshotContext functionSnapshotContext) throws Exception {
        outputFormat.flush();
    }

    @Override
    public void initializeState(FunctionInitializationContext functionInitializationContext) throws Exception {

    }


    @Override
    public void invoke(Row value, Context context) throws Exception {
        outputFormat.writeRecord(value);
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        RuntimeContext ctx = getRuntimeContext();
        outputFormat.setRuntimeContext(ctx);
        outputFormat.open(ctx.getIndexOfThisSubtask(), ctx.getNumberOfParallelSubtasks());
    }

    @Override
    public void close() throws Exception {
        outputFormat.close();
    }
}
