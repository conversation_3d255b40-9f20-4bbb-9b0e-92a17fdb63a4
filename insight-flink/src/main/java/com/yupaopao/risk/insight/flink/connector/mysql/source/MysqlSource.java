package com.yupaopao.risk.insight.flink.connector.mysql.source;

import com.yupaopao.risk.insight.flink.connector.mysql.model.Factor;
import com.yupaopao.risk.insight.flink.connector.redis.sink.client.RedisClient;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.*;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class MysqlSource extends RichSourceFunction<List<Factor>> implements Serializable {
    private static final long serialVersionUID = 5229215513169640784L;
    private static Logger LOG = LoggerFactory.getLogger(MysqlSource.class);

    private Connection connection = null;
    private PreparedStatement ps = null;
    private DBProperties dbProperties;
    private String[] business;
    private Map<Long, Long> lastTimeMap = new HashMap<>();
    private List<String> delList = new ArrayList<>();
    private static JedisPool jedisPool;
    //    private static Jedis jedis;
    private ThreadPoolExecutor threadPoolExecutor;
    private ConcurrentLinkedQueue<String> delCache = new ConcurrentLinkedQueue<>();

    public MysqlSource(DBProperties dbProperties, String... business) {
        this.dbProperties = dbProperties;
        this.business = business;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        connection = getConnection();
        if (business.length > 0) {
            String join = String.join(",", business);
            String sql = String.format("select * from risk_factor where status='ENABLE' and business in (%s)", join);
            ps = connection.prepareStatement(sql);
        } else {
            ps = connection.prepareStatement("select * from risk_factor where status='ENABLE'");
        }
        jedisPool = RedisClient.getClient();
//        jedis = jedisPool.getResource();
        threadPoolExecutor = new ThreadPoolExecutor(1,
                1,
                1000,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(10),
                r -> new Thread(r, "facter-clean"));
    }

    @Override
    public void run(SourceContext<List<Factor>> ctx) throws Exception {
        while (true) {
            List<Factor> factors = new ArrayList<>();
            try (ResultSet resultSet = ps.executeQuery();) {
                while (resultSet.next()) {
                    Factor factor = new Factor();
                    long resetTime = resultSet.getLong("reset_time");
                    factor.setId(resultSet.getLong("id"));
                    factor.setGroupKey(resultSet.getString("group_key") + "##" + resetTime);
                    factor.setAggKey(resultSet.getString("agg_key"));
                    factor.setFunction(resultSet.getString("_function") + "##" + resultSet.getInt("window_type"));
                    factor.setTimeSpan(resultSet.getLong("time_span"));
                    factor.setCondition(resultSet.getString("_condition"));
                    if (lastTimeMap.containsKey(factor.getId()) && lastTimeMap.get(factor.getId()) < resetTime) {
                        delList.add("FACTOR-DATA#" + factor.getId() + "#*");
                    }
                    lastTimeMap.put(factor.getId(), resetTime);
                    factors.add(factor);
                }
            }
            LOG.info("刷新累积因子配置信息，共：{} 条", factors.size());
            ctx.collect(factors);
            if (delList.size() > 0) {
                delCache.addAll(delList);
                delList.clear();
                threadPoolExecutor.execute(() -> {
                    while (!delCache.isEmpty()) {
                        MysqlSource.delete(delCache.poll());
                    }
                });
            }
            factors.clear();
            Thread.sleep(10 * 1000);
        }
    }

    @Override
    public void cancel() {
        try {
            ps.cancel();
            connection.close();
            RedisClient.closePool(jedisPool);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (connection != null) {
            connection.close();
        }
        if (ps != null) {
            ps.close();
        }
        RedisClient.closePool(jedisPool);
    }

    public Connection getConnection() {
        try {
            Class.forName(dbProperties.getDriver());
            connection = DriverManager.getConnection(dbProperties.getUrl(), dbProperties.getUsename(), dbProperties.getPassword());
        } catch (Exception e) {
            LOG.info("connect to mysql error : ", e);
        }
        return connection;
    }

    public static void delete(String param) {
        ScanParams scanParams = new ScanParams();
        scanParams.match(param);
        scanParams.count(1000);

        String scanFlag = "0";
        LOG.info("开始清空 {} 累计因子", param);
        try (Jedis jedis = jedisPool.getResource(); Pipeline pipelined = jedis.pipelined()) {
            do {
                ScanResult<String> ret = jedis.scan(scanFlag, scanParams);
                List<String> result = ret.getResult();
                result.forEach(pipelined::del);
                pipelined.sync();
                scanFlag = ret.getCursor();
            } while (!"0".equals(scanFlag));
            LOG.info("完成清空 {} 累计因子", param);
        } catch (Exception e) {
            LOG.error("累计因子清除失败", e);
        }
    }
}
