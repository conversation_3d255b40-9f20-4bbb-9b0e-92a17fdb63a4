package com.yupaopao.risk.insight.flink.job.etl;

import com.jcraft.jsch.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.Properties;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class SftpUtil {
    private ChannelSftp sftp;

    private Session session;
    /**
     * FTP 登录用户名
     */
    private String userName;
    /**
     * FTP 登录密码
     */
    private String password;
    /**
     * FTP 服务器地址IP地址
     */
    private String host;
    /**
     * FTP 端口
     */
    private int port;

    private String sftpDirectory;

    /**
     * 构造基于密码认证的sftp对象
     *
     * @param userName
     * @param password
     * @param host
     * @param port
     */
    public SftpUtil(String userName, String password, String host, int port) {
        this.userName = userName;
        this.password = password;
        this.host = host;
        this.port = port;
    }

    public SftpUtil(String userName, String password, String host, int port, String directory) {
        this.userName = userName;
        this.password = password;
        this.host = host;
        this.port = port;
        this.sftpDirectory = directory;

    }

    public SftpUtil() {
    }

    /**
     * 连接sftp服务器
     *
     * @throws Exception
     */
    public void login() {
        try {
            JSch jsch = new JSch();

            session = jsch.getSession(userName, host, port);
            log.info("Session is build");
            if (password != null) {
                session.setPassword(password);
            }
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");

            session.setConfig(config);
            session.connect();
            log.info("Session is connected");

            Channel channel = session.openChannel("sftp");
            channel.connect();
            log.info("channel is connected");

            sftp = (ChannelSftp) channel;
            log.info(String.format("sftp server host:[%s] port:[%s] is connect successful", host, port));
        } catch (JSchException e) {
            log.error("Cannot connect to specified sftp server : {}:{} \n Exception message is: {}", new Object[]{host, port, e.getMessage()});
        }
    }


    /**
     * 关闭连接 server
     */
    public void logout() {
        if (sftp != null) {
            if (sftp.isConnected()) {
                sftp.disconnect();
                log.info("sftp is closed already");
            }
        }
        if (session != null) {
            if (session.isConnected()) {
                session.disconnect();
                log.info("sshSession is closed already");
            }
        }
    }

    /**
     * 将输入流的数据上传到sftp作为文件
     *
     * @param directory    上传到该目录
     * @param sftpFileName sftp端文件名
     * @param input        输入流
     * @throws SftpException
     * @throws Exception
     */
    public void upload(String directory, String sftpFileName, InputStream input) throws SftpException {
        try {
            sftp.cd(directory);
        } catch (SftpException e) {
            log.warn("directory is not exist");
            sftp.mkdir(directory);
            sftp.cd(directory);
        }
        sftp.put(input, sftpFileName);
        log.info("file:{} is upload successful", sftpFileName);
    }

    /**
     * 上传单个文件
     *
     * @param directory  上传到sftp目录
     * @param uploadFile 要上传的文件,包括路径
     * @throws FileNotFoundException
     * @throws SftpException
     * @throws Exception
     */
    public void upload(String directory, String uploadFile) throws FileNotFoundException, SftpException {
        File file = new File(uploadFile);
        upload(directory, file.getName(), new FileInputStream(file));
    }

    /**
     * 将byte[]上传到sftp，作为文件。注意:从String生成byte[]是，要指定字符集。
     *
     * @param directory    上传到sftp目录
     * @param sftpFileName 文件在sftp端的命名
     * @param byteArr      要上传的字节数组
     * @throws SftpException
     * @throws Exception
     */
    public void upload(String directory, String sftpFileName, byte[] byteArr) throws SftpException {
        upload(directory, sftpFileName, new ByteArrayInputStream(byteArr));
    }

    /**
     * 将字符串按照指定的字符编码上传到sftp
     *
     * @param directory    上传到sftp目录
     * @param sftpFileName 文件在sftp端的命名
     * @param dataStr      待上传的数据
     * @param charsetName  sftp上的文件，按该字符编码保存
     * @throws UnsupportedEncodingException
     * @throws SftpException
     * @throws Exception
     */
    public void upload(String directory, String sftpFileName, String dataStr, String charsetName) throws UnsupportedEncodingException, SftpException {
        upload(directory, sftpFileName, new ByteArrayInputStream(dataStr.getBytes(charsetName)));
    }


    public boolean zipFile(String filePath, String targetPath, String zipName) {
        boolean flag = false;

        File resourcesFile = new File(filePath);     //源文件
        File targetFile = new File(targetPath);          //目的

        //如果目的路径不存在，则新建
        if (!resourcesFile.exists()) {
            resourcesFile.mkdirs();
        }
        if (!targetFile.exists()) {
            targetFile.mkdirs();
        }
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(targetPath + "/" + zipName);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

        ZipOutputStream out = null;
        try {
            out = new ZipOutputStream(new BufferedOutputStream(outputStream));
            if (!resourcesFile.isDirectory()) {
                log.info("压缩文件必须为整个目录");
                return false;
            }
            int BUFFER_SIZE = 1024;
            byte buff[] = new byte[BUFFER_SIZE];
            File dir = new File(targetPath);
            if (!dir.isDirectory()) {
                throw new IllegalArgumentException(targetPath + " 不是一个文件夹");
            }
            File files[] = dir.listFiles();

            int fileSize = 0;
            for (int i = 0; i < files.length; i++) {
                if (files[i].getName().indexOf(".zip") < 0) {//目录下有一个zip文件 不压缩原zip文件
                    FileInputStream fi = new FileInputStream(files[i]);
                    BufferedInputStream origin = new BufferedInputStream(fi);
                    ZipEntry entry = new ZipEntry(files[i].getName());
                    out.putNextEntry(entry);
                    int count;
                    while ((count = origin.read(buff)) != -1) {
                        out.write(buff, 0, count);
                    }
                    origin.close();
                    fileSize++;
                }
            }

            if (fileSize > 0) {
                flag = true;
            }
            log.info("ZipOutputStream 成功压缩文件的长度: " + fileSize);
        } catch (IOException e) {
            e.printStackTrace();
            log.info("压缩文件到：" + targetPath + " 时发生异常");
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    log.info("ZipOutputStream关闭时 发生异常");
                }
            }
        }
        return flag;
    }
}
