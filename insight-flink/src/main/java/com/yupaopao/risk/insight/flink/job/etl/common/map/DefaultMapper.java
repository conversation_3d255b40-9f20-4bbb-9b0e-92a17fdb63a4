package com.yupaopao.risk.insight.flink.job.etl.common.map;

import com.dtstack.flinkx.enums.ColumnType;
import com.yupaopao.risk.insight.flink.job.etl.common.options.MapperConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.util.Collector;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yupaopao.risk.insight.flink.job.etl.common.options.MapperConfig.KEY_COLUMN_LIST;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc DefaultMapFunction
 * @date 2020/10/15
 */
@Slf4j
public class DefaultMapper extends BaseMapFunction {
    private static final long serialVersionUID = 3463486355714104341L;

    public DefaultMapper(MapperConfig config) throws Exception {
        super(config);
        List columns = (List) config.getVal(KEY_COLUMN_LIST);
        parseSrcColumnNames(columns);
    }

    @Override public void flatMap(Map<String, Object> value, Collector<Map<String, Object>> out) throws Exception {
        super.flatMap(value, out);
        if (isDebug && value.containsKey(KEY_DEBUG)) {
            return;
        }
        if (sourceCols.size() == 0) {
            out.collect(value);
            return;
        }
        Map<String, Object> data = new HashMap<>(sourceCols.size());
        for (int i = 0; i < sourceCols.size(); i++) {
            String[] parts = sourceCols.get(i).split("\\.");

            Map<String, Object> current = value;
            for(int j = 0; j < parts.length - 1; ++j) {
                if (current == null) {
                    return;
                }
                String key = parts[j];
                current = (Map<String, Object>) current.get(key);
            }
            if (null == current.get(parts[parts.length - 1])) {
                continue;
            }
            Object col = parseData(current.get(parts[parts.length - 1]), colTypes.get(i));

            if (col != null) {
                String[] tarParts = targetCols.get(i).split("\\.");

                Map<String, Object> tarCurrent = data;
                for(int j = 0; j < tarParts.length - 1; ++j) {
                    String key = tarParts[j];
                    tarCurrent.computeIfAbsent(key, k -> new HashMap<String, Object>());
                    tarCurrent = (Map<String, Object>) tarCurrent.get(key);
                }

                tarCurrent.put(tarParts[tarParts.length - 1], col);
            }
        }
        if (data.size() > 0) {
            out.collect(data);
        }
    }

    private Object parseData(Object value, String type) {
        ColumnType columnType = ColumnType.getType(type);
        Object result = null;
        try {
            switch (columnType) {
                case BOOLEAN:
                    result = Boolean.valueOf(value.toString().toLowerCase());
                    break;
                case TINYINT:
                    result = Byte.valueOf(value.toString());
                    break;
                case SMALLINT:
                case SHORT:
                    result = Short.valueOf(value.toString());
                    break;
                case INT:
                case INTEGER:
                    result = Integer.valueOf(value.toString());
                    break;
                case BIGINT:
                case LONG:
                    result = Long.valueOf(value.toString());
                    break;
                case FLOAT:
                    result = Float.valueOf(value.toString());
                    break;
                case DOUBLE:
                    result = Double.valueOf(value.toString());
                    break;
                case DECIMAL:
                    result = new BigDecimal(value.toString());
                    break;
                case STRING:
                case VARCHAR:
                case CHAR:
                case TEXT:
                    result = value.toString();
                    break;
                default:
                    log.error("not support for this type: {}", type);
            }
        } catch (Exception e) {
            log.error("type parse error:", e);
        }
        return result;
    }

    private void parseSrcColumnNames(List columns) {
        if (columns == null || columns.isEmpty()) {
            return;
        }

        if (columns.get(0) instanceof Map) {
            for (Object column : columns) {
                Map<String, Object> colMap = (Map<String, Object>) column;
                String sourceName = (String) colMap.get("source");
                String targetName = (String) colMap.get("target");
                String type = (String) colMap.get("type");
                if (StringUtils.isNotBlank(sourceName) && StringUtils.isNotBlank(targetName)) {
                    sourceCols.add(sourceName);
                    targetCols.add(targetName);
                    colTypes.add(type);
                }
            }
        }
    }
}
