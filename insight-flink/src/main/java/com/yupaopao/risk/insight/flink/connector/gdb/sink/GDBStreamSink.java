package com.yupaopao.risk.insight.flink.connector.gdb.sink;

import com.yupaopao.risk.insight.common.beans.graph.EdgeInfo;
import com.yupaopao.risk.insight.common.beans.graph.VertexInfo;
import com.yupaopao.risk.insight.flink.bean.graph.EventGraphElement;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiang<PERSON>i
 * @Date: 2020-05-21 19:34
 *
 ***/
public class GDBStreamSink extends RichSinkFunction<EventGraphElement> implements CheckpointedFunction {

    private GDBOutputFormat outputFormat;

    public GDBStreamSink(GDBOutputFormat outputFormat) {
        this.outputFormat = outputFormat;
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {

    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {

    }

    @Override
    public void invoke(EventGraphElement value, Context context) throws Exception {
        EventGraphElement data = removeValidGdbData(value);
        outputFormat.writeRecord(data);
    }


    //gdb存储62位设备关联数据,其他边数据过滤
    private EventGraphElement removeValidGdbData(EventGraphElement eventElem) {
        if (CollectionUtils.isEmpty(eventElem.getVertexes()) || CollectionUtils.isEmpty(eventElem.getEdges())) {
            return eventElem;
        }
        List<VertexInfo> gdbInvalidDevices =
                eventElem.getVertexes().stream().filter(elem -> {
                    boolean isDevice = elem.getLabel().equalsIgnoreCase("device");
                    if (!isDevice) {
                        //非device===>有效数据
                        return false;
                    }
                    return isNotValidGDBDevice(elem.getId());
                }).collect(Collectors.toList());
        //过滤不合适的device节点
        if (CollectionUtils.isEmpty(gdbInvalidDevices)) {
            return eventElem;
        }

        List<String> allInvalidNodes = gdbInvalidDevices.stream().map(elem -> elem.getId()).collect(Collectors.toList());
        //包含无效节点的无效边
        List<EdgeInfo> gdbInvalidEdges = eventElem.getEdges().stream().filter(elem -> {
            String label = elem.getLabel();
            String deviceId = "";
            if (label.equalsIgnoreCase("hasDevice")) {
                deviceId = elem.getToVertex();
            } else if (label.equalsIgnoreCase("relateToIp")) {
                deviceId = elem.getFromVertex();
            }
            if (StringUtils.isNotEmpty(deviceId) && allInvalidNodes.contains(deviceId)) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        eventElem.getVertexes().removeAll(gdbInvalidDevices);

        eventElem.getEdges().removeAll(gdbInvalidEdges);

        return eventElem;
    }


    private static boolean isNotValidGDBDevice(String deviceId) {
        if (deviceId.length() != 62 && deviceId.length() != 63) {
            return true;
        }
        return false;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        RuntimeContext ctx = getRuntimeContext();
        outputFormat.setRuntimeContext(ctx);
        outputFormat.open(ctx.getIndexOfThisSubtask(), ctx.getNumberOfParallelSubtasks());
    }

    @Override
    public void close() throws Exception {
        outputFormat.close();
    }

    public static void main(String[] args) {
        EventGraphElement eventElem = new EventGraphElement();
        VertexInfo v1 = new VertexInfo("20210618211312c826eb38c0235b663b0ceab9fc17d28f019f5805193e63d1","device",null);
        VertexInfo v2 = new VertexInfo("222380728523514832","user",null);
        VertexInfo v3 = new VertexInfo("************","ip",null);

        EdgeInfo e1 = new EdgeInfo("222380728523514832_20210618211312c826eb38c0235b663b0ceab9fc17d28f019f5805193e63d1","hasDevice",null,"222380728523514832","20210618211312c826eb38c0235b663b0ceab9fc17d28f019f5805193e63d1");
        EdgeInfo e2 = new EdgeInfo("222380728523514832_************","hasIp",null,"222380728523514832","************");
        EdgeInfo e3 = new EdgeInfo("20210618211312c826eb38c0235b663b0ceab9fc17d28f019f5805193e63d1_************","relateToIp",null,"20210618211312c826eb38c0235b663b0ceab9fc17d28f019f5805193e63d1","************");

        eventElem.setEdges(new ArrayList<>());
        eventElem.setVertexes(new ArrayList<>());
        eventElem.getVertexes().add(v1);
        eventElem.getVertexes().add(v2);
        eventElem.getVertexes().add(v3);
        eventElem.getEdges().add(e1);
        eventElem.getEdges().add(e2);
        eventElem.getEdges().add(e3);


        if (CollectionUtils.isEmpty(eventElem.getVertexes()) || CollectionUtils.isEmpty(eventElem.getEdges())) {
            System.err.println("first...");
            return;
        }
        List<VertexInfo> gdbInvalidDevices =
                eventElem.getVertexes().stream().filter(elem -> {
                    boolean isDevice = elem.getLabel().equalsIgnoreCase("device");
                    if (!isDevice) {
                        //非device===>有效数据
                        return true;
                    }
                    return isNotValidGDBDevice(elem.getId());
                }).collect(Collectors.toList());
        //过滤不合适的device节点
        if (CollectionUtils.isEmpty(gdbInvalidDevices)) {
            System.err.println("second...");

        }

        List<String> allInvalidNodes = gdbInvalidDevices.stream().map(elem -> elem.getId()).collect(Collectors.toList());
        //包含无效节点的无效边
        List<EdgeInfo> gdbInvalidEdges = eventElem.getEdges().stream().filter(elem -> {
            String label = elem.getLabel();
            String deviceId = "";
            if (label.equalsIgnoreCase("hasDevice")) {
                deviceId = elem.getToVertex();
            } else if (label.equalsIgnoreCase("relateToIp")) {
                deviceId = elem.getFromVertex();
            }
            if (StringUtils.isNotEmpty(deviceId) && allInvalidNodes.contains(deviceId)) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        eventElem.getVertexes().removeAll(gdbInvalidDevices);

        eventElem.getEdges().removeAll(gdbInvalidEdges);

        System.err.println(eventElem);
    }
}
