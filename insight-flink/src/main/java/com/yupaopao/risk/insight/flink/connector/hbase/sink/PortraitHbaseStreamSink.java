package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.lindorm.client.core.utils.Bytes;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitBean;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitFirstTag;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.hadoop.hbase.client.Put;
import org.springframework.util.CollectionUtils;

import java.util.TreeSet;

import static com.yupaopao.risk.insight.common.constants.HBaseConstants.COLUMN_FAMILY;

@Slf4j
public class PortraitHbaseStreamSink extends RichSinkFunction<Tuple2<Long, PortraitBean>> implements CheckpointedFunction {

    final HBaseProperties hBProperties = HBaseProperties.getProperties(PropertyType.HBASE);

    HBaseBaseOutputFormat hBaseBaseOutputFormat = new HBaseBaseOutputFormat("portrait_tag_etl", hBProperties) {
        @Override
        public void writeRecord(Object record) {
            try {
                Tuple2<Long, PortraitBean> portraitBean = (Tuple2<Long, PortraitBean>) record;
                String hisRowKey = InsightDateUtils.getDateStr(portraitBean.f1.getTimestamp(),InsightDateUtils.DATE_FORMAT_yyyyMMdd)+"_"+portraitBean.f0;
                Put hisPut = new Put(Bytes.toBytes(hisRowKey));
                byte[] columnFamily = Bytes.toBytes(COLUMN_FAMILY);
                hisPut.addColumn(columnFamily, Bytes.toBytes("userId"), Bytes.toBytes(String.valueOf(portraitBean.f0)));
                hisPut.addColumn(columnFamily, Bytes.toBytes("date"), Bytes.toBytes(InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd)));
                TreeSet<PortraitFirstTag> riskTags = portraitBean.f1.getRiskTags();
                if (!CollectionUtils.isEmpty(riskTags)){
                    hisPut.addColumn(columnFamily, Bytes.toBytes("riskTags"), Bytes.toBytes(JSONObject.toJSONString(riskTags)));
                    this.getMutator().mutate(hisPut);
                    if (canFlush()) {
                        flush();
                    }
                }
            } catch (Exception e) {
                log.warn("write portrait record error: ,record: " + record, e);
            }
        }
    };
    private static final String NOT_ATTRIBUTE = "No_Attribute";
    HBaseBaseOutputFormat resultHbaseOutPut = new HBaseBaseOutputFormat("risk_portrait_result", hBProperties) {
        @Override
        public void writeRecord(Object record) {
            try {
                Tuple2<Long, PortraitBean> portraitBean = (Tuple2<Long, PortraitBean>) record;
                if (!NOT_ATTRIBUTE.equals(portraitBean.f1.getTab())) {
                    String rowKey = portraitBean.f0+"_current";
                    Put put = new Put(Bytes.toBytes(rowKey));
                    byte[] columnFamily = Bytes.toBytes(COLUMN_FAMILY);
                    put.addColumn(columnFamily, Bytes.toBytes("portrait"), Bytes.toBytes(JSONObject.toJSONString(portraitBean.f1)));

                    this.getMutator().mutate(put);
                    if (canFlush()) {
                        flush();
                    }
                }
            } catch (Exception e) {
                log.warn("write portrait record error: ,record: " + record, e);
            }
        }
    };

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        hBaseBaseOutputFormat.flush();
        resultHbaseOutPut.flush();
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {

    }

    @Override
    public void invoke(Tuple2<Long, PortraitBean> value, Context context) throws Exception {
        hBaseBaseOutputFormat.writeRecord(value);
        resultHbaseOutPut.writeRecord(value);
    }


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        RuntimeContext ctx = getRuntimeContext();
        hBaseBaseOutputFormat.setRuntimeContext(ctx);
        hBaseBaseOutputFormat.open(ctx.getIndexOfThisSubtask(), ctx.getNumberOfParallelSubtasks());
        resultHbaseOutPut.open(ctx.getIndexOfThisSubtask(), ctx.getNumberOfParallelSubtasks());
    }

    @Override
    public void close() throws Exception {
        hBaseBaseOutputFormat.close();
        resultHbaseOutPut.close();
    }

}
