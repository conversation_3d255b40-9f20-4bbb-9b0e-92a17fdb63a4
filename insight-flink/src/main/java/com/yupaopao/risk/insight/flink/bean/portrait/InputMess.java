package com.yupaopao.risk.insight.flink.bean.portrait;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@NoArgsConstructor
@AllArgsConstructor
@Data
public class InputMess implements Serializable {
    private static final long serialVersionUID = 7084366195569511324L;

    private String key;
    private Object value;
    private String type;

}
