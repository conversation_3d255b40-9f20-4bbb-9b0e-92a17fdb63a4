package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSONObject;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.bean.audit.MxMetric;
import com.yupaopao.risk.insight.flink.utils.FactorUtil;
import com.yupaopao.risk.insight.flink.utils.MxMetricCacheUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Slf4j
public class AuditMetricProcess extends ProcessFunction<String, JSONObject> {

    private static final long serialVersionUID = -7775272177362869097L;

    @Override public void processElement(String value, Context ctx, Collector<JSONObject> out) throws Exception {
        List<MxMetric> metrics = MxMetricCacheUtils.getMetrics();
        if (null == metrics || metrics.size() <= 0) {
            return;
        }
        JSONObject data = JSONObject.parseObject(value);

        metrics.forEach(metric -> {
            if (!data.getString("kafkaTopic").equals(metric.getTopic())) {
                return;
            }
            if (FactorUtil.checkCondition(metric, data)) {
                String aggValue = FactorUtil.getAggValue(metric, data);
                JSONObject result = new JSONObject();
                String groupKey = getGroupKey(data, metric.getGroupKey(), result);
                if ((StringUtils.isNotEmpty(aggValue) || "COUNT".equals(metric.getFunction())) && StringUtils.isNotEmpty(groupKey)) {
                    result.put("groupKey", groupKey);
                    result.put("data", aggValue);
                    addMetric(result, metric);
                    out.collect(result);
                }
            }
        });
    }

    private void addMetric(JSONObject result, MxMetric metric) {
        result.put("function", metric.getFunction());
        result.put("code", metric.getCode());
        result.put("name", metric.getName());
        result.put("topic", metric.getTopic());
    }

    private String getGroupKey(JSONObject data, String group, JSONObject result){
        List<String> groupValues = new LinkedList<>();
        for (String groupKey : group.split(",")) {
            String groupValue;

            if (groupKey.contains(".")) {
                Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                groupValue = dataMap.getOrDefault(groupKey, "").toString();
            } else {
                groupValue = data.getString(groupKey);
            }
            if (StringUtils.isBlank(groupValue)) {
                return "";
            }
            result.put("group_" + groupKey, groupValue);
            groupValues.add(groupValue);
        }
        return String.join("$", groupValues);
    }

}
