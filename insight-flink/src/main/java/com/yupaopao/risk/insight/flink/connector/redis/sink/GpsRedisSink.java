package com.yupaopao.risk.insight.flink.connector.redis.sink;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.constants.FactorConstants;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.apache.flink.util.Preconditions;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class GpsRedisSink extends RichSinkFunction<String> {

    private RedisProperties redisProperties;
    private Jedis jedis;
    private JedisPool jedisPool;

    private Map<String, String> cache = new HashMap<>(2048);
    private long lastFlushTimeStamp = 0;

    public GpsRedisSink(RedisProperties redisProperties) {
        Preconditions.checkNotNull(redisProperties, "Redis connection pool config should not be null");
        this.redisProperties = redisProperties;
    }

    @Override public void invoke(String input, Context context) throws Exception {
        JSONObject data = JSONObject.parseObject(input);
        cache.put(String.format(FlinkConstants.GPS_DATA_PREFIX, data.getString("uid")), input);
        if (System.currentTimeMillis() - lastFlushTimeStamp > 5000 || cache.size() > 1000) {
            lastFlushTimeStamp = System.currentTimeMillis();
            flush();
        }
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(redisProperties.getMaxIdle());
        genericObjectPoolConfig.setMaxTotal(redisProperties.getMaxActive());
        genericObjectPoolConfig.setMinIdle(redisProperties.getMinIdle());

        jedisPool = new JedisPool(genericObjectPoolConfig,
            redisProperties.getHost(),
            redisProperties.getPort(),
            FactorConstants.REDIS_DEFAULT_TIMEOUT,
            redisProperties.getPassword(),
            redisProperties.getDatabase());

        jedis = jedisPool.getResource();
    }

    @Override
    public void close() throws IOException {
        flush();
        this.jedisPool.close();
    }

    private void flush() {
        jedis = jedisPool.getResource();
        for (String key : cache.keySet()) {
            jedis.setex(key, 3 * 60 * 60, cache.get(key));
        }
        cache.clear();
        this.jedis.close();
    }
}
