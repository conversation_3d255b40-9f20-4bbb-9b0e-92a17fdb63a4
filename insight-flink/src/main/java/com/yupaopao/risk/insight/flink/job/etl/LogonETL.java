package com.yupaopao.risk.insight.flink.job.etl;

//import com.alicloud.openservices.tablestore.SyncClient;
//import com.alicloud.openservices.tablestore.model.TableOptions;
//import com.alicloud.openservices.tablestore.model.UpdateTableRequest;
//import com.alicloud.openservices.tablestore.model.UpdateTableResponse;
//import com.ctrip.framework.apollo.Config;
//import com.ctrip.framework.apollo.ConfigService;
//import com.yupaopao.risk.insight.flink.connector.ts.map.TsLogonFlatMap;
//import com.yupaopao.risk.insight.flink.connector.ts.sink.KafkaTsOutputFormat;
//import com.yupaopao.risk.insight.flink.connector.ts.sink.TsTunnelStreamSink;
//import com.yupaopao.risk.insight.flink.connector.ts.source.TsTunnelStreamSource;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsClientFactory;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsTunnelRecordReader;
//import com.yupaopao.risk.insight.flink.constants.TsConstants;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import com.yupaopao.risk.insight.flink.property.PropertiesFactory;
//import com.yupaopao.risk.insight.flink.property.connection.TsProperties;
//import com.yupaopao.risk.insight.flink.property.enums.PropertyType;
//import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

/***
 * risk_hit_log清洗到新表
 * datastream 方式
 */
@Deprecated
public class LogonETL {

//    public static void main(String[] args) throws Exception {
//
//        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
//
//        TsProperties tsProperties = (TsProperties) PropertiesFactory.loadProperty(PropertyType.TABLE_STORE);
//        TsTunnelRecordReader recordReader = new TsTunnelRecordReader(tsProperties, "risk_hit_log", "logonDetailTunnel");
//
//        // define tablestore info
//        TsTableInfo tableInfo = new TsTableInfo().withTsProperties(tsProperties)
//            .withTableName("risk_logon_detail")
//            .withPrimaryKeyType(TsConstants.RISK_HIT_LOG_DATE_PARTITION + "#STRING,"
//                + TsConstants.RISK_HIT_LOG_TRACE_ID + "#STRING,"
//                + TsConstants.RISK_ANALYSIS_RESULT_PK_UUID + "#STRING");
//
//        KafkaTsOutputFormat outputFormat = new KafkaTsOutputFormat(tableInfo);
//        updateTtl("risk_logon_detail", new TsClientFactory(tsProperties).getClient());
//
//        env.addSource(new TsTunnelStreamSource(recordReader))
//            .flatMap(new TsLogonFlatMap())
//            .addSink(new TsTunnelStreamSink(outputFormat))
//            .name("write to table store")
//            .setParallelism(1)
//        ;
//
//        env.execute("logon ETL (tableStore ---> tableStore)");
//    }
//
//    /**
//     * 修改ts表数据ttl 时间
//     */
//    public static void updateTtl(String tableName, SyncClient syncClient) {
//        Config config = ConfigService.getAppConfig();
//        String confKey = tableName + ".ttl";
//        Integer tsTtl = config.getIntProperty(confKey, 60);
//        config.addChangeListener(configChangeEvent -> {
//            if (configChangeEvent.isChanged(confKey)) {
//                Config appConfig = ConfigService.getAppConfig();
//                updateTtl(appConfig.getIntProperty(confKey, 60), tableName, syncClient);
//            }
//        });
//        updateTtl(tsTtl, tableName, syncClient);
//
//    }
//
//    private static void updateTtl(Integer tsTtl, String tableName, SyncClient syncClient) {
//        TableOptions tableOptions = new TableOptions(tsTtl * 24 * 60 * 60);
//        UpdateTableRequest request = new UpdateTableRequest(tableName);
//        request.setTableOptionsForUpdate(tableOptions);
//        UpdateTableResponse response = syncClient.updateTable(request);
////        log.info("更新ts表过期时间: {}", response.getRequestId());
//    }
}
