/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.yupaopao.risk.insight.flink.job.etl.common.connect.clickhouse.writer;

import com.dtstack.flinkx.enums.EWriteMode;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.clickhouse.core.ClickhouseDatabaseMeta;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.rdb.writer.JdbcDataSinkFactory;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.rdb.writer.JdbcOutputFormatBuilder;
import com.yupaopao.risk.insight.flink.job.etl.common.options.DataTransferConfig;

/**
 * Date: 2019/11/05
 * Company: www.dtstack.com
 *
 * <AUTHOR>
 */
public class ClickhouseSinkFactory extends JdbcDataSinkFactory {

    public ClickhouseSinkFactory(DataTransferConfig config) throws Exception {
        super(config);
        if(config.getJob().getSetting().getSpeed().getWriterChannel() != 1){
            throw new UnsupportedOperationException("clickhouse writer's channel setting must be 1");
        }
        if(!EWriteMode.INSERT.name().equalsIgnoreCase(mode)){
            throw new UnsupportedOperationException(mode + " mode is not supported");
        }
        setDatabaseInterface(new ClickhouseDatabaseMeta());
    }

    @Override
    protected JdbcOutputFormatBuilder getBuilder() {
        return new JdbcOutputFormatBuilder(new ClickhouseBatchOutputFormat());
    }
}
