//package com.yupaopao.risk.insight.flink.connector.ts.source;
//
//import com.yupaopao.risk.insight.flink.meta.FlinkMetaInfo;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.flink.api.common.typeinfo.TypeInformation;
//import org.apache.flink.api.java.DataSet;
//import org.apache.flink.api.java.ExecutionEnvironment;
//import org.apache.flink.table.api.TableSchema;
//import org.apache.flink.table.sources.BatchTableSource;
//import org.apache.flink.types.Row;
//
//import java.io.Serializable;
//
//
///***
// * 定义tablesource涉及到flink中的数据类型，以及 数据源的链接信息
// */
//
//@Slf4j
//public class TsBatchTableSource implements BatchTableSource<Row>, Serializable {
//    //flink 中类列的类型
//    private FlinkMetaInfo flinkMetaInfo;
//    //tablestore连接相关的信息
//    private TsTableInfo sourceTable;
//
//    public TsBatchTableSource(TsTableInfo tsTableInfo) {
//        sourceTable = tsTableInfo;
//        flinkMetaInfo = sourceTable.getFlinkMetaInfo();
//    }
//
//
//    @Override
//    public DataSet<Row> getDataSet(ExecutionEnvironment executionEnvironment) {
//        TsInputFormat.TsInputFormatBuilder builder = TsInputFormat.TsInputFormatBuilder.builder();
//        TsInputFormat format = builder.withTsTableInfo(sourceTable)
//                .withFlinkMetaInfo(flinkMetaInfo)
//                .build();
//        return executionEnvironment.createInput(format, getReturnType()).name("read ts data");
//    }
//
//    @Override
//    public TableSchema getTableSchema() {
//        return flinkMetaInfo.toTableSchema();
//    }
//
//    @Override
//    public TypeInformation<Row> getReturnType() {
//        return flinkMetaInfo.toTableSchema().toRowType();
//    }
//
//
//}
