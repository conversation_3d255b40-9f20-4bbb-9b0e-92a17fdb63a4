//package com.yupaopao.risk.insight.flink.connector.ts.source;
//
//import com.alicloud.openservices.tablestore.model.PrimaryKey;
//import com.alicloud.openservices.tablestore.model.PrimaryKeyBuilder;
//import com.alicloud.openservices.tablestore.model.PrimaryKeyValue;
//import com.alicloud.openservices.tablestore.model.Row;
//import com.google.gson.Gson;
//import com.yupaopao.risk.insight.flink.connector.ts.TsFormat;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsClientFactory;
//import com.yupaopao.risk.insight.flink.constants.TsConstants;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import com.yupaopao.risk.insight.flink.utils.InsightDateUtils;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.time.DateUtils;
//import org.apache.flink.api.common.io.DefaultInputSplitAssigner;
//import org.apache.flink.api.common.io.RichInputFormat;
//import org.apache.flink.api.common.io.statistics.BaseStatistics;
//import org.apache.flink.configuration.Configuration;
//import org.apache.flink.core.io.InputSplitAssigner;
//
//import java.io.IOException;
//import java.io.Serializable;
//import java.util.*;
//import java.util.concurrent.LinkedBlockingQueue;
//import java.util.concurrent.TimeUnit;
//
///****
// *
// * @Author: zengxiangcai
// * @Date: 2019-12-28 15:25
// *
// ***/
//
//@Slf4j
//public class TsCleanerInputFormat extends RichInputFormat<Row, TsInputSplit> implements TsFormat,
//        Serializable {
//    private TsTableInfo tsTableInfo;
//    //client provider 提供访问table store 的client
//    private TsClientFactory tsClientFactory;
//
//    /***
//     * 每次getRange的结果放到queue中
//     */
//    private LinkedBlockingQueue<LinkedList<Row>> batchFetchedList = new LinkedBlockingQueue<>(1024*1024);
//
//    private List<TsInputSplitReadThread> threadList = new ArrayList<>();
//
//    /**
//     * 从queue中提取出来的当前待处理 rowList
//     */
//    private LinkedList<Row> currentProcessList = new LinkedList<>();
//
//
//    private transient static Gson gson = new Gson();
//
//    public TsCleanerInputFormat(TsTableInfo tsTableInfo) {
//        this.tsTableInfo = tsTableInfo;
//        createTsClient();
//    }
//
//    @Override
//    public TsClientFactory createTsClient() {
//        if (tsClientFactory == null) {
//            tsClientFactory = new TsClientFactory(tsTableInfo.getTsProperties());
//        }
//        return tsClientFactory;
//    }
//
//    @Override
//    public void closeTsClient() {
//        try {
//            if (tsClientFactory != null) {
//                tsClientFactory.close();
//            }
//        } catch (Exception e) {
//            log.error("close ts Client error: ", e);
//        }
//    }
//
//    @Override
//    public void configure(Configuration parameters) {
//
//    }
//
//    @Override
//    public BaseStatistics getStatistics(BaseStatistics cachedStatistics) throws IOException {
//        return null;
//    }
//
//    @Override
//    public TsInputSplit[] createInputSplits(int minNumSplits) throws IOException {
//        /***清洗数据的split
//         * 将20191209_0000~20121228_2013的数据切分
//         *
//         */
//        //先跑0000区间
//        Date startDay = InsightDateUtils.getDateFromString("20191209", InsightDateUtils.DATE_FORMAT_yyyyMMdd);
//        Date endDay = InsightDateUtils.getDateFromString("20191229", InsightDateUtils.DATE_FORMAT_yyyyMMdd);
//        List<TsInputSplit> resultList = new ArrayList<>();
//
//        int dayIndex = 0;
//        while (startDay.before(endDay)) {
//            addSplit(startDay, resultList);
//            startDay = DateUtils.addDays(startDay, 1);
//        }
//        return resultList.toArray(new TsInputSplit[0]);
//    }
//
//    private void addSplit(Date day, List<TsInputSplit> resultList) {
//        String formatDay = InsightDateUtils.getDateStr(day, InsightDateUtils.DATE_FORMAT_yyyyMMdd);
//        Map<Integer, List<TsInputSplit.SplitBucket>> map = new HashMap();
//        for (int i = 0; i < 1024; i++) {
//            String padBucket = StringUtils.leftPad(String.valueOf(i), 4, '0');
//            String partitionKey = formatDay + "_" + padBucket;
//            PrimaryKeyBuilder builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//            builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_DATE_PARTITION,
//                    PrimaryKeyValue.fromString(partitionKey));
//            builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_TRACE_ID,
//                    PrimaryKeyValue.INF_MIN);
//            PrimaryKey begin = builder.build();
//
//            builder = PrimaryKeyBuilder.createPrimaryKeyBuilder();
//            builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_DATE_PARTITION,
//                    PrimaryKeyValue.fromString(partitionKey));
//            builder.addPrimaryKeyColumn(TsConstants.RISK_HIT_LOG_TRACE_ID,
//                    PrimaryKeyValue.INF_MAX);
//            PrimaryKey end = builder.build();
//            TsInputSplit.SplitBucket pair = new TsInputSplit.SplitBucket(begin, end);
//
//            int mode = i % 516; //每个split处理2个分块[都是经过ts进行读写，瓶颈都在ts]
//            if (map.get(mode) == null) {
//                map.put(mode, new ArrayList<>());
//            }
//            map.get(mode).add(pair);
//        }
//        //
//        for (List<TsInputSplit.SplitBucket> value : map.values()) {
//            TsInputSplit split = new TsInputSplit(resultList.size(), gson.toJson(value));
//            resultList.add(split);
//        }
//
//
//    }
//
//    @Override
//    public InputSplitAssigner getInputSplitAssigner(TsInputSplit[] inputSplits) {
//        return new DefaultInputSplitAssigner(inputSplits);
//    }
//
//    @Override
//    public void open(TsInputSplit split) throws IOException {
//        try {
//            createTsClient();
//            //按split的bucket数量多线程读
//            List<TsInputSplit.SplitBucket> bucketList = split.fetchBucketList();
//            if (CollectionUtils.isEmpty(bucketList)) {
//                return;
//            }
//            int bucketIndex = 0;
//            for (TsInputSplit.SplitBucket bucket : bucketList) {
//                TsInputSplitReadThread readThread = new TsInputSplitReadThread(bucket,
//                        this.tsClientFactory,
//                        batchFetchedList,
//                        tsTableInfo.getTableName(),
//                        tsTableInfo.getTableId(),
//                        tsTableInfo.getTableType(),
//                        tsTableInfo.getColumnNames(),
//                        "cleaner-ts-split-" + split.getSplitNumber() + "-bucket-" + bucketIndex++);
//                threadList.add(readThread);
//                readThread.start();
//                log.info("cleaner read split: {}, thread: {}", bucket, readThread.getName());
//            }
//        } catch (Exception e) {
//            log.error("cleaner open input format error", e);
//            throw e;
//        }
//    }
//
//    @Override
//    public boolean reachedEnd() throws IOException {
//        threadList.removeIf(t -> t.exit);
//        boolean reachEnd =
//                CollectionUtils.isEmpty(threadList) && CollectionUtils.isEmpty(batchFetchedList)
//                        && CollectionUtils.isEmpty(currentProcessList);
//
//        return reachEnd;
//    }
//
//    @Override
//    public Row nextRecord(Row reuse) throws IOException {
//        try {
//            while (CollectionUtils.isEmpty(currentProcessList)) {
//                try {
//                    currentProcessList = batchFetchedList.poll(4, TimeUnit.MILLISECONDS);
//                } catch (Exception e) {
//                    log.error("pull data from queue error: ", e);
//                    return null;
//                }
//                if (CollectionUtils.isEmpty(currentProcessList)) {
//                    return null;
//                }
//                if (CollectionUtils.isEmpty(threadList)) {
//                    //可能已经数据处理完，防止无限循环，判断空就退出判断任务是否结束
//                    return null;
//                } else {
//                    boolean hasFinishedThread = threadList.stream().filter(elem -> elem.exit).findFirst().isPresent();
//                    if (hasFinishedThread) {
//                        return null;
//                    }
//                }
//            }
//            if (CollectionUtils.isEmpty(currentProcessList)) {
//                return null;
//            }
//            return currentProcessList.poll();
//        } catch (Exception e) {
//            log.info("read next row error ", e);
//            return null;
//        }
//    }
//
//    @Override
//    public void close() throws IOException {
//        for (TsInputSplitReadThread t : threadList) {
//            t.interrupt();
//        }
//        for (TsInputSplitReadThread t : threadList) {
//            try {
//                t.join();
//            } catch (InterruptedException e) {
//                log.info("close error", e);
//            }
//        }
//    }
//}
