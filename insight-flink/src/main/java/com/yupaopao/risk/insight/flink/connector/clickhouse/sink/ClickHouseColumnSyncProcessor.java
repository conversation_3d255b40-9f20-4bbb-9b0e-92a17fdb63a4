package com.yupaopao.risk.insight.flink.connector.clickhouse.sink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.CommonClickHouseDataType;
import com.yupaopao.risk.insight.flink.connector.scheduler.IntervalFlushSchedulerTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc ClickHouseColumnSync
 * @date 2020/5/14
 */
@Slf4j
public class ClickHouseColumnSyncProcessor implements Runnable {

    private ClickHouseConnection connection;
    private String tableName;
    private Map<String, String> kafkaColumnMap;
    private ScheduledThreadPoolExecutor scheduler;
    private Map<String, String> ckColumn;
    private Map<String, Integer> errorCount;

    public ClickHouseColumnSyncProcessor(ClickHouseProperties properties, String tableName) {
        connection = ClickHouseUtil.createConnection(properties);
        this.kafkaColumnMap = new HashMap<>(128);
        this.tableName = tableName;
        this.scheduler = IntervalFlushSchedulerTools.createScheduler("sync-clickhouse-" + tableName, properties.getSyncIntervalInMillis(), this);
        this.ckColumn = ClickHouseUtil.fetchColumns(tableName, connection);
        errorCount = new ConcurrentHashMap<>();
    }

    @Override
    public void run() {
        String syncColumnSwitch = ApolloProperties.getConfigStr("global.ck.syncColumn.switch");
        if (!"true".equals(syncColumnSwitch)) {
            return;
        }
        Map<String, String> notExitColumns = fetchNotExitColumns();
        if (!notExitColumns.isEmpty()) {
            log.info("准备为 {} 补充 {} 个字段: {}", tableName, notExitColumns.size(), JSON.toJSONString(notExitColumns));
            ColumnSyncLocker.isSyncColumn = true;
            if (ColumnSyncLocker.isSyncColumn) {
                try {
                    ClickHouseUtil.flushDistributedTable(tableName, connection);
                    ClickHouseUtil.addColumn(tableName + "_local", notExitColumns, connection);
                    ClickHouseUtil.addColumn(tableName, notExitColumns, connection);
                } finally {
                    ColumnSyncLocker.isSyncColumn = false;
                }
            }
        } else {
            log.info("暂无需补充字段, kafka字段个数: {}", kafkaColumnMap.size());
        }
    }

    private Map<String, String> fetchNotExitColumns() {
        Map<String, String> curCkMap = ClickHouseUtil.fetchColumns(tableName, connection);
        if (curCkMap != null && !curCkMap.isEmpty() && curCkMap.size() > ckColumn.size()) {
            this.ckColumn = curCkMap;
        }
        Map<String, String> notExitColumns = new HashMap<>();
        kafkaColumnMap.forEach((key, value) -> {
            if (ckColumn != null && !ckColumn.isEmpty() && !ckColumn.containsKey(key)) {
                notExitColumns.put(key, value);
            }
        });
        return notExitColumns;
    }

    public static void main(String[] args) {
        String target = "{\"acc\":{\"suc\":\"1\",\"enable\":\"0\",\"service\":[]},\"simCountryISO\":\"ph\",\"freeSpace\":105582915584,\"ainfo\":{\"sb_md5\":\"\",\"resett\":-1,\"vf_md5\":\"D41D8CD98F00B204E9800998ECF8427E\",\"riskfile\":[{\"p\":1,\"e\":1,\"h\":[\"/data/app/~~IO7HVSPhYohieP6pHP3vxA==/com.yitantech.gaigai-qtT64DeiD_B2puuDg4Sctw==/lib/arm64/libEncryptorP.so\",\"/data/app/~~IO7HVSPhYohieP6pHP3vxA==/com.yitantech.gaigai-qtT64DeiD_B2puuDg4Sctw==/lib/arm64/libMapiSign.so\",\"/data/app/~~IO7HVSPhYohieP6pHP3vxA==/com.yitantech.gaigai-qtT64DeiD_B2puuDg4Sctw==/lib/arm64/libliteavsdk.so\",\"/data/app/~~IO7HVSPhYohieP6pHP3vxA==/com.yitantech.gaigai-qtT64DeiD_B2puuDg4Sctw==/lib/arm64/liblogan.so\",\"/data/app/~~IO7HVSPhYohieP6pHP3vxA==/com.yitantech.gaigai-qtT64DeiD_B2puuDg4Sctw==/lib/arm64/libsmsdk.so\",\"/data/app/~~IO7HVSPhYohieP6pHP3vxA==/com.yitantech.gaigai-qtT64DeiD_B2puuDg4Sctw==/lib/arm64/libtxffmpeg.so\",\"/data/app/~~IO7HVSPhYohieP6pHP3vxA==/com.yitantech.gaigai-qtT64DeiD_B2puuDg4Sctw==/lib/arm64/libtxsoundtouch.so\",\"/data/app/~~IO7HVSPhYohieP6pHP3vxA==/com.yitantech.gaigai-qtT64DeiD_B2puuDg4Sctw==/lib/arm64/libumeng-spy.so\",\"/data/app/~~IO7HVSPhYohieP6pHP3vxA==/com.yitantech.gaigai-qtT64DeiD_B2puuDg4Sctw==/lib/arm64/libxcrash.so\"],\"key\":\"maps2\"}],\"memreal_md5\":\"\",\"vl_md5\":\"B6CC65146BAB59CBBC38438036248A5D\",\"font_md52\":\"72000F11C3E9D992F5C0D6A97CBFBCAA\",\"gothk\":\"false\",\"probeOpenCamera\":\"8|0xf00b40d1|0x1f0240b9|0xff8302d1|0xe827006d|0xea2f016d|0xec37026d|0xee3f036d|0xf35304a9|\",\"sf_md5\":\"7A786F51708F46013556423B7A7C47DC\",\"root\":\"false\",\"clock_gettime\":\"8|0x7008321ce0|0xfd7bbea9|0xf30b00f9|0xfd030091|0x480400d0|0x080540f9|\",\"mem_md5\":\"9cfac324085c0730e789dbb3042776bb\",\"font_md5\":\"84AD87E7B495BE03BDFE9C16AB84E241\",\"ap_mac\":\"\",\"bootId\":\"fbff91b2-3631-467c-b9d7-d5a7137ee72d\",\"gettimeofday\":\"8|0x7008321d80|0xfd7bbea9|0xf30b00f9|0xfd030091|0x480400d0|0x081540f9|\",\"tmpr_fw\":\"none\",\"ds_md5\":\"\",\"probePreviewCB\":\"8|0xf00b40d1|0x1f0240b9|0xff8302d1|0xe827006d|0xea2f016d|0xec37026d|0xee3f036d|0xf35304a9|\",\"abi\":\"arm64-v8a\",\"sys_props\":{\"ro.product.manufacturer\":\"OPPO\",\"ro.build.version.release\":\"13\",\"gsm.sim.state\":\"LOADED,LOADED\",\"gsm.operator.numeric\":\"51503,51503\",\"ro.boot.vbmeta.digest\":\"5fd3b1d344cda79850e772e7e1cf10e992cc762005007f225eaa40b7e71ab3c7\",\"init.svc.adbd\":\"stopped\",\"gsm.operator.iso-country\":\"ph,ph\",\"ro.product.name\":\"PEQM00\",\"ro.product.brand\":\"OPPO\",\"ro.product.vendor.model\":\"PEQM00\",\"gsm.operator.alpha\":\"SMART,SMART\",\"ro.bootloader\":\"unknown\",\"ro.build.characteristics\":\"default\",\"gsm.sim.operator.alpha\":\"中国移动,Smart Communications\",\"ro.product.model\":\"PEQM00\",\"ro.build.display.id\":\"PEQM00_13.1.0.191(CN01)\",\"ro.boot.hardware\":\"mt6877\",\"gsm.sim.operator.numeric\":\"46007,51503\",\"ro.debuggable\":\"0\",\"ro.build.fingerprint\":\"OPPO/PEQM00/OP4ED5:13/TP1A.220905.001/R.17f0cdb_1:user/release-keys\",\"ro.build.version.sdk\":\"33\",\"ro.product.board\":\"k6877v1_64_k419\",\"gsm.operator.isroaming\":\"true,false\",\"ro.build.version.incremental\":\"R.17f0cdb_1\",\"ro.secure\":\"1\"},\"randomKey\":\"x1Mi07y4Hdql25b95URxDYu6yZ7B4Wnl\",\"emuShareContent\":{\"xy\":\"x1Mi07y4Hdql25b95URxDYu6yZ7B4Wnl\"},\"is_vpn\":\"false\",\"ds_md52\":\"\",\"arpInfo\":[],\"cid\":\"\"},\"appver\":\"9.23.0\",\"debugger\":0,\"apputm\":\"bx-oppo\",\"parentDirReadable\":0,\"screen\":\"1080,2153,480\",\"targetSdk\":30,\"appUsedCount\":463,\"deviceId\":\"20230408231210365d46cae2864bc955e4316cff7a851b01b386d4b73ef0bb\",\"operator\":\"51503\",\"appname\":\"com.yitantech.gaigai\",\"appId\":\"default\",\"band\":\"M_V3_P10,M_V3_P10\",\"screenOn\":1,\"cpuCount\":8,\"launcherInfo\":{\"ver\":\"13.1.30\",\"label\":\"系统桌面\",\"pkg\":\"com.android.launcher\"},\"appTime\":{\"fit\":1715501236861,\"lut\":1719618242460},\"adbEnabled\":0,\"props\":{\"ro.boot.hardware\":\"mt6877\",\"gsm.sim.state\":\"LOADED,LOADED\",\"gsm.operator.alpha\":\"SMART,SMART\",\"sys.usb.state\":\"mtp\",\"ro.debuggable\":\"0\"},\"input\":[\"InputMethodInfo{com.sohu.inputmethod.sogouoem/.SogouIME, settings: com.sohu.inputmethod.sogou.SogouIMESettingsLauncher}\",\"InputMethodInfo{com.baidu.input_oppo/.ImeService, settings: com.baidu.input.ImeMainConfigActivity}\"],\"trafficBytes\":{\"tt\":2228688824,\"mr\":716097163,\"mt\":57074918,\"tr\":25041237811},\"signdn\":\"CN=gaigai, OU=ypp, O=ypp, L=Shanghai, ST=Shanghai, C=86\",\"files\":\"/data/user/0/com.yitantech.gaigai/files\",\"fakeLocExcept\":false,\"option\":\"1101110\",\"mockLoc\":0,\"smid\":\"20230408231210365d46cae2864bc955e4316cff7a851b01b386d4b73ef0bb\",\"bootCount\":74,\"smseq\":\"463\",\"notCollect\":[\"sensorsData\",\"imei\",\"sensor\",\"riskapp\",\"imsi\",\"sn\",\"mac\",\"apps\"],\"uiMode\":1,\"bssid\":\"020000000000\",\"battery\":{\"temp\":387,\"vol\":4,\"level\":92,\"scale\":100,\"status\":4},\"sys\":{\"cpu_abi\":\"arm64-v8a\",\"fingerprint\":\"OPPO/PEQM00/OP4ED5:13/TP1A.220905.001/R.17f0cdb_1:user/release-keys\",\"radioVersion\":\"M_V3_P10,M_V3_P10\",\"model\":\"PEQM00\",\"cpu_abi2\":\"\",\"brand\":\"OPPO\",\"board\":\"k6877v1_64_k419\",\"manufacturer\":\"OPPO\"},\"ssid\":\"<unknown ssid>\",\"network\":\"wifi\",\"sid\":\"1720422710348-65322\",\"virtualuid\":\"u0_a1\",\"rtype\":\"all\",\"sdkBuild\":\"build1\",\"adid\":\"94c1362f3207abbb\",\"mem\":12132048896,\"availableSpace\":105103306752,\"taiJiBasePackage\":0,\"sdkver\":\"2.17.0\",\"outerid\":\"20230408231210365d46cae2864bc955e4316cff7a851b01b386d4b73ef0bb\",\"audio\":{\"call\":7,\"music\":9,\"system\":6,\"ring\":6,\"alarm\":6},\"boot\":1719606938864,\"signhash\":-2023242913,\"oaid\":\"AD66DA96FE394D1A9D1FAA592DD74260095f5aac1eeed00770de46157be49103\",\"networkCountryIso\":\"ph\",\"spSmId294\":\"20230408231210365d46cae2864bc955e4316cff7a851b01b386d4b73ef0bb\",\"cost\":1067,\"dataDirReadable\":0,\"os\":\"android\",\"display\":\"1080,2153\",\"permission\":\"0101100\",\"totalSpace\":************,\"osver\":\"13\",\"cpuHw\":\"MT6877V/ZA\",\"hasMagisk\":0,\"brightness\":7,\"aenc\":\"4\",\"t\":1720422710387,\"wifiip\":\"************\",\"debuggable\":0,\"organization\":\"sdj8X1y4Unvsx2DYN3BU\",\"runningProcessCount\":197,\"collectCost\":1049,\"cpuFreq\":2400000,\"tn\":\"4d2778f383b6b0bee016bbe97dc054e3\",\"axposed\":\"false\",\"binderhook\":\"locateServiceName:android.os.BinderProxy|phoneServiceName:android.os.BinderProxy\"}";
        JSONObject valueJson = JSON.parseObject(target);
        for (String key : valueJson.keySet()) {
            String clickHouseType = CommonClickHouseDataType.getClickHouseType(valueJson.get(key));
            if (StringUtils.isBlank(clickHouseType) || clickHouseType.contains("\\")) {
                continue;
            }
            System.out.println(key);
        }
//        processor.cacheColumn(target);
    }

    public String cacheColumn(String value) {
        JSONObject valueJson = JSONObject.parseObject(value);
        for (String key : valueJson.keySet()) {
            String clickHouseType = CommonClickHouseDataType.getClickHouseType(valueJson.get(key));
            if (StringUtils.isBlank(clickHouseType) || isSpecialKey(clickHouseType)) {
                continue;
            }
            kafkaColumnMap.put(key, clickHouseType);
        }
        //不配置字段校验相对比较耗时，单独放到 CkColumnRepairMapFunction  需要的任务可以自行添加
        return value;
    }

    private void logErrorColumn(String keyMap, String value, String returnJson) {
        String columnKey = tableName + "#" + keyMap;
        if (errorCount.containsKey(columnKey)) {
            errorCount.put(columnKey, errorCount.get(columnKey) + 1);
        } else {
            errorCount.put(columnKey, 1);
        }
        if (errorCount.get(columnKey) <= 100) {
            log.error("column type error: {} , originalValue: {}, returnValue : {}", columnKey, value, returnJson);
        }
    }

    private static boolean isSameType(String ckType, Object value) {
        if (value instanceof Number) {
            ckType = ckType.toLowerCase();
            return ckType.contains("int") || ckType.contains("float") || ckType.contains("decimal");
        } else if (value instanceof String) {
            // string 类型数据,如果ck是number会不一致
            return !ckType.contains("int") && !ckType.contains("float") && !ckType.contains("decimal");
        } else {
            return value instanceof String;
        }
    }

    private Object valueParse(String cktype, Object value) {
        String lowerCkType = cktype.toLowerCase();
        //ck number 类型尝试 由string 转
        if (value instanceof String) {
            if (lowerCkType.contains("int")) {
                return Long.valueOf((String) value);
            } else if (lowerCkType.contains("float")) {
                return Double.valueOf((String) value);
            }
        }

        //string 类型
        switch (lowerCkType) {
            case "string":
            case "datetime":
            case "date":
                // 暂时只对字符串类型做强转处理
                return value.toString();
            default:
                return value;
        }

    }

    private boolean isSpecialKey(String key) {
        return key.contains("\\");
    }

    public void close() {
        log.info("close clickhouse sync...");
        IntervalFlushSchedulerTools.closeScheduler(scheduler);
    }

    private boolean isDataTypeMatch(String ckType, Object value) {
        String lowercaseType = ckType.toLowerCase();
        if (lowercaseType.contains("date") && value instanceof String) {
            return true;
        }
        if (lowercaseType.contains("string") && value instanceof String) {
            return true;
        }
        if (lowercaseType.contains("int") && value instanceof Number && !value.toString().contains(".")) {
            return true;
        }
        if (lowercaseType.contains("float") && value instanceof Number) {
            return true;
        }
        return false;
    }

    //确保数据类型不match的时候才调用此类数据
    private Object fixValue(String ckType, Object value) {
        try {
            //ck中几种特殊数据可以fix
            String lowercaseType = ckType.toLowerCase();
            if (lowercaseType.contains("string")) {
                return value.toString();
            }
            if (lowercaseType.contains("int") && value instanceof Number) {
                if (value.toString().contains(".")) {
                    //float
                    return Double.valueOf(value.toString()).longValue();
                } else {
                    //其他不匹配
                    return null;
                }
            }
            if (value instanceof String) {
                if (StringUtils.isEmpty(value.toString())) {
                    return null;
                }
                String reg = "^(-?\\d+)(\\.\\d+)?$";
                boolean isNumber = value.toString().matches(reg);
                if (lowercaseType.contains("int") && isNumber) {
                    return Long.valueOf(value.toString());
                }
                if (lowercaseType.contains("float32") && isNumber) {
                    return Float.valueOf(value.toString());
                } else if (lowercaseType.contains("float64") && isNumber) {
                    return Double.valueOf(value.toString());
                }
            }

            if (lowercaseType.contains("datetime") && value instanceof Number && !value.toString().contains(".")) {
                //非浮点型的整数可以insert
                return Long.valueOf(value.toString()).toString();
            }

            //历史字段decimal(38, 4)处理
            if (lowercaseType.contains("decimal(38, 4)") && value instanceof BigDecimal) {
                return ((BigDecimal) value).setScale(4, RoundingMode.HALF_UP);
            }

            return null;
        } catch (Exception e) {
            log.error("fixed error: ckType: " + ckType + " value: " + value, e);
            return null;
        }
    }


}
