package com.yupaopao.risk.insight.flink.job.etl.common.connect.kafkanew.core.decoder;

import com.dtstack.flinkx.decoder.IDecode;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/****
 * zengxiangcai
 * 2022/9/19 17:34
 ***/
public class TextDecoder implements IDecode, Serializable {
    @Override
    public Map<String, Object> decode(String s) {
        Map<String, Object> data = new HashMap<>(1);
        data.put("message", s);
        return data;
    }
}
