//package com.yupaopao.risk.insight.flink.connector.ts.source;
//
//import com.alibaba.fastjson.JSON;
//import com.alicloud.openservices.tablestore.model.Row;
//import com.yupaopao.risk.insight.flink.connector.ts.TsFormat;
//import com.yupaopao.risk.insight.flink.connector.ts.source.TsInputSplit.SplitBucket;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsClientFactory;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TypeConvertUtils;
//import com.yupaopao.risk.insight.flink.meta.FlinkMetaInfo;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.flink.api.common.io.DefaultInputSplitAssigner;
//import org.apache.flink.api.common.io.RichInputFormat;
//import org.apache.flink.api.common.io.statistics.BaseStatistics;
//import org.apache.flink.configuration.Configuration;
//import org.apache.flink.core.io.InputSplitAssigner;
//
//import java.io.IOException;
//import java.io.Serializable;
//import java.util.ArrayList;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.concurrent.LinkedBlockingQueue;
//import java.util.concurrent.TimeUnit;
//
///***
// * inputFormat 必须能够序列化
// */
//@Slf4j
//public class TsInputFormat extends RichInputFormat<org.apache.flink.types.Row, TsInputSplit> implements TsFormat, Serializable {
//
//    private TsTableInfo tsTableInfo;
//    //client provider 提供访问table store 的client
//    private TsClientFactory tsClientFactory;
//
//    private FlinkMetaInfo flinkMetaInfo;
//
//
//    /***
//     * 每次getRange的结果放到queue中
//     */
//    private LinkedBlockingQueue<LinkedList<Row>> batchFetchedList = new LinkedBlockingQueue<>(1024*64);
//    /**
//     * 从queue中提取出来的当前待处理 rowList
//     */
//    private LinkedList<Row> currentProcessList = new LinkedList<>();
//
//    /**
//     * 读取该InputFormat负责的splits的线程列表,考虑到每次读取ts会有数据量的限制，调优的时候可以考虑并发读取ts
//     */
//    private List<TsInputSplitReadThread> threadList = new ArrayList<>();
//
//    private final static Long splitSizeUnitInBytes = 1024L * 1024L;
//
//    private final static Long unitsPerSplit = 400L;
//
//
//    @Override
//    public void configure(Configuration configuration) {
//
//    }
//
//
//    @Override
//    public BaseStatistics getStatistics(BaseStatistics baseStatistics) throws IOException {
//        return null;
//    }
//
//    public void closeInputFormat() throws IOException {
//        try {
//            closeTsClient();
//        } catch (Exception e) {
//            log.error("close tsClient error: ", e);
//        }
//    }
//
//    /***
//     * 整个数据源做分片
//     * @param minNumSplits
//     * @return
//     * @throws IOException
//     */
//    @Override
//    public TsInputSplit[] createInputSplits(int minNumSplits) throws IOException {
//        log.info(String.format("createInputSplit: minNumSplits [%d]", minNumSplits));
//        if (minNumSplits < 1) {
//            throw new IllegalArgumentException("Number of input splits has to be at least 1.");
//        }
//        TsInputSplit[] splits = new TsInputSplitBuilder(tsTableInfo, createTsClient()).buildSplits();
//        log.info("splits: {}", JSON.toJSONString(splits));
//        closeTsClient();
//        return splits;
//
//    }
//
//
//    public InputSplitAssigner getInputSplitAssigner(TsInputSplit[] inputSplits) {
//        return new DefaultInputSplitAssigner(inputSplits);
//    }
//
//    /**
//     * 开始读取某一个分片，分配资源
//     *
//     * @param tsInputSplit
//     * @throws IOException
//     */
//    @Override
//    public void open(TsInputSplit tsInputSplit) throws IOException {
//        try {
//            createTsClient();
//            //按split的bucket数量多线程读
//            List<SplitBucket> bucketList = tsInputSplit.fetchBucketList();
//            if (CollectionUtils.isEmpty(bucketList)) {
//                return;
//            }
//            int bucketIndex = 0;
//            for (SplitBucket bucket : bucketList) {
//                TsInputSplitReadThread readThread = new TsInputSplitReadThread(bucket,
//                        this.tsClientFactory,
//                        batchFetchedList,
//                        tsTableInfo.getTableName(),
//                        tsTableInfo.getTableId(),
//                        tsTableInfo.getTableType(),
//                        tsTableInfo.getColumnNames(),
//                        "ts-split-" + tsInputSplit.getSplitNumber() + "-bucket-" + bucketIndex++);
//                threadList.add(readThread);
//                readThread.start();
//                log.info("read split: {}, thread: {}", bucket, readThread.getName());
//            }
//        } catch (Exception e) {
//            log.error("open input format error", e);
//            throw e;
//        }
//    }
//
//    /***
//     * 判断某一个split中是否还有数据需要读取,
//     * 是否还有线程未消耗完,以及是否还有数据未读取完
//     * @return
//     * @throws IOException
//     */
//    @Override
//    public boolean reachedEnd() throws IOException {
//        threadList.removeIf(t -> t.exit);
//        boolean reachEnd =
//                CollectionUtils.isEmpty(threadList) && CollectionUtils.isEmpty(batchFetchedList)
//                        && CollectionUtils.isEmpty(currentProcessList);
//
//        return reachEnd;
//    }
//
//    /**
//     * 读取queue中的下一行数据
//     *
//     * @param reuse
//     * @return
//     * @throws IOException
//     */
//    @Override
//    public org.apache.flink.types.Row nextRecord(org.apache.flink.types.Row reuse) throws IOException {
//        try {
//            while (CollectionUtils.isEmpty(currentProcessList)) {
//                try {
//                    currentProcessList = batchFetchedList.poll(4, TimeUnit.MILLISECONDS);
//                } catch (Exception e) {
//                    log.error("pull data from queue error: ", e);
//                    return null;
//                }
//                if (CollectionUtils.isEmpty(currentProcessList)) {
//                    return null;
//                }
//                if (CollectionUtils.isEmpty(threadList)) {
//                    //可能已经数据处理完，防止无限循环，判断空就退出判断任务是否结束
//                    return null;
//                } else {
//                    boolean hasFinishedThread = threadList.stream().filter(elem -> elem.exit).findFirst().isPresent();
//                    if (hasFinishedThread) {
//                        return null;
//                    }
//                }
//            }
//            if (CollectionUtils.isEmpty(currentProcessList)) {
//                return null;
//            }
//            return TypeConvertUtils.extractNextRecord(currentProcessList.poll(), flinkMetaInfo);
//        } catch (Exception e) {
//            log.info("read next row error ", e);
//            return null;
//        }
//    }
//
//    @Override
//    public void close() throws IOException {
//        for (TsInputSplitReadThread t : threadList) {
//            t.interrupt();
//        }
//        for (TsInputSplitReadThread t : threadList) {
//            try {
//                t.join();
//            } catch (InterruptedException e) {
//                log.info("close error", e);
//            }
//        }
//    }
//
//
//    @Override
//    public TsClientFactory createTsClient() {
//        if (null == tsClientFactory) {
//            tsClientFactory = new TsClientFactory(this.tsTableInfo.getTsProperties());
//        }
//        return tsClientFactory;
//    }
//
//    @Override
//    public void closeTsClient() {
//        if (tsClientFactory != null) {
//            tsClientFactory.close();
//        }
//    }
//
//
//    public static class TsInputFormatBuilder {
//        private final TsInputFormat format;
//
//        public static TsInputFormatBuilder builder() {
//            return new TsInputFormatBuilder();
//        }
//
//        public TsInputFormatBuilder() {
//            format = new TsInputFormat();
//        }
//
//        public TsInputFormatBuilder withTsTableInfo(TsTableInfo tsTableInfo) {
//            format.tsTableInfo = tsTableInfo;
//            return this;
//        }
//
//        public TsInputFormatBuilder withFlinkMetaInfo(FlinkMetaInfo flinkMetaInfo) {
//            format.flinkMetaInfo = flinkMetaInfo;
//            return this;
//        }
//
//
//        public TsInputFormat build() {
//            format.tsTableInfo.getTsProperties().checkEmpty();
//            format.createTsClient();
//            return format;
//        }
//
//    }
//}
