package com.yupaopao.risk.insight.flink.job.etl.common.connect.dubbo.writer;

import com.yupaopao.risk.insight.flink.job.etl.common.connect.SinkFactory;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.dubbo.core.DubboConfigConstants;
import com.yupaopao.risk.insight.flink.job.etl.common.options.DataTransferConfig;
import com.yupaopao.risk.insight.flink.job.etl.common.options.WriterConfig;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2022, jimmy
 *
 * <AUTHOR>
 * @desc DubboSinkFactory
 * @date 2022/4/1
 */
public class DubboSinkFactory extends SinkFactory {

    private String interfaceName;
    private String method;
    private String version;
    private String zkAddress;
    private String[] argTypes;
    private Integer timeout;
    private List<String> argKeys = new ArrayList<>();


    public DubboSinkFactory(DataTransferConfig config) throws Exception {
        super(config);
        WriterConfig writerConfig = config.getJob().getContent().get(0).getWriter();
        interfaceName = writerConfig.getParameter().getStringVal(DubboConfigConstants.KEY_INTERFACE);
        method = writerConfig.getParameter().getStringVal(DubboConfigConstants.KEY_METHOD);
        version = writerConfig.getParameter().getStringVal(DubboConfigConstants.KEY_VERSION);
        zkAddress = writerConfig.getParameter().getStringVal(DubboConfigConstants.KEY_ZK_ADDRESS);
        argKeys.addAll(Arrays.asList(writerConfig.getParameter().getStringVal(DubboConfigConstants.KEY_ARG_KEYS).split(",")));
        argTypes = writerConfig.getParameter().getStringVal(DubboConfigConstants.KEY_ARG_TYPES).split(",");
        timeout = writerConfig.getParameter().getIntVal(DubboConfigConstants.KEY_TIMEOUT, 1000);
    }

    @Override public DataStreamSink<?> createSink(DataStream<Map<String, Object>> dataSet) {
        DubboOutputFormatBuilder builder = new DubboOutputFormatBuilder();
        builder.setInterfaceName(interfaceName);
        builder.setMethod(method);
        builder.setVersion(version);
        builder.setZkAddress(zkAddress);
        builder.setArgKeys(argKeys);
        builder.setArgTypes(argTypes);
        builder.setTimeout(timeout);
        return createOutput(dataSet, builder.finish());
    }
}
