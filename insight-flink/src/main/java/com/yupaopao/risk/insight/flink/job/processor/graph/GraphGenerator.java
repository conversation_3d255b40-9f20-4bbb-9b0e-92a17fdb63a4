package com.yupaopao.risk.insight.flink.job.processor.graph;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.groovy.GroovyCommonExecutor;
import com.yupaopao.risk.insight.common.meta.JobParams;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.source.CKInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.GraphTableSource;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseFullTableInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseInputSplitReadThread;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseSplit;
import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.DataSet;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.operators.DataSource;
import org.apache.flink.graph.Edge;
import org.apache.flink.graph.Graph;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.BatchTableEnvironment;
import org.apache.flink.table.api.internal.TableEnvironmentInternal;
import org.apache.flink.types.Row;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.filter.*;
import org.apache.hadoop.hbase.util.Bytes;

import java.net.URLDecoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

import static com.yupaopao.risk.insight.common.property.enums.PropertyType.CLICK_HOUSE;
import static com.yupaopao.risk.insight.common.property.enums.PropertyType.HBASE;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-11 13:40
 *
 ***/

@Slf4j
public class GraphGenerator {

    private static final Long dayLimits = 1L;
    private static final Long fourteen_DAYS_MILLIS = 1000L * 60 * 60 * 24 * dayLimits;



    public static Graph<String, String, Long> getGraphFromEdge(ExecutionEnvironment env, Map<String, String> argMap) throws Exception {

        ClickHouseProperties clickHouseProperties = ClickHouseProperties.getProperties(CLICK_HOUSE);

        String dayLimit = argMap.get("dayLimit");
        String hourLimit = argMap.get("hourLimit");
        String runTime = argMap.get("runTime");
        String sql = "";
        if (StringUtils.isNotEmpty(dayLimit)) {
            sql = getDayLimitSql(runTime,Integer.parseInt(dayLimit));
        } else {
            sql = getHourLimitSql(runTime, hourLimit);
        }

        CKInputFormat ckInputFormat = new CKInputFormat(clickHouseProperties, sql, null);

        List<String> whiteList = getWhiteList();
        log.warn("white list count: {}", whiteList.size());
        final TypeInformation<Edge<String, Long>> edgeType = TypeInformation.of(new TypeHint<Edge<String, Long>>() {
            @Override
            public TypeInformation<Edge<String, Long>> getTypeInfo() {
                return super.getTypeInfo();
            }
        });

        DataSet<Edge<String, Long>> edges = env.createInput(ckInputFormat).setParallelism(1).name("read_edge_weight")
                .filter(elem->{
                    if(HBaseUtil.emptyResultJson(elem)){
                        return false;
                    }
                    JSONObject obj = JSON.parseObject(elem);
                    String fromVertex = obj.getString("fromVertex");
                    String toVertex = obj.getString("toVertex");
                    if(StringUtils.isEmpty(fromVertex) || StringUtils.isEmpty(toVertex)){
                        return false;
                    }
                    boolean notInWhiteList =  !whiteList.contains(fromVertex) && !whiteList.contains(toVertex);
                    return notInWhiteList;
                }).name("filter_empty_data")
                .map(elem -> {
                    JSONObject obj = JSON.parseObject(elem);
                    Edge<String, Long> edge = new Edge<>(obj.getString("fromVertex"), obj.getString("toVertex"), 1L);
                    return edge;

                }).returns(edgeType).name("map_to_edge");;


        // 过滤ip白名单关联的设备用户（可能公司内部账号）

        Graph<String, String, Long> inputGraph = Graph.fromDataSet(edges, new ConnectedInitLabel(), env);
        return inputGraph;
    }

    private static String getDayLimitSql(String runTime, Integer dayLimit) {
        Date endDate = InsightDateUtils.getDateFromString(runTime, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
        int lowerDay = -1*dayLimit;
        Date startDate = DateUtils.addDays(endDate, lowerDay);

        String strEnd = InsightDateUtils.getDateStr(endDate, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        String strStart = InsightDateUtils.getDateStr(startDate, InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);

        String baseSql = "select distinct source as fromVertex,target as toVertex from edge_weight where runDay> '%s'" +
                "  and runDay <='%s' and source!='' and target!='' and source!='0' and target!='0' and source not " +
                "in ('10','20') and target not in ('10','20') \n" +
                " order by source,target";

        return String.format(baseSql, strStart, strEnd);
    }

    private static String getHourLimitSql(String runTime, String hourLimit){
        String baseSql = "select userId as fromVertex,deviceId as toVertex ,count(distinct traceId) weight from " +
                "risk_hit_log a \n" +
                "where a.createdAt between '%s' and '%s'\n" +
                "and userId!='' and deviceId!='' and userId!='0' and deviceId!='0' and userId!='null' and deviceId " +
                "not in ('10','20')\n" +
                "and length(deviceId)=62\n" +
                "and eventCode not in ('user-login', 'safe-device', 'user-change-mobile', 'user-find-id','user-find-zhima', 'user-forget-password')\n" +
                " and data_riskBaseTraceId='' \n" +
                "           and data_userData_liveRobot!=1\n" +
                "           and data_userData_yuerRobot!=1\n" +
                "           and userId global not in (select valueData from risk_gray_list_sync where syncDate = today())\n" +
                "           and deviceId global not in (select valueData from risk_gray_list_sync where syncDate = today())\n" +
                "group by userId,deviceId order by userId,deviceId";
        int hours = -1;
        if(StringUtils.isNotEmpty(hourLimit)){
            hours = Integer.parseInt(hourLimit)*-1;
        }
        String startTime =
                InsightDateUtils.addHoursStr(runTime,InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss,hours);
        return String.format(baseSql,startTime,runTime);
    }

    private static List<String> getLargeDegreeNodes(Integer dayLimit) {
        long lowerDay = dayLimit - 1;
        String sql = "select nodeId,degree from (\n" +
                "select source as nodeId,count(distinct target) as degree \n" +
                "from edge_weight where runDay>=addDays(today(),-%d) group by source\n" +
                "union all\n" +
                "select target as nodeId,count(distinct source) as degree \n" +
                "from edge_weight where runDay>=addDays(today(),-%d) group by target\n" +
                ") a where a.degree>=10  ";

        sql = String.format(sql,lowerDay,lowerDay);
        List<Map<String,String>> degreeLargeThan10 =
                ClickHouseUtil.executeQuery(ClickHouseUtil.createConnection(ClickHouseUtil.getLongTimeoutProperties()),sql);
        return degreeLargeThan10.stream().map(elem->elem.get("nodeId")).collect(Collectors.toList());
    }

    private static DataSet<Row> getDataSet(ExecutionEnvironment env, Map<String, String> argMap) {
        Long currentTime = System.currentTimeMillis();
        currentTime = currentTime - fourteen_DAYS_MILLIS;

        String defaultSql = "select fromVertex,toVertex,label from graph_edge where " +
                " fromVertex<>'000' and " +
                " toVertex<>'000' " +
                " and fromVertex<>'0' and " +
                " toVertex<>'0' " +
                " and CHAR_LENGTH(toVertex)=62" +
                " and fromVertex<> '201911272150385d904b5a602d8ca75afda02e58aa03a80194957cf9b5f87f'" +
                " and toVertex<>'201911272150385d904b5a602d8ca75afda02e58aa03a80194957cf9b5f87f'" +
                " and label = 'hasDevice' and updateTime>=" + currentTime;

        String apolloSql = ApolloProperties.getConfigStr("application", "connected-component.graph.sql");
        if (StringUtils.isNotEmpty(apolloSql)) {
            defaultSql = apolloSql + currentTime;
        }
        HBaseProperties hBaseProperties = HBaseProperties.getProperties(HBASE);

        JobParams.TableQueryDatePeriod period = new JobParams.TableQueryDatePeriod();
        period.setBeginDate(argMap.get("beginDate"));
        period.setEndDate(argMap.get("endDate"));
        period.setTableName("graph_edge");

        TsTableInfo tsInTable = new TsTableInfo()
                .withTableName("graph_edge")
                .withBucketPerSplit(1)
                .withColumnNameType("fromVertex#STRING,toVertex#STRING,label#STRING,updateTime#BIGINT")
                .withTableQueryPeriod(period)
                .withTableType("SYSTEM");

        final TypeInformation<Edge<String, Long>> edgeType = TypeInformation.of(new TypeHint<Edge<String, Long>>() {
            @Override
            public TypeInformation<Edge<String, Long>> getTypeInfo() {
                return super.getTypeInfo();
            }
        });

        BatchTableEnvironment tableEnv = BatchTableEnvironment.create(env);

        //register source
        GraphTableSource graphEdgeTable = new GraphTableSource(tsInTable, hBaseProperties);
        // register table source

        ((TableEnvironmentInternal) tableEnv).registerTableSourceInternal("graph_edge", graphEdgeTable);

//        tableEnv.fromTableSource(graphEdgeTable);
        //sql
        Table resultTable = tableEnv.sqlQuery(defaultSql);
        DataSet<Row> ds = tableEnv.toDataSet(resultTable, Row.class);
        return ds;
    }


    private static List<String> getWhiteList() {
        //14天内的白名单
        String whiteListSql =
                "select distinct valueData from risk_gray_list_sync where syncDate >= addDays(today(),-14) ";

        List<Map<String, String>> rowList =
                ClickHouseUtil.executeQuery(ClickHouseUtil.createConnection(ClickHouseUtil.getLongTimeoutProperties()),
                        whiteListSql);
        return rowList.stream().map(elem -> elem.get("valueData")).collect(Collectors.toList());
    }

    public static class ConnectedInitLabel implements MapFunction<String, String> {

        @Override
        public String map(String value) throws Exception {
//            return value + "_label";
            return value;
        }
    }


    public static void main(String[] args) throws Exception{
//        String str = "%7B%22dayLimit%22%3A%221%22%2C%22runTime%22%3A%222021-04-18+00%3A00%3A00%22%7D";
//        String inputParam = URLDecoder.decode(str, "UTF-8");
//        log.info("input params : {}", inputParam);
//        Map<String,String> argMap = JSON.parseObject(inputParam, Map.class);
//        String dayLimit = argMap.get("dayLimit");
//        String hourLimit = argMap.get("hourLimit");
//        String runTime = argMap.get("runTime");
        System.err.println(getHourLimitSql("2022-06-01 14:00:00","12"));
    }


}
