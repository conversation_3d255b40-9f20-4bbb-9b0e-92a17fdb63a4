package com.yupaopao.risk.insight.flink.connector.ts.businessmodel;

/**
 * 风险等级
 *
 * <AUTHOR>
 * @date 2018/10/25 2:10 PM
 */
public enum RiskLevel {

    IGNORE(0), PASS(1), REVIEW(2), REJECT(3);

    private int level;

    RiskLevel(int level) {
        this.level = level;
    }

    public int getLevel() {
        return level;
    }

    public static RiskLevel nameOf(String name) {
        for (RiskLevel level : values()) {
            if (level.name().equalsIgnoreCase(name)) {
                return level;
            }
        }
        return null;
    }

    public static RiskLevel codeOf(int level) {
        for (RiskLevel item : values()) {
            if (item.getLevel() == level) {
                return item;
            }
        }
        return null;
    }

}
