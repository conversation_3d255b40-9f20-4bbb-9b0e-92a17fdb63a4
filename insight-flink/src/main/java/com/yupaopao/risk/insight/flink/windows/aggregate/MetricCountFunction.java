package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.alibaba.fastjson.JSONObject;
import org.apache.flink.api.common.functions.AggregateFunction;

public class MetricCountFunction implements AggregateFunction<JSONObject, JSONObject, JSONObject> {

    private static final long serialVersionUID = -8164041694242355573L;

    @Override public JSONObject createAccumulator() {
        JSONObject result = new JSONObject();
        result.put("result", 0);
        return result;
    }

    @Override public JSONObject add(JSONObject value, JSONObject accumulator) {
        accumulator.put("result", accumulator.getDouble("result") + 1);
        accumulator.put("count", accumulator.getLong("result"));
        value.remove("data");
        value.remove("groupKey");
        accumulator.putAll(value);
        return accumulator;
    }

    @Override public JSONObject getResult(JSONObject accumulator) {
        return accumulator;
    }

    @Override public JSONObject merge(J<PERSON>NObject a, J<PERSON>NObject b) {
        return a;
    }
}
