package com.yupaopao.risk.insight.flink.constants;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

@Slf4j
public class FlinkConstants {

    public final static String KAFKA_GROUP_ID_ETL_RISK_HIT_LOG = "risk-insight-risk_hit_log_etl";

    public final static String KAFKA_GROUP_ID_ETL_RISK_HIT_LOG_HBASE = "risk-insight-risk_hit_log_etl_hbase";
    public final static String KAFKA_GROUP_ID_ETL_RISK_HIT_LOG_RISK_USER = "risk-insight-risk_hit_log_etl_risk_user";
    public final static String KAFKA_GROUP_ID_ETL_RISK_HIT_LOG_GDB = "risk-insight-risk_hit_log_gdb";
    public final static String KAFKA_GROUP_ID_ETL_RISK_HIT_FACTOR_CAL = "risk-insight-risk_hit_log_factor_cal";
    public final static String KAFKA_GROUP_ID_ETL_RISK_HIT_PORTRAIT = "risk-insight-risk_hit_log_portrait";
    public final static String KAFKA_GROUP_ID_ETL_WANGAN = "risk-insight-wangan";
    public final static String KAFKA_GROUP_ID_AUDIT_TASK_MASERATI = "risk-insight-audit-service-task-maserati-new";
    public final static String KAFKA_GROUP_ID_OCEAN_ENGINE_SPECIAL_APP_MASERATI = "risk-insight-special-app-maserati";
    public final static String KAFKA_GROUP_ID_AUDIT_TASK_GROUP_MASERATI = "risk-insight-audit-service-task" +
            "-group-maserati";
    public final static String KAFKA_GROUP_ID_ETL_RISK_HIT_TAG_CAL = "risk-insight-risk_hit_log_tag_cal";
    public final static String KAFKA_GROUP_ID_COMMON_FACTOR_CAL = "risk-insight-common_factor_cal";

    public final static String KAFKA_GROUP_ID_KAFKA_CK_SYNC = "risk-insight-sync";
    public final static String KAFKA_GROUP_ID_KAFKA_SLS_CONSUME = "risk-insight-consumer";


    public final static String KAFKA_GROUP_ID_CEP_JOB = "risk-insight-risk_cep_job";

    public final static String KAFKA_GROUP_ID_AGGREGATE_TAG_RESULT = "risk-insight-risk_aggregate_tag_result";
    public final static String KAFKA_GROUP_ID_CEP_JOB_MATCH_RESULT = "risk-insight-risk_cep_job_match_result";
    public final static String KAFKA_GROUP_ID_TAG_RECALL = "risk-insight-risk_tag_recall";
    public final static String KAFKA_GROUP_ID_AUDIT_METRIC = "risk-insight-risk_audit_metric";

    public final static String KAFKA_GROUP_ID_RISK_INSIGHT_COMMON_CEP = "risk-insight-common-cep";

    public final static String KAFKA_GROUP_ID_EXTERNAL_TAG = "risk-insight-risk_external_tag";

    public final static String KAFKA_GROUP_ID_METRICS_JOB = "risk-insight-metrics-job";

    public final static Pattern CREATE_PATTERN = Pattern.compile("^create\\s*table");
    public final static Pattern INSERT_PATTERN = Pattern.compile("^insert\\s*into");
    public final static Pattern GROUP_WINDOW_PATTERN = Pattern.compile("(TUMBLE|HOP|SESSION)+\\(.*?\\)", Pattern.CASE_INSENSITIVE);

    public final static Pattern WITH_PATTERN = Pattern.compile("with.*\\(.*?\\)", Pattern.CASE_INSENSITIVE);
    public final static Pattern EVENT_TIME_PATTERN = Pattern.compile("eventtime.*?(\\(.*?\\))", Pattern.CASE_INSENSITIVE);
    public final static Pattern BRACKETS_PATTERN = Pattern.compile("\\(.*\\)", Pattern.CASE_INSENSITIVE);

    public final static Pattern DELAY_PATTERN = Pattern.compile("\\d+");
    public final static Pattern TIME_PATTERN = Pattern.compile("(second|minute|hour|day)+", Pattern.CASE_INSENSITIVE);

    public final static String KAFKA_TOPIC_KEY = "kafkaTopic"; //解析maserati额外加的字段
    public final static String KAFKA_DATA_RECEIVED_TIME = "msgReceivedTime"; //topic接收数据的时间


    private final static String ROCKS_DB_PATH_TEST = "oss://aliops-k8s01-flink/risk-flink/test/rocksdb";

    private final static String ROCKS_DB_PATH_PROD = "oss://aliops-k8s01-flink/risk-flink/prod/rocksdb";


    private final static String ROCKS_DB_PATH_TEST_NFS = "file:///flink/risk_test_113/checkpoints";

    private final static String ROCKS_DB_PATH_PROD_NFS = "file:///flink/flink_1.13.3/checkpoints";

    public final static String KAFKA_GROUP_ID_TAG_STORE = "risk-insight-tag-store";

    public final static List<String> TAG_TYPE_LIST = Arrays.asList("boolean", "long", "double", "string", "set", "map");

    public final static String TAG_STORE_VALUE_TYPE = "####valueType";

    public final static String KAFKA_GROUP_ID = "group.id";

    public final static String KAFKA_TOPIC_BIG_DATA_AI = "BIGDATA_OPENAI_PLATFORM_INPUT";

    public final static String KAFKA_TOPIC_RISK_ONLINE_RESULT_LOG = "RISK-ONLINE-RESULT-LOG";

    public final static String KAFKA_TOPIC_ASYNC_RULE_RESULT_LOG = "RISK-ASYNC-RULE-RESULT-LOG";
    public static final String SYNC_GROUP_ID = "risk-insight-flink-sync";
    public final static String KAFKA_TOPIC_PAYMENT_RISK_ONLINE_RESULT_LOG = "PAYMENT-RISK-ONLINE-RESULT-LOG";
    public final static String KAFKA_GROUP_ID_RISK_INSIGHT_BIG_DATA_MODEL = "risk-ingisht-bigdata-model";


    public final static String TOPIC_RISK_INSIGHT_CUSTOM_TAG = "RISK-INSIGHT-CUSTOM-TAG";
    public final static String TOPIC_RISK_INSIGHT_TAG_RECALL = "RISK-TAG-RECALL";
    public final static String WANGAN_KAFKA = "RISK-DATA-UPLOAD";

    public final static String TOPIC_RISK_INSIGHT_ABNORMAL_DATA = "RISK-INSIGHT-ABNORMAL-DATA";
    public final static String TOPIC_RISK_INSIGHT_SIGN_RESULT = "RISK-INSIGHT-SIGN-RESULT";

    public final static String KAFKA_GROUP_ID_PAYMENT_TRANSACTION_MASERATI = "risk-insight-payment-transaction-maserati";

    public final static String REALTIME_RISK = "1";
    public final static String OFFLINE_RISK = "2";
    public final static String AUDIT = "3";
    public final static String COMPLAIN = "4";
    public final static String APPEAL = "5";


    public static final String KAFKA_TOPIC_AUDIT_METRIC_SPOT = "AUDIT_METRIC_SPOT";
    public final static String GPS_DATA_PREFIX = "GPS-DATA#%s";


    public final static String TOPIC_FLINK_BIXIN_MAPI = "FLINK_BIXIN_MAPI"; //mapi日志流


    public final static String KAFKA_GROUP_ID_DEVICE_RAW_DATA_FETCHER_JOB = "risk-insight-rawData-fetcher-job";

    public final static int SHUMEI_DEVICE_ID_LENGTH = 62;

    public final static String SHUMEI_DEVICE_ID_RAWDATA_REQUEST_KEY_PREFIX = "risk:insigh-flink" +
            ":deviceIdRawDataRequest:";


    public final static String FLINK_RISK_CK_REDIS_PREFIX = "flink:risk_ck:";


    public final static String KAFKA_TOPIC_RISK_DEVICE_CALLBACK = "risk_device_callback";


    public static String getRocksdbPath() {
        String activeProfile = System.getenv("spring.profiles.active");
        if (StringUtils.isEmpty(activeProfile)) {
            activeProfile = System.getProperty("spring.profiles.active");
        }
        log.info("profile is: {}", activeProfile);
        if (activeProfile != null && activeProfile.startsWith("pro")) {
            return ROCKS_DB_PATH_PROD;
        } else {
            return ROCKS_DB_PATH_TEST;
        }
    }


    /****
     * rocksdb nas
     * @return
     */
    public static String getRocksdbPathWithNFS() {
        String activeProfile = getActiveProfile();
        if (activeProfile != null && activeProfile.startsWith("pro")) {
            return ROCKS_DB_PATH_PROD_NFS;
        } else {
            return ROCKS_DB_PATH_TEST_NFS;
        }
    }

    public static String getActiveProfile() {
        String activeProfile = System.getenv("spring.profiles.active");
        if (StringUtils.isEmpty(activeProfile)) {
            activeProfile = System.getProperty("spring.profiles.active");
        }
        log.info("nfs profile is: {}", activeProfile);
        return activeProfile;
    }

    public static String getDubboVersion() {
        if (getActiveProfile().toLowerCase().startsWith("pro")) {
            return "PROD";
        } else {
            return "TEST";
        }
    }

    public static boolean isLocalProfile() {
        String profile = getActiveProfile();
        if (StringUtils.isEmpty(profile)) {
            return true;
        }
        if (profile.startsWith("pro") || profile.startsWith("k8s")) {
            return false;
        }
        return true;
    }

    public static boolean isProdProfile(){
        String profile = getActiveProfile();
        if (StringUtils.isNotEmpty(profile) && profile.startsWith("pro")) {
            return true;
        }
        return false;
    }

    public static String getCKSqlBasePath() {
        return getOSSBase() + "sql/";
    }

    public static String getOSSBase() {
        String activeProfile = System.getenv("spring.profiles.active");
        if (StringUtils.isEmpty(activeProfile)) {
            activeProfile = System.getProperty("spring.profiles.active");
        }
        log.info("profile is: {}", activeProfile);
        if (activeProfile != null && activeProfile.startsWith("pro")) {
            return "risk-flink/prod/";
        } else {
            return "risk-flink/test/";
        }
    }
}
