package com.yupaopao.risk.insight.flink.job.portrait.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Maps;
import com.yupaopao.risk.insight.common.beans.tag.TagInfoBO;
import com.yupaopao.risk.insight.common.beans.tag.TagMessage;
import com.yupaopao.risk.insight.common.constants.InsightCommonConstants;
import com.yupaopao.risk.insight.common.groovy.GroovyCommonExecutor;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseSupport;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.SecurityAlgorithmUtils;
import com.yupaopao.risk.insight.common.support.TagCacheSupport;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.flink.job.portrait.support.bean.TagExecuteInfo;
import com.yupaopao.risk.insight.flink.utils.FastJsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.util.CollectionUtil;
import org.apache.hadoop.hbase.client.HTable;
import redis.clients.jedis.JedisPool;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
public class OnlineTagSupport {

    private static final String USER_ID = "userId";
    private static final String DEVICE_ID = "deviceId";
    private static final String CLIENT_IP = "clientIp";
    private static final String MOBILE = "mobile";
    private static final String TIAN_XIANG = "TianXiang";
    private static final String Other = "other";

    private static HTable deviceTagTable = null;


    private static Map<String, List<TagExecuteInfo>> rules_map = Maps.newHashMap();

    private static List<TagExecuteInfo> multiAggList = new ArrayList<>();
    private static ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static ExecutorService executorService = Executors.newFixedThreadPool(3);

    static {
        try {
            deviceTagTable = HBaseUtil.getTable("risk_device_tag",
                    HBaseUtil.createConnection(HBaseProperties.getProperties(PropertyType.HBASE)));
        } catch (Exception e) {
            log.error("create htable error", e);
        }

        flushRule();
        scheduler.scheduleAtFixedRate(() -> flushRule(), 2, 2, TimeUnit.MINUTES);
    }

    public static void flushRule() {
        Map<String, List<TagExecuteInfo>> newMap = Maps.newHashMap();
//        newMap.put(TIAN_XIANG, new ArrayList<>());
        newMap.put(Other, new ArrayList<>());
        List<TagExecuteInfo> tmpMultiAggList = new ArrayList<>();
        String source = PortraitApolloProperties.getConfigByKey("online.tag.source", "3");
        if (StringUtils.isEmpty(source)) {
            log.error("未配置设备组 online.tag.type={}", source);
            return;
        }
        Map<String, TagInfoBO> allTagInfo = TagCacheSupport.getAllTagInfo();
        for (Map.Entry<String, TagInfoBO> entry : allTagInfo.entrySet()) {
            if (source.equals(entry.getValue().getSource())) {
                addTagInfoCache(newMap, entry.getValue());
            } else if ("4".equals(entry.getValue().getSource())) {
                TagExecuteInfo tmpExecuteInfo = createTagExecutionInfo(entry.getValue());
                if (tmpExecuteInfo != null) {
                    tmpMultiAggList.add(tmpExecuteInfo);
                }

            }
        }
        if (!newMap.isEmpty()) {
            rules_map = newMap;
        }
        //只处理需要近实时计算的聚合标签
        tmpMultiAggList = tmpMultiAggList.stream().filter(elem -> "devicePortrait".equalsIgnoreCase(elem.getCode())).collect(Collectors.toList());
        if (!tmpMultiAggList.isEmpty()) {
            multiAggList = tmpMultiAggList;
        }

        log.info("flushRule: {}", newMap);
    }

    private static void addTagInfoCache(Map<String, List<TagExecuteInfo>> newMap, TagInfoBO tagInfoBO) {

        TagExecuteInfo tagExecuteInfo = createTagExecutionInfo(tagInfoBO);
        if (tagExecuteInfo == null) {
            return;
        }
//        GroovyCommonExecutor.updateScript(tagInfoBO.getCode(), condition);
        if (TIAN_XIANG.equalsIgnoreCase(tagExecuteInfo.getEventCode())) {
            //重新构建，区分手机号和设备
            List<TagExecuteInfo> tagExecuteInfoList = newMap.computeIfAbsent(TIAN_XIANG + "_" + tagExecuteInfo.getGroupKey(), k -> new ArrayList<>());
            tagExecuteInfoList.add(tagExecuteInfo);
//            newMap.get(TIAN_XIANG).add(tagExecuteInfo);
        } else {
            newMap.get(Other).add(tagExecuteInfo);
        }

    }

    private static TagExecuteInfo createTagExecutionInfo(TagInfoBO tagInfoBO) {
        if (StringUtils.isEmpty(tagInfoBO.getDataContent())) {
            return null;
        }

        JSONObject dataContent = JSON.parseObject(tagInfoBO.getDataContent());
        String fields = dataContent.getString("field");
        String condition = dataContent.getString("condition");
        String dataChannel = dataContent.getString("dataChannel");
        if (StringUtils.isNotEmpty(dataChannel) && !"[]".equals(dataChannel)) {
            return null;
        }
        String eventCode = dataContent.getString("eventCode");
        String groupKey = dataContent.getString("groupKey");
        TagExecuteInfo tagExecuteInfo = new TagExecuteInfo();
        tagExecuteInfo.setCondition(condition);
        tagExecuteInfo.setDependent(fields);
        tagExecuteInfo.setName(tagInfoBO.getName());
        tagExecuteInfo.setCode(tagInfoBO.getCode());
        tagExecuteInfo.setValueType(tagInfoBO.getValueType());
        tagExecuteInfo.setEventCode(eventCode);
        tagExecuteInfo.setGroupKey(groupKey);
        if (StringUtils.isEmpty(condition)) {
            return null;
        }
        return tagExecuteInfo;
    }

    public static List<TagMessage> coverGeneralTag(JSONObject riskLog, List<String> codes, JedisPool jedisPool) {


        List<TagMessage> generalTagList = new ArrayList<>();

        if (Objects.isNull(riskLog) || !riskLog.containsKey("riskAction")) {
            log.error("消息格式错误：{}", riskLog);
            return generalTagList;
        }
        JSONObject riskAction = riskLog.getJSONObject("riskAction");
        CountDownLatch latch = new CountDownLatch(3);
        executorService.submit(() -> {
            generalTagList.addAll(calcDeviceTianXiang(codes, jedisPool, riskAction));
            latch.countDown();
        });
        executorService.submit(() -> {
            generalTagList.addAll(calcMobileTianXiang(codes, jedisPool, riskAction));
            latch.countDown();
        });
        executorService.submit(() -> {
            generalTagList.addAll(calcOther(riskLog, codes, riskAction));
            latch.countDown();
        });

        try {
            boolean completed = latch.await(10000, TimeUnit.MILLISECONDS);
            if (!completed) {
                log.error("部分任务未在指定时间内完成");
            }
        } catch (Exception e) {
            log.error("系统报错:{}", riskLog, e);
        }

        log.info("发送标签:{}", JSONObject.toJSONString(generalTagList));
        return generalTagList;
    }

    private static List<TagMessage> calcOther(JSONObject riskLog, List<String> codes, JSONObject riskAction) {
        Transaction transaction = Cat.newTransaction("risk.insight.flink", "rule.tag");
        List<TagMessage> generalTagList = new ArrayList<>();
        try {
            List<TagExecuteInfo> tagExecuteInfos = rules_map.get(Other);
            for (TagExecuteInfo ruleInfo : tagExecuteInfos) {
                if (null != codes && !codes.contains(ruleInfo.getCode())) {
                    continue;
                }
                if (StringUtils.isNotEmpty(ruleInfo.getEventCode()) && !ruleInfo.getEventCode().contains(riskAction.getString("eventCode"))) {
                    log.debug("当前配置的 eventCode 不支持改规则");
                    continue;
                }
                String value = getValue(ruleInfo.getGroupKey(), riskAction);
                if (StringUtils.isEmpty(value)) {
                    log.debug("配置groupKey 未找到打标对象:{},{},{}", ruleInfo.getGroupKey(), value, riskAction);
                    continue;
                }
                Object result = null;
                try {
                    result = GroovyCommonExecutor.getResult(ruleInfo.getCode() + ruleInfo.getCondition(), ruleInfo.getCondition(), JSONObject.parseObject(riskLog.toJSONString(), Map.class));
                } catch (Exception e) {
                    log.error("error execute groovy, ruleCondition=" + ruleInfo.getCondition() + ", param=" + riskLog, e);
                    continue;
                }
                if (Objects.nonNull(result)) {

                    if (MOBILE.equalsIgnoreCase(ruleInfo.getGroupKey())) {
                        //手机号加密处理
                        TagMessage generalTagEncrypted = buildTagMessage(value, ruleInfo, result, System.currentTimeMillis());
                        String plainGroupV = generalTagEncrypted.getGroupValue();
                        String encryptedGroupV = SecurityAlgorithmUtils.getSha256Str(InsightCommonConstants.mobileSalt, plainGroupV);
                        generalTagEncrypted.setGroupValue(encryptedGroupV);
                        generalTagList.add(generalTagEncrypted);
                    } else {
                        TagMessage generalTagVO = buildTagMessage(value, ruleInfo, result, System.currentTimeMillis());
                        generalTagList.add(generalTagVO);
                    }

                }
            }
        } catch (Exception e) {
            log.error("执行 通用画像规则 报错:{}", riskAction, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }

        return generalTagList;

    }

    private static List<TagMessage> calcMobileTianXiang(List<String> codes, JedisPool jedisPool, JSONObject riskAction) {
        Transaction transaction = Cat.newTransaction("risk.insight.flink", "post.tag");
        List<TagMessage> currentTXTagList = new ArrayList<>();
        try {
            String mobileNo = riskAction.getString(MOBILE);
            String nationCode = riskAction.getJSONObject("data").getString("nationCode");
            Map<String, Object> contextMap = TianXiangTagSupport.fetchTxMobilePortrait(mobileNo, jedisPool);
            if (!CollectionUtil.isNullOrEmpty(contextMap)) {
                if (!contextMap.containsKey("id")) {
                    contextMap.put("id", "");
                }
                for (TagExecuteInfo ruleInfo : rules_map.get(TIAN_XIANG + "_Mobile")) {
                    if (null != codes && !codes.contains(ruleInfo.getCode())) {
                        continue;
                    }

                    Object result = GroovyCommonExecutor.getResult(ruleInfo.getCode() + ruleInfo.getCondition(), ruleInfo.getCondition(), contextMap);

                    if (Objects.nonNull(result)) {
                        TagMessage generalTagEncrypted = buildTagMessage(nationCode + mobileNo, ruleInfo, result, System.currentTimeMillis());
                        String encryptedGroupV = SecurityAlgorithmUtils.getSha256Str(InsightCommonConstants.mobileSalt, generalTagEncrypted.getGroupValue());
                        generalTagEncrypted.setGroupValue(encryptedGroupV);
                        currentTXTagList.add(generalTagEncrypted);
                    }
                }
            }

        } catch (Exception e) {
            log.error("执行 手机号画像规则 报错:{}", riskAction, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return currentTXTagList;
    }

    private static List<TagMessage> calcDeviceTianXiang(List<String> codes, JedisPool jedisPool, JSONObject riskAction) {
        Transaction transaction = Cat.newTransaction("risk.insight.flink", "post.tag");
        List<TagMessage> currentTXTagList = new ArrayList<>();
        try {
            String deviceId = riskAction.getString(DEVICE_ID);
            Map<String, Object> contextMap = TianXiangTagSupport.fetchTxDevicePortrait(deviceId, jedisPool);
            if (!CollectionUtil.isNullOrEmpty(contextMap)) {
                if (!contextMap.containsKey("id")) {
                    contextMap.put("id", "");
                }
                for (TagExecuteInfo ruleInfo : rules_map.get(TIAN_XIANG + "_DeviceId")) {
                    if (null != codes && !codes.contains(ruleInfo.getCode())) {
                        continue;
                    }

                    Object result = GroovyCommonExecutor.getResult(ruleInfo.getCode() + ruleInfo.getCondition(), ruleInfo.getCondition(), contextMap);
                    //标签画像不存在对web localid不打标
                    boolean isWebOrAppletDevice =
                            (deviceId.length()==134 || (deviceId.length()>=200 && deviceId.length()<=400));
                    String realId = FastJsonUtils.read(contextMap,"$.deviceLabels.id",String.class);
                    boolean isLocalId = realId==null || realId.substring(46,48).equalsIgnoreCase("00") ;
                    if("txDevicePortraitNonExist".equalsIgnoreCase(ruleInfo.getCode()) && isWebOrAppletDevice && isLocalId){
                        result = false;
                    }

                    if (Objects.nonNull(result)) {
                        TagMessage generalTagVO = buildTagMessage(deviceId, ruleInfo, result, System.currentTimeMillis());
                        currentTXTagList.add(generalTagVO);
                    }
                }
            }

            /****
             *  针对设备分级做特殊处理：
             *  如果 generalTagList 中包含设备分级依赖的相关字段，则计算设备分级画像标签
             */
            List<TagMessage> aggTagList = generateDevicePortrait(currentTXTagList, deviceId);
            if (CollectionUtils.isNotEmpty(aggTagList)) {
                currentTXTagList.addAll(aggTagList);
            }

        } catch (Exception e) {
            log.error("执行 设备画像规则 报错:{}", riskAction, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return currentTXTagList;
    }


    /***
     * 暂时只处理天象标签相关的聚合标签
     * @param tianxiangTagMessageList 本次查询到的天象设备画像
     * @param deviceId
     * @return
     */
    private static List<TagMessage> generateDevicePortrait(List<TagMessage> tianxiangTagMessageList, String deviceId) {
        //只处理天象设备画像关联的聚合标签
        if (CollectionUtils.isEmpty(tianxiangTagMessageList)) {
            return null;
        }
        List<TagMessage> resultTagList = new ArrayList<>();
        multiAggList.forEach(elem -> {
            String dependentFields = elem.getEventCode();
            if (StringUtils.isEmpty(dependentFields)) {
                return;
            }
            //暂时只支持一个层级的标签
            Map<String, Object> params = new HashMap<>();
            List<String> dependentTags = Arrays.asList(dependentFields.split(","));
            for (TagMessage tmpTag : tianxiangTagMessageList) {
                if (dependentTags.contains(tmpTag.getCode())) {
                    Object tagV = !tmpTag.getValueType().equalsIgnoreCase("boolean") ? tmpTag.getValue() : (((Boolean) tmpTag.getValue()) ? 1 : 0);
                    params.put(tmpTag.getCode(), tagV);
                }
            }
            if (params.isEmpty()) {
                //没有依赖的参数不做计算
                return;
            }
            addHbaseExistsParams(params, dependentTags, deviceId);
            //不存在字段设置为null
            dependentTags.stream().filter(dependentField -> !params.containsKey(dependentField)).forEach(notExistField -> params.put(notExistField, null));

            //计算groovy得到结果
            Object groovyResult = GroovyCommonExecutor.getResult(elem.getCode() + elem.getCondition(), elem.getCondition(), params);
            if (groovyResult == null) {
                return;
            }
            //构建聚合的tagMessage
            TagInfoBO tagCfg = TagCacheSupport.getTagInfoByCode(elem.getCode());
            TagMessage newTag = new TagMessage();
            newTag.setCode(elem.getCode());
            newTag.setValue(groovyResult);
            newTag.setValueType(tagCfg.getValueType());
            newTag.setCreatedAt(new Date().getTime());
            newTag.setType("aggregate");
            newTag.setGroupKey(elem.getGroupKey());
            newTag.setGroupValue(deviceId);
            resultTagList.add(newTag);
        });
        return resultTagList;
    }



    private static void addHbaseExistsParams(Map<String,Object> existParams,List<String> dependentTags,
                                             String deviceId){
        //need query: 如果依赖额字段不仅仅是本次天象查到的，需要查询hbase
        boolean dependentExists = existParams.keySet().containsAll(dependentTags);
        if(dependentExists){
            //已经包含不需要处理
            return;
        }
        //hbase查询其他依赖字段
        Map<String, Object> hbaseParams = null;
        try {
            hbaseParams = HBaseSupport.getRow(deviceId, deviceTagTable);
        } catch (Exception e) {
            log.warn("get hbase row error,deviceId=" + deviceId, e);
        }
        if (hbaseParams != null && !hbaseParams.isEmpty()) {
            for (String field : dependentTags) {
                if (!existParams.containsKey(field) && hbaseParams.containsKey(field)) {
                    existParams.put(field, hbaseParams.get(field));
                }
            }
        }
    }


    private static TagMessage buildTagMessage(String value, TagExecuteInfo tagInfoBO, Object result, Long createAt) {
        TagMessage generalTagVO = new TagMessage();
        generalTagVO.setCode(tagInfoBO.getCode());
        generalTagVO.setName(tagInfoBO.getName());
        generalTagVO.setValue(result);
        generalTagVO.setValueType(tagInfoBO.getValueType());
        generalTagVO.setCreatedAt(createAt);
        generalTagVO.setType("onlineDevice");
        generalTagVO.setGroupKey(tagInfoBO.getGroupKey());
        generalTagVO.setGroupValue(value);
        return generalTagVO;
    }

    public static List<TagMessage> coverGeneralTag(JSONObject riskLog, JedisPool jedisPool) {
        return coverGeneralTag(riskLog, null, jedisPool);
    }

    public static void close() throws Exception {
        GroovyCommonExecutor.cleanAllScript();
    }

    private static String getValue(String groupKey, JSONObject riskAction) {
        String value = StringUtils.EMPTY;
        if (USER_ID.equalsIgnoreCase(groupKey)) {
            value = riskAction.getString(USER_ID);
        } else if (DEVICE_ID.equalsIgnoreCase(groupKey)) {
            value = riskAction.getString(DEVICE_ID);
        } else if (CLIENT_IP.equalsIgnoreCase(groupKey)) {
            value = riskAction.getString(groupKey);
        } else if (MOBILE.equalsIgnoreCase(groupKey)) {
            value = riskAction.getJSONObject("data").getString("nationCode") + riskAction.getString("mobile");
        }
        return value;
    }




}
