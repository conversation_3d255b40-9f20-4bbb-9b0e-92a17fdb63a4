package com.yupaopao.risk.insight.flink.job.processor.audit;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.flink.bean.audit.MxTaskGroup;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.MaseratiKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.process.audit.TaskGroupProcessFunction;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.api.datastream.KeyedStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.KAFKA_GROUP_ID_AUDIT_TASK_GROUP_MASERATI;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-04 11:42
 *
 ***/
public class TaskGroupPerformanceProcessor implements FlinkJobBuilder.MainProcessor {

    //第一次待分配到完成，第一次已经分配到完成

    @Override
    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
        ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.MASERATI_KAFKA);

        //sink,满足5k或者30s更新一次数据
        ClickHouseStreamSink ckSink = new ClickHouseStreamSink(ckProperties, "ods_mx_operation_group_metric_log",
                1000 * 30, 5000);
        //input stream
        Properties maseratiProperties = kafkaProperties.getProperties();
        maseratiProperties.put("group.id", KAFKA_GROUP_ID_AUDIT_TASK_GROUP_MASERATI);
        String topicConfig = ApolloProperties.getConfigStr("application", "audit.task.group.topic");
        List<String> topics = Arrays.asList(topicConfig.split(","));

        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics,
                new MaseratiKafkaDeserializationSchemaWrapper(new SimpleStringSchema(), MxTaskGroup.listenColumns),
                maseratiProperties);
//        consumer.setStartFromTimestamp(System.currentTimeMillis() - 1 * 60000); //before 1min
        KeyedStream<MxTaskGroup, String> inputDs = env.addSource(consumer)
                .name("read_maserati_topic")
                .map(elem -> JSON.parseObject(elem, MxTaskGroup.class))
                .keyBy(elem -> elem.getKafkaTopic() + ":" + elem.getId()); //可能监听多个topic,需要预先keyBy


        SingleOutputStreamOperator<String> mxPerformanceDs = inputDs.process(new TaskGroupProcessFunction()).name(
                "process_task_group_data");

        mxPerformanceDs.addSink(ckSink).name("write_audit_task_group_clickhouse").setParallelism(1);


    }
}
