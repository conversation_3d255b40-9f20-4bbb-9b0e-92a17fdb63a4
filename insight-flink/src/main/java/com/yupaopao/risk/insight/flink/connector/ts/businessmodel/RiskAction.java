package com.yupaopao.risk.insight.flink.connector.ts.businessmodel;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-11-20 11:53
 *
 ***/
public class RiskAction  implements Serializable {
    public final static String KEY_USER_ID = "UserId";
    public final static String KEY_DEVICE_ID = "DeviceId";
    public final static String KEY_CLIENT_IP = "ClientIp";
    public final static String KEY_MOBILE = "Mobile";

    public final static String KEY_APP_ID = "AppId";
    public final static String KEY_TIMEOUT = "Timeout";
    public final static String KEY_EVENT_CODE = "Event";
    public final static String KEY_BUSINESS_CODE = "Business";
    public final static String KEY_ASYNC = "Async";
    public final static String KEY_TRACE_ID = "TraceId";

    private Map<String, Object> data = new ConcurrentHashMap<>();

    public RiskAction() {
    }

    public RiskAction(String eventCode) {
        put(KEY_EVENT_CODE, eventCode);
    }

    public RiskAction put(String k, String v) {
        if (StringUtils.isNotBlank(k)) {
            data.put(k, StringUtils.defaultString(v, StringUtils.EMPTY));
        }
        return this;
    }

    public String getUserId() {
        return getString(KEY_USER_ID);
    }

    public RiskAction setUserId(String userId) {
        return put(KEY_USER_ID, userId);
    }

    public String getDeviceId() {
        return getString(KEY_DEVICE_ID);
    }

    public RiskAction setDeviceId(String deviceId) {
        put(KEY_DEVICE_ID, deviceId);
        return this;
    }

    public String getClientIp() {
        return getString(KEY_CLIENT_IP);
    }

    public RiskAction setEventCode(String eventCode) {
        return put(KEY_EVENT_CODE, eventCode);
    }

    public String getEventCode() {
        return getString(KEY_EVENT_CODE);
    }

    public RiskAction setBusinessCode(String businessCode) {
        return put(KEY_BUSINESS_CODE, businessCode);
    }

    public String getBusinessCode() {
        return getString(KEY_BUSINESS_CODE);
    }

    public RiskAction setClientIp(String clientIp) {
        return put(KEY_CLIENT_IP, clientIp);
    }

    public Integer getAppId() {
        return (StringUtils.isNumeric(getString(KEY_APP_ID))) ? Integer.parseInt(getString(KEY_APP_ID)) : null;
    }

    public RiskAction setAppId(Integer appId) {
        return appId == null ? this : put(KEY_APP_ID, String.valueOf(appId));
    }

    public String getMobile() {
        return getString(KEY_MOBILE);
    }

    public RiskAction setMobile(String mobile) {
        return put(KEY_MOBILE, mobile);
    }

    public RiskAction setTimeout(long mills) {
        return put(KEY_TIMEOUT, String.valueOf(mills));
    }

    public Long getTimeout() {
        return (StringUtils.isNumeric(getString(KEY_TIMEOUT))) ? Long.parseLong(getString(KEY_TIMEOUT)) : null;
    }

    public RiskAction setAsync(boolean async) {
        return put(KEY_ASYNC, String.valueOf(async));
    }

    public boolean isAsync() {
        return (StringUtils.isBlank(getString(KEY_ASYNC))) ? false : Boolean.parseBoolean(getString(KEY_ASYNC));
    }

    protected void setTraceId(String traceId) {
        put(KEY_TRACE_ID, traceId);
    }

    public String getTraceId() {
        return (String) data.get(KEY_TRACE_ID);
    }

    public Map<String, Object> getData() {
        return data;
    }

    protected String getString(String key) {
        Object value = StringUtils.isBlank(key) ? null : data.get(key);
        return value == null ? null : value.toString();
    }

    @Override
    public String toString() {
        return data == null ? "" : data.toString();
    }

    public static RiskAction create(String eventCode) {
        return new RiskAction(eventCode);
    }
}
