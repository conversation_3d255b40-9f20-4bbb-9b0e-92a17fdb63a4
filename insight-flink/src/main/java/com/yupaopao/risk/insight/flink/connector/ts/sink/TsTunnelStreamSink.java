//package com.yupaopao.risk.insight.flink.connector.ts.sink;
//
//import com.alibaba.fastjson.JSONObject;
//import org.apache.flink.api.common.functions.RuntimeContext;
//import org.apache.flink.configuration.Configuration;
//import org.apache.flink.runtime.state.FunctionInitializationContext;
//import org.apache.flink.runtime.state.FunctionSnapshotContext;
//import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
//import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
//
//import java.util.Map;
//
///**
// * 数据清理到新表
// */
//public class TsTunnelStreamSink extends RichSinkFunction<Map<String, Object>> implements CheckpointedFunction {
//    private KafkaTsOutputFormat outputFormat;
//
//    public TsTunnelStreamSink(KafkaTsOutputFormat outputFormat) {
//        this.outputFormat = outputFormat;
//    }
//
//    @Override public void invoke(Map<String, Object> value, Context context) throws Exception {
//        outputFormat.writeRecord(JSONObject.toJSONString(value));
//    }
//
//    @Override public void snapshotState(FunctionSnapshotContext context) throws Exception {
//        outputFormat.flush();
//    }
//
//    @Override public void initializeState(FunctionInitializationContext context) throws Exception {
//    }
//
//    @Override
//    public void open(Configuration parameters) throws Exception {
//        super.open(parameters);
//        RuntimeContext ctx = getRuntimeContext();
//        outputFormat.setRuntimeContext(ctx);
//        outputFormat.open(ctx.getIndexOfThisSubtask(), ctx.getNumberOfParallelSubtasks());
//    }
//}
