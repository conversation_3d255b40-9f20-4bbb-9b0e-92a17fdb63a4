package com.yupaopao.risk.insight.flink.connector.clickhouse.sink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.clickhouse.client.ClickHouseClient;
import com.clickhouse.client.ClickHouseNodes;
import com.clickhouse.client.ClickHouseResponse;
import com.clickhouse.data.ClickHouseFormat;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.clickhouse.jdbc.ClickHouseStatement;
import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.OSSProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.beans.ClickHouseRequestBlank;
import com.yupaopao.risk.insight.flink.connector.scheduler.IntervalFlushSchedulerTools;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.metrics.Counter;

import java.io.ByteArrayInputStream;
import java.io.Closeable;
import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-18 13:58
 * ck数据定时刷新，按量缓存
 ***/

@Slf4j
public class ClickHouseFlushWithTimeProcessor implements Closeable, Runnable {

    //定时刷新数据
    private final ScheduledThreadPoolExecutor scheduler;

    private LinkedBlockingQueue<String> localValues;

    private int maxFlushBufferSize = 1000;

    private Counter errorCounter;

    private Counter writeCounter;


    private String tableName;

    private boolean hug; //是否海外

    private ClickHouseProperties ckProperties;

    private transient ClickHouseClient client;
    private transient ClickHouseNodes server;

    private transient Connection oldCkConn;

    public ClickHouseFlushWithTimeProcessor(ClickHouseProperties properties, String tableName, Counter errorCounter,
                                            Counter writeCounter, int subTaskIndex) {
        this.ckProperties = properties;
        Integer configInterval = properties.getFlushIntervalInMillis();
        if (properties != null && configInterval != null && configInterval > 0) {
            this.scheduler =
                    IntervalFlushSchedulerTools.createScheduler(subTaskIndex + "-write-ck-interval-" + tableName,
                            configInterval, this);

        } else {
            scheduler = null;
        }
        localValues = new LinkedBlockingQueue<>();
        if (properties != null && properties.getBatchSize() > 0) {
            maxFlushBufferSize = properties.getBatchSize();
        }
        this.errorCounter = errorCounter;
        this.writeCounter = writeCounter;
        this.client = ClickHouseUtil.getCkClient(ckProperties);
        this.server = ClickHouseUtil.getServers(ckProperties);
        this.oldCkConn = ClickHouseUtil.createConnectionOld(ckProperties);
        this.tableName = tableName;
        this.hug = properties.getHost().contains("singapore") || properties.getHost().toLowerCase().contains("sg");
    }

    public void addRecord(String rowJson) throws Exception {
        if (localValues.size() >= maxFlushBufferSize || localValues.remainingCapacity() <= 0) {
            flush();
        }
//        localValues.add(rowJson);
        localValues.put(rowJson); //put and wait queue not full
    }


    @Override
    public void close() throws IOException {
        log.info("close ck processor...");
        flush();
        IntervalFlushSchedulerTools.closeScheduler(scheduler);
        client.close();
        server.shutdown();
        try {
            if (oldCkConn != null) {
                oldCkConn.close();
            }
        } catch (Exception e) {
            log.error("close ckConn error", e);
        }

    }


    public void flush() {
        if (ColumnSyncLocker.isSyncColumn) {
            return;
        }
        String logSwitch = ApolloProperties.getConfigStr("logFlushTraceId");
        List<String> localList = new ArrayList<>();
        try {
            int iSize = localValues.size();
            for (int i = 0; i < maxFlushBufferSize && i < iSize; i++) {
                try {
                    String value = localValues.poll(4, TimeUnit.MILLISECONDS);
                    if (StringUtils.isEmpty(value)) {
                        continue;
                    }
                    if ("true".equals(logSwitch) && "risk_hit_log".equals(tableName)) {
                        log.info("flush traceId: {}", JSONObject.parseObject(value).getString("traceId"));
                    }
                    localList.add(value);
                } catch (Exception e1) {
                    log.warn("poll data error: ", e1);
                }

            }
            if (localList.size() > 0) {
                List<String> originalList = Collections.unmodifiableList(localList);
                Map<String, List<String>> batchParts = ClickHouseUtil.processBigTablesParts(originalList, tableName);
                originalList = null;
                for (List<String> part : batchParts.values()) {
                    List<String> target = Collections.unmodifiableList(part);
                    ClickHouseRequestBlank params = new ClickHouseRequestBlank(target, tableName);
                    sendWithRetry(params, 0);
                }

            }

        } catch (Exception e) {
            //字段类型问题记录warn
            if (e.getMessage() != null && e.getMessage().contains("ClickHouse exception, code: 26")) {
                log.warn("ClickHouse build request occurs error: ", e);
            } else {
                log.error("ClickHouse build request occurs error: ", e);
            }

        }
    }


    private void sendWithRetry(ClickHouseRequestBlank requestBlank, int currentRetryCount) {
        if (FlinkConstants.isProdProfile()) {
            httpInsert(requestBlank, currentRetryCount);
        } else {
            //old clickhosue jdbc
            jdbcInsert(requestBlank, currentRetryCount);
        }


    }

    /***
     * 从测试结果看批量写入的时候http的效率比jdbc的高
     * @param requestBlank
     * @param currentRetryCount
     */
    private void httpInsert(ClickHouseRequestBlank requestBlank, int currentRetryCount) {
        log.info("Ready to load data to {}, size = {}, currentRetryCount: {}", requestBlank.getTargetTable(),
                requestBlank.getValues().size(), currentRetryCount);
        String sql = buildSql(requestBlank);
        try {
            client.read(server).format(ClickHouseFormat.JSONEachRow).write().query(sql).executeAndWait();
            log.info("Successful send data to ClickHouse, size = {}, target table = {}, current attempt = {}",
                    requestBlank.getValues().size(),
                    requestBlank.getTargetTable(),
                    requestBlank.getAttemptCounter());
        } catch (Exception e) {
            errorCounter.inc();
            String objectName = writeToOSS(sql);
            Cat.logMetricForCount("ckWrite.error");
            log.error("Error while executing ck objectName= " + objectName, e);
        }
    }

    private void jdbcInsert(ClickHouseRequestBlank requestBlank, int currentRetryCount) {
        log.info("Ready to load data to {}, size = {}, currentRetryCount: {}", requestBlank.getTargetTable(),
                requestBlank.getValues().size(), currentRetryCount);
        String sql = buildSql(requestBlank);
        try (Statement statement = oldCkConn.createStatement();) {
            log.info("start to execute sql, table: {}", tableName);
            statement.executeUpdate(sql);
            log.info("Successful send data to ClickHouse, size = {}, target table = {}, current attempt = {}", requestBlank.getValues().size(), requestBlank.getTargetTable(), requestBlank.getAttemptCounter());
            writeCounter.inc(requestBlank.getValues().size());
        } catch (Exception e) {
            errorCounter.inc();
            String objectName = writeToOSS(sql);
            Cat.logMetricForCount("ckWrite.error");
            log.error("Error while executing ck objectName= " + objectName, e);
        }
    }

    private void retrySleep(int retryCount) {
        try {
            Thread.sleep((long) Math.pow(10, retryCount));
        } catch (InterruptedException e) {

        }
    }

    private String buildSql(ClickHouseRequestBlank requestBlank) {
        String result = "";
        String resultCSV = String.join("\n", requestBlank.getValues());
        String resultFormat = "INSERT INTO %s FORMAT JSONEachRow\n%s";
        String targetTable = requestBlank.getTargetTable();

        if (ClickHouseUtil.getWriteToLocalTables().contains(requestBlank.getTargetTable())) {
            targetTable = requestBlank.getTargetTable() + "_local";
        }
        result = String.format(resultFormat, targetTable, resultCSV);
        return result;
    }

    private String writeToOSS(String content) {

        try {
            String sqlFileName = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyyMMddHHmmss);
            String dirAppend = "";
            if (hug) {
                dirAppend = "hug/";
            }
            String objectName = FlinkConstants.getCKSqlBasePath() + dirAppend + tableName + "/" + sqlFileName + ".sql";
            OSSProperties ossProperties = OSSProperties.getProperties(PropertyType.OSS);
            log.info("start to write oss, object = {}", objectName);
            OSS ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(),
                    ossProperties.getAccessKeySecret());
            ossClient.putObject(ossProperties.getBucket(), objectName, new ByteArrayInputStream(content.getBytes()));
            ossClient.shutdown();
            return objectName;
        } catch (Exception e) {
            log.error("write to oss error: ", e);
        }
        return null;
    }

    @Override
    public void run() {
        flush();
    }

}
