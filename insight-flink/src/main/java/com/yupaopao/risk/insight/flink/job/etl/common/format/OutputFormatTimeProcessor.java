package com.yupaopao.risk.insight.flink.job.etl.common.format;

import com.yupaopao.risk.insight.flink.connector.scheduler.IntervalFlushSchedulerTools;

import java.io.Closeable;
import java.io.IOException;
import java.util.concurrent.ScheduledThreadPoolExecutor;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc OutputFormatTimeProcessor
 * @date 2020/12/15
 */
public class OutputFormatTimeProcessor implements Closeable, Runnable {
    private final BaseRichOutputFormat format;

    private final ScheduledThreadPoolExecutor scheduler;

    public OutputFormatTimeProcessor(BaseRichOutputFormat format, int configInterval) {
        this.format = format;
        this.scheduler = IntervalFlushSchedulerTools.createScheduler("output-interval", configInterval, this);
    }

    @Override public void run() {
        format.writeRecordInternal();
    }


    @Override public void close() throws IOException {
        format.writeRecordInternal();
        IntervalFlushSchedulerTools.closeScheduler(scheduler);
    }
}
