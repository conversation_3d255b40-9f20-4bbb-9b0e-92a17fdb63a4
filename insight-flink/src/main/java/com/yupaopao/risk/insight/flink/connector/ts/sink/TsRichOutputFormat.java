//package com.yupaopao.risk.insight.flink.connector.ts.sink;
//
//import com.yupaopao.risk.insight.flink.connector.ts.TsFormat;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsClientFactory;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import lombok.Getter;
//import lombok.Setter;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.flink.api.common.io.RichOutputFormat;
//
//import java.io.Serializable;
//
///****
// *
// * @Author: zengxiangcai
// * @Date: 2019-12-28 16:12
// *
// ***/
//
//@Getter
//@Setter
//@Slf4j
//public abstract class TsRichOutputFormat<IT> extends RichOutputFormat<IT> implements TsFormat, Serializable {
//
//    private TsTableInfo tableInfo;
//
//    // client provider 提供访问table store 的client
//    private TsClientFactory tsClientFactory;
//
//    public TsRichOutputFormat(TsTableInfo tableInfo) {
//        this.tableInfo = tableInfo;
//        createTsClient();
//    }
//
//    @Override
//    public TsClientFactory createTsClient() {
//        if (tsClientFactory == null) {
//            tsClientFactory = new TsClientFactory(this.tableInfo.getTsProperties());
//        }
//        return tsClientFactory;
//    }
//
//
//    @Override
//    public void closeTsClient() {
//        try {
//            if (tsClientFactory != null) {
//                tsClientFactory.close();
//            }
//        } catch (Exception e) {
//            log.error("close ts Client error: ", e);
//        }
//    }
//
//}
