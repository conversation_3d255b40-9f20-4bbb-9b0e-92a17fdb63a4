package com.yupaopao.risk.insight.flink.connector.ts.serialization;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.streaming.connectors.kafka.internals.KafkaDeserializationSchemaWrapper;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.Date;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-17 20:10
 *
 ***/

@Slf4j
public class MaseratiKafkaDeserializationSchemaWrapper extends KafkaDeserializationSchemaWrapper<String> {

    private final List<String> dateTypeColumns = Arrays.asList("create_time", "update_time", "createTime", "updateTime");

    private DeserializationSchema<String> deserializationSchema;

    private List<String> listenerColumns; //需要监听的变换字段

    public MaseratiKafkaDeserializationSchemaWrapper(DeserializationSchema<String> deserializationSchema, List<String> listenerColumns) {
        super(deserializationSchema);
        this.deserializationSchema = deserializationSchema;
        this.listenerColumns = listenerColumns;
    }

    /***
     * 重写反序列化方法额外读取些 meta info
     * @param record
     * @return
     * @throws Exception
     */
    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<String> out) throws Exception {
        try {

            String value = deserializationSchema.deserialize((byte[]) record.value());
            String key = deserializationSchema.deserialize((byte[]) record.key());
            //解析material结构
            if (StringUtils.isEmpty(value)) {
                return;
            }
            List<Producer> producers = JSON.parseArray(value, Producer.class);
            if (CollectionUtils.isEmpty(producers)) {
                return;
            }

            //将after值取出处理
            Producer producer = producers.get(0);
            //只处理insert,update不处理delete类型数据
            if ("delete".equalsIgnoreCase(producer.getEventType())) {
                return;
            }
            List<Column> columns = producer.getAfterList();
            //对audit 人效计算，只监听status变化了的数据，免得其他一些冗余变换产生影响
            boolean listenedColumnChanged = false;
            if (CollectionUtils.isEmpty(listenerColumns)) {
                //没有配置则所有的都考虑
                listenedColumnChanged = true;
            }

            Map<String, Object> columnMap = new HashMap<>(columns.size());
            for (Column column : columns) {
                //,ck中datetime不能直接插入毫秒数,以字符串插入即可
                if (dateTypeColumns.contains(column.getName().toLowerCase())) {
                    Date dateTime = InsightDateUtils.getDateFromString(column.getValue(),
                            InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
                    columnMap.put(column.getName(), dateTime);
                } else {
                    columnMap.put(column.getName(), column.getValue());
                }

                columnMap.put(column.getName(), column.getValue());
                if (!listenedColumnChanged && listenerColumns != null && listenerColumns.contains(column.getName()) && column.isUpdated()) {
                    listenedColumnChanged = true;
                }

            }
            if (!listenedColumnChanged) {
                //未改变忽略
                return;
            }
            columnMap.put(FlinkConstants.KAFKA_TOPIC_KEY, record.topic());
            columnMap.put(FlinkConstants.KAFKA_DATA_RECEIVED_TIME, record.timestamp());
            String data = JSON.toJSONString(columnMap);
            log.info("topic: {}" + ",message value: {}", record.topic(), data);
            if (StringUtils.isNotEmpty(data)) {
                out.collect(data);
            }

        } catch (Exception e) {
            log.error("deserialize kafka data error maserati: ", e);
        }
    }


    @Getter
    @Setter
    public static class Producer {
        private String schemaName;//库名
        private String tableName;//表名
        private List<Column> beforeList;//修改之前的记录(字段名，字段值，是否被修改)
        private List<Column> afterList;//修改之后的记录
        private String eventType;//监听类型（delete，insert，update）
        private long sequenceId;//时序号（binlog中的执行时间戳）
        private int index;//序号（记录批次内的顺序）
    }


    @Getter
    @Setter
    public static class Column {
        private String name;//字段名
        private String value;//字段值
        private boolean updated;//是否被修改
    }

}
