package com.yupaopao.risk.insight.flink.job.etl.common.format;

import com.dtstack.flinkx.metrics.AccumulatorCollector;
import com.dtstack.flinkx.reader.ByteRateLimiter;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import shade.core.com.google.common.util.concurrent.RateLimiter;

import java.math.BigDecimal;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/****
 * ByteRateLimiter 写入的时候默认限定了1000qps 无法修改，需要做适当的调整，比如clickhouse查询大批量的数据就会显得速度很慢
 * zengxiangcai
 * 2023/6/28 17:37
 * {@link com.dtstack.flinkx.reader.ByteRateLimiter}
 ***/
public class CustomByteRateLimiter {

  private RateLimiter rateLimiter;
  private double expectedBytePerSecond;
  private AccumulatorCollector accumulatorCollector;
  private ScheduledExecutorService scheduledExecutorService;

  public CustomByteRateLimiter(double initRate ,AccumulatorCollector accumulatorCollector,
                               double expectedBytePerSecond) {
    double initialRate = initRate;
    this.rateLimiter = RateLimiter.create(initialRate);
    this.expectedBytePerSecond = expectedBytePerSecond;
    this.accumulatorCollector = accumulatorCollector;
    ThreadFactory threadFactory = (new BasicThreadFactory.Builder()).namingPattern("ByteRateCheckerThread-%d").daemon(true).build();
    this.scheduledExecutorService = new ScheduledThreadPoolExecutor(1, threadFactory);
  }

  public void start() {
    this.scheduledExecutorService.scheduleAtFixedRate(this::updateRate, 0L, 1000L, TimeUnit.MILLISECONDS);
  }

  public void stop() {
    if (this.scheduledExecutorService != null && !this.scheduledExecutorService.isShutdown()) {
      this.scheduledExecutorService.shutdown();
    }

  }

  public void acquire() {
    this.rateLimiter.acquire();
  }

  private void updateRate() {
    long totalBytes = this.accumulatorCollector.getAccumulatorValue("byteRead");
    long thisRecords = this.accumulatorCollector.getLocalAccumulatorValue("numRead");
    long totalRecords = this.accumulatorCollector.getAccumulatorValue("numRead");
    BigDecimal thisWriteRatio = BigDecimal.valueOf(totalRecords == 0L ? 0.0 : (double)thisRecords / (double)totalRecords);
    if (totalRecords > 1000L && totalBytes != 0L && thisWriteRatio.compareTo(BigDecimal.ZERO) != 0) {
      double bpr = (double)totalBytes / (double)totalRecords;
      double permitsPerSecond = this.expectedBytePerSecond / bpr * thisWriteRatio.doubleValue();
      this.rateLimiter.setRate(permitsPerSecond);
    }

  }
}
