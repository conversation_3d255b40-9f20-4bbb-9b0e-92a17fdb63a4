package com.yupaopao.risk.insight.flink.connector.hbase.source;

import com.google.gson.Gson;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import org.apache.flink.api.common.io.InputFormat;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.sources.InputFormatTableSource;
import org.apache.flink.types.Row;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.filter.BinaryComparator;
import org.apache.hadoop.hbase.filter.FilterList;
import org.apache.hadoop.hbase.filter.SingleColumnValueFilter;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-18 15:00
 *
 ***/
public class GraphTableSource extends InputFormatTableSource<Row> implements Serializable {


    private TsTableInfo tsTableInfo;

    private HBaseProperties hBaseProperties;

    private FlinkMetaInfo flinkMetaInfo;

    public GraphTableSource(TsTableInfo tsTableInfo, HBaseProperties hBaseProperties) {
        this.tsTableInfo = tsTableInfo;
        this.hBaseProperties = hBaseProperties;
        this.flinkMetaInfo = tsTableInfo.getFlinkMetaInfo();
    }

    @Override
    public InputFormat<Row, HBaseSplit> getInputFormat() {
        return new HBaseInputFormat(tsTableInfo, hBaseProperties, flinkMetaInfo) {
            @Override
            public HBaseSplit[] createInputSplits(int minNumSplits) throws IOException {
                List<HBaseSplit.BucketSplit> bucketList = new ArrayList<>();
                HBaseSplit.BucketSplit bucketSplit = new HBaseSplit.BucketSplit();
                bucketList.add(bucketSplit);
                HBaseSplit inputSplit = new HBaseSplit(1, new Gson().toJson(bucketList));
                return new HBaseSplit[]{inputSplit};
            }

            @Override
            public FilterList getFilterList() {
                //连通图暂时只分析用户设备
                SingleColumnValueFilter filter = new SingleColumnValueFilter(HBaseConstants.HBASE_FAMILY_KEY,
                        org.apache.hadoop.hbase.util.Bytes.toBytes("label"), CompareOperator.EQUAL,
                        new BinaryComparator(Bytes.toBytes("hasDevice")));

                return new FilterList(filter);
            }
        };
    }

    @Override
    public TableSchema getTableSchema() {
        return flinkMetaInfo.toTableSchema();
    }

    @Override
    public TypeInformation<Row> getReturnType() {
        return getTableSchema().toRowType();
    }
}
