package com.yupaopao.risk.insight.flink.windows.assigner;

import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import com.yupaopao.risk.insight.flink.windows.triggers.FactorTumblingTimeTrigger;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.WindowAssigner;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.joda.time.DateTime;

import java.util.Collection;
import java.util.Collections;

public class FactorTumblingWindowAssigner extends WindowAssigner<FactorCalDetail, TimeWindow> {

    @Override public Collection<TimeWindow> assignWindows(FactorCalDetail element, long timestamp, WindowAssignerContext context) {
        long size = element.getTimeSpan() * 60 * 1000;
        final long now = context.getCurrentProcessingTime();
        long start = getWindowStart(now, element.getTimeSpan());
        return Collections.singletonList(new TimeWindow(start, start + size));
    }

    @Override public Trigger<FactorCalDetail, TimeWindow> getDefaultTrigger(StreamExecutionEnvironment env) {
        return FactorTumblingTimeTrigger.create();
    }

    @Override public TypeSerializer<TimeWindow> getWindowSerializer(ExecutionConfig executionConfig) {
        return new TimeWindow.Serializer();
    }

    @Override public boolean isEventTime() {
        return false;
    }

    private long getWindowStart(long now, long timeSpan) {
        long start;
        DateTime dateTime = new DateTime(now);
        if (timeSpan < 60) {
            // 分钟级别，使用自带窗口对齐
            return TimeWindow.getWindowStartWithOffset(now, 0, timeSpan * 60 * 1000);
        } else if (timeSpan < 720) {
            // 小时级别，窗口从当前小时0分钟开始
            dateTime = dateTime.withSecondOfMinute(0).withMillisOfSecond(0).withMinuteOfHour(0);
        } else if (timeSpan < 1440) {
            // 半天级别，窗口从当前小时0分钟开始
            dateTime = dateTime.withSecondOfMinute(0).withMillisOfSecond(0).withMinuteOfHour(0);
            dateTime = dateTime.getHourOfDay() < 12 ? dateTime.withHourOfDay(0) : dateTime.withHourOfDay(12);
        } else {
            // 天级，窗口从当天0点开始
            dateTime = dateTime.withMillisOfDay(0);
        }

        start = dateTime.getMillis();
        return start;
    }
}
