package com.yupaopao.risk.insight.flink.connector.clickhouse.sink;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.CommonClickHouseDataType;
import com.yupaopao.risk.insight.flink.connector.clickhouse.DataTransferUtils;
import com.yupaopao.risk.insight.flink.connector.scheduler.IntervalFlushSchedulerTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;

/****
 * zengxiangcai
 * 2022/5/24 12:27
 ***/

@Slf4j
public class CkColumnRepairMapFunction extends RichMapFunction<String, String> implements Runnable, AutoCloseable {
    private transient ScheduledThreadPoolExecutor scheduler = null;

    private ClickHouseProperties ckProperties;
    private String tableName;
    private Map<String, String> ckColumn;
    private transient ClickHouseConnection connection;

    private Map<String, Integer> errorCount;

    public CkColumnRepairMapFunction(String tableName, ClickHouseProperties properties) {
        this.tableName = tableName;
        this.ckProperties = properties;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        if (scheduler == null || scheduler.isShutdown()) {
            this.scheduler =
                    IntervalFlushSchedulerTools.createScheduler("fetch-columns-" + tableName,
                            1000 * 2 * 60, this);
        }

        connection = ClickHouseUtil.createConnection(this.ckProperties);

        if (errorCount == null) {
            errorCount = new ConcurrentHashMap<>();
        }
        fetchColumns();
    }

    @Override
    public String map(String value) throws Exception {
        //fixValue
        JSONObject valueJson = JSONObject.parseObject(value);
        List<String> removeColumns = new ArrayList<>(1);
        Map<String, String> errorKeyMap = new HashMap<>();
        //记录历史已经有问题无法修复字段
        String ignoreColumns = ApolloProperties.getConfigStr("ck.fix.ignore.columns." + tableName);
        List<String> ignoreList = new ArrayList();
        if (StringUtils.isNotEmpty(ignoreColumns)) {
            ignoreList = Arrays.asList(ignoreColumns.split(","));
        }
        for (String key : valueJson.keySet()) {
            String clickHouseType = CommonClickHouseDataType.getClickHouseType(valueJson.get(key));
            if (StringUtils.isBlank(clickHouseType) || isSpecialKey(clickHouseType)) {
                continue;
            }
            Object originalValue = valueJson.get(key);
            //过滤空数据
            if (DataTransferUtils.isEmptyObject(originalValue)) {
                removeColumns.add(key);
                continue;
            }
            String ckType = ckColumn.get(key);
            if (ckColumn.containsKey(key) && !isDataTypeMatch(ckType, originalValue)) {
//                    log.warn("type error, ckType:{}, dataType:{}, data: {}", ckColumn.get(key), clickHouseType, valueJson.get(key));
                // 对类型不同的数据，先尝试字段转换，无法转换过滤该字段
                //修复数据暂时直接过滤已知问题字段
                Object fixedValue = null;
//                if (!key.equals("data_deviceDetail_date") && !key.equals("data_receiveUid")) {
                if (!ignoreList.contains(key)) {
                    fixedValue = fixValue(ckType, originalValue);
                }


                if (fixedValue == null) {
                    //无法修复，删除：
//                    logErrorColumn(key, value, ckType);
                    errorKeyMap.put(key, ckType);
                    removeColumns.add(key);
                } else {
                    valueJson.put(key, fixedValue);
                }
            }

        }
        for (String col : removeColumns) {
            valueJson.remove(col);
        }

        String returnJson = valueJson.toJSONString();
        if (!errorKeyMap.isEmpty()) {
            logErrorColumn(JSON.toJSONString(errorKeyMap), value, returnJson);
            errorKeyMap.clear();
        }
        return returnJson;
    }


    @Override
    public void close() throws Exception {
        super.close();
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
        if (connection != null) {
            connection.close();
        }
    }

    @Override
    public void run() {
        fetchColumns();
    }

    private void fetchColumns() {
        Map<String, String> curCkMap = ClickHouseUtil.fetchColumns(tableName, connection);
        if (curCkMap != null && !curCkMap.isEmpty()) {
            this.ckColumn = curCkMap;
        }
    }


    private void logErrorColumn(String keyMap, String value, String returnJson) {
        String columnKey = tableName + "#" + keyMap;
        if (errorCount.containsKey(columnKey)) {
            errorCount.put(columnKey, errorCount.get(columnKey) + 1);
        } else {
            errorCount.put(columnKey, 1);
        }
        if (errorCount.get(columnKey) <= 100) {
            log.error("column type error: {} , originalValue: {}, returnValue : {}", columnKey, value, returnJson);
        }
    }

    //确保数据类型不match的时候才调用此类数据
    private Object fixValue(String ckType, Object value) {
        try {
            //ck中几种特殊数据可以fix
            String lowercaseType = ckType.toLowerCase();
            if (lowercaseType.contains("string")) {
                return value.toString();
            }
            if (lowercaseType.contains("int") && value instanceof Number) {
                if (value.toString().contains(".")) {
                    //float
                    return Double.valueOf(value.toString()).longValue();
                } else {
                    //其他不匹配
                    return null;
                }
            }
            if (value instanceof String) {
                if (StringUtils.isEmpty(value.toString())) {
                    return null;
                }
                String reg = "^(-?\\d+)(\\.\\d+)?$";
                boolean isNumber = value.toString().matches(reg);
                if (lowercaseType.contains("int") && isNumber) {
                    return Long.valueOf(value.toString());
                }
                if (lowercaseType.contains("float32") && isNumber) {
                    return Float.valueOf(value.toString());
                } else if (lowercaseType.contains("float64") && isNumber) {
                    return Double.valueOf(value.toString());
                }
            }

            if (lowercaseType.contains("datetime") && value instanceof Number && !value.toString().contains(".")) {
                //非浮点型的整数可以insert
                return Long.valueOf(value.toString()).toString();
            }

            //历史字段decimal(38, 4)处理
            if (lowercaseType.contains("decimal(38, 4)") && value instanceof BigDecimal) {
                return ((BigDecimal) value).setScale(4, RoundingMode.HALF_UP);
            }

            return null;
        } catch (Exception e) {
            log.error("fixed error: ckType: " + ckType + " value: " + value, e);
            return null;
        }
    }

    private boolean isDataTypeMatch(String ckType, Object value) {
        String lowercaseType = ckType.toLowerCase();
        if (lowercaseType.contains("date") && value instanceof String) {
            return true;
        }
        if (lowercaseType.contains("string") && value instanceof String) {
            return true;
        }
        if (lowercaseType.contains("int") && value instanceof Number && !value.toString().contains(".")) {
            return true;
        }
        if (lowercaseType.contains("float") && value instanceof Number) {
            return true;
        }
        return false;
    }

    private boolean isSpecialKey(String key) {
        return key.contains("\\");
    }

}
