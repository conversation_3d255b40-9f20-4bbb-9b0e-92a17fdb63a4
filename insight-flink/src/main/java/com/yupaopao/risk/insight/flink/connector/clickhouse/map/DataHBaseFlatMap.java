package com.yupaopao.risk.insight.flink.connector.clickhouse.map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.Location;
import com.yupaopao.risk.insight.common.property.connection.OSSProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.constants.CityCodeConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;

import java.io.*;
import java.net.InetAddress;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by Avalon on 2020/3/11 14:03
 */
@Slf4j
public class DataHBaseFlatMap extends RichFlatMapFunction<String, String> implements Serializable {

    private final static FastDateFormat fastDateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    private LongCounter processCount = new LongCounter();

    private DatabaseReader databaseReader;

    private OSS ossClient;
    private OSSObject ossObject;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        getRuntimeContext().addAccumulator("processCount", this.processCount);

//        databaseReader = new DatabaseReader.Builder(readGeoLiteFromOSS()).build();
        databaseReader = new DatabaseReader.Builder(readGeoLiteFromNFS()).build();
    }

    private InputStream readGeoLiteFromOSS() {
        OSSProperties ossProperties = OSSProperties.getProperties(PropertyType.OSS);
        ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(),
                ossProperties.getAccessKeySecret());
        String objectName = "risk-flink/geolite/GeoLite2-City.mmdb";
        ossObject = ossClient.getObject(ossProperties.getBucket(), objectName);
        return ossObject.getObjectContent();
    }

    private InputStream readGeoLiteFromNFS() throws Exception {
        File database = new File("/flink/geolite/GeoLite2-City.mmdb");
        return new BufferedInputStream(new FileInputStream(database));
    }

    @Override
    public void flatMap(String value, Collector<String> out) throws Exception {
        JSONObject tmp = JSON.parseObject(value);
        tmp.remove("full_text");
        tmp.put("createdAt", fastDateFormat.format(tmp.getDate("createdAt")));

        for (Map.Entry<String, Object> entry : tmp.entrySet()) {
            if (entry.getValue() instanceof Number) {
                entry.setValue(((Number) entry.getValue()).longValue());
                continue;
            }
            if (entry.getValue() instanceof List) {
                entry.setValue(JSONObject.toJSONString(entry.getValue()));
                continue;
            }
            if (entry.getValue() instanceof Boolean) {
                Boolean bool = (Boolean) entry.getValue();
                entry.setValue(bool ? 1 : 0);
                continue;
            }
        }

        try {
            Location clientIp = getCoordinate(tmp.getString("clientIp").trim());
            tmp.put("data_clientIpDetail_latitude", clientIp.getLatitude());
            tmp.put("data_clientIpDetail_longitude", clientIp.getLongitude());
        } catch (Exception e) {
            log.info("{} 不支持经纬度转换", tmp.getString("clientIp"));
        }

        Object province = tmp.get("data_clientIpDetail_province");
        Object country = tmp.get("data_clientIpDetail_country");

        if (Objects.nonNull(province)) {
            tmp.put("data_clientIpDetail_provinceCode", CityCodeConstants.PROVINCE_MAP.getOrDefault(province.toString(), 0));
        }
        if (Objects.nonNull(country)) {
            tmp.put("data_clientIpDetail_countryCode", CityCodeConstants.COUNTRY_MAP.getOrDefault(country.toString(), 0));
        }

        processCount.add(1);
        out.collect(tmp.toJSONString());
    }

    private Location getCoordinate(String ip) throws Exception {
        InetAddress ipAddress = InetAddress.getByName(ip);
        CityResponse response = databaseReader.city(ipAddress);
        return response.getLocation();
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (ossObject != null) {
            ossObject.close();
        }
        if (ossClient != null) {
            ossClient.shutdown();
        }
    }
}
