package com.yupaopao.risk.insight.flink.job.etl.common.map;

import com.yupaopao.risk.insight.flink.job.etl.common.connect.redis.core.RedisConfigKey;
import com.yupaopao.risk.insight.flink.job.etl.common.options.MapperConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.io.IOException;
import java.util.*;

/**
 * Copyright (C), 2022, jimmy
 *
 * <AUTHOR>
 * @desc ElasticSearchMapper
 * @date 2022/5/16
 */
@Slf4j
public class RedisMapper extends BaseMapFunction {
    protected final Logger LOG = LoggerFactory.getLogger(getClass());

    private String host;
    private String password;
    private int port;
    private String script;
    private int database;

    private int maxIdle;
    private int maxTotal;
    private int minIdle;
    private int timeout;

    private String scriptId = "";

    private List<String> args = new ArrayList<>();

    private List<String> keys = new ArrayList<>();

    private transient JedisPool jedisPool;

    LongCounter readerCounter;

    LongCounter writerCounter;

    public RedisMapper(MapperConfig config) throws Exception {
        super(config);
        host = config.getStringVal(RedisConfigKey.KEY_HOST);
        port = config.getIntVal(RedisConfigKey.KEY_PORT, 6379);
        password = config.getStringVal(RedisConfigKey.KEY_PASSWORD);
        script = config.getStringVal(RedisConfigKey.KEY_SCRIPT);
        database = config.getIntVal(RedisConfigKey.KEY_DATABASE, 5);

        maxIdle = config.getIntVal(RedisConfigKey.KEY_MAX_IDLE, 5);
        maxTotal = config.getIntVal(RedisConfigKey.KEY_MAX_TOTAL, 70);
        minIdle = config.getIntVal(RedisConfigKey.KEY_MIN_IDLE, 1);
        timeout = config.getIntVal(RedisConfigKey.KEY_TIMEOUT, 2000);

        args.addAll(Arrays.asList(config.getStringVal("args").split(",")));
        keys.addAll(Arrays.asList(config.getStringVal("keys").split(",")));

    }

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMinIdle(minIdle);
        jedisPool = new JedisPool(poolConfig,
            host,
            port,
            timeout,
            password,
            database);
        if (StringUtils.isNotBlank(script)) {
            scriptId = jedisPool.getResource().scriptLoad(script);
        }
        readerCounter = context.getLongCounter("reader_count_" + id);
        writerCounter = context.getLongCounter("writer_count_" + id);

        mapperMetric.addMetric("reader_count_" + id, readerCounter);
        mapperMetric.addMetric("writer_count_" + id, writerCounter);
    }

    @Override public void flatMap(Map<String, Object> value, Collector<Map<String, Object>> out) throws Exception {
        super.flatMap(value, out);
        if (isDebug && value.containsKey(KEY_DEBUG)) {
            return;
        }
        readerCounter.add(1);
        List<String> argList = new ArrayList<>();
        for (String key : this.args) {
            if (StringUtils.isBlank(key)) {
                continue;
            }
            Object keyValue = value.get(key);
            if (Objects.isNull(keyValue) || keyValue.toString().length() == 0) {
                LOG.info("redis 数据处理存在空值，返回");
                return;
            }
            if (keyValue instanceof List) {
                ((List) keyValue).forEach(p-> {
                    argList.add(p.toString());
                });
            } else {
                argList.add(keyValue.toString());
            }
        }

        List<String> keyList = new ArrayList<>();
        for (String key : this.keys) {
            if (StringUtils.isBlank(key)) {
                continue;
            }
            Object keyValue = value.get(key);
            if (Objects.isNull(keyValue) || keyValue.toString().length() == 0) {
                LOG.info("redis 数据处理存在空值，返回");
                return;
            }
            if (keyValue instanceof List) {
                ((List) keyValue).forEach(p-> {
                    keyList.add(p.toString());
                });
            } else {
                keyList.add(keyValue.toString());
            }
        }

        try (Jedis jedis = jedisPool.getResource()) {
            Object eval = jedis.evalsha(scriptId, keyList, argList);
            if (!Objects.isNull(eval)) {
                value.put("redisResult", eval);
                writerCounter.add(1);
                out.collect(value);
            }
        } catch (Exception e) {
            throw new RuntimeException("redis 查询出错", e);
        }
    }

    @Override
    public void close() throws IOException {
        if(jedisPool != null) {
            jedisPool.close();
            jedisPool = null;
        }
    }
}
