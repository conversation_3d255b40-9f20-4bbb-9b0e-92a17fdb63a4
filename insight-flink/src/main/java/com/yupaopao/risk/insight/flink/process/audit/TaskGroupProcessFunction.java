package com.yupaopao.risk.insight.flink.process.audit;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.flink.bean.audit.MxTask;
import com.yupaopao.risk.insight.flink.bean.audit.MxTaskGroup;
import com.yupaopao.risk.insight.flink.bean.audit.PerformanceMxTask;
import com.yupaopao.risk.insight.flink.bean.audit.PerformanceMxTaskGroup;
import com.yupaopao.risk.insight.flink.job.audit.SideOutputConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.util.ArrayList;
import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-04 12:12
 * 任务组计算指标
 ***/
public class TaskGroupProcessFunction extends KeyedProcessFunction<String, MxTaskGroup, String> {

    private ListState<MxTaskGroup> taskGroupProcessListSate;

    @Override
    public void open(Configuration parameters) throws Exception {
        taskGroupProcessListSate = getRuntimeContext().getListState(new ListStateDescriptor<>("taskGroupProcessList",
                MxTaskGroup.class));
    }

    @Override
    public void processElement(MxTaskGroup value, Context ctx, Collector<String> out) throws Exception {
        if (!value.finished()) {
            addToState(value);
            return;
        }
        //已完成状态数据
        List<MxTaskGroup> allStateElements = getAllSateElements();
        allStateElements.add(value);

        //处理完成, 计算所有指标
        PerformanceMxTaskGroup resultTask = PerformanceCalculator.calculateTaskGroup(allStateElements);
        //无batchId,直接输出到sink
        String taskSinkJson = JSON.toJSONString(resultTask);
        out.collect(taskSinkJson);
        taskGroupProcessListSate.clear();
    }


    private void addToState(MxTaskGroup mxTask) throws Exception {
        //防止数据重复，如果当前记录和上一条记录状态相同直接忽略
        MxTaskGroup last = null;
        for (MxTaskGroup task : taskGroupProcessListSate.get()) {
            last = task;
        }
        if (last != null && last.getState().equals(mxTask)) {
            return;
        }
        taskGroupProcessListSate.add(mxTask);
    }

    private List<MxTaskGroup> getAllSateElements() throws Exception {
        List<MxTaskGroup> mxTasks = new ArrayList<>();
        for (MxTaskGroup task : taskGroupProcessListSate.get()) {
            mxTasks.add(task);
        }
        return mxTasks;
    }
}
