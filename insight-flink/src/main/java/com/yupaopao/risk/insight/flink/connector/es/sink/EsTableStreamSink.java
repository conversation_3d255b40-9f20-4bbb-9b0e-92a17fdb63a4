package com.yupaopao.risk.insight.flink.connector.es.sink;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.sinks.AppendStreamTableSink;
import org.apache.flink.table.sinks.TableSink;
import org.apache.flink.types.Row;

/**
 * Copyright (C), 2019, jimmy
 *
 * <AUTHOR>
 * @desc EsStreamSink
 * @date 2020/2/27
 */
public class EsTableStreamSink implements AppendStreamTableSink<Row> {
    private EsProperties esProperties;

    private FlinkMetaInfo flinkMetaInfo;

    public EsTableStreamSink(EsProperties esProperties, FlinkMetaInfo flinkMetaInfo) {
        this.esProperties = esProperties;
        this.flinkMetaInfo = flinkMetaInfo;
    }

    @Override public DataStreamSink<?> consumeDataStream(DataStream<Row> dataStream) {
        EsStreamSinkBase<JSONObject> esStreamSinkBase = new EsStreamSinkBase<>(esProperties);
        String[] fieldNames = flinkMetaInfo.getFieldNames();
        return dataStream.map((MapFunction<Row, JSONObject>) row -> {
            int rowSize = row.getArity();
            JSONObject result = new JSONObject();
            for (int i = 0; i < rowSize; i++) {
                result.put(fieldNames[i], row.getField(i));
            }
            return result;
        }).addSink(esStreamSinkBase).name("es sink");
    }

    @Override
    public TableSink<Row> configure(String[] fieldNames, TypeInformation<?>[] fieldTypes) {
        return new EsTableStreamSink(esProperties, flinkMetaInfo);
    }

    @Override
    public TableSchema getTableSchema() {
        return flinkMetaInfo.toTableSchema();
    }

    @Override
    public TypeInformation<Row> getOutputType() {
        return flinkMetaInfo.toTableSchema().toRowType();
    }

}
