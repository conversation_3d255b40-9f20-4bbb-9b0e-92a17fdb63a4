package com.yupaopao.risk.insight.flink.enums;

import org.apache.commons.lang.StringUtils;

public enum DevicePortraitEnum {

    LEVEL1_EMULATOR("虚拟设备","emulator", 90),
    LEVEL1_ALTERED("篡改设备","altered", 90),
    LEVEL1_MULTI_BOX("多开设备","multiBoxing", 90),
    LEVEL1_FAKER("伪造设备","faker", 90),
    LEVEL1_FARMER("农场设备","farmer", 90),
    LEVEL1_SUSPICIOUS("可疑设备","risk", 70),

    LEVEL2_EMULATOR_PC("PC模拟器","emulatorPC", 90),
    LEVEL2_EMULATOR_CLOUD_PHONE("云手机","emulatorCloudPhone", 90),
    LEVEL2_ALTERED_DATA("篡改信息","alteredData", 90),
    LEVEL2_MULTI_BOX_OS("自带多开","multiBoxingByOs", 90),
    LEVEL2_MULTI_BOX_APP("工具多开","multiBoxingByApp", 90),
    LEVEL2_FAKER_ABSENT_DATA("缺失数据","fakerDataAbsent", 90),
    LEVEL2_FAKER_DATA("伪造数据","fakerDataFake", 90),
    LEVEL2_FARMER_ALTERED("篡改设备农场","farmerAltered", 90),
    LEVEL2_FARMER_NEW_DEVICE("设备农场","farmerNewDevice", 90),
    LEVEL2_FARMER_DEBUG_GABLE("调试设备农场","farmerDebuggable", 90),
    LEVEL2_FARMER_MONKEY("机器设备农场","farmerMonkey", 90),
    LEVEL2_FARMER_RESET("重置设备农场","farmerReset", 90),
    LEVEL2_FARMER_VPN("VPN设备农场","farmerVpn", 90),
    LEVEL2_FARMER_SIM_NOT_READY("无卡设备农场","farmerSimNotReady", 90),
    LEVEL2_SUSPICIOUS_ROOT("ROOT设备","riskRoot", 70),
    LEVEL2_SUSPICIOUS_SIM_NOT_READY("SIM卡异常","riskSimNotReady", 70),
    LEVEL2_SUSPICIOUS_DEBUG("开启调式模式","riskDebuggable", 70),
    LEVEL2_SUSPICIOUS_VPN("使用VPN","riskVpn", 70),
    LEVEL2_SUSPICIOUS_MONKEY_APP("精灵工具","riskMonkeyApp", 70)
    ;


    private String tagName;
    private String tagCode;
    private Integer score;

    DevicePortraitEnum(String tagName, String tagCode, Integer score){
        this.tagName = tagName;
        this.tagCode = tagCode;
        this.score = score;
    }


    public static DevicePortraitEnum getEnumByCode(String code)throws IllegalAccessException{
        if (StringUtils.isEmpty(code)){
            throw new IllegalAccessException("没有这个标签:"+code);
        }
        for (DevicePortraitEnum value : values()) {
            if (value.tagCode.equals(code)){
                return value;
            }
        }
        return null;
    }

    public static DevicePortraitEnum getEnumByName(String tagName)throws IllegalAccessException{
        if (StringUtils.isEmpty(tagName)){
            throw new IllegalAccessException("没有这个标签:"+tagName);
        }
        for (DevicePortraitEnum value : values()) {
            if (value.tagName.equals(tagName)){
                return value;
            }
        }
        return null;
    }

}
