package com.yupaopao.risk.insight.flink.job.cep;

import com.yupaopao.risk.insight.flink.connector.kafka.sink.serialization.KafkaObjSerializationSchema;
import com.yupaopao.risk.insight.common.property.PropertiesFactory;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;

import java.util.Map;
import java.util.Properties;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-17 15:26
 *
 ***/
public class MatchResultHandler {

    public static FlinkKafkaProducer<Map<String, Object>> createKafkaSink() {
        KafkaProperties kafkaProperties = (KafkaProperties) PropertiesFactory.loadProperty(PropertyType.KAFKA_RISK_ALI);
        Properties kafkaProps = kafkaProperties.getProperties();
        kafkaProps.put("transaction.timeout.ms",1000*60*5+"");
        String matchResultTopic = "RISK-INSIGHT-CEP-MATCH";

        FlinkKafkaProducer<Map<String, Object>> matchResultProducer = new FlinkKafkaProducer<Map<String, Object>>(
                matchResultTopic,         // target topic
                new KafkaObjSerializationSchema(matchResultTopic),   // serialization schema
                kafkaProps,
                FlinkKafkaProducer.Semantic.AT_LEAST_ONCE
        );
        return matchResultProducer;
    }
}
