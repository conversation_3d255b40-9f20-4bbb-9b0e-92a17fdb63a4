package com.yupaopao.risk.insight.flink.windows.process;


import com.alibaba.fastjson.JSONObject;
import com.google.common.hash.Hasher;
import com.google.common.hash.Hashing;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

import java.nio.charset.StandardCharsets;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc AccumulateSignProcess
 * @date 2021/8/26
 */
public class AccumulateSignProcess extends ProcessFunction<JSONObject, JSONObject> {
    private static final long serialVersionUID = 7581207639300316193L;

    private final String SALT = "bixintech";

    @Override public void processElement(JSONObject value, Context ctx, Collector<JSONObject> out) throws Exception {
        String headers = value.getString("src_headers").trim();
        String substring = headers.substring(1, headers.length() - 1);
        String[] split = substring.split(",");
        String sign = "", trace = "";
        for (String map : split) {
            String[] item = map.trim().split("=");
            if ("x-sign".equals(item[0]) && item.length > 1) {
                sign = item[1];
            }
            if ("x-biz-trace".equals(item[0]) && item.length > 1) {
                trace = item[1];
            }
        }
        if (StringUtils.isBlank(trace) || StringUtils.isBlank(sign)) {
            return;
        }
        String md5 = Hashing.md5().newHasher().putString(sign + SALT, StandardCharsets.UTF_8).hash().toString();
        if (!trace.equals(md5 + "00")) {
            out.collect(value);
        }
    }
}
