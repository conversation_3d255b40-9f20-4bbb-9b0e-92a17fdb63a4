/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yupaopao.risk.insight.flink.job.etl.common.connect.odps.reader;

import com.aliyun.odps.Column;
import com.aliyun.odps.Odps;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.data.RecordReader;
import com.aliyun.odps.tunnel.TableTunnel;
import com.dtstack.flinkx.reader.MetaColumn;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.odps.core.OdpsUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.format.BaseRichInputFormat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.flink.core.io.InputSplit;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * The odps implementation of InputFormat
 *
 * Company: www.dtstack.com
 * <AUTHOR>
 */
public class OdpsInputFormat extends BaseRichInputFormat {

    protected List<MetaColumn> metaColumns;

    protected String partition;

    protected Integer threadNum;

    protected String projectName;

    protected String tableName;

    protected boolean compress = false;

    protected Map<String,String> odpsConfig;

    protected String tunnelServer;

    protected long startIndex;

    protected long stepCount;

    private transient Odps odps;

    private transient RecordReader recordReader;

    private final LinkedBlockingQueue<Record> cacheQueue = new LinkedBlockingQueue<>(2048);

    private final List<OdpsDownloadThread> threadList = new ArrayList<>();

    @Override
    public void openInputFormat() throws IOException {
        super.openInputFormat();

        odps = OdpsUtil.initOdps(odpsConfig);
    }

    @Override
    public InputSplit[] createInputSplitsInternal(int adviceNum) throws IOException {
        Odps odps = OdpsUtil.initOdps(odpsConfig);
        TableTunnel.DownloadSession session;
        if(StringUtils.isNotBlank(partition)) {
            session = OdpsUtil.createMasterSessionForPartitionedTable(odps, tunnelServer, projectName, tableName, partition);
        } else {
            session = OdpsUtil.createMasterSessionForNonPartitionedTable(odps, tunnelServer, projectName, tableName);
        }

        return split(session, adviceNum);
    }

    private OdpsInputSplit[] split(final TableTunnel.DownloadSession session, int adviceNum) {
        List<OdpsInputSplit> splits = new ArrayList<OdpsInputSplit>();

        long count = session.getRecordCount();

        List<Pair<Long, Long>> splitResult = OdpsUtil.splitRecordCount(count, adviceNum);

        for (Pair<Long, Long> pair : splitResult) {
            long startIndex = pair.getLeft();
            long stepCount = pair.getRight();
            OdpsInputSplit split = new OdpsInputSplit(session.getId(), startIndex, stepCount);
            if(startIndex < stepCount) {
                splits.add(split);
            }
        }

        return splits.toArray(new OdpsInputSplit[0]);
    }

    @Override
    public void openInternal(InputSplit inputSplit) throws IOException {
        OdpsInputSplit split = (OdpsInputSplit) inputSplit;
        String sessionId = split.getSessionId();
        startIndex = split.getStartIndex();
        stepCount = split.getStepCount();

        TableTunnel.DownloadSession downloadSession;
        if(StringUtils.isNotBlank(partition)) {
            downloadSession = OdpsUtil.getSlaveSessionForPartitionedTable(odps, sessionId, tunnelServer, projectName, tableName, partition);
        } else {
            downloadSession = OdpsUtil.getSlaveSessionForNonPartitionedTable(odps, sessionId, tunnelServer, projectName, tableName);
        }

        long count = downloadSession.getRecordCount();

        long step = count / threadNum;
        for (int i = 0; i < threadNum; i++) {
            String threadName = "odps-reader" + split.getSplitNumber() + "-bucket-" + i;
            LOG.info("data: {}  {}" , step * i, i == threadNum - 1 ? count - i * step : step);
            RecordReader recordReader = OdpsUtil.getRecordReader(downloadSession, step * i, i == threadNum - 1 ? count - i * step : step, compress);
            OdpsDownloadThread thread = new OdpsDownloadThread(threadName, recordReader, cacheQueue);
            threadList.add(thread);
            thread.start();
        }
    }

    @Override
    public boolean reachedEnd() throws IOException {
        threadList.removeIf(p->!p.isSurvival());
        return CollectionUtils.isEmpty(threadList) && CollectionUtils.isEmpty(cacheQueue);
    }

    @Override
    public Map<String, Object> nextRecordInternal(Map<String, Object> data) throws IOException {
        Record record = null;
        try {
            record = cacheQueue.poll(4, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            LOG.warn("read next row error ", e);
        }
        if (record == null || record.getColumnCount() == 0) {
            return new HashMap<>();
        }
        data = new HashMap<>(record.getColumnCount());
        Column[] columns = record.getColumns();
        for (int i = 0; i < record.getColumnCount(); i++) {
            Object val;
            String name = columns[i].getName();
            val = record.get(name);
            if (val instanceof byte[]) {
                val = new String((byte[]) val, StandardCharsets.UTF_8);
            }
            data.put(name, val);
        }

        return data;
    }

    @Override
    public void closeInternal() throws IOException {
        if (recordReader != null) {
            recordReader.close();
        }
        for (OdpsDownloadThread t : threadList) {
            t.interrupt();
        }
        for (OdpsDownloadThread t : threadList) {
            try {
                t.join();
            } catch (InterruptedException e) {
                LOG.error("close error", e);
            }
        }
    }

}
