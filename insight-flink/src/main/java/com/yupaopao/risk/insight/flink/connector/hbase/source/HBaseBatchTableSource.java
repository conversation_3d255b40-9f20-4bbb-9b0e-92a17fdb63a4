package com.yupaopao.risk.insight.flink.connector.hbase.source;

import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import org.apache.flink.api.common.io.InputFormat;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.sources.InputFormatTableSource;
import org.apache.flink.types.Row;

import java.io.Serializable;

public class HBaseBatchTableSource extends InputFormatTableSource<Row> implements Serializable {

    //flink 中类列的类型
    private FlinkMetaInfo flinkMetaInfo;
    //tablestore连接相关的信息
    private TsTableInfo sourceTable;

    private HBaseProperties hBaseProperties;

    public HBaseBatchTableSource(TsTableInfo tsTableInfo,HBaseProperties hBaseProperties) {
        sourceTable = tsTableInfo;
        this.hBaseProperties = hBaseProperties;
        flinkMetaInfo = sourceTable.getFlinkMetaInfo();
    }

    @Override public InputFormat<Row, HBaseSplit> getInputFormat() {
        return new HBaseInputFormat(sourceTable, hBaseProperties, flinkMetaInfo);
    }

    @Override
    public TableSchema getTableSchema() {
        return flinkMetaInfo.toTableSchema();
    }

    @Override
    public TypeInformation<Row> getReturnType() {
        return flinkMetaInfo.toTableSchema().toRowType();
    }
}
