package com.yupaopao.risk.insight.flink.job.test;

import com.yupaopao.risk.insight.flink.connector.hbase.sink.HBaseBaseOutputFormat;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.client.Put;

import java.io.IOException;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-10 11:07
 *
 ***/

@Slf4j
public class HBaseTransferOutput extends HBaseBaseOutputFormat<Put> {

    public HBaseTransferOutput(String tableName, HBaseProperties hBaseProperties) {
        super(tableName, hBaseProperties);
    }


    @Override
    public void writeRecord(Put put) throws IOException {
        if (put == null) {
            return;
        }
        try {
            this.getMutator().mutate(put);
            if (canFlush()) {
                flush();
            }
        } catch (Exception e) {
            log.error("write record error: ,record: " + put, e);
        }
    }
}
