/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.yupaopao.risk.insight.flink.job.etl.common.element.column;


import com.yupaopao.risk.insight.flink.job.etl.common.element.AbstractBaseColumn;
import org.apache.flink.util.FlinkRuntimeException;

import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;

/**
 * Date: 2021/04/27 Company: www.dtstack.com
 *
 * <AUTHOR>
 */
public class BytesColumn extends AbstractBaseColumn {
    private String encoding = StandardCharsets.UTF_8.name();

    public BytesColumn(byte[] data) {
        super(data);
    }

    public BytesColumn(byte[] data, String encoding) {
        super(data);
        this.encoding = encoding;
    }

    @Override
    public Boolean asBoolean() {
        if (null == data) {
            return null;
        }
        throw new FlinkRuntimeException(String.format("Byte [Boolean] can not cast to %s.", this.asString()));
    }

    @Override
    public byte[] asBytes() {
        if (null == data) {
            return null;
        }
        return (byte[]) data;
    }

    @Override
    public String asString() {
        if (null == data) {
            return null;
        }
        return new String((byte[]) data, Charset.forName(encoding));
    }

    @Override
    public BigDecimal asBigDecimal() {
        if (null == data) {
            return null;
        }
        throw new FlinkRuntimeException(String.format("Byte [BigDecimal] can not cast to %s.", this.asString()));
    }

    @Override
    public Timestamp asTimestamp() {
        if (null == data) {
            return null;
        }
        throw new FlinkRuntimeException(String.format("Byte [Timestamp] can not cast to %s.", this.asString()));
    }
}
