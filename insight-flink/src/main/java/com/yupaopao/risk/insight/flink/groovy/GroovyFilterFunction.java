package com.yupaopao.risk.insight.flink.groovy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.groovy.GroovyCommonExecutor;
import com.yupaopao.risk.insight.common.groovy.GroovyExecutor;
import groovy.lang.Binding;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.RichFilterFunction;

import java.util.*;


/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-08-06 15:52
 *
 ***/

@Slf4j
public class GroovyFilterFunction extends RichFilterFunction<String> {

    public static void main(String[] args) throws Exception {
        GroovyFilterFunction f = new GroovyFilterFunction("type!=null &&  (type == 'AUDIO_STREAM' || type == " +
                "'VIDEO_STREAM')","type");
       String json ="{\n" +
               "    \"resultTime\": 1639278947817,\n" +
               "    \"traceId\": \"b4d2f78f6a8c4dd3b6df438bb3858c51\",\n" +
               "    \"kafkaTopic\": \"RISK-CONTENT-HIT-LOG\",\n" +
               "    \"result_features\":\n" +
               "    {},\n" +
               "    \"result_errorCode\": 0,\n" +
               "    \"eventCode\": \"user-login\",\n" +
               "    \"createdAt\": \"2021-12-12 11:15:47\",\n" +
               "    \"attrStatus\": \"ENABLE\",\n" +
               "    \"action\": \"algorithmModel\",\n" +
               "    \"result_model\": \"risk_ps_v4\",\n" +
               "    \"result_score\": 0.03706292435526848,\n" +
               "    \"attrName\": \"modelSimulatorCheck\",\n" +
               "    \"result_deviceModel\": \"iPhone13,4\",\n" +
               "    \"result_detectResultCode\": 1\n" +
               "}";

        System.err.println(f.filter(json));;
    }

    private String filterScript;

    private List<String> dependencies;

    public GroovyFilterFunction(String filterScript, String inputDependencies) {
        this.filterScript = filterScript;
        if (StringUtils.isNotEmpty(inputDependencies)) {
            String[] dependencyArr = inputDependencies.split("#");
            this.dependencies = new ArrayList<>(Arrays.asList(dependencyArr));
        }
    }

    @Override
    public boolean filter(String value) throws Exception {
        if (StringUtils.isEmpty(filterScript) || CollectionUtils.isEmpty(dependencies)) {
            return true;
        }
        if (StringUtils.isEmpty(value)) {
            return false;
        }
        try {
            JSONObject obj = JSONObject.parseObject(value);
            Map<String, Object> data = new HashMap<>();
            for (String dep : dependencies) {
                data.put(dep, obj.get(dep));
            }
            Object result = GroovyCommonExecutor.getResult(filterScript, filterScript,data);
//            log.info("result for: {} is : {}", value, result);
            return (boolean) result;
        } catch (groovy.lang.MissingPropertyException e1) {
            return false;
        } catch (Exception e) {
            log.warn("execute filter error, condition=" + filterScript + ", value = " + value, e);
            return true;
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        GroovyExecutor.clearCache();
    }
}
