package com.yupaopao.risk.insight.flink.windows;

import lombok.Data;

@Data
public class AccumulateCalDetail {
    private Integer id;
    private String groupKey;
    private String data;
    private Long timeSpan;
    private String function;
    private Long timeStamp;
    private Long thresholds;
    private boolean purge = false;
    private String uid;
    private String deviceId;
    private String ip;
    private String appId;
    private String appVersion;
}
