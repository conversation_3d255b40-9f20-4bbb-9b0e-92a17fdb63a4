package com.yupaopao.risk.insight.flink.job.etl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.lindorm.client.core.utils.Bytes;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.portrait.DeviceVO;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitEtlVO;
import com.yupaopao.risk.insight.flink.connector.hbase.sink.HBaseBaseOutputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseBatchTableSource;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseSplit;
import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.meta.JobParams;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.utils.ip2region.IPSearchUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.functions.ReduceFunction;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.table.api.DataTypes;
import org.apache.flink.table.types.DataType;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.Put;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.TreeSet;

import static com.yupaopao.risk.insight.common.constants.HBaseConstants.COLUMN_FAMILY;

@Slf4j
@Deprecated
public class RiskPortraitETL {

    public static void main(String[] args) throws Exception {
        if (args == null || args.length <= 0 || StringUtils.isEmpty(args[0])) {
            log.error("入参错误:{}", args);
            return;
        }

        String param = URLDecoder.decode(args[0], "UTF-8");
        JobParams jobParams = JSON.parseObject(param, JobParams.class);
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        HBaseProperties hBProperties = HBaseProperties.getProperties(PropertyType.HBASE);

        TsTableInfo tsTableInfo = new TsTableInfo();
        JobParams.TableQueryDatePeriod tableQueryDatePeriod = new JobParams.TableQueryDatePeriod();
        tableQueryDatePeriod.setBeginDate(jobParams.getDatePeriodList().get(0).getBeginDate());
        tableQueryDatePeriod.setEndDate(jobParams.getDatePeriodList().get(0).getEndDate());
        tableQueryDatePeriod.setTableName("risk_portrait_etl");
        tsTableInfo.setDatePeriod(tableQueryDatePeriod);
        tsTableInfo.setTableName("risk_portrait_etl");
        DataType[] dataTypes = new DataType[5];
        dataTypes[0] = DataTypes.STRING();
        dataTypes[1] = DataTypes.STRING();
        dataTypes[2] = DataTypes.INT();
        dataTypes[3] = DataTypes.STRING();
        dataTypes[4] = DataTypes.INT();
        FlinkMetaInfo flinkMetaInfo = new FlinkMetaInfo(new String[]{"userId", "data_DeviceId", "deviceIdCount", "data_ClientIp", "ipCount"}, dataTypes);
        HBaseBatchTableSource hBaseBatchTableSource = new HBaseBatchTableSource(tsTableInfo, hBProperties);
        HBaseInputFormat hBaseInputFormat = new HBaseInputFormat(tsTableInfo, hBProperties, flinkMetaInfo){
            @Override
            public HBaseSplit[] createInputSplits(int minNumSplits) throws IOException {
                List< HBaseSplit.BucketSplit> list = new ArrayList();
                List<String> dateList = InsightDateUtils.getDateList(tableQueryDatePeriod.getBeginDate(), tableQueryDatePeriod.getEndDate(), InsightDateUtils.DATE_FORMAT_yyyyMMdd);
                HBaseSplit[] hBaseSplits = new HBaseSplit[dateList.size()];
                for (int i = 0;i<dateList.size();i++) {
                    HBaseSplit.BucketSplit bucketSplit = new HBaseSplit.BucketSplit();
                    bucketSplit.setStartKey(dateList.get(i)+"_!");
                    bucketSplit.setEndKey(dateList.get(i)+"_~");
                    list.add(bucketSplit);
                    HBaseSplit hBaseSplit = new HBaseSplit(0, JSONObject.toJSONString(list));
                    hBaseSplits[i] = hBaseSplit;
                }
                return hBaseSplits;
            }
        };

        HBaseBaseOutputFormat hBaseBaseOutputFormat = new HBaseBaseOutputFormat("risk_portrait_result", hBProperties) {
            @Override
            public void writeRecord(Object record) {
                try {
                    Tuple2<String, PortraitEtlVO> tuple = (Tuple2<String, PortraitEtlVO>) record;
                    String rowKey = tuple.f0;
                    Put put = new Put(Bytes.toBytes(rowKey));
                    byte[] columnFamily = Bytes.toBytes(COLUMN_FAMILY);
                    put.addColumn(columnFamily, Bytes.toBytes("activeDeviceList"), Bytes.toBytes(JSONObject.toJSONString(tuple.f1.getActiveDeviceCount())));

                    JSONArray ipList = JSONArray.parseArray(JSONObject.toJSONString(tuple.f1.getActiveIpCount()));
                    TreeSet<DeviceVO> ips = new TreeSet<>();
                    for (Object obj : ipList) {
                        JSONObject ipObj = JSONObject.parseObject(JSONObject.toJSONString(obj));
                        String id = ipObj.getString("id");
                        String num = ipObj.getString("num");
                        DeviceVO activeIp = new DeviceVO(id, Integer.valueOf(num));
                        activeIp.setIpDetail(IPSearchUtil.getIpInfo(id));
                        ips.add(activeIp);
                    }
                    put.addColumn(columnFamily, Bytes.toBytes("activeIp"), Bytes.toBytes(JSONObject.toJSONString(ips)));
                    this.getMutator().mutate(put);
                    if (canFlush()) {
                        flush();
                    }
                } catch (Exception e) {
                    log.error("write record error: ,record: " + record, e);
                }
            }
        };

        env.createInput(hBaseInputFormat).flatMap(new FlatMapFunction<Row, Tuple2<String, PortraitEtlVO>> (){
            @Override
            public void flatMap(Row value, Collector<Tuple2<String, PortraitEtlVO>> out) throws Exception {
                Object uid = value.getField(0);
                if (valid(uid)) {
                    Object deviceId = value.getField(1);
                    Object deviceTotal = value.getField(2);
                    Object clientIp = value.getField(3);
                    Object ipTotal = value.getField(4);
                    PortraitEtlVO portraitEtlVO = new PortraitEtlVO();
                    if (valid(deviceId)){
                        portraitEtlVO.getActiveDeviceCount().add(new DeviceVO(deviceId + "", Integer.valueOf(deviceTotal + "")));
                    }
                    if (valid(clientIp)){
                        portraitEtlVO.getActiveIpCount().add(new DeviceVO(clientIp + "", Integer.valueOf(ipTotal + "")));
                    }
                    out.collect(new Tuple2<>(uid + "", portraitEtlVO));
                }
            }
        }).groupBy(0).reduce(new ReduceFunction<Tuple2<String, PortraitEtlVO>>() {
            @Override
            public Tuple2<String, PortraitEtlVO> reduce(Tuple2<String, PortraitEtlVO> value1, Tuple2<String, PortraitEtlVO> value2) throws Exception {
                Tuple2<String, PortraitEtlVO> first = (Tuple2) value1;
                Tuple2<String, PortraitEtlVO> second = (Tuple2) value2;
                return new Tuple2<>(first.f0, compareTO(first.f1, second.f1));
            }
        }).output(hBaseBaseOutputFormat);
        env.execute("portraitJob");
    }
/*(ReduceFunction) (value1, value2) -> {
        Tuple2<String, PortraitEtlVO> first = (Tuple2) value1;
        Tuple2<String, PortraitEtlVO> second = (Tuple2) value2;
        return new Tuple2<>(first.f0, compareTO(first.f1, second.f1));
    }*/
    /**
     * 排序取前三
     */
    public static PortraitEtlVO compareTO(PortraitEtlVO firstVO, PortraitEtlVO secondVO) {
        PortraitEtlVO result = new PortraitEtlVO();
        result.getActiveDeviceCount().addAll(firstVO.getActiveDeviceCount());
        result.getActiveDeviceCount().addAll(secondVO.getActiveDeviceCount());
        result.getActiveIpCount().addAll(firstVO.getActiveIpCount());
        result.getActiveIpCount().addAll(secondVO.getActiveIpCount());
        for (; result.getActiveDeviceCount().size() > 3; ) {
            DeviceVO last = result.getActiveDeviceCount().last();
            result.getActiveDeviceCount().remove(last);
        }
        Iterator<DeviceVO> ipIterator = result.getActiveIpCount().iterator();
        for (; result.getActiveDeviceCount().size() > 3; ) {
            DeviceVO last = result.getActiveIpCount().last();
            result.getActiveIpCount().remove(last);
        }
        return result;

    }

    /**
     * 过滤无效数据
     * */
    private static boolean valid(Object obj){
        if (null == obj || StringUtils.isEmpty(obj.toString()) || "null".equalsIgnoreCase(obj.toString())){
            return false;
        }
        return true;
    }
}
