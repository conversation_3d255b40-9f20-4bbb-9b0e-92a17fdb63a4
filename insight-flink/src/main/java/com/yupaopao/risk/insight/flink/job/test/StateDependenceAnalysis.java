//package com.yupaopao.risk.insight.flink.job.test;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.io.filefilter.RegexFileFilter;
//import org.apache.flink.runtime.checkpoint.Checkpoints;
//import org.apache.flink.runtime.checkpoint.OperatorState;
//import org.apache.flink.runtime.checkpoint.OperatorSubtaskState;
//import org.apache.flink.runtime.checkpoint.savepoint.Savepoint;
//import org.apache.flink.runtime.state.*;
//import org.apache.flink.runtime.state.filesystem.FileStateHandle;
//import org.joda.time.DateTime;
//
//import java.io.*;
//import java.util.*;
//
///**
// * Copyright (C), 2020, jimmy
// *
// * <AUTHOR>
// * @desc StateDependenceAnalysis
// * @date 2020/6/16
// */
//@Slf4j public class StateDependenceAnalysis {
//    public static void main(String[] args) throws IOException {
//        File allState = new File("/flink/rocksdb/");
//        List<File> todayState = getTodayState(allState);
//        Set<String> stateDep = new HashSet<>();
//
//        for (File file : todayState) {
//            stateDep.addAll(getData(file));
//        }
//        log.info("need state size: {}, list: {}", stateDep.size(), String.join(",", stateDep));
//        cleanUnuseState(allState, stateDep);
//    }
//
//    private static void cleanUnuseState(File allState, Set<String> stateDep) throws IOException {
//        File[] files = allState.listFiles();
//        if (null == files) {
//            return;
//        }
//        Set<String> noUse = new HashSet<>();
//        for (File file : files) {
//            if (!stateDep.contains(file.getName())) {
//                noUse.add(file.getName());
//            }
//        }
//        log.info("{} is no use", String.join(",", noUse));
//    }
//
//    private static List<File> getTodayState(File dir) {
//        List<File> todayState = new ArrayList<>();
//        DateTime dateTime = new DateTime().withMillisOfDay(0);
//        if (dir.isDirectory()) {
//            File[] files = dir.listFiles();
//            if (null == files) {
//                return todayState;
//            }
//            for (File file : files) {
//                long l = file.lastModified();
//                if (l > dateTime.getMillis()) {
//                    todayState.add(file);
//                }
//            }
//        }
//        return todayState;
//    }
//
//    private static Set<String> getData(File file) throws IOException {
//        file = getLastestCheckpoint(file);
//        if (null == file) {
//            return new HashSet<>();
//        }
//        log.info("checkpoint id: " + file.getPath());
//        FileInputStream fis = new FileInputStream(file);
//        BufferedInputStream bis = new BufferedInputStream(fis);
//        DataInputStream dis = new DataInputStream(bis);
//
//        Savepoint savepoint = Checkpoints.loadCheckpointMetadata(dis, StateDependenceAnalysis.class.getClassLoader());
//        Set<String> keyedStateDep = new HashSet<>();
//
//        // 遍历 OperatorState，这里的每个 OperatorState 对应一个 Flink 任务的 Operator 算子
//        for (OperatorState operatorState : savepoint.getOperatorStates()) {
//            // 当前算子的状态大小为 0 ，表示算子不带状态，直接退出
//            if (operatorState.getStateSize() == 0) {
//                continue;
//            }
//
//            // 遍历当前算子的所有 subtask
//            for (OperatorSubtaskState operatorSubtaskState : operatorState.getStates()) {
//                // 解析 operatorSubtaskState 的 KeyedState 和 OperatorState
//                keyedStateDep.addAll(parseManagedState(operatorSubtaskState));
//            }
//        }
//        return keyedStateDep;
//    }
//
//    private static File getLastestCheckpoint(File file) {
//        FilenameFilter filenameFilter = new RegexFileFilter("chk.*");
//        File[] files = file.listFiles(filenameFilter);
//        File lastestCheckpoint = null;
//        if (null == files) {
//            return lastestCheckpoint;
//        }
//        long lastest = 0L;
//        for (File file1 : files) {
//            if (file1.lastModified() > lastest) {
//                lastest = file1.lastModified();
//                lastestCheckpoint = file1;
//            }
//        }
//        return null == lastestCheckpoint ? null : new File(lastestCheckpoint.getPath() + "/_metadata");
//    }
//
//    private static File getSecondCheckpoint(File file) {
//        FilenameFilter filenameFilter = new RegexFileFilter("chk.*");
//        File[] files = file.listFiles(filenameFilter);
//        File secondCheckpoint = null;
//        if (null == files) {
//            return secondCheckpoint;
//        }
//        long lastest = 0L, second = 0L;
//        if (files.length == 1) {
//            secondCheckpoint = files[0];
//        }
//        for (File file1 : files) {
//            if (file1.lastModified() > lastest) {
//                second = lastest;
//                lastest = file1.lastModified();
//            } else if (file1.lastModified() > second) {
//                second = file1.lastModified();
//                secondCheckpoint = file1;
//            }
//        }
//        return null == secondCheckpoint ? null : new File(secondCheckpoint.getPath() + "/_metadata");
//    }
//
//    private static Set<String> parseManagedState(OperatorSubtaskState operatorSubtaskState) {
//        Set<String> dependenceSet = new HashSet<>();
//        for (KeyedStateHandle keyedStateHandle : operatorSubtaskState.getManagedKeyedState()) {
//            // 仅处理 IncrementalRemoteKeyedStateHandle
//            if (keyedStateHandle instanceof IncrementalRemoteKeyedStateHandle) {
//                Map<StateHandleID, StreamStateHandle> sharedState = ((IncrementalRemoteKeyedStateHandle) keyedStateHandle).getSharedState();
//                // 遍历 sharedState 中所有的 sst 文件，key 为 sst 文件名，value 为对应的 hdfs 文件 Handle
//                for (Map.Entry<StateHandleID, StreamStateHandle> entry : sharedState.entrySet()) {
//                    if (entry.getValue() instanceof FileStateHandle) {
//                        String filePath = ((FileStateHandle) entry.getValue()).getFilePath().getPath();
//                        String[] split = filePath.split("/");
//                        if (split.length > 0) {
//                            dependenceSet.add(split[3]);
//                        }
//                    }
//                }
//            }
//        }
//        for (OperatorStateHandle operatorStateHandle : operatorSubtaskState.getManagedOperatorState()) {
//            StreamStateHandle delegateStateHandle = operatorStateHandle.getDelegateStateHandle();
//            if (delegateStateHandle instanceof FileStateHandle) {
//                String filePath = ((FileStateHandle) delegateStateHandle).getFilePath().getPath();
//                String[] split = filePath.split("/");
//                if (split.length > 0) {
//                    dependenceSet.add(split[3]);
//                }
//            }
//        }
//        return dependenceSet;
//    }
//}
