//package com.yupaopao.risk.insight.flink.connector.ts.source;
//
//import com.alicloud.openservices.tablestore.SyncClient;
//import com.alicloud.openservices.tablestore.model.*;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsClientFactory;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TypeConvertUtils;
//import com.yupaopao.risk.insight.flink.meta.FlinkMetaInfo;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.flink.api.common.io.DefaultInputSplitAssigner;
//import org.apache.flink.api.common.io.RichInputFormat;
//import org.apache.flink.api.common.io.statistics.BaseStatistics;
//import org.apache.flink.configuration.Configuration;
//import org.apache.flink.core.io.InputSplitAssigner;
//
//import java.io.IOException;
//import java.io.Serializable;
//import java.util.ArrayList;
//import java.util.Iterator;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.concurrent.BlockingQueue;
//import java.util.concurrent.LinkedBlockingQueue;
//
///***
// * inputFormat 必须能够序列化
// * 参考阿里ots，因在 batchFetchedList 中读取的时候多线程情况hang住，废弃
// */
//@Deprecated
//@Slf4j
//public class TsInputFormatReadSerial extends RichInputFormat<org.apache.flink.types.Row, TsInputSplitOneBucket> implements Serializable {
//
//    private TsTableInfo tsTableInfo;
//    //client provider 提供访问table store 的client
//    private TsClientFactory tsClientFactory;
//
//    private FlinkMetaInfo flinkMetaInfo;
//
//
//    //每次getRange的结果放到queue中
//    BlockingQueue<LinkedList<Row>> batchFetchedList = new LinkedBlockingQueue<>();
//    //从queue中提取出来的当期rowList
//    LinkedList<Row> currentProcessList = new LinkedList<>();
//    //读取该InputFormat负责的splits的线程列表
//    private List<TsInputSplitOpenThread> threadList = new ArrayList<>();
//
//    private final static Long splitSizeUnitInBytes = 1024L * 1024L;
//
//    private final static Long unitsPerSplit = 400L;
//
//    private transient Iterator<Row> iterator;
//    private boolean hasNext;
//
//    private long readCount = 0L;
//
//
//    public TsInputFormatReadSerial() {
//    }
//
//    @Override
//    public void configure(Configuration configuration) {
//    }
//
//
//    @Override
//    public BaseStatistics getStatistics(BaseStatistics baseStatistics) throws IOException {
//        return null;
//    }
//
//    /***
//     * 整个数据源做分片
//     * @param minNumSplits
//     * @return
//     * @throws IOException
//     */
//    @Override
//    public TsInputSplitOneBucket[] createInputSplits(int minNumSplits) throws IOException {
//        log.info(String.format("createInputSplit: minNumSplits [%d]", minNumSplits));
//        if (minNumSplits < 1) {
//            throw new IllegalArgumentException("Number of input splits has to be at least 1.");
//        }
//        try {
//            ComputeSplitsBySizeResponse csbsr = getComputeSplitsBySize(createClientProvider(), tsTableInfo.getTableName());
//            log.info("the splits info: " + csbsr.jsonize());
//            List<Split> splits = csbsr.getSplits();
//
//            //每个分片主键信息
//            List<PrimaryKeySchema> primaryKeySchema = csbsr.getPrimaryKeySchema();
//
//            //主键key列表
//            List<String> primaryKeySchemaString = new ArrayList<>();
//
//            for (int i = 0; i < primaryKeySchema.size(); i++) {
//                primaryKeySchemaString.add(primaryKeySchema.get(i).getName());
//            }
//            //分片数组
//            TsInputSplitOneBucket[] resultTsSplitArray = new TsInputSplitOneBucket[splits.size()];
//            for (int i = 0; i < splits.size(); i++) {
//                PrimaryKey lowerBound = splits.get(i).getLowerBound();
//                PrimaryKey upperBound = splits.get(i).getUpperBound();
//                //每个分片，当前分片号，主键上界，主键下界，主键schema
//                resultTsSplitArray[i] = new TsInputSplitOneBucket(i, lowerBound, upperBound, primaryKeySchemaString);
//            }
//            return resultTsSplitArray;
//        } catch (Exception e) {
//            log.error("createInputSplit error: tableName" + tsTableInfo.getTableName(), e);
//            throw new IOException(e);
//        }
//    }
//
//    /***
//     * 全表数据逻辑上分片信息
//     * https://help.aliyun.com/document_detail/99107.html?aly_as=BRhf8OnW
//     * @param clientFactory
//     * @param tableName
//     * @return
//     */
//    public static ComputeSplitsBySizeResponse getComputeSplitsBySize(TsClientFactory clientFactory, String tableName) {
//        ComputeSplitsBySizeRequest csbsr = new ComputeSplitsBySizeRequest();
//        csbsr.setTableName(tableName);
//        //20M一个分片
//        //csbsr.setSplitSizeInByte(unitsPerSplit, splitSizeUnitInBytes);
//        csbsr.setSplitSizeIn100MB(4);
//        return clientFactory.getClient().computeSplitsBySize(csbsr);
//    }
//
//
//    @Override
//    public InputSplitAssigner getInputSplitAssigner(TsInputSplitOneBucket[] tableStoreInputSplits) {
//        return new DefaultInputSplitAssigner(tableStoreInputSplits);
//    }
//
////    /**
////     * 开始读取某一个分片，分配资源
////     *
////     * @param tableStoreInputSplit
////     * @throws IOException
////     */
////    @Override
////    public void open(TsInputSplit tableStoreInputSplit) throws IOException {
////        try {
////            log.info("open ts input format: {}, {}" , getRuntimeContext().getTaskNameWithSubtasks(),
////                    tableStoreInputSplit.getPrimaryKeySchemaString()+": "+tableStoreInputSplit.getLowerBound()+":"+tableStoreInputSplit.getUpperBound());
////            TsInputSplitOpenThread isot = new TsInputSplitOpenThread(tableStoreInputSplit,
////                    createClientProvider(),
////                    batchFetchedList,
////                    tsTableInfo.getTableName());
////            threadList.add(isot);
////            log.info("create thread: {}, task: {}",isot.getName(),getRuntimeContext().getTaskNameWithSubtasks());
////            isot.start();
////        }catch (Exception e){
////            log.error("open input format error",e);
////            throw e;
////        }
////    }
//
//    public void open(TsInputSplitOneBucket split) throws IOException {
//        RangeIteratorParameter rangeIteratorParameter = new RangeIteratorParameter(tsTableInfo.getTableName());
//
//        rangeIteratorParameter.addColumnsToGet(new String[]{"level"});
//        // 设置起始主键
//        PrimaryKey start = split.getLowerBound();
//        rangeIteratorParameter.setInclusiveStartPrimaryKey(start);
//        // 设置结束主键
//        rangeIteratorParameter.setExclusiveEndPrimaryKey(split.getUpperBound());
//        rangeIteratorParameter.setMaxVersions(1);
//        iterator = tsClientFactory.getClient().createRangeIterator(rangeIteratorParameter);
//        hasNext = iterator.hasNext();
//    }
//
//    /***
//     * 判断某一个split中是否还有数据需要读取,
//     * 是否还有线程未消耗完,以及是否还有数据未读取完
//     * @return
//     * @throws IOException
//     */
//    @Override
//    public boolean reachedEnd() throws IOException {
//        return !hasNext;
//    }
//
//    /**
//     * 读取queue中的下一行数据
//     *
//     * @param reuse
//     * @return
//     * @throws IOException
//     */
//    @Override
//    public org.apache.flink.types.Row nextRecord(org.apache.flink.types.Row reuse) throws IOException {
//        try {
//            if(!hasNext){
//                return null;
//            }
//            org.apache.flink.types.Row flinkRow = TypeConvertUtils.extractNextRecord(iterator.next(), flinkMetaInfo);
//            readCount++;
//            if(readCount%500==0){
//                log.info("current count: {}",readCount);
//            }
//            hasNext = iterator.hasNext();
//            return flinkRow;
//        } catch (Exception e) {
//            log.info("read next row error", e);
//            return null;
//        }
//    }
//
//    @Override
//    public void close() throws IOException {
//        readCount = 0;
//    }
//
//
//    class TsInputSplitOpenThread extends Thread {
//        private final String otsTableName;
//        private final TsInputSplitOneBucket split;
//        private TsClientFactory clientFactory;
//        private BlockingQueue<LinkedList<Row>> rows;
//        //private String[] columns;
//        private volatile boolean exit = false;
//
//        public TsInputSplitOpenThread(TsInputSplitOneBucket split,
//                                      TsClientFactory clientProvider,
//                                      BlockingQueue<LinkedList<Row>> rows,
//                                      String otsTableName) {
//            this.split = split;
//            this.clientFactory = clientProvider;
//            this.rows = rows;
//            this.otsTableName = otsTableName;
//            //this.columns = columns;
//        }
//
//        @Override
//        public void run() {
//            PrimaryKey start = split.getLowerBound();
//            List<String> primaryKeySchemas = split.getPrimaryKeySchemaString();
//            while (!Thread.interrupted()) {
//                //范围读tablestore
//                GetRangeResponse res = getRange(clientFactory.getClient(), start, split.getUpperBound(), primaryKeySchemas);
//                List<Row> temp = res.getRows();
//                log.info("read count: {}", temp != null ? temp.size() : 0);
//
//                //读取结果放入queue中
//                try {
//                    if(CollectionUtils.isEmpty(temp)){
//                        log.info("empty row, nextKey: {}",res.getNextStartPrimaryKey());
//                    }else{
//                        rows.put(new LinkedList<>(temp));
//                    }
//
//                } catch (InterruptedException e) {
//                    log.error("put range response batchFetchedList to queue error", e);
//                }
//                //任然有数据继续读， getRows可能只返回部分
//                if (res.getNextStartPrimaryKey() != null) {
//                    start = res.getNextStartPrimaryKey();
//                } else {
//                    log.info("thread: {} exit flag set",this.getName());
//                    exit = true;
//                    break;
//                }
//            }
//        }
//
//        /***
//         * 范围读取
//         * @param client
//         * @param lowerBound
//         * @param upperBound
//         * @param primaryKeySchemas
//         * @return
//         */
//        //            rec.addColumnsToGet(new String[]{"traceId", "userId", "eventCode", "level"});
//
//
//        private GetRangeResponse getRange(SyncClient client, PrimaryKey lowerBound, PrimaryKey upperBound, List<String> primaryKeySchemas) {
//            GetRangeRequest grr = new GetRangeRequest();
//            RangeRowQueryCriteria rec = new RangeRowQueryCriteria(otsTableName);
//            rec.setDirection(Direction.FORWARD);
//            rec.setInclusiveStartPrimaryKey(lowerBound);
//            rec.setExclusiveEndPrimaryKey(upperBound);
//            rec.setMaxVersions(1);
//            rec.addColumnsToGet(new String[]{"level"});
//            grr.setRangeRowQueryCriteria(rec);
//            return client.getRange(grr);
//        }
//    }
//
//    private TsClientFactory createClientProvider() {
//        if (null == tsClientFactory) {
//            tsClientFactory = new TsClientFactory(this.tsTableInfo.getTsProperties());
//        }
//        return tsClientFactory;
//    }
//
//
//    public static class TsInputFormatBuilder {
//        private final TsInputFormatReadSerial format;
//
//        public static TsInputFormatBuilder builder() {
//            return new TsInputFormatBuilder();
//        }
//
//        public TsInputFormatBuilder() {
//            format = new TsInputFormatReadSerial();
//        }
//
//        public TsInputFormatBuilder withTsTableInfo(TsTableInfo tsTableInfo) {
//            format.tsTableInfo = tsTableInfo;
//            return this;
//        }
//
//        public TsInputFormatBuilder withFlinkMetaInfo(FlinkMetaInfo flinkMetaInfo) {
//            format.flinkMetaInfo = flinkMetaInfo;
//            return this;
//        }
//
//
//        public TsInputFormatReadSerial build() {
//            log.info("build finished with verification ");
//            format.tsTableInfo.getTsProperties().checkEmpty();
//            format.createClientProvider();
//            return format;
//        }
//
//    }
//}
