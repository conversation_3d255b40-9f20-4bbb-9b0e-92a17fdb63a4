//package com.yupaopao.risk.insight.flink.connector.ts.sink;
//
//import com.alicloud.openservices.tablestore.model.BatchWriteRowRequest;
//import com.alicloud.openservices.tablestore.model.BatchWriteRowResponse;
//import com.alicloud.openservices.tablestore.model.Row;
//import com.alicloud.openservices.tablestore.model.RowDeleteChange;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsRiskHitLogCleanerUtil;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsWriter;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.flink.configuration.Configuration;
//
//import java.io.IOException;
//import java.util.LinkedList;
//import java.util.List;
//import java.util.concurrent.BlockingQueue;
//import java.util.concurrent.LinkedBlockingQueue;
//
///****
// *
// * @Author: zengxiangcai
// * @Date: 2019-12-28 16:07
// *
// ***/
//
//@Slf4j
//public class TsCleanerOutputFormat extends TsRichOutputFormat<Row> {
//
//    private BlockingQueue<Row> waitingRows = new LinkedBlockingQueue<>();
//    private BlockingQueue<Row> oldNeedDeleteRows = new LinkedBlockingQueue<>();
//
//    private final static int maxBatchRows = 50;  //批量写入tablestore最大行数,sdk限制200行，4MB
//
//
//    public TsCleanerOutputFormat(TsTableInfo tableInfo) {
//        super(tableInfo);
//    }
//
//    @Override
//    public void configure(Configuration configuration) {
//    }
//
//    @Override
//    public void open(int taskNumber, int numTasks) throws IOException {
//        if (taskNumber < 0 || numTasks < 1) {
//            throw new IllegalArgumentException("TaskNumber: " + taskNumber + ", numTasks: " + numTasks);
//        }
//        log.info("open taskNumber: {}, numTasks: {}", taskNumber, numTasks);
//
//    }
//
//    /***
//     * write every row in flink table to table store table
//     * @param row
//     * @throws IOException
//     */
//    @Override
//    public void writeRecord(Row row) throws IOException {
//
//        /***
//         * 数据清洗转换逻辑
//         */
//        Row newRow = TsRiskHitLogCleanerUtil.toNewRow(row, getTableInfo().getTableName());
//        if (newRow == null) {
//            return;
//        }
//
//        //写数据
//        log.info("row primary key: {}", row.getPrimaryKey());
//        //批量写入
//        try {
//            waitingRows.put(newRow);
//            //oldNeedDeleteRows.put(row);
//        } catch (InterruptedException e) {
//            log.error("write data to queue error", e);
//        }
//        if (waitingRows.size() >= maxBatchRows) {
//            flush();
//            flushDelete();
//        }
//    }
//
//
//    public void flush() {
//        //从queue 提取maxBatchRows的数据
//        List<Row> flushList = new LinkedList<>();
//        try {
//            int fetchRows = Math.min(waitingRows.size(), maxBatchRows);
//            for (int i = 0; i < fetchRows; i++) {
//                com.alicloud.openservices.tablestore.model.Row row = waitingRows.take();
//                flushList.add(row);
//            }
//            if (CollectionUtils.isEmpty(flushList)) {
//                return;
//            }
//            Configuration configuration = (Configuration) this.getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
//            String subTaskName = this.getRuntimeContext().getTaskNameWithSubtasks();
//            TsWriter tsWriter = new TsWriter(createTsClient().getClient(), this.getTableInfo(), configuration, subTaskName);
//            tsWriter.batchWrite(flushList);
//        } catch (Exception e) {
//            for (Row row : flushList) {
//                log.info("flush error batch row: {}" + row.getPrimaryKey());
//            }
//            log.error("write element from queue error: ", e);
//        }
//
//    }
//
//
//    public void flushDelete() {
////        //flush delete
////        try {
////            List<Row> delete = new LinkedList<>();
////            int fetchRows = Math.min(oldNeedDeleteRows.size(), maxBatchRows);
////            for (int i = 0; i < fetchRows; i++) {
////                com.alicloud.openservices.tablestore.model.Row row = oldNeedDeleteRows.take();
////                delete.add(row);
////            }
////            if (CollectionUtils.isEmpty(delete)) {
////                return;
////            }
////            batchDelete(delete);
////        } catch (InterruptedException e) {
////            log.error("delete element from queue error", e);
////        }
//    }
//
//
//    @Override
//    public void close() throws IOException {
//        try {
//            while (waitingRows != null && waitingRows.size() > 0) {
//                flush();
//            }
////            while (oldNeedDeleteRows != null && oldNeedDeleteRows.size() > 0) {
////                flushDelete();
////            }
//        } finally {
//            waitingRows = null;
//            oldNeedDeleteRows = null;
//            closeTsClient();
//        }
//    }
//
//    private void batchDelete(List<Row> rows) {
//        if (CollectionUtils.isEmpty(rows)) {
//            return;
//        }
//        BatchWriteRowRequest batchWriteRowRequest = new BatchWriteRowRequest();
//        for (Row row : rows) {
//            RowDeleteChange rowDeleteChange = new RowDeleteChange(getTableInfo().getTableName(), row.getPrimaryKey());
//            batchWriteRowRequest.addRowChange(rowDeleteChange);
//        }
//
//        BatchWriteRowResponse response = createTsClient().getClient().batchWriteRow(batchWriteRowRequest);
//        if (!response.isAllSucceed()) {
//            for (BatchWriteRowResponse.RowResult rowResult : response.getFailedRows()) {
//                log.info("delete failed row: {}", rowResult.getRow());
//                log.info("delete the failed waitingRows for batch write：" + batchWriteRowRequest.getRowChange(rowResult.getTableName(), rowResult.getIndex()).getPrimaryKey());
//                log.info("delete failed reason：" + rowResult.getError());
//            }
//        }
//    }
//
//
//}
