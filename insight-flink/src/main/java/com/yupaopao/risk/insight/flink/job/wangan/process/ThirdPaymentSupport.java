package com.yupaopao.risk.insight.flink.job.wangan.process;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.yupaopao.platform.common.utils.EnDecryptUtil;
import com.yupaopao.risk.insight.flink.job.wangan.constant.TopicConstant;
import com.yupaopao.risk.insight.flink.job.wangan.dto.RiskThirdPaymentDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.util.Collector;
import org.springframework.util.CollectionUtils;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class ThirdPaymentSupport {

    private WangAnHbaseTableSupport wangAnHbaseTableSupport;

    private ThirdPaymentSupport(){}

    public ThirdPaymentSupport(WangAnHbaseTableSupport wangAnHbaseTableSupport){
        this.wangAnHbaseTableSupport = wangAnHbaseTableSupport;
    }

    private  String passWord = "ICp#ffufJ#lMOA2n22";

    public void send(String topic, JSONObject messsage, Collector<Map> collector) throws Exception{
        if (!isPass(topic)) {
            return;
        }
        log.debug("支付消费:{},{}", topic,messsage);
        RiskThirdPaymentDto riskThirdPaymentDto = null;
        try {
            riskThirdPaymentDto = getPayInfo(putHbase(messsage, topic));
        } catch (Exception e) {
            log.error("支付信息补全错误:{}", messsage, e);
        }
        if (Objects.isNull(riskThirdPaymentDto)) {
            return;
        }
        if (StringUtils.isNotBlank(riskThirdPaymentDto.getPayUser())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getUserId())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getProductName())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getOrderNo())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getPayNo())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getOrderPayChannel())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getReceivePayChannel())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getMerchantId())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getPayAmount())
                && LocalDateTime.parse(riskThirdPaymentDto.getPayTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                .isAfter(LocalDateTime.now().minusDays(7))
                && StringUtils.isNotBlank(riskThirdPaymentDto.getClientIp())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getDeviceId())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getTerminalType())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getTerminalVersion())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getTerminalOsType())
                && StringUtils.isNotBlank(riskThirdPaymentDto.getTerminalOsVersion())){
            Map<String, String> jsonObject = new HashMap<>();
            jsonObject.put("module", "payment");
            jsonObject.put("newJob", "true");
            jsonObject.put("riskThirdPayment", EnDecryptUtil.encrypt(JSONObject.toJSONString(riskThirdPaymentDto),passWord));
            log.debug("网安上报支付信息:{}", jsonObject);
            Cat.logMetricForCount("wangan."+this.getClass().getName());
            collector.collect(jsonObject);
        }

    }

    private JSONObject putHbase(JSONObject dataJSON, String topic){
        JSONObject jsonObject = WangAnHbaseSupport.covertData(dataJSON.getJSONArray("data"));
        String rowKey = jsonObject.getString("pay_no");
        if (StringUtils.isEmpty(rowKey)){
            rowKey = jsonObject.getString("PAY_TRADE_NO");
        }
        if (!StringUtils.isEmpty(rowKey)){
            wangAnHbaseTableSupport.putRow(rowKey, topic, jsonObject);
        }
        return jsonObject;
    }

    private RiskThirdPaymentDto getPayInfo(JSONObject messsage) throws Exception {
        RiskThirdPaymentDto riskThirdPaymentDto = new RiskThirdPaymentDto();
        String payNo = messsage.getString("pay_no");
        String uid = messsage.getString("uid");

        if (StringUtils.isEmpty(payNo)){
            payNo = messsage.getString("PAY_TRADE_NO");
            uid = messsage.getString("from_uid");
        }

        if (StringUtils.isEmpty(payNo)){
            return riskThirdPaymentDto;
        }
        riskThirdPaymentDto.setPayNo(payNo);
        riskThirdPaymentDto.setProductAmount("1");
        riskThirdPaymentDto.setTerminalType("0");
        Map<String, Object> paymentThirdPay = wangAnHbaseTableSupport.getRow( TopicConstant.PAYMENT_THIRD_PAY,payNo,
                Arrays.asList("uid", "pay_user", "biz_type","order_no","pay_no","pay_channel","merchant_id","pay_amount","pay_time"));

        if (!CollectionUtils.isEmpty(paymentThirdPay)) {
            if (StringUtils.isEmpty(uid)){
                uid = paymentThirdPay.containsKey("uid")?paymentThirdPay.get("uid")+"":"";
                riskThirdPaymentDto.setUserId(uid);
            }
            riskThirdPaymentDto.setPayUser(paymentThirdPay.containsKey("pay_user")?paymentThirdPay.get("pay_user")+"":"");
            riskThirdPaymentDto.setOrderNo(paymentThirdPay.containsKey("order_no")?paymentThirdPay.get("order_no")+"":"");
            String productName = "下单";
            if ("RECHARGE".equals(paymentThirdPay.get("biz_type"))) {
                productName = "充值";
            }
            riskThirdPaymentDto.setProductName(productName);
            String payChannel = paymentThirdPay.containsKey("pay_channel")? paymentThirdPay.get("pay_channel")+"":"";
            riskThirdPaymentDto.setOrderPayChannel(payChannel);
            riskThirdPaymentDto.setReceivePayChannel(payChannel);
            riskThirdPaymentDto.setMerchantId(paymentThirdPay.containsKey("merchant_id")?paymentThirdPay.get("merchant_id")+"":"");
            riskThirdPaymentDto.setPayAmount(paymentThirdPay.containsKey("pay_amount")?paymentThirdPay.get("pay_amount")+"":"");
            riskThirdPaymentDto.setPayTime(paymentThirdPay.containsKey("pay_time")?paymentThirdPay.get("pay_time")+"":"");
        }

        if (StringUtils.isEmpty(uid)){
            Map<String, Object> paymentRechargeOrder = wangAnHbaseTableSupport.getRow( TopicConstant.PAYMENT_RECHARGE_ORDER, payNo,
                    Arrays.asList("uid"));
            if (!CollectionUtils.isEmpty(paymentRechargeOrder) && paymentRechargeOrder.containsKey("uid")) {
                uid = paymentRechargeOrder.get("uid")+"";
                riskThirdPaymentDto.setUserId(uid);
            }
        }

        if (StringUtils.isEmpty(uid)){
            Map<String, Object> tPaymentTrade = wangAnHbaseTableSupport.getRow(TopicConstant.T_PAYMENT_TRADE, payNo,
                    Arrays.asList("from_uid"));
            if (!CollectionUtils.isEmpty(tPaymentTrade) && tPaymentTrade.containsKey("from_uid")) {
                uid = tPaymentTrade.get("from_uid")+"";
                riskThirdPaymentDto.setUserId(uid);
            }

        }

        Map<String, Object> loginDevice =null;
        if (!StringUtils.isEmpty(uid)){
            riskThirdPaymentDto.setUserId(uid);
            loginDevice = wangAnHbaseTableSupport.getRow(TopicConstant.T_LOGIN_DEVICE, riskThirdPaymentDto.getUserId(),
                    Arrays.asList("uid", "terminalType", "os", "version", "equipment"));
            if (!CollectionUtils.isEmpty(loginDevice)) {
                riskThirdPaymentDto.setTerminalVersion(loginDevice.containsKey("equipment")?loginDevice.get("equipment")+"":"");
                riskThirdPaymentDto.setTerminalOsType(loginDevice.containsKey("os")?loginDevice.get("os")+"":"");
                riskThirdPaymentDto.setTerminalOsVersion(loginDevice.containsKey("version")?loginDevice.get("version")+"":"");
            }
        }
        Map<String, Object> paymentThirdPayAttach = wangAnHbaseTableSupport.getRow(TopicConstant.PAYMENT_THIRD_PAY_ATTACH,payNo,
                Arrays.asList("client_ip", "device_id"));

        if (!CollectionUtils.isEmpty(paymentThirdPayAttach)) {
            riskThirdPaymentDto.setClientIp(paymentThirdPayAttach.containsKey("client_ip")?paymentThirdPayAttach.get("client_ip")+"":"");
            riskThirdPaymentDto.setDeviceId(paymentThirdPayAttach.containsKey("device_id")?paymentThirdPayAttach.get("device_id")+"":"");
        }
        log.debug("支付补全信息：{},{},{},{}",riskThirdPaymentDto,paymentThirdPay, loginDevice,paymentThirdPayAttach);
        return riskThirdPaymentDto;
    }

    public boolean isPass(String topic) {
        return TopicConstant.PAYMENT_THIRD_PAY.equals(topic)||
                TopicConstant.PAYMENT_RECHARGE_ORDER.equals(topic)||
                TopicConstant.T_PAYMENT_TRADE.equals(topic)||
                TopicConstant.PAYMENT_THIRD_PAY_ATTACH.equals(topic);
    }

}
