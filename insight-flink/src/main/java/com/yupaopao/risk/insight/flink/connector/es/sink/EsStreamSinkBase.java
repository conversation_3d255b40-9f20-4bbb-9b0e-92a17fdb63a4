package com.yupaopao.risk.insight.flink.connector.es.sink;

import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

public class EsStreamSinkBase<T> extends RichSinkFunction<T> {

    private EsBaseOutputFormat<T> esBaseOutputFormat;

    public EsStreamSinkBase(EsProperties esProperties) {
        this.esBaseOutputFormat = new EsBaseOutputFormat<>(esProperties);
    }

    @Override public void invoke(T value, Context context) throws Exception {
        esBaseOutputFormat.writeRecord(value);
    }

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        RuntimeContext ctx = getRuntimeContext();
        esBaseOutputFormat.setRuntimeContext(ctx);
        esBaseOutputFormat.open(ctx.getIndexOfThisSubtask(), ctx.getNumberOfParallelSubtasks());
    }

    @Override public void close() throws Exception {
        super.close();
        esBaseOutputFormat.close();
    }
}
