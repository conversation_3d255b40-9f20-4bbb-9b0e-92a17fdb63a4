package com.yupaopao.risk.insight.flink.connector.mysql.sink;

import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

public class MysqlStreamSinkBase extends RichSinkFunction<String> implements CheckpointedFunction {

    private MysqlBaseOutputFormat mysqlBaseOutputFormat;

    public MysqlStreamSinkBase(String tableName, DBProperties dbProperties) {
        mysqlBaseOutputFormat = new MysqlBaseOutputFormat(tableName, dbProperties);
    }

    @Override public void invoke(String value, Context context) throws Exception {
        mysqlBaseOutputFormat.writeRecord(value);
    }

    @Override public void snapshotState(FunctionSnapshotContext functionSnapshotContext) throws Exception {
        mysqlBaseOutputFormat.flush();
    }

    @Override public void initializeState(FunctionInitializationContext functionInitializationContext) throws Exception {

    }

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        RuntimeContext ctx = getRuntimeContext();
        mysqlBaseOutputFormat.setRuntimeContext(ctx);
        mysqlBaseOutputFormat.open(ctx.getIndexOfThisSubtask(), ctx.getNumberOfParallelSubtasks());
    }

    @Override public void close() throws Exception {
        super.close();
        mysqlBaseOutputFormat.close();
    }
}
