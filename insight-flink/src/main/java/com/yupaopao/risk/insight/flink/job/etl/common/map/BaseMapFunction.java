package com.yupaopao.risk.insight.flink.job.etl.common.map;

import com.dtstack.flinkx.metrics.BaseMetric;
import com.yupaopao.risk.insight.flink.job.etl.common.options.MapperConfig;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.operators.StreamingRuntimeContext;
import org.apache.flink.util.Collector;

import java.util.*;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc BaseMapFunction
 * @date 2020/10/15
 */
public abstract class BaseMapFunction extends RichFlatMapFunction<Map<String, Object>, Map<String, Object>> {
    private static final long serialVersionUID = -4680979801770245460L;
    protected List<String> sourceCols = new ArrayList<>();
    protected List<String> targetCols = new ArrayList<>();
    protected List<String> colTypes = new ArrayList<>();
    protected StreamingRuntimeContext context;

    protected transient BaseMetric mapperMetric;

    protected Boolean isDebug;

    protected String KEY_DEBUG = "debug";

    protected String id = "";

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        context = (StreamingRuntimeContext) getRuntimeContext();
        mapperMetric = new BaseMetric(context);
    }

    @Override public void flatMap(Map<String, Object> value, Collector<Map<String, Object>> out) throws Exception {
        // 写入debug表
        // debug字段为 true 表示其他算子数据，不参与本算子计算；false 表示为本算子数据参与计算
        if (isDebug) {
            Boolean debug = (Boolean) value.getOrDefault("debug", false);
            if (!debug) {
                // 本算子数据添加上 debug 字段传到下个算子
                value.put("nodeId", id);
                value.put("debug", true);
                out.collect(value);
                // 移除debug字段参与本算子计算
                value.remove("nodeId");
                value.remove("debug");
            } else {
                // 直接传到下个算子
                out.collect(value);
            }
        }
    }

    public BaseMapFunction(MapperConfig config) throws Exception {
        isDebug = config.getBooleanVal("debug", false);
        id = config.getStringVal("id", UUID.randomUUID().toString());
    }

    public List<String> getSourceCols() {
        return sourceCols;
    }

    public void setSourceCols(List<String> sourceCols) {
        this.sourceCols = sourceCols;
    }

    public List<String> getTargetCols() {
        return targetCols;
    }

    public void setTargetCols(List<String> targetCols) {
        this.targetCols = targetCols;
    }

    @Override public void close() throws Exception {
        if (mapperMetric != null) {
            mapperMetric.waitForReportMetrics();
        }
    }
}
