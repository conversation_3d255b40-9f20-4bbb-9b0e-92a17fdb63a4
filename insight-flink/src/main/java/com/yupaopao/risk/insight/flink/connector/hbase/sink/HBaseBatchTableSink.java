package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.sinks.StreamTableSink;
import org.apache.flink.table.sinks.TableSink;
import org.apache.flink.types.Row;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-01-07 19:20
 *
 ***/
public class HBaseBatchTableSink  implements StreamTableSink<Row> {

    private FlinkMetaInfo flinkMetaInfo;

    private String tableName;

    private HBaseProperties hBaseProperties;

    private FlinkSQLOutputFormat outputFormat;

    public HBaseBatchTableSink(String tableName, FlinkMetaInfo flinkMetaInfo, HBaseProperties hBaseProperties) {
        this.tableName = tableName;
        this.flinkMetaInfo = flinkMetaInfo;
        this.hBaseProperties = hBaseProperties;
        //构建本sink 的format
        createFormat();

    }

    private void createFormat() {
        outputFormat = new FlinkSQLOutputFormat(tableName, hBaseProperties, flinkMetaInfo);
    }

    @Override
    public final DataStreamSink<Row> consumeDataStream(DataStream<Row> dataStream) {
        return dataStream
            .writeUsingOutputFormat(outputFormat)
            .setParallelism(1).name("write hbase result");
    }

    @Override
    public TableSink<Row> configure(String[] fieldNames, TypeInformation<?>[] fieldTypes) {
        HBaseBatchTableSink copy = new HBaseBatchTableSink(tableName, flinkMetaInfo, hBaseProperties);
        return copy;
    }

    @Override
    public TableSchema getTableSchema() {
        return flinkMetaInfo.toTableSchema();
    }

    @Override
    public TypeInformation<Row> getOutputType() {
        return flinkMetaInfo.toTableSchema().toRowType();
    }
}
