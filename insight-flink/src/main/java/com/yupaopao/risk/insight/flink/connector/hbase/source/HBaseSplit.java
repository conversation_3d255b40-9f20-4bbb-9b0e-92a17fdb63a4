package com.yupaopao.risk.insight.flink.connector.hbase.source;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.flink.core.io.InputSplit;

import java.io.Serializable;
import java.util.List;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-01-07 14:51
 *
 ***/

@Getter
@Setter
public class HBaseSplit  implements InputSplit, Serializable {

    private int splitNumber; //分片号

    private String bucketJson;



    public HBaseSplit(int splitNumber, String bucketJson){
        this.splitNumber = splitNumber;//数组下表
        this.bucketJson = bucketJson;//[{"startKey":"12321_!","123"}]
    }

    public List<BucketSplit> fetchBucketList(){
        return new Gson().fromJson(bucketJson, new TypeToken<List<HBaseSplit.BucketSplit>>() {
        }.getType());
    }


    @Override
    public int getSplitNumber() {
        return splitNumber;
    }

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BucketSplit{
        private String startKey;
        private String endKey;

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this, ToStringStyle.SIMPLE_STYLE);
        }
    }
}
