package com.yupaopao.risk.insight.flink.job.portrait.support;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yupaopao.risk.insight.flink.job.portrait.support.bean.TagExecuteInfo;
import com.yupaopao.risk.insight.flink.utils.FastJsonUtils;
import groovy.lang.Binding;
import groovy.lang.GroovyClassLoader;
import groovy.lang.Script;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 */
public class GroovyRunSupport {


    public static void main(String[] args) throws Exception {
        String request ="{\"costTime\":135,\"riskAction\":{\"async\":false,\"businessCode\":\"userService\",\"clientIp\":\"*************\",\"data\":{\"userData\":{},\"Platform\":\"ANDROID\",\"countByClientIp24hForRegister\":1.0,\"DeviceId\":\"202005161703269282c77c0cc582b3bbfdef3d661553fb0167d7230f357ba2\",\"deviceRegRejectCount10Min\":1.0,\"clientIpCountDistinctMobile24Hours\":2.0,\"mobileRegRejectCount1Min\":1.0,\"mobileRegRejectCount1H\":1.0,\"ClientIp\":\"*************\",\"countByDeviceId24hForRegister\":1.0,\"riskRequestTime\":\"1591067522977\",\"countSuccDeviceId24hRegister\":0.0,\"nationCode\":\"86\",\"Business\":\"userService\",\"Timeout\":\"950\",\"Mobile\":\"***********\",\"clientIpDetail\":{\"country\":\"中国\",\"riskLevel\":\"PASS\",\"province\":\"四川省\",\"city\":\"成都市\",\"isp\":\"电信\",\"cityId\":2470,\"region\":\"西南\"},\"registerCheck\":{\"detectChannel\":\"SHUMEI\",\"riskLevel\":\"REJECT\",\"detail\":{\"hits\":[{\"score\":800,\"riskLevel\":\"REJECT\",\"description\":\"高风险设备：bssid异常聚集\",\"model\":\"M00630007\",\"descriptionV2\":\"高风险设备：bssid异常聚集\"},{\"score\":800,\"riskLevel\":\"REJECT\",\"description\":\"高风险设备：手机号异常聚集\",\"model\":\"M00630008\",\"descriptionV2\":\"高风险设备：手机号异常聚集\"},{\"score\":450,\"riskLevel\":\"REVIEW\",\"description\":\"相同设备3天内注册账号的数量过多\",\"model\":\"M02060103\"},{\"score\":400,\"riskLevel\":\"REVIEW\",\"description\":\"同城市高危设备1天注册账号数量过多\",\"model\":\"M02090101\"},{\"score\":400,\"riskLevel\":\"REVIEW\",\"description\":\"同城市高危设备7天注册账号数量过多\",\"model\":\"M02090102\"},{\"score\":600,\"riskLevel\":\"REVIEW\",\"description\":\"安装作弊软件\",\"model\":\"M02020131\",\"descriptionV2\":\"高风险设备：多开设备\"},{\"score\":700,\"riskLevel\":\"REJECT\",\"description\":\"相同设备14天内注册账号数量过多\",\"model\":\"M02060101\",\"descriptionV2\":\"高风险设备：账号异常聚集\"},{\"score\":799,\"riskLevel\":\"REJECT\",\"description\":\"高风险设备：篡改设备设备\",\"model\":\"M002050016\",\"descriptionV2\":\"高风险设备：篡改设备设备\"}],\"riskInfo\":[],\"relatedItems\":[],\"description\":\"高风险设备：bssid异常聚集\",\"model\":\"M00630007\",\"descriptionV2\":\"高风险设备：bssid异常聚集\",\"token\":{\"riskReason\":\"\",\"score\":0,\"riskType\":\"\",\"groupId\":\"\",\"groupSize\":0,\"riskGrade\":\"\"}}},\"UserId\":\"\",\"isOneKey\":\"0\",\"Event\":\"user-register\",\"TraceId\":\"58c6e1a9b5774f62a3776618741c014c\"},\"deviceId\":\"202005161703269282c77c0cc582b3bbfdef3d661553fb0167d7230f357ba2\",\"eventCode\":\"user-register\",\"mobile\":\"***********\",\"timeout\":950,\"traceId\":\"58c6e1a9b5774f62a3776618741c014c\",\"userId\":\"\"},\"riskResult\":{\"businessCode\":\"userService\",\"eventCode\":\"user-register\",\"level\":\"REJECT\",\"reason\":\"机器注册\",\"reply\":\"设备异常，不允许注册\",\"rule\":273,\"success\":true,\"traceId\":\"58c6e1a9b5774f62a3776618741c014c\"}}";
        TagExecuteInfo tagExecuteInfo = new TagExecuteInfo();
        GroovyClassLoader groovyLoader = new GroovyClassLoader();
        String condition = "if((riskResult.eventCode == 'user-login' || riskResult.eventCode == 'user-register')){\n" +
                "  if(riskAction.data.loginCheck!=null && riskAction.data.loginCheck.detail!=null && riskAction.data.loginCheck.detail.hits!=null){\n" +
                "  for(Map map:riskAction.data.loginCheck.detail.hits){\n" +
                "  \tif(map.containsValue(\"安装作弊软件1\") && map.containsValue(\"高风险设备：多开设备1\")){\n" +
                "  \t\treturn true;\n" +
                "  \t}\n" +
                "  }\n" +
                "  }\n" +
                "  if(riskAction.data.registerCheck !=null && riskAction.data.registerCheck.detail!=null && riskAction.data.registerCheck.detail.hits!=null){\n" +
                "  for(Map map:riskAction.data.registerCheck.detail.hits){\n" +
                "  \tif(map.containsValue(\"安装作弊软件1\") && map.containsValue(\"高风险设备：多开设备1\")){\n" +
                "  \t\treturn true;\n" +
                "  \t}\n" +
                "  }\n" +
                "  \n" +
                "  }\n" +
                "  return false;\n" +
                " }\n" +
                " return false;";
        Class<Script> groovyClass = (Class<Script>) groovyLoader.parseClass(condition);
        Script script = groovyClass.newInstance();
        tagExecuteInfo.setScript(script);
        tagExecuteInfo.setDependent("riskResult,riskAction");
        Object o = executeRule(JSONObject.parseObject(request, Map.class), tagExecuteInfo);
        System.out.println(o);
    }

    public static Object executeRule(Map request, TagExecuteInfo tagExecuteInfo) throws Exception {
        Map<String, Object>  dependentMap = getAllDependencies(request, tagExecuteInfo.getDependent());
        return doExecuteRule(dependentMap, tagExecuteInfo);
    }

    public static Object doExecuteRule(Map contextMap, TagExecuteInfo tagExecuteInfo) throws Exception {
        Object result = null;
        Script script = tagExecuteInfo.getScript();
        Binding binding = new Binding(contextMap);
        script.setBinding(binding);
        result = script.run();
        return result;
    }

    public static  Object executeRuleForOpenAi(Map request, TagExecuteInfo tagExecuteInfo){
        Object result = null;
        Map<String, Object> dependentMap = null;
        Script script = tagExecuteInfo.getScript();
        dependentMap = getAllDependencies(request, tagExecuteInfo.getDependent());
        Object outputMess = request.get("outputMess");
        JSONObject jsonObject = JSONObject.parseObject(outputMess+"");
        List resultList = JSONObject.parseObject(jsonObject.getString("result"), List.class);
        JSONObject newJSON = new JSONObject();
        if (dependentMap.containsKey("outputMess")) {
            HashMap<String, Object> resultMap = Maps.newHashMap();
            resultMap.put("result", resultList);
            dependentMap.put("outputMess", resultMap);
            dependentMap.put("outputMess.result", resultList);
        }
        Binding binding = new Binding(dependentMap);
        script.setBinding(binding);
        result = script.run();
        return result;

    }

    public static Map<String, Object> getAllDependencies(Map record, String dependent) {
        Map<String, Object> context = new HashMap<>();
        Set<String> dependencies = getDependencies(dependent);
        if (CollectionUtils.isNotEmpty(dependencies)) {
            dependencies.forEach(dependency -> context.put(dependency, getProperty(record, dependency)));
        }
        return context;
    }

    private static Object getProperty(Map param, String key) {
        if (key.indexOf(".") > 0) {
            return FastJsonUtils.get(new JSONObject(param), key);
        }
        return param.get(key);
    }

    private static Set<String> getDependencies(String dependent) {
        Set<String> set = new HashSet<>();
        if (StringUtils.isNotEmpty(dependent)) {
            String[] split = dependent.split(",");
            for (String attribute : split) {
                set.add(attribute);
            }
        }
        return set;
    }
}
