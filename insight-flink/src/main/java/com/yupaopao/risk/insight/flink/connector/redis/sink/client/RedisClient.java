package com.yupaopao.risk.insight.flink.connector.redis.sink.client;

import com.yupaopao.risk.insight.flink.constants.FactorConstants;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

public class RedisClient {

    /***
     * 申请资源的方法一般放在open里面，在close方法里面关闭
     * */
    public static JedisPool getClient() {
        RedisProperties redisProperties = RedisProperties.getProperties(PropertyType.REDIS);
        GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
        genericObjectPoolConfig.setMaxIdle(redisProperties.getMaxIdle());
        genericObjectPoolConfig.setMaxTotal(redisProperties.getMaxActive());
        genericObjectPoolConfig.setMinIdle(redisProperties.getMinIdle());
        return new JedisPool(genericObjectPoolConfig,
                redisProperties.getHost(),
                redisProperties.getPort(),
                FactorConstants.REDIS_DEFAULT_TIMEOUT,
                redisProperties.getPassword(),
                redisProperties.getDatabase());
    }

    public static void closePool(JedisPool pool) {
        if (pool != null && !pool.isClosed()) {
            pool.close();
        }
    }

    public static void closeJedis(Jedis jedis) {
        if (jedis != null) {
            jedis.close();
        }
    }
}
