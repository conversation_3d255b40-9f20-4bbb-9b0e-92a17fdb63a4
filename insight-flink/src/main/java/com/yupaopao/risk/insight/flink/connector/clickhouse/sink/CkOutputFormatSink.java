package com.yupaopao.risk.insight.flink.connector.clickhouse.sink;

import org.apache.flink.api.common.functions.RuntimeContext;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-08-25 12:04
 *
 ***/
public class CkOutputFormatSink extends RichSinkFunction<String> {

    private CKOutputFormat outputFormat;

    public CkOutputFormatSink(CKOutputFormat outputFormat) {
        this.outputFormat = outputFormat;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        RuntimeContext ctx = getRuntimeContext();
        outputFormat.setRuntimeContext(ctx);
        outputFormat.open(ctx.getIndexOfThisSubtask(), ctx.getNumberOfParallelSubtasks());
    }

    public void invoke(String record) throws Exception {
        outputFormat.writeRecord(record);
    }

    @Override
    public void close() throws Exception {
        outputFormat.close();
    }
}
