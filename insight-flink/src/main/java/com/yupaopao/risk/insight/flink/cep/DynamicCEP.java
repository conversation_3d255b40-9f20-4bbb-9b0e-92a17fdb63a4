package com.yupaopao.risk.insight.flink.cep;

import com.yupaopao.risk.insight.flink.cep.inject.CepListener;
import org.apache.flink.cep.EventComparator;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.streaming.api.datastream.DataStream;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-11 20:00
 *
 ***/
public class DynamicCEP {

    public DynamicCEP() {
    }

    public static <T> PatternStream<T> pattern(DataStream<T> input, Pattern<T, ?> pattern, CepListener<T> cepListener) {
        return new PatternStream(input, pattern, cepListener);
    }

    public static <T> PatternStream<T> pattern(DataStream<T> input, Pattern<T, ?> pattern, EventComparator<T> comparator, CepListener<T> cepListener) {
        PatternStream<T> stream = new PatternStream(input, pattern,cepListener);
        return stream.withComparator(comparator);
    }
}
