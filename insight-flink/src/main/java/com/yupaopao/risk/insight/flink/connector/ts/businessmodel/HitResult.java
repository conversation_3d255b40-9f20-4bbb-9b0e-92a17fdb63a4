package com.yupaopao.risk.insight.flink.connector.ts.businessmodel;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;


@Getter
@Setter
public class HitResult implements Serializable {


    private String traceId;


    private String eventCode;



    private String userId;


    private String deviceId;


    private String clientIp;


    private String mobileNo;

    private String mac;


    private Long costTime;

    private String level;

    private String reply;

    private String reason;


    private Date createdAt;

    private static final long serialVersionUID = 1L;



    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SIMPLE_STYLE);
    }
}