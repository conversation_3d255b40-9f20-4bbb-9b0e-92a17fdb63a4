package com.yupaopao.risk.insight.flink.job.portrait.async;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Maps;
import com.yupaopao.risk.insight.flink.connector.mysql.source.MysqlSource;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;

public class RiskEventProcess {

    private static Logger LOG = LoggerFactory.getLogger(MysqlSource.class);
    private static AtomicLong eventUpdateTime = new AtomicLong(0);

    public static boolean flushEvent(Boolean atOnceFlash) {
        long currentTime = System.currentTimeMillis();
        if (!atOnceFlash){
            String flushTimeGap = PortraitApolloProperties.getConfigByKey("risk.event.flush.time.gap", "20");
            if (currentTime-Long.valueOf(flushTimeGap)*60*1000<eventUpdateTime.get()){
                return true;
            }
        }
        eventUpdateTime.set(currentTime);
        Transaction transaction = Cat.newTransaction("insight.portrait", "flush.risk.event.map");
        String sql = PortraitApolloProperties.getConfigByKey("portrait.event.sql", "select code, name from risk_event");
        Connection connection = getConnection().get();
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(sql);
            ResultSet resultSet = ps.executeQuery();
            Map<String, String> map = Maps.newHashMap();
            while (resultSet.next()){
                String code = resultSet.getString("code");
                String name = resultSet.getString("name");
                if (StringUtils.isNotEmpty(code) && StringUtils.isNotEmpty(name)){
                    map.put(code, name);
                }
            }
            PortraitApolloProperties.setEventMap(map);
            long currentTimeMillis = System.currentTimeMillis();
        } catch (SQLException e) {
            LOG.error("查询事件报错:", e);
            transaction.setStatus(e);
        }finally {
            try {
                if (null != ps){
                    ps.cancel();
                }
                if (null!=connection){
                    connection.close();
                }
            } catch (SQLException e) {
                transaction.setStatus(e);
                LOG.error("关闭sql链接出错:", e);
            }
            transaction.complete();
        }

        return true;
    }

    private static Optional<Connection> getConnection() {
        try {
            DBProperties dbProperties = DBProperties.getProperties(PropertyType.DB);
            Class.forName(dbProperties.getDriver());
            Connection connection = DriverManager.getConnection(dbProperties.getUrl(), dbProperties.getUsename(), dbProperties.getPassword());

            return Optional.of(connection);
        } catch (Exception e) {
            LOG.info("connect to mysql error : ", e);
        }
        return Optional.empty();
    }
}
