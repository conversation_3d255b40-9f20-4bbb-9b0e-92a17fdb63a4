package com.yupaopao.risk.insight.flink.job.processor.graph;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseFullTableInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseInputSplitReadThread;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseSplit;
import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import com.yupaopao.risk.insight.common.meta.JobParams;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import groovy.lang.GroovyClassLoader;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.graph.Edge;
import org.apache.hadoop.hbase.CompareOperator;
import org.apache.hadoop.hbase.client.HTable;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Scan;
import org.apache.hadoop.hbase.filter.BinaryComparator;
import org.apache.hadoop.hbase.filter.SingleColumnValueFilter;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;

import static com.yupaopao.risk.insight.common.property.enums.PropertyType.CLICK_HOUSE;
import static com.yupaopao.risk.insight.common.property.enums.PropertyType.HBASE;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-11 16:58
 *
 ***/

@Slf4j
public class EdgeExportProcessor implements BatchJobBuilder.MainProcessor {
    @Override
    public void internalProcess(ExecutionEnvironment env, Map<String, String> argMap) throws Exception {
        //sink
        ClickHouseProperties clickHouseProperties = ClickHouseProperties.getProperties(CLICK_HOUSE);
        clickHouseProperties.setBatchSize(50000);
        CKOutputFormat ckOutputFormat = new CKOutputFormat(clickHouseProperties, "graph_edge");

        HBaseProperties hBaseProperties = HBaseProperties.getProperties(HBASE);

        JobParams.TableQueryDatePeriod period = new JobParams.TableQueryDatePeriod();
        period.setBeginDate(argMap.get("beginDate"));
        period.setEndDate(argMap.get("endDate"));
        period.setTableName("graph_edge");

        TsTableInfo tsInTable = new TsTableInfo()
                .withTableName("graph_edge")
                .withBucketPerSplit(1)
                .withColumnNameType("fromVertex#String,toVertex#String,label#String")
                .withTableQueryPeriod(period)
                .withTableType("SYSTEM");
        LinkedBlockingQueue<Result> cache = new LinkedBlockingQueue<>(1024 * 10);


        final TypeInformation<Edge<String, Long>> edgeType = TypeInformation.of(new TypeHint<Edge<String, Long>>() {
            @Override
            public TypeInformation<Edge<String, Long>> getTypeInfo() {
                return super.getTypeInfo();
            }
        });

        env.createInput(new HBaseFullTableInputFormat(tsInTable, hBaseProperties,
                cache) {

            private transient GroovyClassLoader groovyLoader = new GroovyClassLoader();
            private transient groovy.lang.Script script;

            public void openInputFormat() throws IOException {
                super.openInputFormat();

                try {
                    String filterCondition = argMap.get("filterCondition");
                    if(StringUtils.isNotBlank(filterCondition)){
                        Class<Script> groovyClass = (Class<Script>) groovyLoader.parseClass(filterCondition);
                        script = groovyClass.newInstance();
                    }
                }catch (Exception e){
                    log.error("error for create groovy script: ",e);
                }
            }

            public void closeInputFormat() throws IOException {
                super.closeInputFormat();
                script = null;
                if (groovyLoader != null) {
                    groovyLoader.clearCache();
                }
            }

            @Override
            public String parseHBaseResult(Result rowResult) {
                String rowKey = Bytes.toString(rowResult.getRow());
//                if (rowKey.endsWith("user-follow") || rowKey.endsWith("im-message")) {
//                    return "{}";
//                }
                byte[] fromVertexByte = rowResult.getValue(HBaseConstants.HBASE_FAMILY_KEY, Bytes.toBytes("fromVertex"));
                byte[] toVertexByte = rowResult.getValue(HBaseConstants.HBASE_FAMILY_KEY, Bytes.toBytes("toVertex"));
                byte[] labelByte = rowResult.getValue(HBaseConstants.HBASE_FAMILY_KEY, Bytes.toBytes("label"));
                String label = Bytes.toString(labelByte);
//                if ("relateToIp".equals(label)) {
//                    return "{}";
//                }
                String fromVertex = Bytes.toString(fromVertexByte);
                String toVertex = Bytes.toString(toVertexByte);
                if ("0.0.0.0".equals(fromVertex) || "0.0.0.0".equals(toVertex)) {
                    return "{}";
                }
                if ("000".equals(fromVertex) || "000".equals(toVertex)) {
                    return "{}";
                }
                Map<String, String> rowData = new HashMap<>();
                rowData.put("fromVertex", fromVertex);
                rowData.put("toVertex", toVertex);
                rowData.put("label",label);
                return JSON.toJSONString(rowData);
            }

            @Override
            public HBaseInputSplitReadThread initReadThread(HBaseSplit.BucketSplit bucket, HTable currentTable,
                                                            String hbaseTableName, String threadName,
                                                            TsTableInfo tsTableInfo, LinkedBlockingQueue<Result> queue) {
                HBaseInputSplitReadThread readThread = new HBaseInputSplitReadThread(bucket,
                        currentTable,
                        queue,
                        hbaseTableName,
                        tsTableInfo.getTableId(),
                        tsTableInfo.getTableType(),
                        tsTableInfo.getColumnNames(),
                        threadName
                ) {

                    public Scan initScan() {
                        Scan scan = super.initScan();
                        SingleColumnValueFilter filter = new SingleColumnValueFilter(HBaseConstants.HBASE_FAMILY_KEY,
                                Bytes.toBytes("label"), CompareOperator.EQUAL,
                                new BinaryComparator(Bytes.toBytes("hasDevice")));
                        scan.setFilter(filter);
                        return scan;
                    }
                };
                return readThread;
            }

        }).setParallelism(1)
                .filter(elem -> StringUtils.isNotEmpty(elem) && !HBaseUtil.emptyResultJson(elem))
                .output(ckOutputFormat);
    }
}
