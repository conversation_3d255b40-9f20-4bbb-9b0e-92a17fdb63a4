package com.yupaopao.risk.insight.flink.connector.clickhouse.beans;

import lombok.Data;

import java.util.List;

/**
 * Created by Avalon on 2020/3/11 18:42
 */
@Data
public class ClickHouseRequestBlank {
    private final List<String> values;
    private final String targetTable;
    private int attemptCounter;

    public ClickHouseRequestBlank(List<String> values, String targetTable) {
        this.values = values;
        this.targetTable = targetTable;
        this.attemptCounter = 0;
    }

    public List<String> getValues() {
        return values;
    }

    public void incrementCounter() {
        this.attemptCounter++;
    }

    public int getAttemptCounter() {
        return attemptCounter;
    }

    public String getTargetTable() {
        return targetTable;
    }


}