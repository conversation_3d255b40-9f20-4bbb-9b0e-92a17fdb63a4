package com.yupaopao.risk.insight.flink.connector.clickhouse.source;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.io.DefaultInputSplitAssigner;
import org.apache.flink.api.common.io.RichInputFormat;
import org.apache.flink.api.common.io.statistics.BaseStatistics;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.io.InputSplitAssigner;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-05-11 18:46
 * 读取ck，简单版，适合一次读取数据量不大的，如果数据量很大需要考虑定制inputSplit，避免一次查询过多
 ***/


@Slf4j
public class CKInputFormat extends RichInputFormat<String, CKInputSplit> {

    private ClickHouseProperties properties;
    //    private String tableName;
//    private String startDate; //yyyyMMdd include
//    private String endDate;  //yyyyMMdd  exclude
    private String baseSql;
    private String splitSql;
    private String jsonParam;
    private LinkedBlockingQueue<String> fetchedCacheList = null;

    private LongCounter readCounter = new LongCounter();

    private List<CKReadThread> threadList = new ArrayList<>();


    public CKInputFormat(ClickHouseProperties clickHouseProperties, String baseSql, String jsonParam) {
        this.properties = clickHouseProperties;
        this.baseSql = baseSql;
        this.jsonParam = jsonParam;
//        this.startDate = startDate;
//        this.endDate = endDate;
//        this.waitTime = waitTime;
    }

    @Override
    public void configure(Configuration parameters) {

    }

    @Override
    public BaseStatistics getStatistics(BaseStatistics cachedStatistics) throws IOException {
        return null;
    }

    @Override
    public CKInputSplit[] createInputSplits(int minNumSplits) throws IOException {
        Map<String, Object> paramMap = null;
        if (StringUtils.isNotEmpty(jsonParam)) {
            paramMap = JSON.parseObject(jsonParam);
        }
        if (paramMap == null) {
            paramMap = new HashMap<>();
        }
        if (!paramMap.containsKey("startDate") || !paramMap.containsKey("endDate")) {
            CKInputSplit split = new CKInputSplit(baseSql, 0);
            return new CKInputSplit[]{split};
        } else {
            Date start = InsightDateUtils.getDateFromString(paramMap.get("startDate").toString(),
                    InsightDateUtils.DATE_FORMAT_yyyyMMdd);
            Date end = InsightDateUtils.getDateFromString(paramMap.get("endDate").toString(),
                    InsightDateUtils.DATE_FORMAT_yyyyMMdd);
            List<CKInputSplit> splits = new ArrayList<>();
            //按天split
            while (start.before(end)) {
                String newSql = baseSql;
                Date nextDay = DateUtils.addDays(start, 1);
                if (!baseSql.contains("where")) {
                    newSql += " where 1=1 ";
                }
                newSql += " and createdAt between '" + InsightDateUtils.getDateStr(start,
                        InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss) + "' and '" + InsightDateUtils.getDateStr(nextDay,
                        InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss) +
                        "'   order by createdAt asc ";
                splits.add(new CKInputSplit(newSql, splits.size()));
                start = nextDay;
            }
            return splits.toArray(new CKInputSplit[0]);
        }
    }

    @Override
    public InputSplitAssigner getInputSplitAssigner(CKInputSplit[] inputSplits) {
        return new DefaultInputSplitAssigner(inputSplits);
    }

    @Override
    public void open(CKInputSplit split) throws IOException {
        try {
            log.info("start to open ckInputFormat ... ");
            getRuntimeContext().addAccumulator("ckReadCounter-" + split.getSplitNumber(), readCounter);
            properties.setDataTransferTimeout(60000 * 5); //20min for large data
            properties.setSocketTimeout(60000 * 5); //20min for large data
            properties.setConnectionTimeout(60000 * 1); //20min for large data
//            connection = ClickHouseUtil.createConnection(properties);
            splitSql = split.getSql();
            fetchedCacheList = new LinkedBlockingQueue<>(1024*32);
            CKReadThread t = new CKReadThread(splitSql, fetchedCacheList, properties);
            threadList.add(t);
            t.start();
            readCounter.add(fetchedCacheList.size());
            log.info("split sql: {}", splitSql);
        } catch (Exception e) {
            log.error("open ckInputForm error: ", e);
            throw new IOException(e);
        }
    }

    @Override
    public boolean reachedEnd() throws IOException {
        threadList.removeIf(e -> e.exit);
        return CollectionUtils.isEmpty(threadList) && CollectionUtils.isEmpty(fetchedCacheList);
    }

    @Override
    public String nextRecord(String reuse) throws IOException {
        if (fetchedCacheList == null) {
            return "{}";
        }
        try {
            String rowResult = fetchedCacheList.poll(4, TimeUnit.MILLISECONDS);
            if (rowResult == null) {
                return "{}";
            }
            return rowResult;
        } catch (InterruptedException e) {
            return "{}";
        }
    }

    @Override
    public void close() throws IOException {
        try {
            if (fetchedCacheList != null && !fetchedCacheList.isEmpty()) {
                fetchedCacheList.clear();
            }
        } catch (Exception e) {
            log.error("close ckInputForm error: ", e);
            throw new IOException(e);
        }
    }


}
