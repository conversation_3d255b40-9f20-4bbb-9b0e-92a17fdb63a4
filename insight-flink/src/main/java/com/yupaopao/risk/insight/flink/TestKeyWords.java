package com.yupaopao.risk.insight.flink;


import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.flink.api.common.ExecutionConfig;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.operators.translation.KeyExtractingMapper;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.windowing.assigners.*;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.triggers.ProcessingTimeTrigger;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.streaming.api.windowing.windows.Window;
import redis.clients.jedis.ZParams;

import java.util.*;
import java.util.concurrent.TimeUnit;

/****
 * zengxiangcai
 * 2022/6/24 13:50
 ***/
public class TestKeyWords {

    public static void main(String[] args) throws Exception {
       /* Long timestamp = System.currentTimeMillis();
        System.err.println(InsightDateUtils.getDateStr(timestamp,InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));
        Long size = 10*60*1000L;
        Long slide = 3*1000L*60;

        List<TimeWindow> windows = new ArrayList<>((int) (size / slide));
        long lastStart = TimeWindow.getWindowStartWithOffset(timestamp, 16 * 60 * 60 * 1000, slide);
        for (long start = lastStart; start > timestamp - size; start -= slide) {
            TimeWindow w = new TimeWindow(start, start + size);
            System.err.println("start: " + new Date(w.getStart()) + ", end: " + new Date(w.getEnd()));
            windows.add(w);
        }

//        System.err.println(new Date(start));
        System.err.println("ok");


        testRegisterHistoryTime();*/

    }

    private static void testRegisterHistoryTime() throws Exception {
        //1661843975402 -- 2022-08-30 15:19:35
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        //1661843675000 -- 2022-08-30 15:14:35
        EventData e0 = new EventData("zxc",300,1661843675001L);

        EventData e1 = new EventData("zxc",100,1661843975402L);
        //1661843675000 -- 2022-08-30 15:14:35
        EventData e2 = new EventData("zxc",200,1661843675000L);

        List<EventData> dataList = new ArrayList<>();
        dataList.add(e0);
        dataList.add(e1);
        dataList.add(e2);

        env.fromCollection(dataList)
                .keyBy(EventData::getKey)
                .window(new TestAssigner())
                .aggregate(new AggregateFunction<EventData, Integer, Integer>() {
                    @Override
                    public Integer createAccumulator() {
                        return null;
                    }

                    @Override
                    public Integer add(EventData value, Integer accumulator) {
                        if(accumulator!=null){
                            return accumulator>value.getValue()?value.getValue():accumulator;
                        }
                        return value.getValue();
                    }

                    @Override
                    public Integer getResult(Integer accumulator) {
                        return accumulator;
                    }

                    @Override
                    public Integer merge(Integer a, Integer b) {
                        return a>b?b:a;
                    }
                }).printToErr();

        env.execute("test");
        ;
    }

    public static class TestAssigner  extends WindowAssigner<EventData, TimeWindow> {

        @Override
        public Collection<TimeWindow> assignWindows(EventData element, long timestamp, WindowAssignerContext context) {
            long now = element.getEventTime();
            long size = 10*60*1000L;
            long start =
                    TimeWindow.getWindowStartWithOffset(
                            now, 0, size);
            return Collections.singletonList(new TimeWindow(start, start + size));
        }

        @Override
        public Trigger<EventData, TimeWindow> getDefaultTrigger(StreamExecutionEnvironment env) {
            return new TestTrigger();
        }

        @Override
        public TypeSerializer<TimeWindow> getWindowSerializer(ExecutionConfig executionConfig) {
            return new TimeWindow.Serializer();
        }

        @Override
        public boolean isEventTime() {
            return false;
        }
    }

    public static class TestTrigger extends Trigger<EventData, TimeWindow> {

        @Override
        public TriggerResult onElement(
                EventData element, long timestamp, TimeWindow window, TriggerContext ctx) {
            ctx.registerProcessingTimeTimer(window.maxTimestamp());
            return TriggerResult.CONTINUE;
        }

        @Override
        public TriggerResult onEventTime(long time, TimeWindow window, TriggerContext ctx)
                throws Exception {
            return TriggerResult.CONTINUE;
        }

        @Override
        public TriggerResult onProcessingTime(long time, TimeWindow window, TriggerContext ctx) {
            return TriggerResult.FIRE;
        }

        @Override
        public void clear(TimeWindow window, TriggerContext ctx) throws Exception {
            ctx.deleteProcessingTimeTimer(window.maxTimestamp());
        }

        @Override
        public boolean canMerge() {
            return true;
        }

        @Override
        public void onMerge(TimeWindow window, OnMergeContext ctx) {
            // only register a timer if the time is not yet past the end of the merged window
            // this is in line with the logic in onElement(). If the time is past the end of
            // the window onElement() will fire and setting a timer here would fire the window twice.
            long windowMaxTimestamp = window.maxTimestamp();
            if (windowMaxTimestamp > ctx.getCurrentProcessingTime()) {
                ctx.registerProcessingTimeTimer(windowMaxTimestamp);
            }
        }

        @Override
        public String toString() {
            return "TestTrigger()";
        }
    }

    private static void testProcessingTime(){
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        List<EventData> dataList = getTestList();
        env.fromCollection(dataList)
                .keyBy(EventData::getValue)
                .window(TumblingProcessingTimeWindows.of(Time.of(5, TimeUnit.MINUTES)));


        env.fromCollection(dataList)
                .keyBy(EventData::getValue)
                .window(SlidingProcessingTimeWindows.of(Time.of(10,TimeUnit.MINUTES),Time.of(2,TimeUnit.MINUTES)));
    }

    private static void testEventTime(){
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        List<EventData> dataList = getTestList();
        env.fromCollection(dataList)
                .keyBy(EventData::getValue)
                .window(SlidingEventTimeWindows.of(Time.of(10,TimeUnit.MINUTES),Time.of(2,TimeUnit.MINUTES)));

        env.fromCollection(dataList)
                .keyBy(EventData::getValue)
                .window(TumblingEventTimeWindows.of(Time.of(10,TimeUnit.MINUTES),Time.of(2,TimeUnit.MINUTES)));

    }


    private static List<EventData> getTestList() {
        List<EventData> dataList = new ArrayList<>();
        Long currentTime = System.currentTimeMillis();
        Random random = new Random();
        for (int i = 0; i < 5; i++) {
            dataList.add(new EventData("1",5 - random.nextInt(5), currentTime - i*1000*60 * random.nextInt(5)));
        }
        return dataList;
    }


    @Getter
    @Setter
    @AllArgsConstructor
    public static class EventData {
        private String key;
        private Integer value;
        private Long eventTime; // 毫秒数
    }


}
