package com.yupaopao.risk.insight.flink.job.processor.graph.algorithm;

import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.operators.base.ReduceOperatorBase;
import org.apache.flink.api.java.DataSet;
import org.apache.flink.graph.Edge;
import org.apache.flink.graph.Graph;
import org.apache.flink.graph.Vertex;
import org.apache.flink.graph.asm.degree.annotate.DegreeAnnotationFunctions;
import org.apache.flink.graph.utils.proxy.GraphAlgorithmWrappingDataSet;
import org.apache.flink.types.LongValue;
import org.apache.flink.util.Collector;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-19 15:44
 *
 ***/
public class UndirectedVertexDegree<K, VV, EV> extends GraphAlgorithmWrappingDataSet<K, VV, EV, Vertex<K, LongValue>> {

    @Override
    protected DataSet<Vertex<K, LongValue>> runInternal(Graph<K, VV, EV> input) throws Exception {
        return input.getEdges().flatMap(new MapEdgeToVertex()).groupBy(0).reduce(new DegreeAnnotationFunctions.DegreeCount<>())
                .setCombineHint(ReduceOperatorBase.CombineHint.HASH)
                .setParallelism(parallelism)
                .name("undirected Degree count");
    }


    public static class MapEdgeToVertex<K, EV> implements FlatMapFunction<Edge<K, EV>, Vertex<K, LongValue>> {
        private LongValue vertexDegree = new LongValue(1);

        @Override
        public void flatMap(Edge<K, EV> value, Collector<Vertex<K, LongValue>> out) throws Exception {
            out.collect(new Vertex<>(value.f0, vertexDegree)); //source
            out.collect(new Vertex<>(value.f1, vertexDegree)); //target
        }

    }
}
