package com.yupaopao.risk.insight.flink.connector.ts.serialization;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-11-20 11:12
 *
 ***/

@Slf4j
public class WangAnDeserializationSchemaWrapper extends org.apache.flink.streaming.connectors.kafka.internals.KafkaDeserializationSchemaWrapper<String> {
    private DeserializationSchema<String> deserializationSchema;
    public WangAnDeserializationSchemaWrapper(DeserializationSchema<String> deserializationSchema) {
        super(deserializationSchema);
        this.deserializationSchema = deserializationSchema;
    }

    /***
     * 重写反序列化方法额外读取些 meta info
     * @param record
     * @return
     * @throws Exception
     */
    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<String> out) throws Exception {
        try {
            String value = deserializationSchema.deserialize((byte[]) record.value());
            String key = deserializationSchema.deserialize((byte[]) record.key());
            JSONObject jsonObject = new JSONObject();
            if (value.startsWith("[")) {
                jsonObject.put("data",  JSONArray.parseArray(value));
            }else {
                jsonObject.put("data",  JSONObject.parseObject(value));
            }
            if (!jsonObject.containsKey("timestamp")) {
                jsonObject.put("timestamp", record.timestamp());
            }
            jsonObject.put("kafkaTopic", record.topic());
            out.collect(JSONObject.toJSONString(jsonObject));
        } catch (Exception e) {
            log.error("deserialize kafka data error", e);
        }
    }

    private String topicMeta(ConsumerRecord<byte[], byte[]> record){
        long now = System.currentTimeMillis();
        return String.format("[delay: %d ms, topic: %s, partition: %s , offset: %s ]",(now-record.timestamp()),
                record.topic(),
                record.partition(),record.offset());
    }

}
