package com.yupaopao.risk.insight.flink.job.portrait.process;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.bean.portrait.*;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;

import java.io.*;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

@Slf4j
public class SendOpenAiProcess {


    private static KafkaProducer<String, String> producer;


    public static void sendAi(PortraitBean portraitBean, TreeSet<PortraitFirstTag> riskTags) throws ExecutionException, InterruptedException {
        if (Objects.isNull(producer)){
            if (initKafkaProduce()) {
                log.info("kafka producer init end");
            }
        }
        OpenAiInputVO openAiInputVO = setAiInputVO(portraitBean, riskTags);
        String s = JSONObject.toJSONString(openAiInputVO);
        ProducerRecord<String, String> message = new ProducerRecord<>("BIGDATA_OPENAI_PLATFORM_INPUT", JSONObject.toJSONString(openAiInputVO));
        log.info("发送用户画像到算法平台:{}", openAiInputVO);
        Future<RecordMetadata> send = producer.send(message);
        log.info("发送用户画像到算法平台结束:ObjectId{},{}", openAiInputVO.getObjectId(), send.get());
    }

    private static OpenAiInputVO setAiInputVO(PortraitBean portraitBean, TreeSet<PortraitFirstTag> riskTags){
        OpenAiInputVO openAiInputVO = new OpenAiInputVO();
        openAiInputVO.setBizType(3000);
        openAiInputVO.setObjectId(portraitBean.getUid()+"");
        openAiInputVO.setTime(System.currentTimeMillis());
        openAiInputVO.setEventType("risk_portrait_score");

        List<InputMess> inputMessList = new ArrayList();
        inputMessList.add(setInputMess("iTokenIdFirstActiveTimestamp", portraitBean.getITokenIdFirstActiveTimestamp(), "accountActiveInfo"));
        inputMessList.add(setInputMess("ismIdFirstActiveTimestamp", portraitBean.getIsmIdFirstActiveTimestamp(), "accountActiveInfo"));
        inputMessList.add(setInputMess("iTokenIdActiveDays7d", portraitBean.getITokenIdActiveDays7d(), "accountActiveInfo"));
        inputMessList.add(setInputMess("ismIdActiveDays7d", portraitBean.getIsmIdActiveDays7d(), "accountActiveInfo"));
        inputMessList.add(setInputMess("iTokenIdActiveDays4w", portraitBean.getITokenIdActiveDays4w(), "accountActiveInfo"));
        inputMessList.add(setInputMess("ismIdActiveDays4w", portraitBean.getIsmIdActiveDays4w(), "accountActiveInfo"));
        inputMessList.add(setInputMess("iTokenIdLoginCnt1d", portraitBean.getITokenIdLoginCnt1d(), "accountActiveInfo"));
        inputMessList.add(setInputMess("iTokenIdLoginCnt7d", portraitBean.getITokenIdLoginCnt7d(), "accountActiveInfo"));
        inputMessList.add(setInputMess("ismIdRegisterCnt1d", portraitBean.getIsmIdRegisterCnt1d(), "accountActiveInfo"));
        inputMessList.add(setInputMess("ismIdRegisterCnt7d", portraitBean.getIsmIdRegisterCnt7d(), "accountActiveInfo"));
        inputMessList.add(setInputMess("ismIdLoginCnt1d", portraitBean.getIsmIdLoginCnt1d(), "accountActiveInfo"));
        inputMessList.add(setInputMess("ismIdLoginCnt7d", portraitBean.getIsmIdLoginCnt7d(), "accountActiveInfo"));
        inputMessList.add(setInputMess("ismIdRelateTokenIdCnt1d", portraitBean.getIsmIdRelateTokenIdCnt1d(), "accountRelateInfo"));
        inputMessList.add(setInputMess("ismIdRelateTokenIdCnt7d", portraitBean.getIsmIdRelateTokenIdCnt7d(), "accountRelateInfo"));
        inputMessList.add(setInputMess("ismIdRelateIpCityCnt1d", portraitBean.getIsmIdRelateIpCityCnt1d(), "accountRelateInfo"));
        inputMessList.add(setInputMess("ismIdRelateIpCityCnt7d", portraitBean.getITokenIdFirstActiveTimestamp(), "accountRelateInfo"));
        inputMessList.add(setInputMess("iTokenIdRelateSmIdCnt1d", portraitBean.getITokenIdRelateSmIdCnt1d(), "accountRelateInfo"));
        inputMessList.add(setInputMess("iTokenIdRelateSmIdCnt7d", portraitBean.getITokenIdRelateSmIdCnt7d(), "accountRelateInfo"));
        inputMessList.add(setInputMess("iTokenIdRelateIpCityCnt1d", portraitBean.getITokenIdRelateIpCityCnt1d(), "accountRelateInfo"));
        inputMessList.add(setInputMess("iTokenIdRelateIpCityCnt7d", portraitBean.getITokenIdRelateIpCityCnt1d(), "accountRelateInfo"));
        JSONArray sTokenIdRelateSmIdInfoMap4w = portraitBean.getSTokenIdRelateSmIdInfoMap4w();
        setInputMess(inputMessList, "sTokenIdRelateSmIdInfoMap4w_", sTokenIdRelateSmIdInfoMap4w, "smid", "days");
        JSONArray sTokenIdRelateIpCityInfoMap4w = portraitBean.getSTokenIdRelateIpCityInfoMap4w();
        setInputMess(inputMessList, "sTokenIdRelateIpCityInfoMap4w_", sTokenIdRelateIpCityInfoMap4w, "city", "days");

        List<InputMess> sendOpenAiTagList = PortraitApolloProperties.getSendOpenAiTagList();
        for (InputMess inputMess : sendOpenAiTagList) {
            Integer value = 0;
            if (containTag(inputMess.getKey(), riskTags)) {
                value = 1;
            }
            inputMessList.add(setInputMess(inputMess.getKey(), value, inputMess.getType()));
        }

        openAiInputVO.setInputMess(inputMessList);
        return openAiInputVO;
    }

    private static boolean containTag(String code, TreeSet<PortraitFirstTag> riskTags){
        for (PortraitFirstTag riskTag : riskTags) {
            for (PortraitSubTag portraitSubTag : riskTag.getSubtag()) {
                if (portraitSubTag.getCode().equals(code)){
                    return true;
                }

            }
        }
        return false;
    }


    private static void setInputMess(List<InputMess> inputMessList, String keyPrefix, JSONArray jsonArray,String subKey,String subValue){
        if (Objects.nonNull(jsonArray) && !jsonArray.isEmpty()){
            for (Object cityObj : jsonArray) {
                JSONObject cityJSON = JSONObject.parseObject(JSONObject.toJSONString(cityObj));
                String key = cityJSON.getString(subKey);
                Integer value = cityJSON.getInteger(subValue);
                inputMessList.add(setInputMess(keyPrefix+key, value, "accountCommonInfo"));
            }
        }
    }

    private synchronized static boolean initKafkaProduce(){
        if (Objects.isNull(producer)){
            KafkaProperties kafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA_BIGDATA);
            Properties kafkaProps = kafkaProperties.getProperties();
            //        String matchResultTopic = "RISK-INSIGHT-CEP-MATCH";
            kafkaProps.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
            kafkaProps.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
            producer = new KafkaProducer<>(kafkaProps);
        }
        return true;
    }


    private static InputMess setInputMess(String key,Object value, String type){
        InputMess inputMess = new InputMess();
        inputMess.setKey(key);
        if (Objects.isNull(value) || Integer.valueOf("0") ==value){
            inputMess.setValue(0);
        }else {
            inputMess.setValue(value);
        }
        inputMess.setType(type);

        return inputMess;
    }


    public static void main(String[] args) throws IOException {
        File file = new File("/Users/<USER>/portraitScore.txt");
        FileReader fileReader = new FileReader(file);
        BufferedReader bufferedReader = new BufferedReader(fileReader);
        String line;
        File file2 = new File("/Users/<USER>/portraitScore_2.txt");

        FileWriter fileWriter = new FileWriter(file2);

        while ((line = bufferedReader.readLine())!=null) {
            PortraitBean portraitBean = JSONObject.parseObject(line, PortraitBean.class);
            TreeSet<PortraitFirstTag> riskTags = portraitBean.getRiskTags();
            if (Objects.nonNull(riskTags) && !riskTags.isEmpty()) {
                for (PortraitFirstTag riskTag : riskTags) {
                    String code = riskTag.getCode();
                    if (code.equals("fakeDevice") || code.equals("deviceSuspiciousLabels")
                            || code.equals("machineAccountRisk") || code.equals("UGCAccountRisk")){
                        OpenAiInputVO openAiInputVO = setAiInputVO(portraitBean, riskTags);
                        String s = JSONObject.toJSONString(openAiInputVO);
                        fileWriter.write(s+"\n");
                        fileWriter.flush();
                    }
                }

            }
        }
        fileReader.close();
        fileWriter.close();
    }

}
