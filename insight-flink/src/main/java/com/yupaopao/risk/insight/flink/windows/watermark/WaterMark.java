package com.yupaopao.risk.insight.flink.windows.watermark;

import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import org.apache.flink.streaming.api.functions.AssignerWithPeriodicWatermarks;
import org.apache.flink.streaming.api.watermark.Watermark;

import javax.annotation.Nullable;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc FactorWaterMark
 * @date 2020/5/26
 */
public class WaterMark implements AssignerWithPeriodicWatermarks<FactorCalDetail> {
    private static final long serialVersionUID = -8826150331695788368L;
    private Long currentMaxTimestamp = 0L;
    private Long maxOutOfOrderness;

    public WaterMark(long maxOutOfOrderness) {
        this.maxOutOfOrderness = maxOutOfOrderness;
    }

    @Nullable
    @Override public Watermark getCurrentWatermark() {
        return new Watermark(currentMaxTimestamp - maxOutOfOrderness);
    }


    @Override public long extractTimestamp(FactorCalDetail element, long previousElementTimestamp) {
        long timestamp = element.getTimeStamp();
        currentMaxTimestamp = Math.max(timestamp, currentMaxTimestamp);
        return timestamp;
    }
}
