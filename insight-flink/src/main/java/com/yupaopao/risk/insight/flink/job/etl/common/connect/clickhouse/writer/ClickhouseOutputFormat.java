/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.yupaopao.risk.insight.flink.job.etl.common.connect.clickhouse.writer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.dtstack.flinkx.exception.WriteRecordException;
import com.dtstack.flinkx.restore.FormatState;
import com.dtstack.flinkx.util.ClassUtil;
import com.dtstack.flinkx.util.ExceptionUtil;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.property.connection.OSSProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.CommonClickHouseDataType;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ColumnSyncLocker;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.clickhouse.core.ClickhouseUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.rdb.core.util.DbUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.rdb.writer.JdbcOutputFormat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

@Deprecated
public class ClickhouseOutputFormat extends JdbcOutputFormat {

    private static final Logger LOG = LoggerFactory.getLogger(ClickhouseOutputFormat.class);
    private static final long serialVersionUID = -3658374580410601823L;
    private static final long INTERVAL = 30 * 60 * 1000;

    private long lastSync = 0L;
    private final Map<String, String> columnMap = new HashMap<>();

    @Override protected void openInternal(int taskNumber, int numTasks) {
        try {
            ClassUtil.forName(driverName, getClass().getClassLoader());
            dbConn = ClickhouseUtil.getConnection(dbUrl, username, password);

            if (restoreConfig.isRestore()) {
                dbConn.setAutoCommit(false);
            }

            if (CollectionUtils.isEmpty(fullColumn) || fullColumnType == null) {
                initFullColumnAndType();
            }

            for (String col : column) {
                for (int i = 0; i < fullColumn.size(); i++) {
                    if (col.equalsIgnoreCase(fullColumn.get(i))) {
                        columnType.add(fullColumnType.get(i));
                        break;
                    }
                }
            }

            preparedStatement = prepareTemplates();
            readyCheckpoint = false;

            LOG.info("subTask[{}}] wait finished", taskNumber);
        } catch (SQLException sqe) {
            throw new IllegalArgumentException("open() failed.", sqe);
        }
    }

    @Override protected void writeSingleRecordInternal(Map<String, Object> row) throws WriteRecordException {
        this.save(Collections.singletonList(row));
        if (lastSync < System.currentTimeMillis() - INTERVAL) {
            syncColumn();
            lastSync = System.currentTimeMillis();
        }
    }

    @Override protected void writeMultipleRecordsInternal() throws Exception {
        this.save(rows);
        if (lastSync < System.currentTimeMillis() - INTERVAL) {
            syncColumn();
            lastSync = System.currentTimeMillis();
        }
    }

    @Override public FormatState getFormatState() {
        if (formatState != null){
            formatState.setMetric(outputMetric.getMetricCounters());
        }
        return formatState;
    }

    private void syncColumn() {
        Map<String, String> notExitColumns = fetchNotExitColumns();
        if (!notExitColumns.isEmpty()) {
            LOG.info("准备为 {} 补充 {} 个字段.", table, notExitColumns.size());
            try {
                flushDistributedTable();
                addColumn(table + "_local", notExitColumns);
                addColumn(table, notExitColumns);
            } finally {
                ColumnSyncLocker.isSyncColumn = false;
            }
        } else {
            LOG.info("暂无需补充字段, 数据字段个数: {}", fullColumn.size());
        }
    }

    private Map<String, String> fetchNotExitColumns() {
        initFullColumnAndType();
        Map<String, String> notExitColumns = new HashMap<>();
        columnMap.forEach((key, value) -> {
            if (!fullColumn.contains(key)) {
                notExitColumns.put(key, value);
            }
        });
        return notExitColumns;
    }

    private void cacheColumn(Map<String, Object> value) {
        JSONObject cache = JSONObject.parseObject(JSONObject.toJSONString(value));
        for (String key : cache.keySet()) {
            int index = fullColumn.indexOf(key);
            if (index > -1) {
                if (!isSameType(fullColumnType.get(index), value.get(key))) {
//                    LOG.warn("type error, ckType:{}, data: {}", fullColumnType.get(index), value.get(key));
                    value.put(key, valueParse(fullColumnType.get(index), value.get(key)));
                }
            } else {
                String clickHouseType = CommonClickHouseDataType.getClickHouseType(value.get(key));
                if (StringUtils.isNotBlank(clickHouseType) && !isSpecialKey(clickHouseType)) {
                    columnMap.put(key, clickHouseType);
                }
            }
        }
    }

    private static boolean isSameType(String ckType, Object value) {
        ckType = ckType.toLowerCase();
        if (value instanceof Number) {
            return ckType.contains("int") || ckType.contains("float") || ckType.contains("decimal");
        } else if (value instanceof Boolean) {
            return ckType.contains("int");
        } else  {
            return value instanceof String;
        }
    }

    private Object valueParse(String cktype, Object value) {
        switch (cktype.toLowerCase()) {
            case "string":
            case "datetime":
            case "date":
                // 暂时只对字符串类型做强转处理
                return value.toString();
            default:
                return value;
        }
    }

    private boolean isSpecialKey(String key) {
        return key.contains("\\");
    }

    private void initFullColumnAndType() {
        List<String> nameList = new ArrayList<>();
        List<String> typeList = new ArrayList<>();

        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = dbConn.createStatement();
            rs = stmt.executeQuery("desc " + table);
            while (rs.next()) {
                nameList.add(rs.getString(1));
                typeList.add(rs.getString(2));
            }
        } catch (SQLException e) {
            LOG.error("error to get {} schema, e = {}", table, ExceptionUtil.getErrorMessage(e));
        } finally {
            DbUtil.closeDbResources(rs, stmt, null, false);
        }

        if (CollectionUtils.isEmpty(fullColumn)) {
            fullColumn = nameList;
        }
        if (fullColumnType == null) {
            fullColumnType = typeList;
        }
    }

    private boolean flushDistributedTable() {
        String sql = "SYSTEM FLUSH DISTRIBUTED " + table;
        Statement stmt = null;
        try {
            stmt = dbConn.createStatement();
            LOG.info("flush table: {}", table);
            return stmt.execute(sql);
        } catch (Exception e) {
            LOG.error("sql execute error: ", e);
        } finally {
            DbUtil.closeDbResources(null, stmt, null, false);
        }
        return true;
    }

    private boolean addColumn(String table, Map<String, String> newColumns) {
        StringBuilder sql = new StringBuilder("ALTER TABLE ").append(table).append(" ON CLUSTER default");
        newColumns.forEach((key, value) -> {
            sql.append(" ADD COLUMN IF NOT EXISTS ").append(key).append(" ").append(value).append(",");
        });

        Statement stmt = null;
        try {
            stmt = dbConn.createStatement();
            String alterSql = sql.deleteCharAt(sql.length() - 1).toString();
            LOG.info("alter sql: {}", alterSql);
            return stmt.execute(alterSql);
        } catch (RuntimeException e) {
            // ru.yandex.clickhouse 暂不支持 alter 结果解析，会抛出 JsonParseException，字段会正常修改，忽视该异常
            LOG.info("alter 结果解析异常, 字段会正常修改");
            return true;
        } catch (Exception e) {
            LOG.error("sql execute error: ", e);
        } finally {
            DbUtil.closeDbResources(null, stmt, null, false);
        }
        return false;
    }

    private void save(List<Map<String, Object>> records) {
        if (records.size() == 0) {
            return;
        }
        List<String> datas = new ArrayList<>();
        for (Map<String, Object> record : records) {
            Map<String, Object> mapFlatter = flatterKeyParse(JsonFlatterUtils.toMap(JSON.toJSONStringWithDateFormat(record, "yyyy-MM-dd HH:mm:ss.SSS"), FlattenMode.KEEP_ARRAYS));

            cacheColumn(mapFlatter);
            if (!mapFlatter.containsKey("createdAt")) {
                mapFlatter.put("createdAt", DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            } else {
                mapFlatter.put("createdAt", formatTime(mapFlatter.get("createdAt")));
            }
            datas.add(JSONObject.toJSONString(mapFlatter));
        }
        String sql = buildSql(datas);
        Statement stmt = null;
        try {
            stmt = dbConn.createStatement();
            stmt.execute(sql);
        } catch (Exception e) {
            String objectName = writeToOSS(sql);
            LOG.error("sql execute error: {}, objectName {}", e, objectName);
        } finally {
            DbUtil.closeDbResources(null, stmt, null, false);
        }
    }

    private String formatTime(Object createdAt) {
        if (createdAt instanceof Long) {
            return new DateTime(createdAt).toString("yyyy-MM-dd HH:mm:ss");
        } else if (createdAt instanceof String) {
            try {
                return new DateTime(createdAt).toString("yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                return (String) createdAt;
            }
        }
        return DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    private String buildSql(List<String> datas) {
        String resultLines = String.join("\n", datas);
        String resultFormat = "INSERT INTO %s FORMAT JSONEachRow\n%s";
        return String.format(resultFormat, table, resultLines);
    }

    private String writeToOSS(String content) {
        try {
            String sqlFileName = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyyMMddHHmmss);
            String objectName = FlinkConstants.getCKSqlBasePath() + this.table + "/" + sqlFileName + ".sql";
            OSSProperties ossProperties = OSSProperties.getProperties(PropertyType.OSS);
            OSS ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(),
                ossProperties.getAccessKeySecret());
            ossClient.putObject(ossProperties.getBucket(), objectName, new ByteArrayInputStream(content.getBytes()));
            ossClient.shutdown();
            return objectName;
        } catch (Exception e) {
            LOG.error("write to oss error: ", e);
        }
        return null;
    }
}
