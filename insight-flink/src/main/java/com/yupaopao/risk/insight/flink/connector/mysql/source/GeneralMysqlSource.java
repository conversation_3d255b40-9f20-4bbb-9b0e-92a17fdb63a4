package com.yupaopao.risk.insight.flink.connector.mysql.source;

import com.google.common.collect.Maps;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class GeneralMysqlSource extends RichSourceFunction<List<Map>> implements Serializable {

    private static Logger LOG = LoggerFactory.getLogger(MysqlSource.class);

    private Connection connection = null;
    private PreparedStatement ps = null;
    private DBProperties dbProperties;
    private String sql;
    private Map<String, String> fields;

    public GeneralMysqlSource(DBProperties dbProperties, String sql, Map<String, String> fields) {
        this.dbProperties = dbProperties;
        if (StringUtils.isEmpty(sql)){
            throw new IllegalArgumentException("必须参数sql:"+sql);
        }
        if (MapUtils.isEmpty(fields)){
            throw new IllegalArgumentException("必须参数字段名和类型映射集合:"+fields);
        }
        this.sql = sql;
        this.fields = fields;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        connection = getConnection();
        ps = connection.prepareStatement(sql);

    }

    @Override
    public void run(SourceContext<List<Map>> ctx) throws Exception {
        while (true) {
            ResultSet resultSet = ps.executeQuery();
            List<Map> resultList = new ArrayList<>();
            while (resultSet.next()){
                Set<Map.Entry<String, String>> entries = fields.entrySet();
                Map result = Maps.newHashMap();
                for (Map.Entry<String, String> entry : entries) {
                    result.put(entry.getKey(), getResultSetValue(entry.getKey(), entry.getValue(), resultSet));
                }
                resultList.add(result);
            }
            ctx.collect(resultList);

            Thread.sleep(10 * 1000);
        }
    }

    private Object getResultSetValue(String field, String type, ResultSet resultSet) throws SQLException {

        if ("String".equalsIgnoreCase(type)){
            return resultSet.getString(field);
        }else if ("Long".equalsIgnoreCase(type)){
            return resultSet.getLong(field);
        }else {
            return resultSet.getObject(field);
        }
    }

    @Override
    public void cancel() {
        try {
            ps.cancel();
            connection.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public Connection getConnection() {
        try {
            Class.forName(dbProperties.getDriver());
            connection = DriverManager.getConnection(dbProperties.getUrl(), dbProperties.getUsename(), dbProperties.getPassword());
        } catch (Exception e) {
            LOG.info("connect to mysql error : ", e);
        }
        return connection;
    }
}
