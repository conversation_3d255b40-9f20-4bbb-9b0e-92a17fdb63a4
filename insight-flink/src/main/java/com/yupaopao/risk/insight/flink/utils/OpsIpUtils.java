package com.yupaopao.risk.insight.flink.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/****
 * zengxiangcai
 * 2023/5/11 15:34
 ***/

@Slf4j
public class OpsIpUtils {
    //查询ip信息
    private static String ipSearchUrl = "https://innerapi.yupaopao.com/ops/ip-geo";

    private static OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(1, TimeUnit.SECONDS)//设置连接超时时间
            .readTimeout(5, TimeUnit.SECONDS)//设置读取超时时间
            .writeTimeout(5, TimeUnit.SECONDS)
            .build();

    public static Map<String, String> searchIp(String ip) {
        Map<String, String> resultMap = new HashMap<>();
        Map<String, String> queryParam = new HashMap<>();
        queryParam.put("ip", ip);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), JSON.toJSONString(queryParam));

        Request request = new Request.Builder()
                .url(ipSearchUrl)
                .post(body)
                .build();
        Response response = null;
        try {
            response = httpClient.newCall(request).execute();
            if (response == null || response.body() == null) {
                return resultMap;
            }
            String strResponse = response.body().string();
            JSONObject respObj = JSON.parseObject(strResponse);
            if (respObj != null && respObj.containsKey("result")) {
                JSONObject ipDetail = respObj.getJSONObject("result");
                resultMap.put("country", ipDetail.getString("country"));
                resultMap.put("city", ipDetail.getString("city"));
                resultMap.put("province", ipDetail.getString("province"));
            }
        } catch (Exception e) {
            log.error("query ip city error, ip=" + ip, e);
        }
        return resultMap;

    }
}
