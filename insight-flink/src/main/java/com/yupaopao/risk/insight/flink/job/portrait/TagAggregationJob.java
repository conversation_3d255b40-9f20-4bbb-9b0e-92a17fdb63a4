package com.yupaopao.risk.insight.flink.job.portrait;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.yupaopao.risk.insight.common.beans.tag.TagMessage;
import com.yupaopao.risk.insight.flink.bean.portrait.AggTag;
import com.yupaopao.risk.insight.flink.connector.kafka.sink.serialization.KafkaObjSerializationSchema;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.OriginalStringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.connector.ts.serialization.StringKafkaDeserializationSchemaWrapper;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.base.CheckpointSetting;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.portrait.support.MysqlSupport;
import com.yupaopao.risk.insight.flink.job.portrait.support.TagAggSupport;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import com.yupaopao.risk.insight.common.property.connection.KafkaProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.windows.AggTagDetail;
import com.yupaopao.risk.insight.flink.windows.aggregate.*;
import com.yupaopao.risk.insight.flink.windows.assigner.TagDaySlidingWindowAssigner;
import com.yupaopao.risk.insight.flink.windows.process.TagAggTransferProcess;
import com.yupaopao.risk.insight.flink.windows.triggers.TagTumblingTimeTrigger;
import com.yupaopao.risk.insight.flink.windows.watermark.TagWaterMark;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.api.TimeCharacteristic;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.util.*;

import static com.yupaopao.risk.insight.flink.constants.FlinkConstants.KAFKA_GROUP_ID_AGGREGATE_TAG_RESULT;

@Slf4j
public class TagAggregationJob {

    public static void main(String[] args) throws Exception {
        new FlinkJobBuilder()
            .withJobName("tag-aggragate")
            .withCheckpointSetting(new CheckpointSetting(60 * 1000, 40 * 1000, true))
            .withMainProcessor(new TagAggregationProcessor())
            .start(args);
    }

    private static class TagAggregationProcessor implements FlinkJobBuilder.MainProcessor {
        private static final long serialVersionUID = 3186574626429327749L;

        @Override public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
            env.getCheckpointConfig().setCheckpointTimeout(20 * 60 * 1000);
            env.setStreamTimeCharacteristic(TimeCharacteristic.EventTime);
            Config config = ConfigService.getAppConfig();
            int parallelism = config.getIntProperty("tag.parallelism", 1);
            KafkaProperties riskKafkaProperties = KafkaProperties.getProperties(PropertyType.KAFKA_RISK_ALI);
            riskKafkaProperties.getProperties().put("group.id", KAFKA_GROUP_ID_AGGREGATE_TAG_RESULT); // consume  group id

            KafkaProperties riskKafkaAliProperties = KafkaProperties.getProperties(PropertyType.KAFKA_RISK_ALI);
            riskKafkaAliProperties.getProperties().put("group.id", KAFKA_GROUP_ID_AGGREGATE_TAG_RESULT); // consume  group id

            DBProperties dbProperties = DBProperties.getProperties(PropertyType.DB_INSIGHT);
            Set<String> dataChannel = new MysqlSupport(dbProperties).getDataChannelBySource("1");
            dataChannel.add("RISK-ONLINE-RESULT-LOG");// "", "risk_factor_operate"
            dataChannel.add("risk_factor_operate");
            String[] objects = dataChannel.toArray(new String[0]);
            // 数据流
            DataStream<String> stream = env.addSource(getKafkaConsumer(new StringKafkaDeserializationSchemaWrapper(new SimpleStringSchema()), riskKafkaProperties, objects))
                .setParallelism(parallelism)
                .name("tag kafka source");

            DataStream<String> recallStream = env.addSource(getKafkaConsumer(new OriginalStringKafkaDeserializationSchemaWrapper(new SimpleStringSchema()), riskKafkaAliProperties, "RISK-TAG-RECALL"))
                .setParallelism(parallelism)
                .name("tag recall kafka source");

            SingleOutputStreamOperator<AggTagDetail> process = stream.connect(recallStream)
                .process(new TagAggTransferProcess()).setParallelism(parallelism)
                .assignTimestampsAndWatermarks(new TagWaterMark(10 * 1000))
                .process(new ProcessFunction<AggTagDetail, AggTagDetail>() {
                    private static final long serialVersionUID = 5079846577378042343L;

                    @Override public void processElement(AggTagDetail value, Context ctx, Collector<AggTagDetail> out) throws Exception {
                        TagAggragateEnum enumData = TagAggragateEnum.getEnumData(value.getFunction());
                        if (null != enumData) {
                            ctx.output(enumData.getOutputTag(), value);
                        }
                    }
                }).setParallelism(parallelism)
                ;

            FlinkKafkaProducer<TagMessage> kafkaProducer =
                    getKafkaProducer(KafkaProperties.getProperties(PropertyType.KAFKA_RISK_ALI));

            for (TagAggragateEnum tagAggragateEnum : TagAggragateEnum.values()) {
                process.getSideOutput(tagAggragateEnum.getOutputTag())
                    .keyBy(AggTagDetail::getGroupKey)
                    .window(new TagDaySlidingWindowAssigner())
                    .trigger(new TagTumblingTimeTrigger())
                    .aggregate(tagAggragateEnum.getAggregateFunction()).setParallelism(parallelism)
                    .flatMap(new FlatMapFunction<TagAggregateResult, TagMessage>() {
                        @Override public void flatMap(TagAggregateResult value, Collector<TagMessage> out) throws Exception {
                            AggTag aggTag = TagAggSupport.getAggTagById(value.getId());
                            if (Objects.isNull(aggTag)) {
                                return;
                            }
                            TagMessage result = new TagMessage();
                            switch (tagAggragateEnum.key) {
                                case "COUNT":
                                    result.setValue(value.getCount());
                                    break;
                                case "COUNT_DISTINCT":
                                    if (value.getCount() > 100) {
                                        log.info("large distinct code:{}, key: {}, size:{}", aggTag.getCode(), value.getGroupKey(), value.getCount());
                                    }
                                    result.setValue(value.getCount());
                                    break;
                                case "SET":
                                    result.setValue(value.getDistinct());
                                    break;
                                case "MAP":
                                    if (value.getMap().size() > 100) {
                                        log.info("large map code:{}, key: {}, size:{}", aggTag.getCode(), value.getGroupKey(), value.getMap().size());
                                    }
                                    result.setValue(value.getMap());
                                    break;
                                default:
                                    return;
                            }
                            if ("nationCode,Mobile".equals(aggTag.getGroupKey())) {
                                result.setGroupKey("Mobile");
                            } else {
                                result.setGroupKey(aggTag.getGroupKey());
                            }
                            String[] split = value.getGroupKey().split("#");
                            result.setGroupValue(split[split.length - 1]);
                            result.setType(value.getType());
                            result.setCode(aggTag.getCode());
                            result.setName(aggTag.getName());
                            result.setCreatedAt(System.currentTimeMillis());
                            result.setValueType(aggTag.getValueType());
//                            log.info("发送聚合标签结果：{}", result);
                            out.collect(result);
                        }
                    }).setParallelism(parallelism)
                    .addSink(kafkaProducer)
                    .name(tagAggragateEnum.getKey()).setParallelism(parallelism)
                ;
            }
        }
    }

    private static FlinkKafkaConsumer<String> getKafkaConsumer(StringKafkaDeserializationSchemaWrapper wrapper, KafkaProperties kafkaProperties, String... topic) {
        List<String> topics = Arrays.asList(topic);

        FlinkKafkaConsumer<String> consumer = new FlinkKafkaConsumer<>(topics,
            wrapper,
            kafkaProperties.getProperties());
        // 设定项目启动读取时间，不影响从checkpoint 恢复
        consumer.setStartFromTimestamp(System.currentTimeMillis() - 1000);
        return consumer;
    }

    private static FlinkKafkaProducer<TagMessage> getKafkaProducer(KafkaProperties kafkaProperties) {
        Properties kafkaProps = kafkaProperties.getProperties();
        kafkaProps.put("transaction.timeout.ms", 1000 * 60 * 5 + "");
        String tagTopic = FlinkConstants.TOPIC_RISK_INSIGHT_CUSTOM_TAG;

        return new FlinkKafkaProducer<>(
                tagTopic,
            new KafkaObjSerializationSchema<>(tagTopic),
            kafkaProps, FlinkKafkaProducer.Semantic.AT_LEAST_ONCE);
    }

    private enum TagAggragateEnum {
        /**
         * OutputTag与AggregateFunction enum
         */
        COUNT("COUNT", new OutputTag<AggTagDetail>("count") {}, new TagCountFunction()),
        COUNT_DISTINCT("COUNT_DISTINCT", new OutputTag<AggTagDetail>("count_distinct") {}, new TagDistinctCountFunction()),
        SET("SET", new OutputTag<AggTagDetail>("set") {}, new TagSetFunction()),
        MAP("MAP", new OutputTag<AggTagDetail>("map") {}, new TagMapFunction()),
        ;

        TagAggragateEnum(String key, OutputTag<AggTagDetail> outputTag, AggregateFunction<AggTagDetail, TagAggregateResult, TagAggregateResult> aggregateFunction) {
            this.key = key;
            this.outputTag = outputTag;
            this.aggregateFunction = aggregateFunction;
        }

        private String key;
        private OutputTag<AggTagDetail> outputTag;
        private AggregateFunction<AggTagDetail, TagAggregateResult, TagAggregateResult> aggregateFunction;

        public String getKey() {
            return key;
        }

        public OutputTag<AggTagDetail> getOutputTag() {
            return outputTag;
        }

        public AggregateFunction<AggTagDetail, TagAggregateResult, TagAggregateResult> getAggregateFunction() {
            return aggregateFunction;
        }

        public static TagAggragateEnum getEnumData(String key) {
            return Arrays.stream(TagAggragateEnum.values()).filter(p -> p.key.equals(key)).findFirst().orElse(null);
        }
    }
}
