package com.yupaopao.risk.insight.flink.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.flink.bean.audit.MxMetric;
import groovy.lang.GroovyClassLoader;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Avalon on 2020/7/1 22:07
 */
@Slf4j
public class MxMetricCacheUtils {

    private volatile static Map<String/** function */, List<MxMetric>> metricFuncMap = new HashMap<>();
    private volatile static List<MxMetric> allMetric = new ArrayList<>();

    private final static String METRIC_NAMESPACE = "middleware.audit.metric";

    private static Config config;

    static {
        config = ApolloProperties.getConfig(METRIC_NAMESPACE);
        config.addChangeListener((configChangeEvent -> {
            Set<String> keys = configChangeEvent.changedKeys();
            if (CollectionUtils.isNotEmpty(keys)) {
                refreshAllMetricCache();
            }
        }));
        refreshAllMetricCache();
    }


    private static void refreshAllMetricCache() {
        try {
            Map<String/** function */, List<MxMetric>> tmpMetricFuncMap = new HashMap<>();
            List<MxMetric> tmpMetricList = new ArrayList<>();

            Set<String> properties = config.getPropertyNames();
            properties.stream().forEach(code -> {
                String metricConfig = config.getProperty(code, "");
                if (StringUtils.isEmpty(metricConfig)) {
                    return;
                }
                try {
                    MxMetric metric = JSON.parseObject(metricConfig, MxMetric.class);
                    if (metric != null && !StringUtils.isEmpty(metric.getFunction())) {
                        List<MxMetric> metrics = tmpMetricFuncMap.computeIfAbsent(metric.getFunction(), k -> new ArrayList<>());
                        if (StringUtils.isNotEmpty(metric.getContent())) {
                            metric.setScript(assembleScript(metric.getContent()));
                        }
                        metrics.add(metric);
                        tmpMetricList.add(metric);
                    }
                } catch (Exception e) {
                    log.error("parse metric[{}] config occurs error: ", metricConfig, e);
                }
            });
            metricFuncMap = tmpMetricFuncMap;
            allMetric = tmpMetricList;
        } catch (Exception e) {
            log.error("refresh local metric cache occurs error: ", e);
        }
    }

    private static Script assembleScript(String content) throws IllegalAccessException, InstantiationException {
        GroovyClassLoader groovyLoader = new GroovyClassLoader();
        Class<Script> groovyClass = (Class<Script>) groovyLoader.parseClass(content);
        return groovyClass.newInstance();
    }

    public static Map<String/** function */, List<MxMetric>> getAllMetric() {
        return metricFuncMap;
    }

    public static List<MxMetric> getMetrics() {
        return allMetric;
    }


    public static List<MxMetric> getMetricByFunc(String function) {
        return metricFuncMap.get(function);
    }

    public static void main(String[] args) {
        System.out.println(JSONObject.toJSONString(MxMetricCacheUtils.getAllMetric()));
        System.out.println(JSONObject.toJSONString(MxMetricCacheUtils.getMetricByFunc("SUM")));
        System.out.println(JSONObject.toJSONString(MxMetricCacheUtils.getMetricByFunc("SUM1")));
    }

}
