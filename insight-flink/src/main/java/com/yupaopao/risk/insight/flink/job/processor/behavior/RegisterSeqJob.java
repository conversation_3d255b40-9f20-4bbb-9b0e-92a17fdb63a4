package com.yupaopao.risk.insight.flink.job.processor.behavior;

import com.alibaba.alink.operator.batch.BatchOperator;
import com.alibaba.alink.operator.batch.similarity.StringApproxNearestNeighborPredictBatchOp;
import com.alibaba.alink.operator.batch.similarity.StringApproxNearestNeighborTrainBatchOp;
import com.alibaba.alink.operator.batch.similarity.TextApproxNearestNeighborPredictBatchOp;
import com.alibaba.alink.operator.batch.similarity.TextApproxNearestNeighborTrainBatchOp;
import com.alibaba.alink.operator.batch.source.MemSourceBatchOp;
import com.alibaba.alink.params.similarity.StringTextApproxNearestNeighborTrainParams;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.job.base.ALinkJobBuilder;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.types.Row;

import java.net.URLEncoder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2021-07-06 16:02
 *
 ***/
public class RegisterSeqJob {
    public static void main(String[] args) throws Exception {

        ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        ckProperties.setBatchSize(10000);

        CKOutputFormat ckOutputFormat = new CKOutputFormat(ckProperties, "temp_device_20210716");

        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        env.readCsvFile("oss://aliops-k8s01-flink/risk-flink/M89020810_ypp.csv")
                .ignoreFirstLine()
                .types(String.class)
                .map(elem->{
                    Map<String,String> row = new HashMap<>();
                    row.put("deviceId",elem.toString().trim());
                    return JSON.toJSONString(row);
                }).output(ckOutputFormat);
        env.execute("write to ck ");
//        new ALinkJobBuilder()
//                .withJobName("register-cluster-sequence")
//                .withProcessor(new TextCompareProcessor())
//                .start(args);
//
//        JSONObject obj = JSONObject.parseObject("{\"actionType\":\"user-like\",\"createdAt\":\"2021-07-08 17:37:16\"}");
//        System.err.println(URLEncoder.encode(obj.toJSONString(),"UTF-8"));
//
//        List<Row> datalist = Arrays.asList(
//                Row.of(1,"user-follow_191460728362122859 user-follow_192740517573726872 ".replace("_","").replace("-","") +
//                        "user-follow_200640703096957975 user-follow_200991401228006121 user-follow_201470686299400426 user-follow_1833901995500113 im-message_210250378479478466 im-message_210250378479478466 ".replace("_","").replace("-","") +
//                        "im-message_210250378479478466 im-message_211291308273556986".replace("_","").replace("-","")),
//                Row.of(2,"user-follow_200931063067867616 user-follow_1833904480700084 user-follow_201970929540701279 user-follow_193310899574913836 user-follow_192740517573726872 user-follow_201390853008926314 user-follow_201390853008926314 user-follow_193310899574913836 user-follow_192740517573726872 user-follow_201970929540701279 user-follow_1833904480700084 user-follow_200931063067867616".replace("_","").replace("-",""))
//                ,
//                Row.of(3,"user-follow_203360973427059054 user-follow_201930790120738365 user-follow_203000854574059293 user-follow_201390853008926314 user-follow_192761433473766903 user-follow_203141017244764017".replace("_","").replace("-","")));
//
//        BatchOperator dict = new MemSourceBatchOp(datalist, new String[] {"id", "str"});
//        BatchOperator query = new MemSourceBatchOp(datalist, new String[] {"id", "str"});
//
//        TextApproxNearestNeighborTrainBatchOp train = new TextApproxNearestNeighborTrainBatchOp()
//                .setIdCol("id")
//                .setSelectedCol("str")
//                .setMetric(StringTextApproxNearestNeighborTrainParams.Metric.SIMHASH_HAMMING_SIM)
//                .linkFrom(dict);
//
//        TextApproxNearestNeighborPredictBatchOp predict = new TextApproxNearestNeighborPredictBatchOp()
////                .setNumThreads(4)
//                .setSelectedCol("str")
//                .setTopN(3)
//                .setOutputCol("topN")
//                .linkFrom(train, query);
//
//        List<Row> res = predict.collect();
//        System.err.println(res);
    }
}
