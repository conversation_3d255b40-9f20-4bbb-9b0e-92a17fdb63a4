package com.yupaopao.risk.insight.flink.windows.triggers;

import com.yupaopao.risk.insight.flink.bean.portrait.PortraitBean;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.streaming.api.windowing.triggers.Trigger;
import org.apache.flink.streaming.api.windowing.triggers.TriggerResult;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;

import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 */
public class PortraitTagTumblingTimeTrigger extends Trigger<Tuple2<Long, PortraitBean>, TimeWindow> {
    private static final long serialVersionUID = -6399900166516462452L;

    private AtomicLong times = new AtomicLong(System.currentTimeMillis());

    @Override public TriggerResult onElement(Tuple2<Long, PortraitBean> element, long timestamp, TimeWindow window, TriggerContext ctx) throws Exception {
        ctx.registerProcessingTimeTimer(window.maxTimestamp());
        //处理数据
        String gapTimeStr = PortraitApolloProperties.getConfigByKey("portrait.flush.tag.gap.time", "5");
        long gapTime = Long.valueOf(gapTimeStr)*60*1000;
        if (timestamp-times.get() >= gapTime) {
            times = new AtomicLong(System.currentTimeMillis());
            return TriggerResult.FIRE;
        }
        return TriggerResult.CONTINUE;
    }

    @Override public TriggerResult onProcessingTime(long time, TimeWindow window, TriggerContext ctx) throws Exception {
        return TriggerResult.CONTINUE;
    }

    @Override public TriggerResult onEventTime(long time, TimeWindow window, TriggerContext ctx) throws Exception {
        //你操作
        return TriggerResult.CONTINUE;
    }

    @Override public void clear(TimeWindow window, TriggerContext ctx) throws Exception {
        ctx.deleteProcessingTimeTimer(window.maxTimestamp());
    }

    @Override
    public boolean canMerge() {
        return true;
    }

    @Override
    public void onMerge(TimeWindow window,
                        OnMergeContext ctx) {
        // only register a timer if the time is not yet past the end of the merged window
        // this is in line with the logic in onElement(). If the time is past the end of
        // the window onElement() will fire and setting a timer here would fire the window twice.
        long windowMaxTimestamp = window.maxTimestamp();
        if (windowMaxTimestamp > ctx.getCurrentProcessingTime()) {
            ctx.registerProcessingTimeTimer(windowMaxTimestamp);
        }
    }

    public static PortraitTagTumblingTimeTrigger create() {
        return new PortraitTagTumblingTimeTrigger();
    }
}
