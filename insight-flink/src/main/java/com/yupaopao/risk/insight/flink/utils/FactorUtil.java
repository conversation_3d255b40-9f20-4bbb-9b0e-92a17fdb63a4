package com.yupaopao.risk.insight.flink.utils;

import com.alibaba.fastjson.JSONObject;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.bean.audit.MxMetric;
import com.yupaopao.risk.insight.flink.bean.portrait.AggTag;
import com.yupaopao.risk.insight.flink.windows.FactorCalDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.joda.time.DateTime;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc FactorUtil
 * @date 2020/6/28
 */
@Slf4j
public class FactorUtil {
    public static boolean checkCondition(JSONObject param, AggTag tag) {
        if (StringUtils.isEmpty(tag.getCondition())) {
            return true;
        }

        try {
            Expression expression = new SpelExpressionParser().parseExpression(tag.getCondition());
            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setVariables(param);
            Object result = expression.getValue(context);
            log.debug("前置表达式执行结果:{} - {} - {}", result, tag.getCondition(), param);
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else {
                return result != null;
            }
        } catch (Throwable e) {
            log.error("执行聚合标签表达式出错: " + tag.getCondition() + param.toJSONString(), e);
        }
        return false;
    }

    public static String getGroupKey(AggTag aggTag, JSONObject data){
        List<String> groupValues = new LinkedList<>();
        for (String groupKey : aggTag.getGroupKey().split(",")) {
            String groupValue;

            if (groupKey.contains(".")) {
                Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                groupValue = dataMap.getOrDefault(groupKey, "").toString();
            } else {
                groupValue = data.getString(groupKey);
            }
            // 缺失任意groupKeyValue则放弃
            if (StringUtils.isBlank(groupValue)) {
                return "";
            }
            groupValues.add(groupValue);
        }
        return String.join("$", groupValues);
    }

    public static String getAggValue(AggTag aggTag, JSONObject data){
        data.put("day", InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd));
        if(StringUtils.isEmpty(aggTag.getAggKey())){
            return "";
        } else {
            List<String> aggValues = new LinkedList<>();
            boolean hasValue = false;
            for (String aggKey : aggTag.getAggKey().split(",")) {
                String aggValue;

                if (aggKey.contains(".")) {
                    Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                    aggValue = dataMap.getOrDefault(aggKey, "").toString();
                } else {
                    aggValue = data.getOrDefault(aggKey, "").toString();
                }
                if (StringUtils.isNotBlank(aggValue)) {
                    hasValue = true;
                }
                aggValues.add(aggValue);
            }
            if (hasValue) {
                return String.join("$", aggValues);
            } else {
                return "";
            }
        }
    }

    public static boolean checkCondition(MxMetric metric, JSONObject param) {
        if (StringUtils.isEmpty(metric.getCondition())) {
            return true;
        }

        try {
            Expression expression = new SpelExpressionParser().parseExpression(metric.getCondition());
            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setVariables(param);
            Object result = expression.getValue(context);
            log.debug("前置表达式执行结果:{} - {} - {}", result, metric.getCondition(), param);
            if (result instanceof Boolean) {
                return (Boolean) result;
            } else {
                return result != null;
            }
        } catch (Throwable e) {
            log.error("执行聚合标签表达式出错: " + metric.getCondition() + param.toJSONString(), e);
        }
        return false;
    }

    public static String getGroupKey(MxMetric metric, JSONObject data){
        List<String> groupValues = new LinkedList<>();
        for (String groupKey : metric.getGroupKey().split(",")) {
            String groupValue;

            if (groupKey.contains(".")) {
                Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                groupValue = dataMap.getOrDefault(groupKey, "").toString();
            } else {
                groupValue = data.getString(groupKey);
            }
            // 缺失任意groupKeyValue则放弃
            if (StringUtils.isBlank(groupValue)) {
                groupValue = "";
            }
            groupValues.add(groupValue);
        }
        return String.join("$", groupValues);
    }

    public static String getAggValue(MxMetric metric, JSONObject data){
        if(StringUtils.isEmpty(metric.getAggKey()) || null == data){
            return "";
        } else {
            List<String> aggValues = new LinkedList<>();
            boolean hasValue = false;
            for (String aggKey : metric.getAggKey().split(",")) {
                String aggValue;

                if (aggKey.contains(".")) {
                    Map<String, Object> dataMap = JsonFlatterUtils.toMap(data.toJSONString(), FlattenMode.KEEP_ARRAYS, '.');
                    aggValue = dataMap.getOrDefault(aggKey, "").toString();
                } else {
                    Object orDefault = data.getOrDefault(aggKey, "");
                    if (null != orDefault) {
                        aggValue = orDefault.toString();
                    } else {
                        aggValue = "";
                    }
                }
                if (StringUtils.isNotBlank(aggValue)) {
                    hasValue = true;
                }
                aggValues.add(aggValue);
            }
            if (hasValue) {
                return String.join("$", aggValues);
            } else {
                return "";
            }
        }
    }

    public static long getWindowStart(long now, long timeSpan) {
        long start;
        DateTime dateTime = new DateTime(now);
        if (timeSpan < 60) {
            // 分钟级别，使用自带窗口对齐
            return TimeWindow.getWindowStartWithOffset(now, 0, timeSpan * 60 * 1000);
        } else if (timeSpan < 720) {
            // 小时级别，窗口从当前小时0分钟开始
            dateTime = dateTime.withSecondOfMinute(0).withMillisOfSecond(0).withMinuteOfHour(0);
        } else if (timeSpan < 1440) {
            // 半天级别，窗口从当前小时0分钟开始
            dateTime = dateTime.withSecondOfMinute(0).withMillisOfSecond(0).withMinuteOfHour(0);
            dateTime = dateTime.getHourOfDay() < 12 ? dateTime.withHourOfDay(0) : dateTime.withHourOfDay(12);
        } else if (timeSpan < 10080) {
            // 天级，窗口从当天0点开始
            dateTime = dateTime.withMillisOfDay(0);
        } else if (timeSpan < 40320) {
            // 周级，窗口从周一0点开始
            dateTime = dateTime.withDayOfWeek(1).withMillisOfDay(0);
        } else if (timeSpan < 518400) {
            // 月级，窗口从1号0点开始
            dateTime = dateTime.withDayOfMonth(1).withMillisOfDay(0);
        } else {
            // 年级，窗口从1月1日0点开始
            dateTime = dateTime.withMonthOfYear(1).withDayOfMonth(1).withMillisOfDay(0);
        }

        start = dateTime.getMillis();
        return start;
    }

    public static Long getRemainTime(FactorCalDetail value) {
        if (value.getWindowType() == 2) {
            long timeMillis = System.currentTimeMillis();
            long windowStart = getWindowStart(timeMillis, value.getTimeSpan());
            return value.getTimeSpan() - (timeMillis - windowStart) / 60000;
        }
        return value.getTimeSpan();
    }
}
