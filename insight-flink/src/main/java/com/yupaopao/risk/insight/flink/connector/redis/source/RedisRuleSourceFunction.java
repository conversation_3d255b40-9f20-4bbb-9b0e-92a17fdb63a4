package com.yupaopao.risk.insight.flink.connector.redis.source;

import com.yupaopao.risk.insight.common.cep.beans.CepRule;
import com.yupaopao.risk.insight.flink.cep.inject.RuleGetter;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import org.apache.commons.collections.CollectionUtils;
import org.apache.flink.api.common.state.ListState;
import org.apache.flink.api.common.state.ListStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;

import java.util.ArrayList;
import java.util.List;

import static com.yupaopao.risk.insight.common.cep.constants.CepConstants.ruleBroadcastInterval;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-02-24 15:30
 *
 ***/
public class RedisRuleSourceFunction extends BaseRedisSource<List<CepRule>> implements CheckpointedFunction {


    private volatile boolean isRunning = false;

    private transient ListState<CepRule> ruleListState;

    private List<CepRule> redisRuleList;

    private String dataSource;

    public RedisRuleSourceFunction(RedisProperties redisProperties, String dataSource) {
        super(redisProperties);
        this.dataSource = dataSource;
    }


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        isRunning = true;
        redisRuleList = new ArrayList<>();
    }

    @Override
    public void run(SourceContext<List<CepRule>> sourceContext) throws Exception {
        while (isRunning) {
            List<CepRule> ruleList = RuleGetter.getLatestRules(super.getJedisPool(), "broadcast", dataSource);
            if (CollectionUtils.isEmpty(ruleList)) {
                if (CollectionUtils.isNotEmpty(redisRuleList)) {
                    sourceContext.collect(redisRuleList);
                }
                Thread.sleep(ruleBroadcastInterval);
                continue;
            }
            redisRuleList.clear();
            redisRuleList.addAll(ruleList);
            sourceContext.collect(ruleList);
            // 2 min 更新一次
            Thread.sleep(ruleBroadcastInterval);
        }
    }

    @Override
    public void cancel() {
        isRunning = false;
    }

    @Override
    public void snapshotState(FunctionSnapshotContext functionSnapshotContext) throws Exception {
        ruleListState.clear();
        ruleListState.addAll(redisRuleList);

    }

    @Override
    public void initializeState(FunctionInitializationContext functionInitializationContext) throws Exception {
        ListStateDescriptor<CepRule> listDesc = new ListStateDescriptor<CepRule>("rulesInRedis",
                TypeInformation.of(new TypeHint<CepRule>() {
                    @Override
                    public TypeInformation<CepRule> getTypeInfo() {
                        return super.getTypeInfo();
                    }
                }));

        ruleListState = functionInitializationContext.getOperatorStateStore().getListState(listDesc);
        if (redisRuleList == null) {
            redisRuleList = new ArrayList<>();
        }
        if (functionInitializationContext.isRestored()) {
            for (CepRule rule : ruleListState.get()) {
                redisRuleList.add(rule);
            }
        }

    }
}
