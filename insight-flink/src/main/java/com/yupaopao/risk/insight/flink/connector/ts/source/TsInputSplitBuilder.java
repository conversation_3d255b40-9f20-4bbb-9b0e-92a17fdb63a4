//package com.yupaopao.risk.insight.flink.connector.ts.source;
//
//import com.alicloud.openservices.tablestore.model.ComputeSplitsBySizeRequest;
//import com.alicloud.openservices.tablestore.model.ComputeSplitsBySizeResponse;
//import com.alicloud.openservices.tablestore.model.PrimaryKey;
//import com.alicloud.openservices.tablestore.model.Split;
//import com.google.gson.Gson;
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsClientFactory;
//import com.yupaopao.risk.insight.flink.constants.TsConstants;
//import com.yupaopao.risk.insight.flink.meta.JobParams;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import com.yupaopao.risk.insight.flink.utils.InsightDateUtils;
//import com.yupaopao.risk.insight.flink.utils.TsPrimaryKeyTools;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.time.DateFormatUtils;
//import org.apache.commons.lang3.time.DateUtils;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
///****
// *
// * @Author: zengxiangcai
// * @Date: 2019-12-21 17:42
// *
// ***/
//
//
//@Slf4j
//public class TsInputSplitBuilder {
//
//    private TsTableInfo tsTableInfo;
//
//    private TsClientFactory tsClientFactory;
//
//    public TsInputSplitBuilder(TsTableInfo tsTableInfo, TsClientFactory clientFactory) {
//        this.tsTableInfo = tsTableInfo;
//        this.tsClientFactory = clientFactory;
//    }
//
//
//    private transient static Gson gson = new Gson();
//
//
//    public TsInputSplit[] buildSplits() throws IOException {
//        TsInputSplit[] splits = null;
//        if (tsTableInfo.getDatePeriod() == null) {
//            log.info("create table split by size {}", tsTableInfo.getTableName());
//            splits = createSplitBySize();
//        } else {
//            log.info("create table split with custom {}", tsTableInfo.getTableName());
//            splits = createSplitByCustom();
//        }
//        return splits;
//    }
//
//
//    private TsInputSplit[] createSplitByCustom() throws IOException {
//
//        /***
//         * 目前每天总共有1024个桶，每天的数据最大分摊到1024个分区
//         * 0000_yyyyMMdd 到1023_yyyyMMdd
//         */
//        JobParams.TableQueryDatePeriod period = tsTableInfo.getDatePeriod();
//
//        Date startDate = InsightDateUtils.getDateFromString(period.getBeginDate(), InsightDateUtils.DATE_FORMAT_yyyyMMdd);
//        Date endDate = StringUtils.isEmpty(period.getEndDate()) ?
//                InsightDateUtils.getDateFromString(InsightDateUtils.getNextDayStr(), InsightDateUtils.DATE_FORMAT_yyyyMMdd)
//                : InsightDateUtils.getDateFromString(period.getEndDate(), InsightDateUtils.DATE_FORMAT_yyyyMMdd);
//        List<TsInputSplit.SplitBucket> allDayBucket = new ArrayList<>(1024);
//        while (startDate.before(endDate)) {
//            //splitList.addAll(createDaySplit(startDate, dayLoop));
//            addDaySplitToList(startDate, allDayBucket);
//            startDate = DateUtils.addDays(startDate, 1);
//        }
//        List<TsInputSplit> splitList = createSplitFromDayBucketSet(allDayBucket);
//        return splitList.toArray(new TsInputSplit[0]);
//    }
//
//
//    private void addDaySplitToList(Date day, List<TsInputSplit.SplitBucket> resultList) {
//        String dayPrefix = DateFormatUtils.format(day, InsightDateUtils.DATE_FORMAT_yyyyMMdd);
//        //每天的数据分为1024分片
//        for (int i = 0; i < TsPrimaryKeyTools.RISK_HIT_LOG_BUCKET_PER_DAY; i++) {
//            PrimaryKey lower = TsPrimaryKeyTools.buildRiskHitLogPrimaryKeyLowerBound(dayPrefix, i);
//            PrimaryKey upper = TsPrimaryKeyTools.buildRiskHitLogPrimaryKeyUpperBound(dayPrefix, i);
//            TsInputSplit.SplitBucket pair = new TsInputSplit.SplitBucket(lower, upper);
//            resultList.add(pair);
//        }
//    }
//
//    /***
//     * 对当前查询的所有分区键进行处理，生成inputSplit
//     * @param allDayBucket
//     */
//    private List<TsInputSplit> createSplitFromDayBucketSet(List<TsInputSplit.SplitBucket> allDayBucket) {
//        //ts读取可能比较慢，一个split处理过程中，启动多个线程读取
//        int allDayBucketSize = allDayBucket.size();
//        int bucketPerSplit = tsTableInfo.getBucketPerSplit();
//        int totalSplit = (int) Math.ceil(allDayBucketSize * 1.00 / bucketPerSplit);
//        List<TsInputSplit> inputSplits = new ArrayList<>(totalSplit);
//        for (int i = 0; i < totalSplit; i++) {
//            int startIndex = i * bucketPerSplit;
//            int lastIndex = (i + 1) * bucketPerSplit;
//            if (lastIndex >= allDayBucketSize) {
//                lastIndex = allDayBucketSize;
//            }
//            if (startIndex >= lastIndex) {
//                break;
//            }
//            List<TsInputSplit.SplitBucket> list = allDayBucket.subList(startIndex, lastIndex);
//            TsInputSplit split = new TsInputSplit(i, gson.toJson(list));
//            inputSplits.add(i, split);
//        }
//        return inputSplits;
//    }
//
////    private List<TsInputSplit> createDaySplit(Date day, int dayLoop) {
////        String dayPrefix = DateFormatUtils.format(day, InsightDateUtils.DATE_FORMAT_yyyyMMdd);
////        int bucketPerSplit = tsTableInfo.getBucketPerSplit();
////        int totalSplit = (int) Math.ceil(RISK_HIT_LOG_TOTAL_BUCKET_PER_DAY * 1.00 / bucketPerSplit);
////        List<TsInputSplit> inputSplits = new ArrayList<>(totalSplit);//默认1天128分片
////
////        for (int i = 0; i < totalSplit; i++) {
////            //每个分片对应的桶
////            List<TsInputSplit.SplitBucket> list = new ArrayList<>();
////            int currentBucket = i * bucketPerSplit;
////            for (int k = 0; k < bucketPerSplit; k++) {
////                PrimaryKey start = TsPrimaryKeyTools.buildRiskHitLogPrimaryKey(dayPrefix, currentBucket + k);
////                PrimaryKey end = TsPrimaryKeyTools.buildRiskHitLogPrimaryKey(dayPrefix, currentBucket + k + 1);
////                TsInputSplit.SplitBucket pair = new TsInputSplit.SplitBucket(start, end);
////                list.add(pair);
////            }
////            TsInputSplit split = new TsInputSplit(dayLoop * totalSplit + i, gson.toJson(list));
////
////            inputSplits.add(i, split);
////        }
////        return inputSplits;
////    }
//
//    private TsInputSplit[] createSplitBySize() throws IOException {
//        String tsTable = tsTableInfo.getTableName();
//        try {
//            if (TsConstants.TABLE_TYPE_TEMP.equalsIgnoreCase(tsTableInfo.getTableType())) {
//                tsTable = TsConstants.TABLE_RISK_USER_DEFINE_DATA;
//            }
//            ComputeSplitsBySizeResponse csbsr = getComputeSplitsBySize(tsClientFactory, tsTable);
//            log.info("the splits info: " + csbsr.jsonize());
//            List<Split> splits = csbsr.getSplits();
//
//            //分片数组
//            TsInputSplit[] resultTsSplitArray = new TsInputSplit[splits.size()];
//            for (int i = 0; i < splits.size(); i++) {
//                PrimaryKey lowerBound = splits.get(i).getLowerBound();
//                PrimaryKey upperBound = splits.get(i).getUpperBound();
//                TsInputSplit.SplitBucket pair = new TsInputSplit.SplitBucket(lowerBound, upperBound);
//                List<TsInputSplit.SplitBucket> bucketList = new ArrayList<>();
//                bucketList.add(pair);
//                resultTsSplitArray[i] = new TsInputSplit(i, gson.toJson(bucketList));
//            }
//            return resultTsSplitArray;
//        } catch (Exception e) {
//            log.error("createInputSplit error: tableName" + tsTable, e);
//            throw new IOException(e);
//        }
//    }
//
//    /***
//     * 全表数据逻辑上分片信息
//     * https://help.aliyun.com/document_detail/99107.html?aly_as=BRhf8OnW
//     * @param clientFactory
//     * @param tableName
//     * @return
//     */
//    public static ComputeSplitsBySizeResponse getComputeSplitsBySize(TsClientFactory clientFactory, String tableName) {
//        ComputeSplitsBySizeRequest csbsr = new ComputeSplitsBySizeRequest();
//        csbsr.setTableName(tableName);
//        //200M一个分片
//        //csbsr.setSplitSizeInByte(unitsPerSplit, splitSizeUnitInBytes);
//        csbsr.setSplitSizeIn100MB(2);
//        return clientFactory.getClient().computeSplitsBySize(csbsr);
//    }
//
//}
