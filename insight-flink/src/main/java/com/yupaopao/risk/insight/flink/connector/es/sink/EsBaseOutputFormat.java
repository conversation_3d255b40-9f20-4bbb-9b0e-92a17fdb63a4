package com.yupaopao.risk.insight.flink.connector.es.sink;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.configuration.Configuration;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;

import java.io.IOException;

@Slf4j
public class EsBaseOutputFormat<T> extends RichOutputFormat<T> {
    private EsProperties esProperties;
    private RestHighLevelClient client;

    public EsBaseOutputFormat(EsProperties esProperties) {
        this.esProperties = esProperties;
    }

    @Override public void configure(Configuration parameters) {

    }

    @Override public void open(int taskNumber, int numTasks) throws IOException {
        String[] split = esProperties.getHosts().split(":");
        RestClientBuilder builder = RestClient.builder(new HttpHost(split[0].trim(), Integer.parseInt(split[1].trim())));
        if (StringUtils.isNotBlank(esProperties.getUsername()) && StringUtils.isNotBlank(esProperties.getPassword())) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(esProperties.getUsername(), esProperties.getPassword()));
            builder.setHttpClientConfigCallback(httpClient -> httpClient.setDefaultCredentialsProvider(credentialsProvider));
        }
        client = new RestHighLevelClient(builder);
    }

    @Override public void writeRecord(T record) throws IOException {
        IndexRequest indexRequest = new IndexRequest(esProperties.getIndex(), esProperties.getType());
        indexRequest.source(JSONObject.toJSONString(record), XContentType.JSON);
        client.index(indexRequest);
    }

    @Override public void close() throws IOException {
        this.client.close();
    }
}
