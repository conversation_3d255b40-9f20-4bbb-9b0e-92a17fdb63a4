package com.yupaopao.risk.insight.flink.job.cal.fixFactorCal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.google.common.util.concurrent.RateLimiter;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.RedisProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.source.CKInputFormat;
import com.yupaopao.risk.insight.flink.constants.FactorConstants;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.flink.windows.FactorCaches;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.flink.api.common.io.RichOutputFormat;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.tuple.Tuple5;
import org.apache.flink.configuration.Configuration;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.Pipeline;

import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/****
 * zengxiangcai
 * 2023/1/30 15:21
 *
 * job jar name fix_factor_cal_rollback.jar
 ***/


@Slf4j
public class FactorCalRollbackJob {

    public static void buildArgs() throws Exception {
        String time = "2023-02-06 11:28:20";
        System.err.println(URLEncoder.encode(time,"UTF-8"));
    }

    public static void main(String[] args) throws Exception {
//        if(true){
//            buildArgs();
//            return;
//        }
        /****
         * 1、读取ck表数据，然后写入redis(需要限制qps不能大于8万qps)
         */

        if (args == null || args.length < 1) {
            throw new Exception("need rollback data params: createdAt");
        }

        String rollbackDataCreatedAt = URLDecoder.decode(args[0],"UTF-8");

        ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        RedisProperties redisProperties = RedisProperties.getProperties(PropertyType.REDIS);
        String baseSql = String.format("select * from flink_checkpoint_rollback where createdAt >= '%s'" +
                " order by `key` asc ", rollbackDataCreatedAt);

        String jsonParams = "";
        CKInputFormat ckInputFormat = new CKInputFormat(ckProperties, baseSql, jsonParams);

        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();

        env.createInput(ckInputFormat).filter(elem -> !HBaseUtil.emptyResultJson(elem)).map(elem -> {

            JSONObject obj = JSON.parseObject(elem);
            String operatorType = obj.getString("operatorType");
            String key = obj.getString("key");
            Long timespan = obj.getLong("timeSpan");
            String dataType = obj.getString("dataType");
            if ("DISTINCT".equalsIgnoreCase(operatorType)) {
                //key ,value,timespan,type
                return new Tuple5<String, String, Long, String, String>(key, obj.getString("distinctArr"), timespan,
                        operatorType, dataType);
            } else {
                return new Tuple5<String, String, Long, String, String>(key, obj.getString("result"), timespan,
                        operatorType,
                        dataType);
            }
        }).returns(new TypeHint<Tuple5<String, String, Long, String, String>>() {
            @Override
            public TypeInformation<Tuple5<String, String, Long, String, String>> getTypeInfo() {
                return super.getTypeInfo();
            }
        }).output(new RedisOutputFormat(redisProperties)).setParallelism(8);

        env.execute("write ck states to redis");
    }

    public static class RedisOutputFormat extends RichOutputFormat<Tuple5<String, String, Long, String, String>> {

        private transient volatile RateLimiter rateLimiter;

        private JedisPool jedisPool;

        private RedisProperties redisProperties;

        private int maxPipeline = 40;

        private LinkedBlockingQueue<Tuple5<String, String, Long, String, String>> cacheList =
                new LinkedBlockingQueue<>(1024 * 16);

        public RedisOutputFormat(RedisProperties redisProperties) {
            this.redisProperties = redisProperties;
        }

        @Override
        public void configure(Configuration parameters) {

        }

        @Override
        public void open(int taskNumber, int numTasks) throws IOException {
            String rateLimit = ApolloProperties.getConfigStr("factor_cal.checkpoints.rollback.rateLimit");
            if (StringUtils.isNotEmpty(rateLimit)) {
                rateLimiter = RateLimiter.create(Double.valueOf(rateLimit));
            } else {
                rateLimiter = RateLimiter.create(Double.valueOf("10000"));
            }

            GenericObjectPoolConfig genericObjectPoolConfig = new GenericObjectPoolConfig();
            genericObjectPoolConfig.setMaxIdle(redisProperties.getMaxIdle());
            genericObjectPoolConfig.setMaxTotal(redisProperties.getMaxActive());
            genericObjectPoolConfig.setMinIdle(redisProperties.getMinIdle());

            jedisPool = new JedisPool(genericObjectPoolConfig, redisProperties.getHost(), redisProperties.getPort(), FactorConstants.REDIS_DEFAULT_TIMEOUT, redisProperties.getPassword(), redisProperties.getDatabase());

        }

        @Override
        public void writeRecord(Tuple5<String, String, Long, String, String> record) throws IOException {
            if (true) {
                log.info("write data: {}", record);
                return;
            }
            boolean canInsert = cacheList.offer(record);
            if (canInsert) {
                return;
            }

            //queue if full --> flush --> insert
            try (Jedis jedis = jedisPool.getResource(); Pipeline pipeline = jedis.pipelined();) {
                Tuple5<String, String, Long, String, String> tmp = null;
                int batchCount = 0;

                while ((tmp = cacheList.poll()) != null) {
                    String dataType = tmp.f4;
                    String operatorDesc = tmp.f3;
                    String factorId = tmp.f0.split("#")[1];
                    rateLimiter.tryAcquire(1, Long.valueOf(10), TimeUnit.MILLISECONDS);
                    if ("NEW".equals(dataType)) {
                        //new data need to deleted...
                        pipeline.del(tmp.f0);
                    } else {
                        int expireTime = (int) getExpireTime(factorId, tmp.f2);
                        if (expireTime < 0) {
                            continue;
                        }
                        if ("DISTINCT".equals(operatorDesc)) {
                            //distinct 处理，list类型数据
                            pipeline.del(tmp.f0);
                            List<String> update = Arrays.asList(tmp.f1.split("####"));
                            pipeline.lpush(tmp.f0, update.toArray(new String[]{}));
                            pipeline.expire(tmp.f0, expireTime);
                        } else {
                            //其他count/max/sum等处理
                            pipeline.setex(tmp.f0, expireTime, tmp.f1);
                        }
                    }
                    Cat.logMetricForCount("factor.redis.rollback." + operatorDesc);
                    batchCount++;
                    if (batchCount >= maxPipeline) {
                        pipeline.sync();
                        batchCount = 0;
                    }
                }
                pipeline.sync();
            }

            try {
                cacheList.put(record);
            } catch (Exception e) {
                log.error("put record to cache error: " + record, e);
            }


        }

        @Override
        public void close() throws IOException {

        }

//        private String getFunctionType(String key) {
//            if (StringUtils.isEmpty(key)) {
//                return "null";
//            }
//            return FactorCaches.getFunctionType(key.split("#")[1]);
//        }

        /***
         *
         * @param factorId
         * @param timeSpan
         * @return
         */
        private long getExpireTime(String factorId, long timeSpan) {
            String strSlideNum = PortraitApolloProperties.getConfigByKey("factor.slide.avg.count", "10");
            Long slideNum = Long.valueOf(strSlideNum);
            Integer windowType = FactorCaches.getWindowType(factorId);
            if (windowType != null && windowType == 1) {
                //滑动窗口时间为滑动间隔*1.05
                long originalExpireSeconds = (long) (timeSpan * 60 * 1.0 / slideNum);
                long expireTimeSeconds = (long) (timeSpan * 60 * 1.05 / slideNum);
                if (expireTimeSeconds - originalExpireSeconds > 120) {
                    //超过了2分钟，做限制最高不错过2min
                    expireTimeSeconds = originalExpireSeconds + 120;
                }
            }
            return timeSpan * 60;
        }
    }
}
