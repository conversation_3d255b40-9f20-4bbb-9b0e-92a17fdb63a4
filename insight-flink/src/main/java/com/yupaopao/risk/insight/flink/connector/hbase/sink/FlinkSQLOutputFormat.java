package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.alibaba.lindorm.client.core.utils.Bytes;
import com.yupaopao.risk.insight.common.constants.TsConstants;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.constants.ParamConstants;
import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.types.Row;
import org.apache.hadoop.hbase.client.Put;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-01-07 19:24
 * flink table 结果写入  HBase, 列统一当做String 类型
 ***/

@Slf4j
public class FlinkSQLOutputFormat extends HBaseBaseOutputFormat<Row> implements Serializable {

    private FlinkMetaInfo flinkMetaInfo;

    private List<String> rowKeyField;

    private long writeCount = 0;

    public FlinkSQLOutputFormat(String tableName, HBaseProperties hBaseProperties, FlinkMetaInfo flinkMetaInfo) {
        super(tableName, hBaseProperties);
        this.flinkMetaInfo = flinkMetaInfo;
    }


    @Override
    public void writeRecord(Row record) throws IOException {
        if (record == null) {
            return;
        }

        try {
            byte[] rowKey = null;
            Map<String, String> userParameters = this.getRuntimeContext().getExecutionConfig().getGlobalJobParameters().toMap();
            //构建rowKey
            if (TsConstants.isAnalysisTable(getTableName())) {
                //写sql分析结果
                //写查询结果
                long timestamp = System.currentTimeMillis();
                timestamp += writeCount++;
                String jobId = userParameters.getOrDefault(ParamConstants.CURRENT_JOB_ID, "noJobId");
                rowKey = Bytes.toBytes(jobId + "_" + String.valueOf(timestamp));
            } else if (TsConstants.TABLE_RISK_PORTRAIT_ETL.equals(getTableName())){
                //暂时无其他类型表写入，后续通过HBase写入其他表的话可以考虑 从 record取
                rowKey = Bytes.toBytes(InsightDateUtils.getBeforeDayStr()+ "_" + record.getField(0)+"_"+System.currentTimeMillis());
            }else {
                //DDL 写查询结果
                long timestamp = System.currentTimeMillis();
                timestamp += writeCount++;
                String jobId = userParameters.getOrDefault(ParamConstants.CURRENT_JOB_ID, "noJobId");
                rowKey = Bytes.toBytes(jobId + "_" + timestamp);
//                throw new RuntimeException("not support for other table, tableName: " + getTableName());
            }

            Put put = new Put(rowKey);
            HBaseUtil.transFlinkRowToHBaseColumn(put, record, flinkMetaInfo);
            getMutator().mutate(put);
            if (canFlush()) {
                flush();
            }
            //table.put(put);
        } catch (Exception e) {
            log.error("write record error: ,record: " + record, e);
        }
    }
}
