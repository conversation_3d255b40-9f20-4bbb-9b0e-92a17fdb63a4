package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSink;
import org.apache.flink.table.api.TableSchema;
import org.apache.flink.table.sinks.AppendStreamTableSink;
import org.apache.flink.table.sinks.TableSink;
import org.apache.flink.types.Row;

public class HBaseStreamTableSink implements AppendStreamTableSink<Row> {

    private FlinkMetaInfo flinkMetaInfo;

    private String tableName;

    private HBaseProperties hBaseProperties;

    private FlinkSQLOutputFormat outputFormat;

    public HBaseStreamTableSink(String tableName, FlinkMetaInfo flinkMetaInfo, HBaseProperties hBaseProperties) {
        this.tableName = tableName;
        this.flinkMetaInfo = flinkMetaInfo;
        this.hBaseProperties = hBaseProperties;
        //构建本sink 的format
        createFormat();

    }

    private void createFormat() {
        outputFormat = new FlinkSQLOutputFormat(tableName, hBaseProperties, flinkMetaInfo);
    }


    @Override public DataStreamSink<?> consumeDataStream(DataStream<Row> dataStream) {
       return dataStream.addSink(new HBaseStreamRowSink(tableName, flinkMetaInfo, this.hBaseProperties)).name("hbase sink");
    }

    @Override
    public TableSink<Row> configure(String[] fieldNames, TypeInformation<?>[] fieldTypes) {
        HBaseStreamTableSink copy = new HBaseStreamTableSink(tableName, flinkMetaInfo, hBaseProperties);
        return copy;
    }

    @Override
    public TableSchema getTableSchema() {
        return flinkMetaInfo.toTableSchema();
    }

    @Override
    public TypeInformation<Row> getOutputType() {
        return flinkMetaInfo.toTableSchema().toRowType();
    }
}
