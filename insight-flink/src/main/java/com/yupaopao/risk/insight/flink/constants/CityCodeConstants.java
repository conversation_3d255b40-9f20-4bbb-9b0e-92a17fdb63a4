package com.yupaopao.risk.insight.flink.constants;

import java.util.HashMap;
import java.util.Map;

public class CityCodeConstants {
    public static Map<String, Integer> PROVINCE_MAP = new HashMap<>();
    public static Map<String, Integer> COUNTRY_MAP = new HashMap<>();
    static {
        PROVINCE_MAP.put("北京市", 110000);
        PROVINCE_MAP.put("天津市", 120000);
        PROVINCE_MAP.put("河北省", 130000);
        PROVINCE_MAP.put("山西省", 140000);
        PROVINCE_MAP.put("内蒙古自治区", 150000);
        PROVINCE_MAP.put("辽宁省", 210000);
        PROVINCE_MAP.put("吉林省", 220000);
        PROVINCE_MAP.put("黑龙江省", 230000);
        PROVINCE_MAP.put("上海市", 310000);
        PROVINCE_MAP.put("江苏省", 320000);
        PROVINCE_MAP.put("浙江省", 330000);
        PROVINCE_MAP.put("安徽省", 340000);
        PROVINCE_MAP.put("福建省", 350000);
        PROVINCE_MAP.put("江西省", 360000);
        PROVINCE_MAP.put("山东省", 370000);
        PROVINCE_MAP.put("河南省", 410000);
        PROVINCE_MAP.put("湖北省", 420000);
        PROVINCE_MAP.put("湖南省", 430000);
        PROVINCE_MAP.put("广东省", 440000);
        PROVINCE_MAP.put("广西壮族自治区", 450000);
        PROVINCE_MAP.put("海南省", 460000);
        PROVINCE_MAP.put("重庆市", 500000);
        PROVINCE_MAP.put("四川省", 510000);
        PROVINCE_MAP.put("贵州省", 520000);
        PROVINCE_MAP.put("云南省", 530000);
        PROVINCE_MAP.put("西藏自治区", 540000);
        PROVINCE_MAP.put("陕西省", 610000);
        PROVINCE_MAP.put("甘肃省", 620000);
        PROVINCE_MAP.put("青海省", 630000);
        PROVINCE_MAP.put("宁夏回族自治区", 640000);
        PROVINCE_MAP.put("新疆维吾尔自治区", 650000);
        PROVINCE_MAP.put("台湾省", 710000);
        PROVINCE_MAP.put("香港特别行政区", 810000);
        PROVINCE_MAP.put("澳门特别行政区", 820000);

        COUNTRY_MAP.put("巴哈马", 1);
        COUNTRY_MAP.put("伯利兹", 2);
        COUNTRY_MAP.put("加拿大", 3);
        COUNTRY_MAP.put("哥斯达黎加", 4);
        COUNTRY_MAP.put("古巴", 5);
        COUNTRY_MAP.put("多米尼加共和国", 6);
        COUNTRY_MAP.put("格陵兰", 7);
        COUNTRY_MAP.put("危地马拉", 8);
        COUNTRY_MAP.put("洪都拉斯", 9);
        COUNTRY_MAP.put("海地", 10);
        COUNTRY_MAP.put("牙买加", 11);
        COUNTRY_MAP.put("墨西哥", 12);
        COUNTRY_MAP.put("尼加拉瓜", 13);
        COUNTRY_MAP.put("巴拿马", 14);
        COUNTRY_MAP.put("波多黎各", 15);
        COUNTRY_MAP.put("萨尔瓦多", 16);
        COUNTRY_MAP.put("特立尼达和多巴哥", 17);
        COUNTRY_MAP.put("美国", 18);
        COUNTRY_MAP.put("阿根廷", 19);
        COUNTRY_MAP.put("玻利维亚", 20);
        COUNTRY_MAP.put("巴西", 21);
        COUNTRY_MAP.put("智利", 22);
        COUNTRY_MAP.put("哥伦比亚", 23);
        COUNTRY_MAP.put("秘鲁", 24);
        COUNTRY_MAP.put("福克兰群岛", 25);
        COUNTRY_MAP.put("圭亚那", 26);
        COUNTRY_MAP.put("厄瓜多尔", 27);
        COUNTRY_MAP.put("巴拉圭", 28);
        COUNTRY_MAP.put("苏里南", 29);
        COUNTRY_MAP.put("乌拉圭", 30);
        COUNTRY_MAP.put("委内瑞拉", 31);
        COUNTRY_MAP.put("安哥拉", 32);
        COUNTRY_MAP.put("布隆迪", 33);
        COUNTRY_MAP.put("贝宁", 34);
        COUNTRY_MAP.put("布基纳法索", 35);
        COUNTRY_MAP.put("博茨瓦纳", 36);
        COUNTRY_MAP.put("中非共和国", 37);
        COUNTRY_MAP.put("科特迪瓦", 38);
        COUNTRY_MAP.put("刚果", 39);
        COUNTRY_MAP.put("喀麦隆", 40);
        COUNTRY_MAP.put("刚果金", 41);
        COUNTRY_MAP.put("吉布提", 42);
        COUNTRY_MAP.put("阿尔及利亚", 43);
        COUNTRY_MAP.put("埃及", 44);
        COUNTRY_MAP.put("厄立特里亚", 45);
        COUNTRY_MAP.put("埃塞俄比亚", 46);
        COUNTRY_MAP.put("加蓬", 47);
        COUNTRY_MAP.put("加纳", 48);
        COUNTRY_MAP.put("冈比亚", 49);
        COUNTRY_MAP.put("几内亚", 50);
        COUNTRY_MAP.put("几内亚比绍", 51);
        COUNTRY_MAP.put("肯尼亚", 52);
        COUNTRY_MAP.put("几内亚等式", 53);
        COUNTRY_MAP.put("莱索托", 54);
        COUNTRY_MAP.put("利比亚", 55);
        COUNTRY_MAP.put("利比里亚", 56);
        COUNTRY_MAP.put("摩洛哥", 57);
        COUNTRY_MAP.put("马达加斯加", 58);
        COUNTRY_MAP.put("马里", 59);
        COUNTRY_MAP.put("莫桑比克", 60);
        COUNTRY_MAP.put("毛里塔尼亚", 61);
        COUNTRY_MAP.put("尼日利亚", 62);
        COUNTRY_MAP.put("马拉维", 63);
        COUNTRY_MAP.put("卢旺达", 64);
        COUNTRY_MAP.put("尼日尔", 65);
        COUNTRY_MAP.put("纳米比亚", 66);
        COUNTRY_MAP.put("W.撒哈拉", 67);
        COUNTRY_MAP.put("苏丹", 68);
        COUNTRY_MAP.put("S.苏丹", 69);
        COUNTRY_MAP.put("塞拉利昂", 70);
        COUNTRY_MAP.put("塞内加尔", 71);
        COUNTRY_MAP.put("索马里兰", 72);
        COUNTRY_MAP.put("乍得", 73);
        COUNTRY_MAP.put("索马里", 74);
        COUNTRY_MAP.put("斯威士兰", 75);
        COUNTRY_MAP.put("多哥", 76);
        COUNTRY_MAP.put("突尼斯", 77);
        COUNTRY_MAP.put("坦桑尼亚", 78);
        COUNTRY_MAP.put("乌干达", 79);
        COUNTRY_MAP.put("南非", 80);
        COUNTRY_MAP.put("赞比亚", 81);
        COUNTRY_MAP.put("津巴布韦", 82);
        COUNTRY_MAP.put("阿富汗", 83);
        COUNTRY_MAP.put("阿拉伯联合酋长国", 84);
        COUNTRY_MAP.put("亚美尼亚", 85);
        COUNTRY_MAP.put("阿塞拜疆", 86);
        COUNTRY_MAP.put("孟加拉国", 87);
        COUNTRY_MAP.put("文莱", 88);
        COUNTRY_MAP.put("不丹", 89);
        COUNTRY_MAP.put("中国", 90);
        COUNTRY_MAP.put("塞浦路斯", 91);
        COUNTRY_MAP.put("N.塞浦路斯", 92);
        COUNTRY_MAP.put("乔治亚", 93);
        COUNTRY_MAP.put("印度尼西亚", 94);
        COUNTRY_MAP.put("印度", 95);
        COUNTRY_MAP.put("伊朗", 96);
        COUNTRY_MAP.put("伊拉克", 97);
        COUNTRY_MAP.put("以色列", 98);
        COUNTRY_MAP.put("约旦", 99);
        COUNTRY_MAP.put("日本", 100);
        COUNTRY_MAP.put("哈萨克斯坦", 101);
        COUNTRY_MAP.put("吉尔吉斯斯坦", 102);
        COUNTRY_MAP.put("柬埔寨", 103);
        COUNTRY_MAP.put("韩国", 104);
        COUNTRY_MAP.put("科威特", 105);
        COUNTRY_MAP.put("老挝人民民主共和国", 106);
        COUNTRY_MAP.put("黎巴嫩", 107);
        COUNTRY_MAP.put("斯里兰卡", 108);
        COUNTRY_MAP.put("缅甸", 109);
        COUNTRY_MAP.put("蒙古", 110);
        COUNTRY_MAP.put("马来西亚", 111);
        COUNTRY_MAP.put("阿曼", 112);
        COUNTRY_MAP.put("尼泊尔", 113);
        COUNTRY_MAP.put("巴基斯坦", 114);
        COUNTRY_MAP.put("菲律宾", 115);
        COUNTRY_MAP.put("德国马克。韩国", 116);
        COUNTRY_MAP.put("巴勒斯坦", 117);
        COUNTRY_MAP.put("卡塔尔", 118);
        COUNTRY_MAP.put("沙特阿拉伯", 119);
        COUNTRY_MAP.put("叙利亚", 120);
        COUNTRY_MAP.put("塔吉克斯坦", 121);
        COUNTRY_MAP.put("泰国", 122);
        COUNTRY_MAP.put("东帝汶", 123);
        COUNTRY_MAP.put("土库曼斯坦", 124);
        COUNTRY_MAP.put("土耳其", 125);
        COUNTRY_MAP.put("台湾", 126);
        COUNTRY_MAP.put("乌兹别克斯坦", 127);
        COUNTRY_MAP.put("越南", 128);
        COUNTRY_MAP.put("也门", 129);
        COUNTRY_MAP.put("澳大利亚", 130);
        COUNTRY_MAP.put("斐济", 131);
        COUNTRY_MAP.put("新喀里多尼亚", 132);
        COUNTRY_MAP.put("新西兰", 133);
        COUNTRY_MAP.put("巴布亚新几内亚", 134);
        COUNTRY_MAP.put("所罗门是.", 135);
        COUNTRY_MAP.put("瓦努阿图", 136);
        COUNTRY_MAP.put("阿尔巴尼亚", 137);
        COUNTRY_MAP.put("奥地利", 138);
        COUNTRY_MAP.put("比利时", 139);
        COUNTRY_MAP.put("保加利亚", 140);
        COUNTRY_MAP.put("白俄罗斯", 141);
        COUNTRY_MAP.put("波斯尼亚和赫尔茨", 142);
        COUNTRY_MAP.put("瑞士", 143);
        COUNTRY_MAP.put("捷克共和国", 144);
        COUNTRY_MAP.put("德国", 145);
        COUNTRY_MAP.put("丹麦", 146);
        COUNTRY_MAP.put("西班牙", 147);
        COUNTRY_MAP.put("爱沙尼亚", 148);
        COUNTRY_MAP.put("芬兰", 149);
        COUNTRY_MAP.put("法国", 150);
        COUNTRY_MAP.put("英国", 151);
        COUNTRY_MAP.put("希腊", 152);
        COUNTRY_MAP.put("克罗地亚", 153);
        COUNTRY_MAP.put("匈牙利", 154);
        COUNTRY_MAP.put("爱尔兰", 155);
        COUNTRY_MAP.put("意大利", 156);
        COUNTRY_MAP.put("冰岛", 157);
        COUNTRY_MAP.put("卢森堡", 158);
        COUNTRY_MAP.put("立陶宛", 159);
        COUNTRY_MAP.put("科索沃", 160);
        COUNTRY_MAP.put("拉脱维亚", 161);
        COUNTRY_MAP.put("摩尔多瓦", 162);
        COUNTRY_MAP.put("黑山", 163);
        COUNTRY_MAP.put("马其顿", 164);
        COUNTRY_MAP.put("荷兰", 165);
        COUNTRY_MAP.put("挪威", 166);
        COUNTRY_MAP.put("葡萄牙", 167);
        COUNTRY_MAP.put("罗马尼亚", 168);
        COUNTRY_MAP.put("波兰", 169);
        COUNTRY_MAP.put("俄罗斯", 170);
        COUNTRY_MAP.put("塞尔维亚", 171);
        COUNTRY_MAP.put("斯洛伐克", 172);
        COUNTRY_MAP.put("斯洛文尼亚", 173);
        COUNTRY_MAP.put("瑞典", 174);
        COUNTRY_MAP.put("乌克兰", 175);
    }
}
