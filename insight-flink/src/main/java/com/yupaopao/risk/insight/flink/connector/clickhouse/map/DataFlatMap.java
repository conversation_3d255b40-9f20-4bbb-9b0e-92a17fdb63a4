package com.yupaopao.risk.insight.flink.connector.clickhouse.map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.OSSObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.github.wnameless.json.flattener.FlattenMode;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.Location;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.connection.OSSProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import com.yupaopao.risk.insight.flink.connector.redis.sink.client.RedisClient;
import com.yupaopao.risk.insight.flink.constants.CityCodeConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import redis.clients.jedis.JedisPool;

import java.io.*;
import java.net.InetAddress;
import java.util.*;

/**
 * Created by Avalon on 2020/3/11 14:03
 */
@Slf4j
public class DataFlatMap extends RichFlatMapFunction<String, String> implements Serializable {

    private final static FastDateFormat fastDateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    private LongCounter processCount = new LongCounter();

    private DatabaseReader databaseReader;


    private Map<String, String> gpsTypeMap = new HashMap<>();
    private HTable gpsTable;
    private GPSProcess gpsProcess;

    private OSS ossClient;
    private OSSObject ossObject;

    private JedisPool jedisPool;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        getRuntimeContext().addAccumulator("processCount", this.processCount);

//        databaseReader = new DatabaseReader.Builder(readGeoLiteFromOSS()).build();
        databaseReader = new DatabaseReader.Builder(readGeoLiteFromOSS()).build();

//        jedis = jedisPool.getResource();
        gpsTypeMap.put("lng", "String");
        gpsTypeMap.put("lat", "String");
        gpsTypeMap.put("locateCity", "String");
        gpsTypeMap.put("updateTime", "String");
        Connection connection = HBaseUtil.createConnection(HBaseProperties.getProperties(PropertyType.HBASE));
        this.gpsTable = HBaseUtil.getTable("risk_gps_info", connection);
        jedisPool = RedisClient.getClient();
        gpsProcess = new GPSProcess(jedisPool);
    }

    private InputStream readGeoLiteFromOSS() {
        OSSProperties ossProperties = OSSProperties.getProperties(PropertyType.OSS);
        ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(),
                ossProperties.getAccessKeySecret());
        String objectName = "risk-flink/geolite/GeoLite2-City.mmdb";
        ossObject = ossClient.getObject(ossProperties.getBucket(), objectName);
        return ossObject.getObjectContent();
    }

    private InputStream readGeoLiteFromNFS() throws Exception {
        File database = new File("/flink/geolite/GeoLite2-City.mmdb");
        return new BufferedInputStream(new FileInputStream(database));
    }

    @Override
    public void flatMap(String value, Collector<String> out) throws Exception {
        JSONObject tmp = JSON.parseObject(value);
        tmp.remove("full_text");
        tmp.put("createdAt", fastDateFormat.format(tmp.getDate("createdAt")));
        List<String> doubleColumns = null;
        String cfgDoubleColumns = ApolloProperties.getConfigStr("risk_hit_log.doubleFields");
        if(StringUtils.isNotBlank(cfgDoubleColumns)){
            doubleColumns = Arrays.asList(cfgDoubleColumns.split("#"));
        }
        Map<String, Object> flatMapFlatter = JsonFlatterUtils.toMap(JSON.toJSONString(tmp), FlattenMode.KEEP_ARRAYS);
        Map<String, Object> flatMap = new HashMap<>(flatMapFlatter.size());
        for (Map.Entry<String, Object> entry : flatMapFlatter.entrySet()) {
            if (entry.getValue() instanceof Number) {
                if(doubleColumns!=null && doubleColumns.contains(entry.getKey())){
                    entry.setValue(((Number) entry.getValue()).doubleValue());
                }else{
                    entry.setValue(((Number) entry.getValue()).longValue());
                }
            } else if (entry.getValue() instanceof List) {
                entry.setValue(JSONObject.toJSONString(entry.getValue()));
            } else if (entry.getValue() instanceof Boolean) {
                Boolean bool = (Boolean) entry.getValue();
                entry.setValue(bool ? 1 : 0);
            }

            //对flatten后的json含有_的特殊类型字段处理
            String newKey = "";
            if (entry.getKey().contains("[\\\"") && entry.getKey().contains("\\\"]")) {
                newKey = entry.getKey().replace("[\\\"", "_").replace("\\\"]", "");
                flatMap.put(newKey, entry.getValue());
            } else {
                flatMap.put(entry.getKey(), entry.getValue());
            }
        }

        try {
            Location clientIp = getCoordinate(tmp.getString("clientIp").trim());
            flatMap.put("data_clientIpDetail_latitude", clientIp.getLatitude());
            flatMap.put("data_clientIpDetail_longitude", clientIp.getLongitude());
        } catch (Exception e) {
            log.debug("{} 不支持经纬度转换", tmp.getString("clientIp"));
        }

        Object province = flatMap.get("data_clientIpDetail_province");
        Object country = flatMap.get("data_clientIpDetail_country");

        if (Objects.nonNull(province)) {
            flatMap.put("data_clientIpDetail_provinceCode", CityCodeConstants.PROVINCE_MAP.getOrDefault(province.toString(), 0));
        }
        if (Objects.nonNull(country)) {
            flatMap.put("data_clientIpDetail_countryCode", CityCodeConstants.COUNTRY_MAP.getOrDefault(country.toString(), 0));
        }
        Transaction gpsTraction = Cat.newTransaction("insight.gps.query", "gps.query");
        gpsProcess.getGpsInfo(flatMap);
        gpsTraction.complete();

        processCount.add(1);
        out.collect(JSON.toJSONString(flatMap));
    }

    @Override
    public void close() throws Exception {
        super.close();
        this.gpsTable.close();

        if (ossObject != null) {
            ossObject.close();
        }
        if (ossClient != null) {
            ossClient.shutdown();
        }
        RedisClient.closePool(jedisPool);
    }

    private Location getCoordinate(String ip) throws Exception {
        InetAddress ipAddress = InetAddress.getByName(ip);
        CityResponse response = databaseReader.city(ipAddress);
        return response.getLocation();
    }

//    void getGpsInfo(Map<String, Object> flatMap) {
//        Transaction totalTransaction = Cat.newTransaction("insight.gps.query", "query.gps.info");
//        String userId = flatMap.getOrDefault("userId", "").toString();
//        if (StringUtils.isEmpty(userId)) {
//            return;
//        }
//        if (null == jedis) {
//            jedis = jedisPool.getResource();
//        }
//        Transaction redisTransaction = Cat.newTransaction("insight.gps.query", "redis.read");
//        String value = jedis.get(String.format(FlinkConstants.GPS_DATA_PREFIX, userId));
//        redisTransaction.complete();
//        Map<String, Object> data = null;
//        if (StringUtils.isEmpty(value)) {
//            Cat.logMetricForCount("gps.hbase.query.count");
//            Transaction hbaseTransaction = Cat.newTransaction("insight.gps.query", "hbase.read");
//            try {
//                data = HBaseSupport.getRow(userId, gpsTable, gpsTypeMap);
//            } catch (Exception e) {
//                log.error("gps hbase query fail");
//            }
//            finally {
//                hbaseTransaction.complete();
//            }
//        } else {
//            Cat.logMetricForCount("gps.redis.query.count");
//            data = JSONObject.parseObject(value);
//        }
//
//        if (!Objects.isNull(data) && data.size() > 0) {
//            flatMap.put("gps_lng", data.get("lng"));
//            flatMap.put("gps_lat", data.get("lat"));
//            flatMap.put("gps_city", data.get("locateCity"));
//            flatMap.put("gps_updateTime", data.get("updateTime").toString());
//        }
//        totalTransaction.complete();
//    }
}
