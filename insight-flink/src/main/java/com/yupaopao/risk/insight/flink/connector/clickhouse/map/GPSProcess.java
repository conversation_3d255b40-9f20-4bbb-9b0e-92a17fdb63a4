package com.yupaopao.risk.insight.flink.connector.clickhouse.map;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseSupport;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.flink.connector.redis.sink.client.RedisClient;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j public class GPSProcess {
    private JedisPool jedisPool;
    private Map<String, String> gpsTypeMap = new HashMap<>();
    private HTable gpsTable;

    public GPSProcess(JedisPool jedisPool) throws IOException {
        gpsTypeMap.put("lng", "String");
        gpsTypeMap.put("lat", "String");
        gpsTypeMap.put("locateCity", "String");
        gpsTypeMap.put("updateTime", "String");
        Connection connection = HBaseUtil.createConnection(HBaseProperties.getProperties(PropertyType.HBASE));
        this.gpsTable = HBaseUtil.getTable("risk_gps_info", connection);
        this.jedisPool = jedisPool;

    }

    public void getGpsInfo(Map<String, Object> flatMap) {
        String userId = flatMap.getOrDefault("userId", "").toString();
        if (StringUtils.isEmpty(userId)) {
            return;
        }
        try (Jedis jedis = jedisPool.getResource()) {

            Transaction redisTransaction = Cat.newTransaction("insight.gps.query", "redis.read");
            String value = jedis.get(String.format(FlinkConstants.GPS_DATA_PREFIX, userId));
            redisTransaction.complete();

            Map<String, Object> data = null;
            if (StringUtils.isEmpty(value)) {
                Cat.logMetricForCount("gps.hbase.query.count");

                Transaction hbaseTransaction = Cat.newTransaction("insight.gps.query", "hbase.read");
                try {
                    data = HBaseSupport.getRow(userId, gpsTable, gpsTypeMap);
                } catch (Exception e) {
                    log.error("gps hbase query fail");
                }
                hbaseTransaction.complete();
            } else {
                Cat.logMetricForCount("gps.redis.query.count");
                data = JSONObject.parseObject(value);
            }

            if (!Objects.isNull(data) && data.size() > 0) {
                flatMap.put("gps_lng", data.get("lng"));
                flatMap.put("gps_lat", data.get("lat"));
                flatMap.put("gps_city", data.get("locateCity"));
                flatMap.put("gps_updateTime", data.get("updateTime").toString());
            }
        } catch (Exception e) {
            log.error("get gps info error: ", e);
        }

    }
}
