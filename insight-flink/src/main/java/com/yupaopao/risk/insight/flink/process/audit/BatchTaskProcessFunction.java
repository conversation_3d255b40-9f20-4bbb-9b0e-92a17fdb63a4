package com.yupaopao.risk.insight.flink.process.audit;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.flink.bean.audit.MxTask;
import com.yupaopao.risk.insight.flink.bean.audit.PerformanceMxTask;
import com.yupaopao.risk.insight.flink.job.audit.SideOutputConstants;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.common.typeutils.TypeSerializer;
import org.apache.flink.api.common.typeutils.base.ListSerializer;
import org.apache.flink.api.common.typeutils.base.LongSerializer;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-16 14:05
 * 批量任务处理
 ***/
@Slf4j
public class BatchTaskProcessFunction extends KeyedProcessFunction<String, List<MxTask>, String> {

    private MapState<Long, List<MxTask>> batchTaskProcessMapState;
    private ValueState<Long> timerState;
    //兜底处理，batch任务理论是一批同时完同时接受到，如果1天还没有到达直接状态清除，把已有的数据做计算
    private final static long taskTimeoutInMillis = 30000L * 1 * 1;

    private Counter timeoutCounter;


    @Override
    public void open(Configuration parameters) throws Exception {
        TypeSerializer<MxTask> elemSerializer =
                TypeInformation.of(MxTask.class).createSerializer(getRuntimeContext().getExecutionConfig());
        ListSerializer<MxTask> valueSerializer = new ListSerializer<>(elemSerializer);
        LongSerializer keySerializer = LongSerializer.INSTANCE;
        MapStateDescriptor<Long, List<MxTask>> stateDescriptor = new MapStateDescriptor("batchProcessList",
                keySerializer, valueSerializer);

        batchTaskProcessMapState = getRuntimeContext().getMapState(stateDescriptor);

        //防止数据丢失或者晚到比较长
        timerState = getRuntimeContext().getState(new ValueStateDescriptor<Long>("timeoutTimer",
                LongSerializer.INSTANCE));

        this.timeoutCounter = getRuntimeContext().getMetricGroup().counter("batchTimeoutCounter");
    }

    /***
     *
     * @param mxTasks 每一个taskId对应的各状态列表
     * @param context
     * @param collector
     * @throws Exception
     */
    @Override
    public void processElement(List<MxTask> mxTasks, Context context, Collector<String> collector) throws Exception {


        long batchSize = parseBatchSize(mxTasks.get(mxTasks.size() - 1));
        if (batchSize <= 0) {
            log.error("invalid batchSize for taskList: {}", JSON.toJSONString(mxTasks));
            mxTasks.clear();
            return;
        }

        if (batchSize != 1) {

            if (batchTaskProcessMapState.isEmpty()) {
                //首次记录时间，超过1天清理数据
                String batchTaskDelayTime = ApolloProperties.getConfigStr("application", "audit.task.batch.delayTime");
                Long delayTimeInMillis = taskTimeoutInMillis;
                if (StringUtils.isNotEmpty(batchTaskDelayTime)) {
                    delayTimeInMillis = Long.parseLong(batchTaskDelayTime);
                }
                long registerTime = System.currentTimeMillis() + delayTimeInMillis;
                timerState.update(registerTime);
                context.timerService().registerProcessingTimeTimer(registerTime);
            }
            batchTaskProcessMapState.put(mxTasks.get(0).getId(), mxTasks);
        } else {
            //单条记录直接处理
            if (getCurrentSateSize() == batchSize - 1) {
                //同一个批次的数据都已经拿到
                Map<Long, List<MxTask>> batchTaskMap = getStateMapElements();
                batchTaskMap.put(mxTasks.get(0).getId(), mxTasks);
                List<PerformanceMxTask> resultList = PerformanceCalculator.calculate(batchTaskMap);
                resultList.forEach(elem -> {
                    String jsonResult = null;
                    try {
                        jsonResult = JSON.toJSONString(elem);
                        collector.collect(jsonResult);
                        context.output(SideOutputConstants.performanceResultTask, elem);
                    }catch (Exception e){
                        log.warn("output task result error: " + jsonResult);
                    }
                });
                //清空状态
                clear(context);
            } else {
                log.warn("the state info not correct: {}",JSON.toJSONString(mxTasks));
                batchTaskProcessMapState.put(mxTasks.get(0).getId(), mxTasks);
            }
        }


    }

    public void onTimer(long timestamp, OnTimerContext ctx, Collector<String> out) throws Exception {
        long stateTime = timerState.value();
        final String remarks;
        if (timestamp == stateTime) {
            //collector with without remark
//            remarks = "batch delay collector";
//            log.warn("timeout for batch task: {}");
        } else {
            remarks = "batch timeout collector-time-not-equal";
            log.warn("illegal state onTimer, timestamp: {}, stateValue: {}", timestamp, stateTime);
        }
        Map<Long, List<MxTask>> batchTaskMap = getStateMapElements();
        if (batchTaskMap.isEmpty()) {
            log.error("invalid state for batch task, empty state");
            return;
        }
        //记录日志
        long parsedBatchSize = 1;
        for (List<MxTask> taskElem : batchTaskMap.values()) {
            parsedBatchSize = parseBatchSize(taskElem.get(taskElem.size() - 1));
            break;
        }
        if (parsedBatchSize != batchTaskMap.size()) {
            Cat.logMetricForCount("audit.batch.task.batchSize.notEqual");
        }
        List<PerformanceMxTask> resultList = PerformanceCalculator.calculate(batchTaskMap);
        resultList.forEach(elem -> {
//            elem.setFlinkRemark(remarks);
            out.collect(JSON.toJSONString(elem));
            ctx.output(SideOutputConstants.performanceResultTask,elem);
        });
        if (!batchTaskMap.isEmpty()) {
            timeoutCounter.inc();
        }
        clear(ctx);
    }

    private void clear(Context context) throws IOException {
        if(timerState!=null && timerState.value()!=null){
            context.timerService().deleteProcessingTimeTimer(timerState.value());
            timerState.clear();
        }
        if(batchTaskProcessMapState!=null){
            batchTaskProcessMapState.clear();
        }

    }

    /***
     * 根据batchId解析每个批次的条数
     * @param mxTask
     * @return
     */
    private long parseBatchSize(MxTask mxTask) {
        String batchIdSplit[] = mxTask.getBatchId().split("-");
        if (batchIdSplit == null || batchIdSplit.length == 1) {
            return -1; //异常数据
        }
        return Long.parseLong(batchIdSplit[1]);
    }

    private long getCurrentSateSize() throws Exception {
        long currentDataSize = 0;
        for (Long key : batchTaskProcessMapState.keys()) {
            currentDataSize++;
        }
        return currentDataSize;
    }

    private Map<Long, List<MxTask>> getStateMapElements() throws Exception {
        Map<Long, List<MxTask>> taskMap = new HashMap<>();
        for (List<MxTask> taskList : batchTaskProcessMapState.values()) {
            taskMap.put(taskList.get(0).getId(), taskList);
        }
        return taskMap;
    }


}
