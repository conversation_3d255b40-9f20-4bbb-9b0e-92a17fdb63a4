package com.yupaopao.risk.insight.flink.connector.ts.serialization;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.serialization.DeserializationSchema;

import java.util.Date;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-04-28 15:43
 * kafka json数据扁平化后的结果
 ***/
@Slf4j
public class HistoryKafkaDeserializationSchemaWrapper extends StringKafkaDeserializationSchemaWrapper {


    public HistoryKafkaDeserializationSchemaWrapper(DeserializationSchema<String> deserializationSchema) {
        super(deserializationSchema);
    }

    public HistoryKafkaDeserializationSchemaWrapper(DeserializationSchema<String> deserializationSchema,
                                                          boolean needLog) {
        super(deserializationSchema, needLog);
    }


    @Override
    public String getJsonResult(String value, long timestamp, String topic) {
        if (StringUtils.isEmpty(value) || timestamp > 1660289740000L) {
            if (timestamp % 8 == 0) {
                log.info("数据补充结束");
            }
            return null;
        }
        Map<String, Object> flattenMap = getStringObjectMap(value);

        if(!flattenMap.containsKey("createdAt")){
            String createdAt = InsightDateUtils.getDateStr(new Date(timestamp),
                    InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
            flattenMap.put("createdAt", createdAt);
        }

        flattenMap.put("kafkaTopic",topic);
        return JSON.toJSONString(flattenMap);
    }

    public Map<String, Object> getStringObjectMap(String value) {
       return JsonFlatterUtils.toCkMap(value);
    }


}
