package com.yupaopao.risk.insight.flink.job.classification;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import com.yupaopao.risk.insight.common.property.connection.OSSProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import com.yupaopao.risk.insight.flink.job.classification.FeatureExtractor.FlinkFeatureCfg;
import com.yupaopao.risk.insight.flink.utils.DBUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.ExecutionEnvironment;
import org.apache.flink.api.java.io.DiscardingOutputFormat;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.apache.flink.types.RowUtils;

import java.io.File;
import java.io.FileInputStream;
import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.Statement;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/****
 * zengxiangcai
 * 2023/11/2 19:03
 * decision-tree-constructor.jar
 ***/

@Slf4j
public class DecisionTreeModelBuilderJob {

    public static void main(String[] args) throws Exception {
        new BatchJobBuilder()
                .withJobName("decision tree model building")
                .withProcessor(new ModelBuildingProcessor())
                .start(args);
//                .start(new String[]{testParams()});
    }

    private static String testParams() throws Exception {
        Map<String, String> argMap = new HashMap<>();
        argMap.put("taskId", "12");
        argMap.put("batchId", "bad5ed9648984ad99ca9d27189ec8947");
        argMap.put("runId", "edc1300061d548d1a0a83d000b24a895");
        String data = URLEncoder.encode(JSON.toJSONString(argMap), "UTF-8");
        System.err.println(data);
        return data;
    }

    public static class ModelBuildingProcessor implements BatchJobBuilder.MainProcessor {

        @Override
        public void internalProcess(ExecutionEnvironment env, Map<String, String> argMap) throws Exception {
            env.setParallelism(1);
            ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);

            String batchId = argMap.get("batchId");
            String taskId = argMap.get("taskId");
            String runId = argMap.get("runId");
            String sqlTemplate = "select model_id, model_info,label_value from risk_model_info where batchId='%s' and" +
                    " runId = '%s'";
            String finalSql = String.format(sqlTemplate, batchId, runId);
            env.fromElements(batchId).map(elem -> {
                try (Connection conn = ClickHouseUtil.getCkConn(ckProperties)) {
                    List<Map<String, String>> modelRows = ClickHouseUtil.executeQuery(conn, finalSql);
                    if (CollectionUtils.isNotEmpty(modelRows)) {
                        List<Row> rows = modelRows.stream().map(modelElem -> {
                            String model_id = modelElem.get("model_id");
                            String model_info = modelElem.get("model_info");
                            String label_value = modelElem.get("label_value");
                            Long modelId = Long.valueOf(model_id);
                            LinkedHashMap<String, Integer> namePositions = new LinkedHashMap<>();
                            namePositions.put("model_id", 0);
                            namePositions.put("model_info", 1);
                            namePositions.put("label_value", 2);
                            Object[] rowData = new Object[]{modelId, model_info, label_value};
                            return RowUtils.createRowWithNamedPositions(RowKind.INSERT, rowData, namePositions);
                        }).collect(Collectors.toList());

                        CustomTreeModelInfo model =
                                new CustomTreeModelInfo(rows);
                        new ModelPostProcess(taskId, batchId, runId).accept(model);
                    }
                }

                return elem;
            }).returns(String.class).output(new DiscardingOutputFormat<>());

        }
    }

    public static class ModelPostProcess implements Consumer<CustomTreeModelInfo> {
        private String taskId;
        private String batchId;

        private String runId;

        public ModelPostProcess(String taskId, String batchId, String runId) {
            this.taskId = taskId;
            this.batchId = batchId;
            this.runId = runId;
        }

        public void accept(CustomTreeModelInfo value) {
            try {

                String sqlRule = value.getSqlRule();
                //打印规则
                log.info("decision tree result: {}", sqlRule);

                //打印决策树图片
                String path = "/Users/<USER>/Documents/决策树分析demo/v2/";
                path = "/flink/decision_tree/";
                String dateDir = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyyMMdd);
                String extendDir = taskId + "/" + batchId + "/" + dateDir;
                path += extendDir;
                File f = new File(path);
                if (!f.exists()) {
                    f.mkdirs();
                }
                String fileName = runId + "_tree.png";
                String imgPath = path + "/" + fileName;
                value.saveTreeAsImage(
                        imgPath, true);
                //写入oos
                boolean saveOssSuccess = false;
                try (FileInputStream img = new FileInputStream(new File(imgPath))) {
                    String objectName = FlinkConstants.getOSSBase() + "imgs/" + extendDir + "/" + fileName;
                    OSSProperties ossProperties = OSSProperties.getProperties(PropertyType.OSS);
                    OSS ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(),
                            ossProperties.getAccessKeySecret());
                    ossClient.putObject(ossProperties.getBucket(), objectName, img);
                    ossClient.shutdown();
                    saveOssSuccess = true;


                } catch (Exception e) {
                    log.error("write to oss error: ", e);
                }

                //write to db
                String dbSaveImg = extendDir + "/" + fileName;
                String sql = "update t_strategy_task_result set run_status =?,result_img = ?," +
                        " update_time = now() where task_id = ? and sample_batchid=? and run_id = ?";

                //parse sql:
                String checkSql = parseSql(sqlRule);

                try (Connection conn =
                             DBUtil.getConnection(DBProperties.getProperties(PropertyType.DB_INSIGHT))) {
                    List<Object> params = Arrays.asList(saveOssSuccess ? 1 : 2, saveOssSuccess ? dbSaveImg : "",
                            taskId, batchId, runId);
                    DBUtil.executeUpdate(conn, sql, params);
                }

                try (Connection conn = ClickHouseUtil.getCkConn(ClickHouseUtil.getLongTimeoutProperties());
                     Statement stmt = conn.createStatement()) {

                    Map<String, String> row = new HashMap<>();
                    row.put("runId", runId);
                    row.put("taskId", taskId);
                    row.put("rawResult", sqlRule);
                    row.put("resultSql", checkSql);
                    row.put("createdAt",
                            InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss));

                    String resultCSV = String.join("\n", Arrays.asList(JSON.toJSONString(row)));
                    String resultFormat = "INSERT INTO %s FORMAT JSONEachRow\n%s";
                    String insertSql = String.format(resultFormat, "t_strategy_task_result", resultCSV);
                    stmt.executeUpdate(insertSql);
                }

            } catch (Exception e) {
                log.error("process model file error", e);
            }
        }


        private String parseSql(String sqlRule) {
            if (StringUtils.isEmpty(sqlRule)) {
                return "";
            }
            List<CustomTreeModelInfo.MarkedLeafNode> ruleList = JSON.parseArray(sqlRule,
                    CustomTreeModelInfo.MarkedLeafNode.class);
            List<String> finalCheckList = new ArrayList<>();
            for (CustomTreeModelInfo.MarkedLeafNode node : ruleList) {
                String checkData = parseRule(node.getCondition());
                finalCheckList.add(checkData);
            }
            return JSON.toJSONString(finalCheckList);
        }

        private String parseRule(String rule) {
            String conditions[] = rule.split(" and ");
            List<FeatureExtractor.FlinkFeatureCfg> featureCfgs = FeatureExtractor.getFeatureConfig(null);
            //code,groupType
            Map<String, String> featureCoeGroupTypeMap =
                    featureCfgs.stream().collect(Collectors.toMap(FlinkFeatureCfg::getFeatureCode,
                            FlinkFeatureCfg::getGroupType));
            //按照userId,deviceId,mobile 维度分组
            Map<String, List<String>> dimensionConditions = new HashMap<>();
            for (String condition : conditions) {
                String featureCode = condition.split(" ")[0];
                String groupType = featureCoeGroupTypeMap.get(featureCode);
                List<String> resultList = dimensionConditions.getOrDefault(groupType, new ArrayList<>());
                resultList.add(condition);
                dimensionConditions.put(groupType, resultList);
            }

            //
            String checkSql = "select userId,deviceId from risk_hit_log where createdAt>=addDays(now(),-2) \n" +
                    " and data_Business!='riskPayment'\n";
            if (dimensionConditions.containsKey("0")) {
                //userId
                String whereUserCondition = dimensionConditions.get("0").stream().collect(Collectors.joining(" and "));
                String whereUser =
                        "select id from dwd_risk_user_feature_df where dt>=addDays(today(),-7) and " + whereUserCondition;
                checkSql += " and userId global in (" +
                        whereUser +
                        ")";
            }

            if (dimensionConditions.containsKey("1")) {
                //deviceId
                String whereUserCondition = dimensionConditions.get("1").stream().collect(Collectors.joining(" and "));
                String whereDevice =
                        "select id from dwd_risk_device_feature_df where dt>=addDays(today(),-7) and " + whereUserCondition;
                checkSql += " and deviceId global in (" +
                        whereDevice +
                        ")";
            }
            if (dimensionConditions.containsKey("2")) {
                //userId
                String whereUserCondition = dimensionConditions.get("2").stream().collect(Collectors.joining(" and "));
                String whereMobile =
                        "select id from dwd_risk_user_feature_df where dt>=addDays(today(),-7) and " + whereUserCondition;
                checkSql += " and userId global in (" +
                        whereMobile +
                        ")";
            }

            checkSql += "\n group by userId,deviceId \n limit 1000";

            return checkSql;
        }


    }


}
