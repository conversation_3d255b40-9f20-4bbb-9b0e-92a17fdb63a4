//package com.yupaopao.risk.insight.flink.connector.ts.source;
//
//import com.yupaopao.risk.insight.flink.connector.ts.util.TsTunnelRecordReader;
//import org.apache.flink.streaming.api.functions.source.RichParallelSourceFunction;
//
//import java.io.Serializable;
//import java.util.Map;
//
//public class TsTunnelStreamSource extends RichParallelSourceFunction<Map<String, Object>> implements Serializable {
//
//    private TsTunnelRecordReader recordReader;
//
//    public TsTunnelStreamSource(TsTunnelRecordReader recordReader) {
//        this.recordReader = recordReader;
//    }
//
//    @Override public void run(SourceContext<Map<String, Object>> sourceContext) throws Exception {
//        while (recordReader.isCancel()) {
//            if (recordReader.shouldFetchOts()) {
//                recordReader.getData().forEach(sourceContext::collect);
//            } else {
//                //数据量较小，放慢频率
//                Thread.sleep(1000);
//            }
//        }
//        close();
//    }
//
//    @Override public void cancel() {
//        recordReader.interrupt();
//    }
//
//    @Override public void close() throws Exception {
//        recordReader.close();
//    }
//}
