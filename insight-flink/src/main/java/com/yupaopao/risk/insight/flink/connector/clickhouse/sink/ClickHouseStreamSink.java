package com.yupaopao.risk.insight.flink.connector.clickhouse.sink;

import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yupaopao.risk.insight.common.property.ApolloProperties;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.support.SecurityAlgorithmUtils;
import com.yupaopao.risk.insight.flink.connector.redis.sink.client.RedisClient;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.metrics.Counter;
import org.apache.flink.runtime.state.FunctionInitializationContext;
import org.apache.flink.runtime.state.FunctionSnapshotContext;
import org.apache.flink.streaming.api.checkpoint.CheckpointedFunction;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.io.IOException;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Created by Avalon on 2020/3/11 16:21
 */
@Slf4j
public class ClickHouseStreamSink extends RichSinkFunction<String> implements CheckpointedFunction {

    private static final long serialVersionUID = -7972620482088250227L;
    private ClickHouseProperties properties;

    private String tableName;

    private transient Counter errorCounter;

    private transient Counter writeCounter;

    private transient ClickHouseFlushWithTimeProcessor flushWithTimeProcessor;

    private transient ClickHouseColumnSyncProcessor syncProcessor;

    private boolean needSync = false;

    private Cache<String, String> cache;

    private String uniqueKey;

    private Integer maxCacheSize = 50000;

    private Integer cacheExpireSeconds = 180;

    private Integer redisCacheExpireSeconds = 300;

    private JedisPool jedisPool;

    private Jedis resource;

    public ClickHouseStreamSink(ClickHouseProperties properties, String tableName) {
        this(properties, tableName, null, null);
    }

    public ClickHouseStreamSink(ClickHouseProperties properties, String tableName, Integer flushIntervalInMillis,
                                Integer batchSize) {
        this(properties, tableName, flushIntervalInMillis, batchSize, false);
    }

    public ClickHouseStreamSink(ClickHouseProperties properties, String tableName, Integer flushIntervalInMillis,
                                Integer batchSize, Boolean needSync) {
        //flush 时间和BatchSize可调整
        if (flushIntervalInMillis != null && flushIntervalInMillis > 0) {
            properties.setFlushIntervalInMillis(flushIntervalInMillis);
        }
        if (batchSize != null && batchSize > 0) {
            properties.setBatchSize(batchSize);
        }
        this.properties = properties;
        this.tableName = tableName;
        this.needSync = needSync;
    }

    @Override
    public void open(Configuration parameters) throws Exception {

//        if(getRuntimeContext().getMaxNumberOfParallelSubtasks()>1){
//            throw new RuntimeException("the clickhouse sink parallelism cannot be great than 1");
//        }

        super.open(parameters);

        this.errorCounter = getRuntimeContext().getMetricGroup().counter("ckErrorCount");
        this.writeCounter = getRuntimeContext().getMetricGroup().counter(tableName + "_ckWriteRecords");


        flushWithTimeProcessor = new ClickHouseFlushWithTimeProcessor(this.properties, this.tableName, errorCounter,
                writeCounter, getRuntimeContext().getIndexOfThisSubtask());

        if (this.needSync) {
            this.syncProcessor = new ClickHouseColumnSyncProcessor(properties, tableName);
        }

        initRedis();
        log.info("Instance ClickHouse Sink, target table = {}, properties: {}", tableName, properties);
    }

    private void initRedis() {
        if (null == jedisPool || null == resource) {
            jedisPool = RedisClient.getClient();
            resource = jedisPool.getResource();
        }
    }

    @Override
    public void invoke(String value, Context context) throws Exception {
        try {
            if (StringUtils.isNotEmpty(uniqueKey)) {
                JSONObject jsonObject = JSONObject.parseObject(value);

                String[] uniqueKeys = uniqueKey.split("#");//支持多字段组合
                String uniqueData = getUniqueData(jsonObject, uniqueKeys);

                String logSwitch = ApolloProperties.getConfigStr("logInvokeTraceId");
                if ("true".equals(logSwitch) && "risk_hit_log".equals(tableName)) {
                    log.info("invoke traceId: {}", uniqueData);
                }

                if (cache == null) {
                    cache = CacheBuilder.newBuilder()
                            .maximumSize(maxCacheSize)
                            .expireAfterWrite(cacheExpireSeconds, TimeUnit.SECONDS)
                            .build();
                }

                if (null != cache.getIfPresent(uniqueData)) {
                    log.info("skip exit key: {}", uniqueData);
                    return;
                }
                if (StringUtils.isNotEmpty(uniqueData)) {
                    cache.put(uniqueData, "");
                }
            }

            value = columnCache(value);
            flushWithTimeProcessor.addRecord(value);
        } catch (Exception e) {
            log.error("Error while sending data to ClickHouse, record = {}", value, e);
            throw new RuntimeException(e);
        }
    }


    private static String getUniqueData(JSONObject data, String[] keys) {
        StringBuilder builder = new StringBuilder();
        for (String key : keys) {
            String keyValue = data.getString(key);
            if (StringUtils.isNotEmpty(keyValue)) {
                builder.append(keyValue).append("#");
            }

        }
        return builder.toString();
    }

    @Override
    public void close() throws IOException {
        if (flushWithTimeProcessor != null) {
            flushWithTimeProcessor.close();
        }
        if (syncProcessor != null) {
            syncProcessor.close();
        }
        if (StringUtils.isNotEmpty(uniqueKey)) {
            cachePersistence();
        }
        if (cache != null) {
            cache.cleanUp();
        }
        RedisClient.closeJedis(resource);
        RedisClient.closePool(jedisPool);
    }

    private String columnCache(String value) {
        if (this.needSync && syncProcessor != null) {
            return syncProcessor.cacheColumn(value);
        }
        return value;
    }

    public void setUniqueKey(String uniqueKey) {
        this.uniqueKey = uniqueKey;
    }

    public void setMaxCacheSize(Integer maxCacheSize) {
        this.maxCacheSize = maxCacheSize;
    }

    public void setCacheExpireSeconds(Integer cacheExpireSeconds) {
        this.cacheExpireSeconds = cacheExpireSeconds;
    }


    private void cachePersistence() {
        if (cache != null && cache.size() > 0) {
            Set<String> strings = cache.asMap().keySet();
            String join = String.join(",", strings);
            initRedis();
            resource.setex(String.format(getCkUniqueRedisPrefix() + "clickhouse_cache_%s_%s",
                            tableName, uniqueKey),
                    redisCacheExpireSeconds,
                    join);
        }
    }

    @Override
    public void snapshotState(FunctionSnapshotContext context) throws Exception {
        cachePersistence();
    }

    @Override
    public void initializeState(FunctionInitializationContext context) throws Exception {
        if (StringUtils.isNotEmpty(uniqueKey)) {
            log.info("init state");
            if (null == cache) {
                cache = CacheBuilder.newBuilder()
                        .maximumSize(maxCacheSize)
                        .expireAfterWrite(cacheExpireSeconds, TimeUnit.SECONDS)
                        .build();
            }
            initRedis();
            String result = resource.get(String.format(getCkUniqueRedisPrefix() + "clickhouse_cache_%s_%s", tableName,
                    uniqueKey));
            if (StringUtils.isNotEmpty(result)) {
                String[] split = result.split(",");
                for (String element : split) {
                    cache.put(element, "");
                }
            }
            log.info("init size: {}", cache.size());
        }
    }


    /****
     * 兼容不同库不同任务的情况，如果同时有两个任务跑同一个表相同时间段的topic，可能互相影响，兼容了host作为key，如果是同一个库，需要自己修改前缀临时处理(比如FlinkConstants.FLINK_RISK_CK_REDIS_PREFIX)
     * @return
     */
    private String getCkUniqueRedisPrefix() {
        return FlinkConstants.FLINK_RISK_CK_REDIS_PREFIX + SecurityAlgorithmUtils.getMD5Str("", properties.getHost()) +
                ":";
    }


}
