package com.yupaopao.risk.insight.flink.utils;

import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;

import java.util.concurrent.TimeUnit;

/**
 * Created by Avalon on 2020/3/13 19:50
 */
public class HTTPUtil {

    public static OkHttpClient buildOkHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        builder.retryOnConnectionFailure(true)
                .connectTimeout(3 * 60 * 1000, TimeUnit.MILLISECONDS) //连接超时
                .readTimeout(3 * 60 * 1000, TimeUnit.MILLISECONDS) //读取超时
                .writeTimeout(3 * 60 * 1000, TimeUnit.MILLISECONDS) //写超时
                .connectionPool(new ConnectionPool(100, 1, TimeUnit.MINUTES))
                .build();
        return builder.build();
    }

    public static void releaseResource(OkHttpClient client){
        client.connectionPool().evictAll();
        client.dispatcher().executorService().shutdown();
    }


}
