package com.yupaopao.risk.insight.flink.windows.aggregate;

import lombok.Getter;
import lombok.Setter;
import org.roaringbitmap.longlong.Roaring64NavigableMap;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

@Getter
@Setter
public class TagAggregateResult implements Serializable {
    private Integer id;
    private String groupKey;
    private Double count = 0.0;
    private Set<String> distinct;
    private Map<String, Integer> map;
    private String type;
    private Roaring64NavigableMap bitMap;
}
