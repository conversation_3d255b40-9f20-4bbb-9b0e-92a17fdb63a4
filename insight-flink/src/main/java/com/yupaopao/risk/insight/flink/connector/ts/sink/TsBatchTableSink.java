//package com.yupaopao.risk.insight.flink.connector.ts.sink;
//
//import com.yupaopao.risk.insight.flink.meta.FlinkMetaInfo;
//import com.yupaopao.risk.insight.flink.meta.TsTableInfo;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.flink.api.common.typeinfo.TypeInformation;
//import org.apache.flink.api.java.DataSet;
//import org.apache.flink.table.api.TableSchema;
//import org.apache.flink.table.sinks.BatchTableSink;
//import org.apache.flink.table.sinks.TableSink;
//import org.apache.flink.types.Row;
//
//
//@Slf4j
//public class TsBatchTableSink implements BatchTableSink<Row> {
//
//    private FlinkMetaInfo flinkMetaInfo;
//
//    private TsTableInfo tsTableInfo;
//
//    private FlinkTableTsOutputFormat outputFormat;
//
//    public TsBatchTableSink(TsTableInfo tsTableInfo, FlinkMetaInfo flinkMetaInfo) {
//        this.tsTableInfo = tsTableInfo;
//        this.flinkMetaInfo = flinkMetaInfo;
//        //构建本sink 的format
//        createFormat();
//
//    }
//
//    private void createFormat() {
//        outputFormat = new FlinkTableTsOutputFormat(tsTableInfo,flinkMetaInfo);
//    }
//
//
//    //out format
//    @Override
//    public void emitDataSet(DataSet<Row> dataSet) {
//        //转为ts row，然后写入,ts数据按照主键升序，为了实现有序性，将并行度设置为1，结果按照毫秒数排序
//        dataSet.output(outputFormat).name("write ts result")
//                .setParallelism(1)
//                ;
//    }
//
//    @Override
//    public TableSink<Row> configure(String[] fieldNames, TypeInformation<?>[] fieldTypes) {
//        TsBatchTableSink copy = new TsBatchTableSink(tsTableInfo, flinkMetaInfo);
//        return copy;
//    }
//
//    @Override
//    public TableSchema getTableSchema() {
//        return flinkMetaInfo.toTableSchema();
//    }
//
//    public TypeInformation<Row> getOutputType() {
//        return flinkMetaInfo.toTableSchema().toRowType();
//    }
//
//}
