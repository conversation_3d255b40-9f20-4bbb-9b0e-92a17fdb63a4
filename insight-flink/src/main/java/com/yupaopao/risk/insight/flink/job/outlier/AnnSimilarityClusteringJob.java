package com.yupaopao.risk.insight.flink.job.outlier;

import com.alibaba.alink.common.MLEnvironment;
import com.alibaba.alink.operator.batch.source.DataSetWrapperBatchOp;
import com.alibaba.alink.pipeline.Pipeline;
import com.alibaba.alink.pipeline.dataproc.Imputer;
import com.alibaba.alink.pipeline.dataproc.MinMaxScaler;
import com.alibaba.alink.pipeline.feature.PCA;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.clickhouse.jdbc.ClickHouseConnection;
import com.github.jelmerk.knn.DistanceFunctions;
import com.github.jelmerk.knn.Item;
import com.github.jelmerk.knn.SearchResult;
import com.github.jelmerk.knn.hnsw.HnswIndex;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.connector.clickhouse.source.CKInputFormat;
import com.yupaopao.risk.insight.flink.job.base.ALinkJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.graph.algorithm.ConnectedComponentWithParam;
import com.yupaopao.risk.insight.flink.job.processor.graph.algorithm.UndirectedVertexDegree;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.functions.JoinFunction;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.DataSet;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.api.java.tuple.Tuple3;
import org.apache.flink.api.java.tuple.Tuple8;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.graph.Edge;
import org.apache.flink.graph.Graph;
import org.apache.flink.graph.Vertex;
import org.apache.flink.types.LongValue;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

import java.util.*;
import java.util.stream.Collectors;

/****
 * zengxiangcai
 * 2022/3/14 4:43 PM
 ***/

@Slf4j
public class AnnSimilarityClusteringJob {

    public static final String DISTANCE_THRESHOLD = "0.04";

    public static void main(String[] args) throws Exception {
        new ALinkJobBuilder()
                .withJobName("ann clustering")
                .withProcessor(new AnnSimilarityClusteringJob.AnnSimilarityClusteringProcessor())
                .start(args);

    }


    private static class AnnSimilarityClusteringProcessor implements ALinkJobBuilder.MainProcessor {
        @Override
        public void internalProcess(MLEnvironment mlEnv, Long mlSessionId, Map<String, String> argMap) throws Exception {


            String runDay;
            String dataDay;
            if (StringUtils.isEmpty(argMap.get("runDay"))) {
                dataDay = InsightDateUtils.getBeforeDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
                runDay = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
            } else {
                runDay = argMap.get("runDay");
                dataDay = runDay;
            }
            String distanceThreshold;
            if (argMap.containsKey("distanceThreshold")) {
                distanceThreshold = argMap.get("distanceThreshold");
            } else {
                distanceThreshold = DISTANCE_THRESHOLD;
            }
            List<String> allEvents = null;

            ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
            try (ClickHouseConnection cc = ClickHouseUtil.createConnection(ckProperties);) {

                List<Map<String, String>> events = ClickHouseUtil.executeQuery(cc, String.format(allEventsSql, dataDay, dataDay));
                allEvents =
                        events.stream().map(elem -> elem.get("eventCode").replaceAll("-", "_")).collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(allEvents)) {
                log.info("query events error");
                return;
            }

            CKInputFormat eventCountInput = new CKInputFormat(ckProperties, String.format(eventCountSql, dataDay,
                    dataDay), null);

            CKInputFormat eventGapInput = new CKInputFormat(ckProperties, String.format(eventGapSql, dataDay,
                    dataDay), null);
            List<String> gapColumns =
                    Arrays.asList("stdGap,minGap,maxGap,avgGap,medianGap,minuteCount,hourCount".split(","));

            DataSet<Tuple2<String, String>> eventCountDs =
                    mlEnv.getExecutionEnvironment().createInput(eventCountInput).filter(elem -> !HBaseUtil.emptyResultJson(elem))
                            .map(elem -> {
                                JSONObject row = JSON.parseObject(elem);
                                return Tuple2.of(row.getString("userId"), row.getString("eventList"));
                            }).returns(new TypeHint<Tuple2<String, String>>() {
                                @Override
                                public TypeInformation<Tuple2<String, String>> getTypeInfo() {
                                    return super.getTypeInfo();
                                }
                            }).name("read eventCount");
            DataSet<Tuple8<String, Double, Double, Double, Double, Double, Double, Double>> eventGapDs =
                    mlEnv.getExecutionEnvironment().createInput(eventGapInput).filter(elem -> !HBaseUtil.emptyResultJson(elem))
                            .map(elem -> {
                                JSONObject row = JSON.parseObject(elem);
                                Tuple8<String, Double, Double, Double, Double, Double, Double, Double> tuple8 = new Tuple8<>();
                                tuple8.setField(row.getString("userId"), 0);
                                for (int idx = 1; idx <= gapColumns.size(); idx++) {
                                    tuple8.setField(row.getDoubleValue(gapColumns.get(idx - 1)), idx);
                                }
                                return tuple8;
                            }).returns(new TypeHint<Tuple8<String, Double, Double, Double, Double, Double, Double, Double>>() {
                                @Override
                                public TypeInformation<Tuple8<String, Double, Double, Double, Double, Double, Double, Double>> getTypeInfo() {
                                    return super.getTypeInfo();
                                }
                            }).name("read eventGap");


            DataSet<Row> ds =
                    eventCountDs.leftOuterJoin(eventGapDs).where(0).equalTo(0).with(new JoinFunction<Tuple2<String,
                            String>, Tuple8<String, Double, Double, Double, Double, Double, Double, Double>, Row>() {

                        @Override
                        public Row join(Tuple2<String, String> first, Tuple8<String, Double, Double, Double, Double, Double, Double, Double> second) throws Exception {
                            Row row = Row.withNames();
                            row.setField("userId", first.f0);
                            String[] countList = first.f1.substring(1, first.f1.length() - 1).split(",");
                            for (String tempEventCount : countList) {
                                String data[] = tempEventCount.substring(1, tempEventCount.length() - 1).split("####");
                                String name = data[0].replaceAll("-", "_");
                                String value = data[1];
                                row.setField(name, Integer.valueOf(value));
                            }

                            if (second == null) {
                                return row;
                            }
                            for (int i = 1; i <= gapColumns.size() - 1; i++) {
                                row.setField(gapColumns.get(i - 1), second.getField(i));
                            }
                            return row;
                        }
                    }).name("ds join");


            //events,gap,userId
            List<String> columnNames = new ArrayList<>();
            List<TypeInformation> columnTypes = new ArrayList<>();
            columnNames.add("userId");
            columnTypes.add(TypeInformation.of(String.class));
            for (String e : allEvents) {
                columnNames.add(e);
                columnTypes.add(TypeInformation.of(Integer.class));
            }
            for (String col : gapColumns) {
                columnNames.add(col);
                columnTypes.add(TypeInformation.of(Double.class));
            }

            String[] COL_NAMES = columnNames.toArray(new String[0]);
            String[] selectedColNames = Arrays.copyOfRange(COL_NAMES, 1, COL_NAMES.length);

            DataSetWrapperBatchOp source = new DataSetWrapperBatchOp(ds, COL_NAMES,
                    columnTypes.toArray(new TypeInformation[0]));


            Imputer imputer = new Imputer()
                    .setStrategy("value")
                    .setFillValue("0")
                    .setSelectedCols(selectedColNames);
            MinMaxScaler minMaxScaler = new MinMaxScaler()
                    .setSelectedCols(selectedColNames);

            int pcaK = columnNames.size() < 100 ? columnNames.size() - 10 : 100;
            PCA pca = new PCA().setK(pcaK)
                    .setSelectedCols(selectedColNames)
                    .setPredictionCol("pred");

            Pipeline pipeline = new Pipeline().add(imputer).add(minMaxScaler).add(pca);
            DataSet<Row> featureDs = pipeline.fit(source).transform(source).select("userId,pred").getDataSet();


            DataSet<Tuple3<String, String, Float>> resultDs = mlEnv.getExecutionEnvironment().fromElements("startHnsw")
                    .flatMap(new HnswRichFlatMap(distanceThreshold))
                    .returns(new TypeHint<Tuple3<String, String, Float>>() {
                        @Override
                        public TypeInformation<Tuple3<String, String, Float>> getTypeInfo() {
                            return super.getTypeInfo();
                        }
                    }).withBroadcastSet(featureDs,"features");

            CKOutputFormat graphOutput = new CKOutputFormat(ckProperties, "ann_graph");
            resultDs.map(elem -> {
                Map<String, Object> row = new HashMap<>();
                row.put("id", elem.f0);
                row.put("toId", elem.f1);
                row.put("weight", elem.f2);
                row.put("runDay", runDay);
                return JSON.toJSONString(row);
            }).output(graphOutput).name("writeAnnGraph");


            CKOutputFormat ccOutput = new CKOutputFormat(ckProperties, "ann_graph_community");

            //图社区分割
            DataSet<Edge<String, Float>> edgeDs = resultDs.map(elem -> {

                Edge<String, Float> edge = new Edge<>(elem.f0, elem.f1, elem.f2);
                return edge;

            }).returns(new TypeHint<Edge<String, Float>>() {
                @Override
                public TypeInformation<Edge<String, Float>> getTypeInfo() {
                    return super.getTypeInfo();
                }
            });
            Graph<String, String, Float> inputGraph = Graph.fromDataSet(edgeDs, new ConnectedInitLabel(), mlEnv.getExecutionEnvironment());

            //return vertex set , node belong to same label (the label is vertex value)
            DataSet<Vertex<String, String>> ccResultDs = inputGraph.run(new ConnectedComponentWithParam<>(15, 4));

            //计算各图中各定点的度
            DataSet<Vertex<String, LongValue>> degree = inputGraph.run(new UndirectedVertexDegree<String, String, Float>().setParallelism(4));

            ccResultDs.join(degree).where(0).equalTo(0).map(new MapFunction<Tuple2<Vertex<String, String>,
                    Vertex<String, LongValue>>, String>() {
                @Override
                public String map(Tuple2<Vertex<String, String>, Vertex<String, LongValue>> value) throws Exception {
                    Map<String, Object> resultRow = new HashMap<>();
                    resultRow.put("id", value.f0.getId());
                    resultRow.put("clusterId", value.f0.getValue());
                    resultRow.put("degree", value.f1.getValue().getValue());
                    resultRow.put("runDay", runDay);
                    return JSON.toJSONString(resultRow);
                }
            }).output(ccOutput);


        }


        @Getter
        @Setter
        public static class RowItem implements Item<String, float[]> {

            private String id;
            private final float[] vector;

            public RowItem(String id, float[] vec) {
                this.id = id;
                this.vector = vec;
            }

            @Override
            public String id() {
                return id;
            }

            @Override
            public float[] vector() {
                return vector;
            }

            @Override
            public int dimensions() {
                return vector.length;
            }

            public static RowItem fromPcaRow(Row pca){
                String id = pca.getField(0).toString();
                String strVec[] = pca.getField(1).toString().split(" ");
                float[] vec = new float[strVec.length];
                for (int temp = 0; temp < strVec.length; temp++) {
                    vec[temp] = Float.valueOf(strVec[temp]);
                }
                return new RowItem(id, vec);
            }
        }

        public static class HnswRichFlatMap extends RichFlatMapFunction<String, Tuple3<String, String, Float>>{

            private transient HnswIndex<String, float[], RowItem, Float> hnswIndex;
            private transient List<Row> rows ;
            private String distanceThreshold;
            public HnswRichFlatMap(String distanceThreshold) {
                this.distanceThreshold = distanceThreshold;
            }


            @Override
            public void flatMap(String value, Collector<Tuple3<String, String, Float>> out) throws Exception {
                for (int index = 0; index < rows.size(); index++) {
                    RowItem curRow = RowItem.fromPcaRow(rows.get(index));
                    List<SearchResult<RowItem, Float>> approximateResults =
                            hnswIndex.findNearest(curRow.getVector(), 20);
                    for (SearchResult<RowItem, Float> result : approximateResults) {
                        if (curRow.id().equals(result.item().id())) {
                            continue;
                        }
                        if (result.distance() >= Float.valueOf(distanceThreshold)) {
                            continue;
                        }
                        out.collect(Tuple3.of(curRow.id(), result.item().id(), result.distance()));

                    }
                }
            }

            @Override
            public void open(Configuration parameters) throws Exception {
                rows = getRuntimeContext().getBroadcastVariable("features");
                RowItem firstRow = RowItem.fromPcaRow(rows.get(0));
                log.info("Constructing index.");

                int maxItemCount = rows.size();
                int dimensions = firstRow.dimensions();
                hnswIndex = HnswIndex.newBuilder(dimensions, DistanceFunctions.FLOAT_EUCLIDEAN_DISTANCE, maxItemCount).withM(64).withEf(200).withEfConstruction(400).build();
                List<RowItem> allRows = new ArrayList<>();
                for (Row row : rows) {
                    allRows.add(RowItem.fromPcaRow(row));
                }
                hnswIndex.addAll(allRows,Runtime.getRuntime().availableProcessors(),
                        (workDone, max) -> log.info(String.format("Added %d out of %d words to the index.%n",
                        workDone, max)),10000);
                log.info("end constructing index");

            }
        }

        public static class HnswFlatMap extends RichFlatMapFunction<String, Tuple3<String, String, Float>> {

            public HnswFlatMap(List<Row> features, String distanceThreshold) {
                this.rows = features;
                this.distanceThreshold = distanceThreshold;
            }

            private transient HnswIndex<String, float[], RowItem, Float> hnswIndex = null;
            private List<Row> rows;
            private String distanceThreshold;

            private transient List<RowItem> items;

            @Override
            public void flatMap(String value, Collector<Tuple3<String, String, Float>> out) throws Exception {
                for (int index = 0; index < items.size(); index++) {
                    List<SearchResult<RowItem, Float>> approximateResults = hnswIndex.findNearest(items.get(index).getVector(), 20);
                    for (SearchResult<RowItem, Float> result : approximateResults) {
                        if (items.get(index).id().equals(result.item().id())) {
                            continue;
                        }
                        if (result.distance() >= Float.valueOf(distanceThreshold)) {
                            continue;
                        }
                        out.collect(Tuple3.of(items.get(index).id(), result.item().id(), result.distance()));

                    }
                }

            }

            @Override
            public void open(Configuration parameters) throws Exception {
                System.out.println("Constructing index.");
                items = new ArrayList<>();
                for (Row row : rows) {
                    String id = row.getField(0).toString();
                    String strVec[] = row.getField(1).toString().split(" ");
                    float[] vec = new float[strVec.length];
                    for (int temp = 0; temp < strVec.length; temp++) {
                        vec[temp] = Float.valueOf(strVec[temp]);
                    }
                    items.add(new RowItem(id, vec));
                }


                int maxItemCount = items.size();
                int dimensions = items.get(0).dimensions();
                hnswIndex = HnswIndex.newBuilder(dimensions, DistanceFunctions.FLOAT_EUCLIDEAN_DISTANCE, maxItemCount).withM(64).withEf(200).withEfConstruction(400).build();
                hnswIndex.addAll(items, (workDone, max) -> System.out.printf("Added %d out of %d rows to " + "the index.%n", workDone, max));

            }
        }

        public static class ConnectedInitLabel implements MapFunction<String, String> {

            @Override
            public String map(String value) throws Exception {
//            return value + "_label";
                return value;
            }
        }

    }


    public static String eventCountSql = "\n" + "select userId,groupArray(concat(eventCode,'####',toString(eCount))) eventList from (\n" + " select userId,eventCode,count(1) eCount " +
            " from risk_hit_log where createdAt between '%s 00:00:00' and " + "'%s 23:59:59' and data_riskBaseTraceId='' \n" + "   and " +
            "deviceId!='' and clientIp!='' and clientIp not in ('0.0.0.0','127.0.0.1')\n" + "   and userId!=''\n" + "   and userId global not in (\n" + "                  select valueData from risk_gray_list_sync where syncDate =today()\n" + "               ) \n" + "   and clientIp global not in (\n" + "                  select valueData from risk_gray_list_sync where syncDate =today()\n" + "            ) \n" + "   and deviceId global not in (\n" + "                  select valueData from risk_gray_list_sync where syncDate =today()\n" + "   ) \n" + "   group by userId,eventCode\n" + ") group by userId";

    public static String eventGapSql = "select userId,\n" + "       stddevPop(reqGap)   stdGap,\n" + "       min(reqGap) minGap,\n" + "       max(reqGap) maxGap,\n" + "       avg(reqGap) avgGap,\n" + "       median(reqGap) medianGap,\n" + "       arrayJoin(groupArray(1)(activeMinCount))  minuteCount,\n" + "       arrayJoin(groupArray(1)(activeHourCount)) hourCount\n" + "from (\n" + "         select userId,\n" + "                arrayJoin(arraySlice(arraySort(arrayDifference(groupArray(reqTime))), 2))\n" + "                    reqGap,\n" + "                count(distinct substring(toString(toDateTime(reqTime)), 1, 16)) activeMinCount,\n" + "                count(distinct substring(toString(toDateTime(reqTime)), 1, 13)) activeHourCount\n" + "         from (\n" + "               select userId,\n" + "                      toInt64OrZero(substring(data_riskRequestTime, 1, length(data_riskRequestTime) - 3))\n" + "                          as reqTime\n" + "               " +
            " from risk_hit_log a\n" + "               where createdAt between '%s 00:00:00' and '%s 23:59:59' and data_riskBaseTraceId=''\n" +
            "               and deviceId!='' and clientIp!='' and clientIp not in ('0.0.0.0','127.0.0.1')\n" + "               and userId!=''\n" + "               and userId global not in (\n" + "                  select valueData from risk_gray_list_sync where syncDate =today()\n" + "               ) \n" + "               and clientIp global not in (\n" + "                  select valueData from risk_gray_list_sync where syncDate =today()\n" + "               ) \n" + "               and deviceId global not in (\n" + "                  select valueData from risk_gray_list_sync where syncDate =today()\n" + "               ) \n" + "               order by reqTime\n" + "                  )\n" + "         group by userId\n" + "\n" + "         ) bb\n" + "where reqGap < 60 * 5\n" + "group by userId\n" + ";";


    public static String allEventsSql = "select distinct eventCode from " + "risk_hit_log where createdAt between " + "'%s 00:00:00' and '%s 23:59:59' and userId!='' and deviceId!='' and clientIp!='' and clientIp " + "not in ('0.0.0.0','127.0.1')";
}
