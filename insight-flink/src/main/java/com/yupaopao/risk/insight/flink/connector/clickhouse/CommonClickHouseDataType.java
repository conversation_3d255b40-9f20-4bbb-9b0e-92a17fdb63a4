package com.yupaopao.risk.insight.flink.connector.clickhouse;


import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Date;
import java.util.List;

public enum CommonClickHouseDataType {
    /**
     * click house 类型映射
     */
    UInt64(BigInteger.class, "Int64"),
    Int64(Long.class, "Int64"),
    Int32(Integer.class, "Int64"),
    Date(Date.class, "Date"),
    Float32(Float.class, "Float64"),
    Float64(Double.class, "Float64"),
    Decimal128(BigDecimal.class, "Float64"),
    String(String.class, "String"),
    List(List.class, "String"),
    Boolean(Boolean.class, "UInt8"),
    ;
    private final Class<?> javaClass;
    private String clickHouseType;

    CommonClickHouseDataType(Class<?> javaClass, String clickHouseType) {
        this.javaClass = javaClass;
        this.clickHouseType = clickHouseType;
    }

    public static String getClickHouseType(Object object) {
        if (object instanceof List) {
            return "String";
        }
        for (CommonClickHouseDataType dataType : values()) {
            if (dataType.javaClass.equals(object.getClass())) {
                return dataType.clickHouseType;
            }
        }
        return "";
    }
}
