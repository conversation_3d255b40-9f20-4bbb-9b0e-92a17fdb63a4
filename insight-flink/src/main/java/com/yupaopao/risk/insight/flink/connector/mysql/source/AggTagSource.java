package com.yupaopao.risk.insight.flink.connector.mysql.source;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.bean.portrait.AggTag;
import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.source.RichSourceFunction;

import java.io.Serializable;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class AggTagSource extends RichSourceFunction<List<AggTag>> implements Serializable {

    private Connection connection = null;
    private PreparedStatement ps = null;
    private DBProperties dbProperties;

    public AggTagSource(DBProperties dbProperties) {
        this.dbProperties = dbProperties;
    }

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        connection = getConnection();
        ps = connection.prepareStatement("select info.id,info.code,info.name,detail.data_content,detail.value_type"
            + " from t_tag_info as info, t_tag_detail as detail"
            + " where info.source = 1 and info.id = detail.tag_id");
    }

    @Override public void run(SourceContext<List<AggTag>> ctx) throws Exception {
        while (true) {
            ResultSet resultSet = ps.executeQuery();
            List<AggTag> aggTags = new ArrayList<>();
            while (resultSet.next()){
                AggTag aggTag = new AggTag();
                aggTag.setId(resultSet.getInt("id"));
                aggTag.setCode(resultSet.getString("code"));
                aggTag.setName(resultSet.getString("name"));
                aggTag.setValueType(resultSet.getString("value_type"));
                JSONObject dataContent = JSONObject.parseObject(resultSet.getString("data_content"));
                aggTag.setGroupKey(dataContent.getString("groupKey"));
                aggTag.setAggKey(dataContent.getString("aggKey"));
                aggTag.setFunction(dataContent.getString("function"));
                aggTag.setTimeSpan(dataContent.getLong("timeSpan"));
                aggTag.setCondition(dataContent.getString("condition"));
                aggTags.add(aggTag);
            }
            ctx.collect(aggTags);

            Thread.sleep(30 * 1000);
        }
    }

    @Override public void cancel() {
        try {
            ps.cancel();
            connection.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public Connection getConnection() {
        try {
            Class.forName(dbProperties.getDriver());
            connection = DriverManager.getConnection(dbProperties.getUrl(), dbProperties.getUsename(), dbProperties.getPassword());
        } catch (Exception e) {
            log.info("connect to mysql error : ", e);
        }
        return connection;
    }
}
