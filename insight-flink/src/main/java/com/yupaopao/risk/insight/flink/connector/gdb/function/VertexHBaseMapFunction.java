package com.yupaopao.risk.insight.flink.connector.gdb.function;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yupaopao.risk.insight.common.beans.graph.VertexInfo;
import org.apache.flink.api.common.functions.MapFunction;

import java.util.HashMap;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-08 18:37
 *
 ***/
public class VertexHBaseMapFunction implements MapFunction<VertexInfo,String> {

    @Override
    public String map(VertexInfo elem) throws Exception {
        Map<String,Object> vertex = new HashMap();
        vertex.put("id",elem.getId());
        vertex.put("label",elem.getLabel());
        Map<String,Object> vertexProps = elem.getProperties();
        if(vertexProps!=null && !vertexProps.isEmpty()){
            vertex.put("p",vertexProps);
        }
        return JSON.toJSONString(vertex, SerializerFeature.DisableCircularReferenceDetect);
    }
}
