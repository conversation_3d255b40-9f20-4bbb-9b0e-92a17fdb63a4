package com.yupaopao.risk.insight.flink.connector.gdb.function;

import com.yupaopao.risk.insight.common.beans.graph.EdgeInfo;
import com.yupaopao.risk.insight.common.beans.graph.VertexInfo;
import com.yupaopao.risk.insight.flink.bean.graph.EventGraphElement;
import com.yupaopao.risk.insight.flink.bean.graph.UserGraphModel;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.java.tuple.Tuple2;

import java.util.*;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-01 19:11
 *
 ***/
public class EventGraphAggregationFunction implements AggregateFunction<UserGraphModel, Tuple2<List<VertexInfo>,
        List<EdgeInfo>>, EventGraphElement> {

    @Override
    public Tuple2<List<VertexInfo>, List<EdgeInfo>> createAccumulator() {
        return new Tuple2<>(new ArrayList<>(), new ArrayList<>());
    }

    @Override
    public Tuple2<List<VertexInfo>, List<EdgeInfo>> add(UserGraphModel value, Tuple2<List<VertexInfo>, List<EdgeInfo>> accumulator) {

        List<VertexInfo> existVertexList = accumulator.getField(0);
        List<EdgeInfo> existEdgeList = accumulator.getField(1);

        List<VertexInfo> newVertexList = value.getAllVertexInfo();
        List<EdgeInfo> newEdgeList = value.getAllEdgeInfo();
        //节点存在则不需要添加
        for (VertexInfo newVertex : newVertexList) {
            Optional<VertexInfo> existVertex =
                    existVertexList.stream().filter(elem -> elem.equals(newVertex)).findFirst();

            //存在更新节点
            if (existVertex.isPresent()) {
                if (newVertex.getProperties() != null && !newVertex.getProperties().isEmpty()) {
                    if(existVertex.get().getProperties()!=null && !existVertex.get().getProperties().isEmpty()){
                        existVertex.get().getProperties().putAll(newVertex.getProperties());
                    }
                }

            } else {
                //不存在
                existVertexList.add(newVertex);
            }
        }
        for (EdgeInfo newEdge : newEdgeList) {
            Optional<EdgeInfo> existEdge =
                    existEdgeList.stream().filter(elem -> elem.equals(newEdge)).findFirst();


            if (existEdge.isPresent()) {
                //存在更新property
                if (newEdge.getProperties() != null && !newEdge.getProperties().isEmpty()) {
                    existEdge.get().setProperties(newEdge.getProperties());
                }
            } else {

                //不存在
                existEdgeList.add(newEdge);
            }
        }
        return new Tuple2<>(existVertexList, existEdgeList);
    }

    @Override
    public EventGraphElement getResult(Tuple2<List<VertexInfo>, List<EdgeInfo>> accumulator) {
        return new EventGraphElement(accumulator.f0, accumulator.f1);
    }

    @Override
    public Tuple2<List<VertexInfo>, List<EdgeInfo>> merge(Tuple2<List<VertexInfo>, List<EdgeInfo>> a, Tuple2<List<VertexInfo>, List<EdgeInfo>> b) {
        //合并
        List<VertexInfo> mergedVertexList = new ArrayList<>();
        List<EdgeInfo> mergedEdgeList = new ArrayList<>();
        if (a.f0 != null) {
            mergedVertexList.addAll(a.f0);
        }
        if (a.f1 != null) {
            mergedEdgeList.addAll(a.f1);
        }
        if (b.f0 != null) {
            mergedVertexList.addAll(b.f0);
        }
        if (b.f1 != null) {
            mergedEdgeList.addAll(b.f1);
        }
        return new Tuple2<>(mergedVertexList, mergedEdgeList);
    }
}
