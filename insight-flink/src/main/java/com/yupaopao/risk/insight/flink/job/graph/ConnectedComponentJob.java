package com.yupaopao.risk.insight.flink.job.graph;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.cep.beans.RiskEvent;
import com.yupaopao.risk.insight.flink.job.base.BatchJobBuilder;
import com.yupaopao.risk.insight.flink.job.processor.graph.ConnectedComponentProcessor;
import org.apache.flink.cep.pattern.GroupPattern;
import org.apache.flink.cep.pattern.Pattern;
import org.apache.flink.cep.pattern.conditions.SimpleCondition;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-17 17:58
 * 连通图job
 ***/
public class ConnectedComponentJob {

    public static void main(String[] args) throws Exception {
        new BatchJobBuilder()
                .withJobName("connected-detection")
                .withDefaultExecute(true)
                .withProcessor(new ConnectedComponentProcessor())
                .start(args);

    }

}
