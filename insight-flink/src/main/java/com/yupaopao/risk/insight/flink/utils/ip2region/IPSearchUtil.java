package com.yupaopao.risk.insight.flink.utils.ip2region;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2018-08-20
 */
public class IPSearchUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(IPSearchUtil.class);
    private static DbSearcher dbSearcher;

    static {
        String fileName = "/flink/ip/ip2region.db";
        try {
//            String objectName = "risk-flink/ip/ip2region.db";
            dbSearcher = new DbSearcher(fileName, IPFilePathEnum.TEMP_FILE);
            IPDetail IPDetail = dbSearcher.memorySearch("*************");
            LOGGER.info("IP信息库初始化完成,测试数据:{}", IPDetail);
            dbSearcher.closeRaf();
            Runtime.getRuntime().addShutdownHook(new Thread(() -> dbSearcher.clearDbBinStr()));
        } catch (Exception e) {
            LOGGER.error("ip2region.db失败读取: " + e.getMessage(), e);
            throw new RuntimeException("ip2region.db读取失败", e);
        }
    }

    public static IPDetail getIpInfo(String ip) {
        try {
            return (StringUtils.isNotBlank(ip) && Util.isIpAddress(ip)) ? dbSearcher.memorySearch(ip) : null;
        } catch (Exception e) {
            LOGGER.error("解析IP信息失败: ip=" + ip, e);
        }
        return null;
    }

    public static void main(String[] args) {
        System.err.println(getIpInfo("*************"));
    }

}
