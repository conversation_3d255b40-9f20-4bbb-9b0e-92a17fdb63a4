package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;

import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-01 11:11
 *
 ***/
public class CepOutputFormat extends CommonHBaseOutputFormat {

    public CepOutputFormat(String tableName, HBaseProperties hBaseProperties) {
        super(tableName, hBaseProperties);
    }

    @Override
    public String getRowKey(Map<String, Object> flattenMap) {
        /***
         * rowKey: patternId+subPatternName+traceId+timestamp
         */
        return (String) flattenMap.get("hbaseRowKey");
    }
}
