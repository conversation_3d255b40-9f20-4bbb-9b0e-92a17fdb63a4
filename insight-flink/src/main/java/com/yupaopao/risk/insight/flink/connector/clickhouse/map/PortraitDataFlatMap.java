package com.yupaopao.risk.insight.flink.connector.clickhouse.map;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import org.apache.commons.lang3.time.FastDateFormat;
import org.apache.flink.api.common.accumulators.LongCounter;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.HTable;
import org.joda.time.DateTime;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;

/**
 * Copyright (C), 2020, jimmy
 *
 * <AUTHOR>
 * @desc PortraitDataFlatMap 画像数据导入
 * @date 2020/4/15
 */
public class PortraitDataFlatMap extends RichFlatMapFunction<String, String> implements Serializable {
    private final static FastDateFormat FAST_DATE_FORMAT = FastDateFormat.getInstance("yyyy-MM-dd");

    private LongCounter processCount = new LongCounter();

    private HTable hTable;

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        HBaseProperties hbProperties = HBaseProperties.getProperties(PropertyType.HBASE);
        Connection connection = HBaseUtil.createConnection(hbProperties);
        hTable = HBaseUtil.getTable("risk_portrait_result", connection);
        getRuntimeContext().addAccumulator("processCount", this.processCount);
    }

    @Override public void flatMap(String value, Collector<String> out) throws Exception {
        JSONObject portrait = JSONObject.parseObject(value).getJSONObject("portrait");
        portrait.put("createdAt", FAST_DATE_FORMAT.format(new DateTime().getMillis()));
        portrait.put("uid", portrait.getString("uid"));
        portrait.put("mobile", portrait.getString("mobile"));

        JSONArray riskTags = getRiskTag(portrait.getString("uid"));
        if (null == riskTags || riskTags.size() == 0) {
            riskTags = portrait.getJSONArray("riskTags");
        }

        if (null != riskTags && riskTags.size() > 0) {
            String[] codeArray = new String[riskTags.size()];
            String[] nameArray = new String[riskTags.size()];
            String[] createAtArray = new String[riskTags.size()];
            String[] updateArray = new String[riskTags.size()];
            Integer[] riskLevelArray = new Integer[riskTags.size()];
            Integer[] riskTotalArray = new Integer[riskTags.size()];
            Integer[] freeDaysArray = new Integer[riskTags.size()];
            String[] subtagsArray = new String[riskTags.size()];
            Double[] scoreArray = new Double[riskTags.size()];

            for (int i = 0; i < riskTags.size(); i++) {
                JSONObject riskTag = riskTags.getJSONObject(i);
                codeArray[i] = riskTag.getString("code");
                nameArray[i] = riskTag.getString("name");
                createAtArray[i] = riskTag.getString("createAt");
                updateArray[i] = riskTag.getString("updateTime");
                riskLevelArray[i] = riskTag.getInteger("riskLevel");
                riskTotalArray[i] = riskTag.getInteger("riskTotal");
                freeDaysArray[i] = riskTag.getInteger("freeDays");
                subtagsArray[i] = riskTag.getJSONArray("subtag").toJSONString();
                scoreArray[i] = riskTag.getDouble("score");
            }
            portrait.put("riskTags.code", codeArray);
            portrait.put("riskTags.name", nameArray);
            portrait.put("riskTags.createAt", createAtArray);
            portrait.put("riskTags.updateTime", updateArray);
            portrait.put("riskTags.riskLevel", riskLevelArray);
            portrait.put("riskTags.freeDays", freeDaysArray);
            portrait.put("riskTags.riskTotal", riskTotalArray);
            portrait.put("riskTags.subtag", subtagsArray);
            portrait.put("riskTags.score", scoreArray);
        }
        portrait.remove("riskTags");
        processCount.add(1);
        out.collect(portrait.toJSONString());
    }

    private JSONArray getRiskTag(String uid) throws Exception {
        try {
            Map<String, Object> riskTags = HBaseUtil.getRow(uid, hTable, Collections.singletonList("riskTag"));
            if (!riskTags.isEmpty()) {
                return JSONObject.parseArray(riskTags.get("riskTag").toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
