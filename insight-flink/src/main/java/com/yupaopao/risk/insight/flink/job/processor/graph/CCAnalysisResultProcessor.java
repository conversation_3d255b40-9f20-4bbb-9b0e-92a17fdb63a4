package com.yupaopao.risk.insight.flink.job.processor.graph;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.graph.CCAnalysisParams;
import com.yupaopao.risk.insight.flink.connector.clickhouse.ClickHouseUtil;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CKOutputFormat;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.CkOutputFormatSink;
import com.yupaopao.risk.insight.flink.connector.clickhouse.source.CKInputFormat;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import com.yupaopao.risk.insight.flink.process.graph.ConnectedComponentDetailProcessFun;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.flink.utils.InnerFlinkJobInvoker;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.io.IOException;
import java.io.Serializable;
import java.net.URLEncoder;
import java.util.Map;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-19 16:47
 * 连通图结果统计分析
 *
 ***/

@Slf4j
public class CCAnalysisResultProcessor implements FlinkJobBuilder.MainProcessor, Serializable {


    @Override
    public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {

        CCAnalysisParams analysisParams = JSON.parseObject(JSON.toJSONString(argMap), CCAnalysisParams.class);

        String runDay;
        if (argMap != null && argMap.containsKey("runDay")) {
            runDay = argMap.get("runDay");
        } else {
            runDay = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd);
        }
        ClickHouseProperties ckProperties = ClickHouseUtil.getLongTimeoutProperties();

        String topConnectedComponentSql = "\n" +
                "select connectedLabel,count() ccCount from cc_detection_result where runDay = '%s' group by " +
                "connectedLabel\n" +
                "having ccCount> %d\n" +
                "order by ccCount desc ;";


        CKInputFormat inputFormat = new CKInputFormat(ckProperties,
                String.format(topConnectedComponentSql, runDay, analysisParams.getCcVertexCountLimit()), "{}") {

//            @Override
//            public void openInputFormat() throws IOException {
//                super.openInputFormat();
//                try (ClickHouseConnection ckConn = ClickHouseUtil.createConnection(ckProperties);) {
//                    new ConnectedComponentDetailService(analysisParams, runDay).updateTopEvents(ckConn);
//                } catch (Exception e) {
//                    log.error("update cc topEvents error: ", e);
//                }
//            }
        };

//        ClickHouseStreamSink ckSink = new ClickHouseStreamSink(ckProperties, "cc_detection_result_detail",
//                1 * 60 * 1000L, 20000);

        ckProperties.setBatchSize(10000);
        CKOutputFormat ckDetailOut = new CKOutputFormat(ckProperties, "cc_detection_result_detail") {
            //invoke merge detail
            @Override
            public void finalizeGlobal(int parallelism) throws IOException {
                log.info("start to invoke finalizeGlobal in CCAnalysisResultProcessor ... ");
            }
        };

        CkOutputFormatSink ckDetailOutSink = new CkOutputFormatSink(ckDetailOut) {

            @Override
            public void close() throws Exception {
                super.close();
                log.info("ckDetailOutSink invoke community merge");
                String jarName = "community-merge.jar";
                argMap.put("runDay", runDay);
                InnerFlinkJobInvoker.invokeJobFromTaskManager(jarName, URLEncoder.encode(JSON.toJSONString(argMap), "UTF-8"));
            }
        };


        env.createInput(inputFormat).setParallelism(1)
                .filter(elem -> !HBaseUtil.emptyResultJson(elem)).setParallelism(1)
                .process(new ConnectedComponentDetailProcessFun(analysisParams, runDay)).setParallelism(3).name(
                        "process_per_cc")
                .addSink(ckDetailOutSink).setParallelism(1).name("write_cc_detection_result_detail_to_ck");


    }

}
