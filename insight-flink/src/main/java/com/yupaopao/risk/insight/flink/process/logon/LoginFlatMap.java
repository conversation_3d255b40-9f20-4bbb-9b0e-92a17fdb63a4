package com.yupaopao.risk.insight.flink.process.logon;

import com.alibaba.fastjson.JSONObject;
import com.yupaopao.risk.insight.flink.connector.redis.sink.client.RedisClient;
import com.yupaopao.risk.insight.flink.constants.FlinkConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.util.Collector;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

@Slf4j
public class LoginFlatMap extends RichFlatMapFunction<Tuple2<String, String>, Tuple2<String, String>> {
    private static final long serialVersionUID = -7851661820135204504L;
    private JedisPool jedisPool;

    private Jedis jedis;
    private String frontEventCode;

    public LoginFlatMap(String frontEventCode) {
        this.frontEventCode = frontEventCode;
    }

    @Override public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        jedisPool = RedisClient.getClient();
        this.jedis = jedisPool.getResource();
    }

    @Override public void flatMap(Tuple2<String, String> value, Collector<Tuple2<String, String>> out) throws Exception {
        String cacheValue = jedis.get(FlinkConstants.FLINK_RISK_CK_REDIS_PREFIX + value.f0 + "before");
        cacheValue = StringUtils.isNotBlank(cacheValue) ? cacheValue : getFrontData(value.f1);
        if (StringUtils.isNotBlank(cacheValue)) {
            value.setField(cacheValue, 1);
            out.collect(value);
        } else {
            jedis.setex(FlinkConstants.FLINK_RISK_CK_REDIS_PREFIX + value.f0 + "post", 60, "post");
            log.info("未找到前置数据，尝试恢复数据顺序: {}", value.f0);
        }
    }

    @Override public void close() throws Exception {
        super.close();
        RedisClient.closeJedis(jedis);
        RedisClient.closePool(jedisPool);
    }

    private String getFrontData(String data) {
        String eventCode = JSONObject.parseObject(data).getString("eventCode");
        if (frontEventCode.equals(eventCode)) {
            return data;
        }
        return "";
    }
}
