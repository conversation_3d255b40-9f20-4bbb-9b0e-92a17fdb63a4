package com.yupaopao.risk.insight.flink.connector.ts.businessmodel;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/10/23 12:09 PM
 */
public class HitResultLog implements Serializable {

    private RiskAction riskAction;
    private RiskResult riskResult;
    private Long costTime;

    public RiskAction getRiskAction() {
        return riskAction;
    }

    public void setRiskAction(RiskAction riskAction) {
        this.riskAction = riskAction;
    }

    public RiskResult getRiskResult() {
        return riskResult;
    }

    public void setRiskResult(RiskResult riskResult) {
        this.riskResult = riskResult;
    }

    public Long getCostTime() {
        return costTime;
    }

    public void setCostTime(Long costTime) {
        this.costTime = costTime;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SIMPLE_STYLE);
    }
}
