package com.yupaopao.risk.insight.flink.windows.aggregate;

import com.dianping.cat.Cat;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.portrait.OpenAiOutPutVO;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitBean;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitFirstTag;
import com.yupaopao.risk.insight.flink.job.portrait.PortraitApolloProperties;
import com.yupaopao.risk.insight.flink.job.portrait.process.PortraitTagMergeSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.common.functions.AggregateFunction;
import org.apache.flink.api.java.tuple.Tuple2;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
@Slf4j
public class PortraitTagAggregateFunction implements AggregateFunction<Tuple2<Long, PortraitBean>, Tuple2<Long, PortraitBean>, Tuple2<Long, PortraitBean>> {

    private static final String COMPLAIN_TAB = "No_Attribute";

    @Override public Tuple2<Long, PortraitBean> createAccumulator() {
        Tuple2<Long, PortraitBean> result = new Tuple2<>();
        return result;
    }

    @Override public Tuple2<Long, PortraitBean> add(Tuple2<Long, PortraitBean> value, Tuple2<Long, PortraitBean> accumulator) {
        Map<String, String> nameCode = PortraitApolloProperties.getNameCode();
        //TODO 算法过来的标签做分数更新 其他标签合并
        if (null == accumulator.f0){
            accumulator.f0 = value.f0;
            accumulator.f1 = value.f1;
        }else {
            PortraitBean portraitBean = mergeAttribute(value.f1, accumulator.f1);

            String dateStr = InsightDateUtils.getDateStr(System.currentTimeMillis(), InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
            if (Objects.isNull(portraitBean.getITokenIdFirstActiveTimestamp())){
                portraitBean.setITokenIdFirstActiveTimestamp(dateStr);
            }
            if (Objects.isNull(portraitBean.getIsmIdFirstActiveTimestamp())){
                portraitBean.setIsmIdFirstActiveTimestamp(dateStr);
            }

            TreeSet<PortraitFirstTag> newRiskTags = value.f1.getRiskTags();
            TreeSet<PortraitFirstTag> portraitFirstTags = null;
            try {
                if (value.f1 instanceof OpenAiOutPutVO) {
                    //合并算法平台分数
                    OpenAiOutPutVO openAiOutPutVO = (OpenAiOutPutVO) value.f1;
                    PortraitTagMergeSupport.mergeTagScore(openAiOutPutVO, accumulator.f1);
                }else {
                    portraitFirstTags = PortraitTagMergeSupport.mergeTag(accumulator.f1.getRiskTags(), newRiskTags);
                }
           } catch (Exception e) {
                log.warn("合并标签错误:",e);
            }
            if (Objects.nonNull(portraitFirstTags)){
                portraitBean.setRiskTags(portraitFirstTags);
                accumulator.f1=portraitBean;
            }

        }

        Cat.logMetricForCount("portrait.tag.count");
        return accumulator;
    }
    private  boolean containsCode(TreeSet<PortraitFirstTag> bRiskTags, String code){
        for (PortraitFirstTag bRiskTag : bRiskTags) {
            if (bRiskTag.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    private Optional<PortraitFirstTag> getTag(TreeSet<PortraitFirstTag> bRiskTags, String code){
        for (PortraitFirstTag bRiskTag : bRiskTags) {
            if (bRiskTag.getCode().equals(code)) {
                return Optional.of(bRiskTag);
            }
        }
        return Optional.empty();
    }



    @Override public Tuple2<Long, PortraitBean> getResult(Tuple2<Long, PortraitBean> accumulator) {
        return accumulator;
    }

    @Override public Tuple2<Long, PortraitBean> merge(Tuple2<Long, PortraitBean> portraitA, Tuple2<Long, PortraitBean> portraitB) {
        Tuple2<Long, PortraitBean> mergeResult = new Tuple2<>();
        if (portraitA.f0.equals(portraitB.f0)) {
            //合并属性
            PortraitBean portraitBean = mergeAttribute(portraitA.f1, portraitB.f1);
            if (portraitA.f1.getRiskTags().isEmpty()){
                TreeSet<PortraitFirstTag> riskTags = portraitB.f1.getRiskTags();
                portraitBean.setRiskTags(riskTags);
                portraitB.f1 = portraitBean;
                return portraitB;
            }
            if (portraitB.f1.getRiskTags().isEmpty()){
                TreeSet<PortraitFirstTag> riskTags = portraitA.f1.getRiskTags();
                portraitBean.setRiskTags(riskTags);
                portraitA.f1 = portraitBean;
                return portraitA;
            }
            mergeResult.f0 = portraitA.f0;
            try {
                portraitBean.setRiskTags(PortraitTagMergeSupport.mergeTag(portraitA, portraitB));
            } catch (Exception e) {
                e.printStackTrace();
            }
            mergeResult.f1 = portraitBean;
        }
        return mergeResult;
    }

    private PortraitBean mergeAttribute(PortraitBean portraitA, PortraitBean portraitB) {

        // 合并属性 那条消息是新的，取哪条数据的属性，忽略举报惩罚数据, complain 标记 数据
        if (COMPLAIN_TAB.equals(portraitA.getTab()) && COMPLAIN_TAB.equals(portraitB.getTab())){
            return initAttribute(portraitA, portraitB);
        }
        if (COMPLAIN_TAB.equals(portraitA.getTab())){
            return portraitB;
        }
        if (COMPLAIN_TAB.equals(portraitB.getTab())){
            return portraitA;
        }
        if (portraitA.getTimestamp()>portraitB.getTimestamp()) {
            return portraitA;
        }
        return portraitB;
    }



    private PortraitBean initAttribute(PortraitBean oldPortrait, PortraitBean newPortrait) {

        PortraitBean portraitBean = new PortraitBean();
        portraitBean.setUid(oldPortrait.getUid());
        if (oldPortrait.getTimestamp()>newPortrait.getTimestamp()) {
            portraitBean.setCurrentCity(newPortrait.getCurrentCity());
            portraitBean.setCurrentIsmId(newPortrait.getCurrentIsmId());
        }
        portraitBean.setTab(oldPortrait.getTab());
        String dateStr = InsightDateUtils.getDateStr(System.currentTimeMillis(), InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
        portraitBean.setITokenIdFirstActiveTimestamp(dateStr);
        portraitBean.setIsmIdFirstActiveTimestamp(dateStr);
        portraitBean.setITokenIdActiveDays7d(1);
        portraitBean.setITokenIdActiveDays4w(1);
        portraitBean.setIsmIdActiveDays4w(1);

        portraitBean.setITokenIdLoginCnt7d(1);
        portraitBean.setIsmIdLoginCnt7d(1);
        portraitBean.setITokenIdLoginCnt1d(1);
        portraitBean.setIsmIdLoginCnt1d(1);

        portraitBean.setIsmIdRelateTokenIdCnt7d(1);
        portraitBean.setIsmIdRelateTokenIdCnt1d(1);
        portraitBean.setIsmIdRelateIpCityCnt1d(1);
        portraitBean.setIsmIdRelateIpCityCnt7d(1);
        portraitBean.setITokenIdRelateSmIdCnt1d(1);
        portraitBean.setITokenIdRelateSmIdCnt7d(1);

        portraitBean.setDeviceTotal(1);
        portraitBean.setRelateCityTotal(1);
        portraitBean.setTimestamp(newPortrait.getTimestamp());
        portraitBean.setIsmIdActiveDays7d(1);

        portraitBean.setITokenIdRelateIpCityCnt1d(1);
        portraitBean.setITokenIdRelateIpCityCnt7d(1);

        portraitBean.setIsmIdRegisterCnt1d(1);
        portraitBean.setIsmIdRegisterCnt7d(1);
        return portraitBean;
    }

}
