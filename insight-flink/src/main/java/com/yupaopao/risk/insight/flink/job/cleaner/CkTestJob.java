package com.yupaopao.risk.insight.flink.job.cleaner;

import com.alibaba.fastjson.JSON;
import com.yupaopao.risk.insight.common.beans.CkQueryResult;
import com.yupaopao.risk.insight.common.clickhouse.CKConnectionConfig;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.ExecutionEnvironment;

import java.util.Map;

/****
 * zengxiangcai
 * 2023/5/16 15:52
 ***/

@Slf4j
public class CkTestJob {


    public static void main(String[] args) throws Exception {
        ExecutionEnvironment env = ExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        ClickHouseProperties ckProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        CKConnectionConfig cfg = new CKConnectionConfig(ckProperties);
        CkQueryResult<Map<String, String>> res =  cfg.executeQuery("select userId,groupArray(10)(eventCode) from " +
                "risk_hit_log where createdAt>='2023-05-23 " +
                "00:00:00' and userId!='' group by userId limit 10");
        System.err.println(JSON.toJSONString(res));

//        String sql;
//        if(args!=null && args.length>0){
//            sql  = args[0];
//        }else {
//            sql = "select pid,createdAt,uid,planetLiveLevel from risk_punish_record_inside where createdAt >=addDays" +
//                    "(now(),-1) order by createdAt asc limit 2;";
//        }
//        env.fromElements(1).map(new RichMapFunction<Integer, Integer>() {
//            private transient ClickHouseConnectionConfig cfg;
//
//            @Override
//            public void open(Configuration parameters) throws Exception {
//                cfg = new ClickHouseConnectionConfig(ckProperties);
//            }
//
//            @Override
//            public void close() throws Exception {
//                cfg.close();
//            }
//
//            @Override
//            public Integer map(Integer value) throws Exception {
//                List<Map<String, String>> list = cfg.executeQuery("select eventCode,count(1) eCount from risk_hit_log" +
//                        " where" +
//                        " createdAt>=addDays(now()," +
//                        "-1) group by eventCode limit 3");
//                log.error(JSON.toJSONString(list));
//
//                list = cfg.executeQuery("desc zxc_test_create_table");
//
//                log.error(JSON.toJSONString(list));
//
//                Map<String,Object> row1 =new HashMap<>();
//                row1.put("userId","1111");
//                row1.put("amount",12);
//                Map<String,Object> row2 =new HashMap<>();
//                row2.put("userId","2222");
//
//
////                cfg.executeInsertWithJson("insert into zxc_test_create_table  FORMAT JSONEachRow \n"+JSON.toJSONString(row1)+"\n"+JSON.toJSONString(row2));
//
//                list = cfg.executeQuery(sql);
//                log.error(JSON.toJSONString(list));
//                log.error(JSON.toJSONString(cfg.executeQueryForObject(sql)));
//                return 1;
//            }
//        }).returns(Integer.class).output(new PrintingOutputFormat<>());
//        env.execute("test sql");

    }
}
