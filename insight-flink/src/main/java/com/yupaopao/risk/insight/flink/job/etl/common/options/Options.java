/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yupaopao.risk.insight.flink.job.etl.common.options;

import lombok.Getter;
import lombok.Setter;

/**
 * This class define commandline options for the Launcher program
 *
 * Company: www.dtstack.com
 * <AUTHOR>
 */
@Setter
@Getter
public class Options {
    @OptionRequired(description = "job type:sql or sync")
    private String jobType = "sync";

    @OptionRequired(required = true, description = "Job config")
    private String job;

    @OptionRequired(description = "Job name")
    private String jobName = "ETL job";

    @OptionRequired(description = "Flink configuration directory")
    private String flinkconf;

    @OptionRequired(description = "env properties")
    private String pluginRoot;

    @OptionRequired(description = "Task parallelism")
    private String parallelism = "1";

    @OptionRequired(description = "Task priority")
    private String priority = "1";

    @OptionRequired(description = "ext flinkLibJar")
    private String flinkLibJar;

    @OptionRequired(description = "env properties")
    private String confProp = "{}";

    @OptionRequired(description = "json modify")
    private String p = "";

    @OptionRequired(description = "savepoint path")
    private String savepoint;

    @OptionRequired(description = "sql ext jar,eg udf jar")
    private String addjar;
}
