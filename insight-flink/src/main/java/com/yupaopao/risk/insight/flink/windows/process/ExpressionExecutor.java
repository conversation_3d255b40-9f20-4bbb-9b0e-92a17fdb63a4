package com.yupaopao.risk.insight.flink.windows.process;

import com.alibaba.fastjson.JSON;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/****
 * zengxiangcai
 * 2022/12/9 10:51
 ***/
public class ExpressionExecutor {
    private static final Map<String, ThreadLocal<Expression>> EXPRESSION_MAP = new ConcurrentHashMap<>();

    private static final SpelExpressionParser parser = new SpelExpressionParser();

    /****
     *
     * @param condition
     * @return
     */
    public static Expression getExpression(String condition) {
        if (EXPRESSION_MAP != null && !EXPRESSION_MAP.containsKey(condition)) {
            synchronized (EXPRESSION_MAP) {
                if (EXPRESSION_MAP != null && !EXPRESSION_MAP.containsKey(condition)) {
                    EXPRESSION_MAP.put(condition, new ThreadLocal<>());
                }
            }
        }
        ThreadLocal<Expression> threadLocal = EXPRESSION_MAP.get(condition);
        if (null != threadLocal && null != threadLocal.get()) {
            return threadLocal.get();
        } else {
            Expression expression = parser.parseExpression(condition);
            threadLocal.set(expression);
            EXPRESSION_MAP.put(condition, threadLocal);
            return expression;
        }
    }


    public static Object execute(String condition, Map params) {
        Expression expression = getExpression(condition);
        //context不可共享
        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariables(params);
        Object result = expression.getValue(context);
        return result;
    }


    public static void main(String[] args) {

        String  condition = "(#Event=='QUICKLY_CONSUME_AFTER' || #Event=='SLOWLY_CONSUME_AFTER') && (#UserId?.length" +
                "() >= 16 || #uid?.length() >= 16) && ((#bizLineDetect!=null && #bizLineDetect.get('isYuer') && " +
                "(#businessCode == 'MESSAGE$MESSAGE_REWARD' || #businessCode == 'MOMENT$MOMENT_REWARD')) || #businessCode == 'LIVE$NOBLE_PRIVILEGE_DIRECT' || #businessCode == 'GIFT$MESSAGE_REWARD' || #businessCode == 'TREASURE_BOX$TREASURE_BOX_GIFT' || #businessCode == 'LIVE$BITE_HAND_SHARK' || #businessCode == 'LIVE$LIVE_JOIN_FANS_GROUP' || #businessCode == 'LIVE$LIVE_PLAYBACK' || #businessCode == 'LIVE$LIVE_REWARD' || #businessCode == 'LIVE$BUY_MIKE_POSITION' || #businessCode == 'LIVE$DRAW_GUESS' || #businessCode == 'LIVE$FACE_GINI' || #businessCode == 'LIVE$MUSIC_GAME' || #businessCode == 'LIVE$GRAB_RED_ENVELOPE' || #businessCode == 'ACTIVITY$ACTIVITY' || #businessCode == 'P_M_000003$P_T_200010' || #businessCode == 'P_M_000003$P_T_200011' || #businessCode == 'P_M_000003$P_T_200118' || #businessCode == 'P_M_000003$P_T_200119') && #currency != 'GIFT'\n" +
                "";
        String json = "{\"DIAMOND\":\"9900.00\",\"kafkaTopic\":\"payment_risk_rule_executor_after_response\"," +
                "\"DeviceId\":\"202112192147404cfe3f89bc12c45af09b413a34d61e230156728c220467d0\",\"T_DeviceId\":\"202112192147404cfe3f89bc12c45af09b413a34d61e230156728c220467d0\",\"relationTradeCurrency\":\"SYSTEM_ASSET\",\"ClientIp\":\"*************\",\"canRetFlag\":\"true\",\"cnyAmount\":\"99.00\",\"payRiskInterfaceType\":\"old\",\"Result\":{\"eventCode\":\"QUICKLY_CONSUME_AFTER\",\"traceId\":\"5694b9449aaf43c48e3833e3637bc8af\",\"businessCode\":\"riskPayment\",\"level\":\"PASS\",\"success\":true,\"ruleName\":\"命中白名单\",\"auditMap\":{\"comment\":\"业务重点使用账号—季祥勇\",\"type\":\"WHITE\",\"dimension\":\"USERID\"}},\"kafkaMsgTime\":1685360082064,\"GIFT\":\"0\",\"relationCnyAmount\":\"99.00\",\"businessCode\":\"ACTIVITY$ACTIVITY\",\"riskRequestTime\":\"1685360082058\",\"merchantId\":\"P_M_200007\",\"Async\":\"true\",\"currency\":\"DIAMOND\",\"tradeType\":\"P_T_200099\",\"Business\":\"riskPayment\",\"app\":\"YUER\",\"riskBaseTraceId\":\"918e3e5754724fc0a25ee7c09d0661c1\",\"BatchSize\":1,\"riskEventSwitch\":\"2\",\"relationUID\":\"1000000039\",\"allRuleLevels\":\"\",\"Timeout\":\"60000\",\"BatchId\":\"7e7b63212ef04315965f5680c6f08c94\",\"profiles\":\"prod\",\"invoker\":\"payment-risk\",\"Mobile\":\"\",\"clientIpDetail\":{\"country\":\"中国\",\"riskLevel\":\"PASS\",\"province\":\"广东\",\"city\":\"佛山\",\"cityId\":0},\"tradeTime\":\"1685360082037\",\"tradeAmount\":\"9900.00\",\"STAR_DIAMOND\":\"0\",\"AppId\":\"20\",\"T_Ip\":\"*************\",\"frameworkTraceId\":\"ed11970bc7104448acb51e1103458d54\",\"UserId\":\"213631154206303177\",\"allGroupRuleLevels\":\"\",\"Event\":\"QUICKLY_CONSUME_AFTER\",\"TraceId\":\"5694b9449aaf43c48e3833e3637bc8af\",\"operationDirection\":\"FORWORE_DIRECTION\",\"relationTradeAmount\":\"99.00\",\"RiskLevel\":\"PASS\",\"tradeId\":\"01000000661213168536008199600000\",\"business_order_no\":\"ab53aff738bc4dc6a1eb7db3bbfb91f5\"} ";


        Object result = ExpressionExecutor.execute(condition, JSON.parseObject(json));

        System.err.println(result);
//        Expression expression = new SpelExpressionParser().parseExpression(condition);
//        StandardEvaluationContext context = new StandardEvaluationContext();
//        context.setVariables(param);
//        Object result = expression.getValue(context);
//        LOGGER.debug("前置表达式执行结果:{} - {} - {}", result, factor.getCondition(), param);
//        if (result instanceof Boolean) {
//            System.err.println(result);
//        } else {
//            System.err.println(result);;
//        }

//        Map<String, Object> params = new HashMap<>();
//        params.put("Event", "user-login");
//        Map<String, Object> params2 = new HashMap<>();
//        params2.put("Event", "register");
//        String condition = "#Event == 'user-login'";
//        String condition2 = "#Event == 'register'";
//
//        Thread a = new Thread(() -> {
//            for (int i = 0; i < 100000; i++) {
//                Object data = execute(condition, params);
//                Object data2 = execute(condition2, params);
//                if (!data.toString().equals("true")) {
//                    System.err.println(" a error");
//                }
//                if (!data2.toString().equals("false")) {
//                    System.err.println(" a error");
//                }
//            }
//        });
//        Thread b = new Thread(() -> {
//            for (int i = 0; i < 100000; i++) {
//                Object data = execute(condition, params2);
//                if (data.toString().equals("true")) {
//                    System.err.println("error");
//                }
//                Object secondTimeData = execute(condition, params2);
//                if (secondTimeData.toString().equals("true")) {
//                    System.err.println("error");
//                }
//                Object data2 = execute(condition2, params2);
//                if (data2.toString().equals("false")) {
//                    System.err.println("error");
//                }
////                System.out.println("thread2\t" + data.toString());
//            }
//        });
//        a.start();
//        b.start();
    }
}
