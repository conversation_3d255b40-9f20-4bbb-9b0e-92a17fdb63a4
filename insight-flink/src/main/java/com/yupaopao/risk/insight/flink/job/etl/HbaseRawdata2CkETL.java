package com.yupaopao.risk.insight.flink.job.etl;

import com.alibaba.fastjson.JSON;
import com.alibaba.lindorm.client.core.utils.Bytes;
import com.yupaopao.risk.insight.common.constants.HBaseConstants;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseFullTableInputFormat;
import com.yupaopao.risk.insight.flink.job.base.CheckpointSetting;
import com.yupaopao.risk.insight.flink.job.base.FlinkJobBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.hadoop.hbase.client.Result;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.NavigableMap;
import java.util.concurrent.LinkedBlockingQueue;

/****
 * zengxiangcai
 * 2022/6/22 10:47
 ***/

@Slf4j
public class HbaseRawdata2CkETL {

    public static void main(String[] args) throws Exception {
        new FlinkJobBuilder()
                .withJobName("hbase2ck-device-rawdata[daily]")
                .withCheckpointSetting(new CheckpointSetting(60000, 30000, false))
                .isBatchJob(true)
                .withMainProcessor(new RadataSyncProcessor())
                .start(args);
    }

    public static class RadataSyncProcessor implements FlinkJobBuilder.MainProcessor {

        @Override
        public void internalProcess(StreamExecutionEnvironment env, Map<String, String> argMap) {
            String tableName = "risk_device_rawdata";
            //hbase全量读取数据同步
            TsTableInfo tsInTable = new TsTableInfo().withTableName(tableName).withBucketPerSplit(1).withTableType("SYSTEM");
            LinkedBlockingQueue<Result> cache = new LinkedBlockingQueue<>(1024 * 10);
            HBaseProperties hBaseProperties = HBaseProperties.getProperties(PropertyType.HBASE);

            ClickHouseProperties clickHouseProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);


            env.createInput(new HBaseFullTableInputFormat(tsInTable, hBaseProperties, cache) {
                        @Override
                        public String parseHBaseResult(Result rowResult) {
                            this.getReaderCounter().add(1);
                            return transDeviceRawdata2CkRow(rowResult);
                        }

                        @Override
                        public void close() throws IOException {
                            super.close();
                        }

                        private String transDeviceRawdata2CkRow(Result result) {
                            //转换hbase 行为ck tagJson
                            NavigableMap<byte[], byte[]> kvMap = result.getNoVersionMap().get(HBaseConstants.HBASE_FAMILY_KEY); //get latest version from
                            // hbase
                            Map<String, Object> rowMap = new LinkedHashMap<>();
                            String rowKey = Bytes.toString(result.getRow());
                            rowMap.put("deviceId", rowKey);
                            for (NavigableMap.Entry<byte[], byte[]> kvEntry : kvMap.entrySet()) {

                                String columnName = Bytes.toString(kvEntry.getKey());
                                try {
                                    if (kvEntry.getValue() == null) {
                                        continue;
                                    }

                                    Object columnValue = Bytes.toString(kvEntry.getValue());
                                    if (columnValue == null) {
                                        continue;
                                    }
                                    rowMap.put(columnName, columnValue);
                                } catch (Exception e) {
                                    log.warn("error, rowKey:" + rowKey + ", column: " + columnName + ", result=" + result, e);
                                    throw e;
                                }
                            }
                            rowMap.put("syncDate", InsightDateUtils.getDateStr(System.currentTimeMillis(),
                                    InsightDateUtils.DATE_FORMAT_yyyy_MM_dd));
                            return JSON.toJSONString(rowMap);
                        }

                    }).name("read_hbase[" + tableName + "]")
                    .filter(elem -> !HBaseUtil.emptyResultJson(elem))
                    .addSink(new ClickHouseStreamSink(clickHouseProperties, tableName, 60 * 1000, 20000))
                    .name("write_ck[" + tableName + "]")
                    .setParallelism(1);

        }
    }
}
