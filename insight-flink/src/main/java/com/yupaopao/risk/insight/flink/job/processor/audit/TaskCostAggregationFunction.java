package com.yupaopao.risk.insight.flink.job.processor.audit;

import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.audit.AuditMetric;
import com.yupaopao.risk.insight.flink.bean.audit.PerformanceMxTask;
import org.apache.flink.api.common.functions.AggregateFunction;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-07-03 17:46
 * 聚合1分钟用户任务绩效
 ***/
public class TaskCostAggregationFunction implements AggregateFunction<PerformanceMxTask, TaskCostMetrics, TaskCostMetrics> {

    public static final String PUBLISH_COST = "pub";
    public static final String AUDIT_COST = "audit";
    public static final String COUNT = "flinkAuditProcessCount";

    @Override
    public TaskCostMetrics createAccumulator() {
        return new TaskCostMetrics();
    }

    @Override
    public TaskCostMetrics add(PerformanceMxTask value, TaskCostMetrics accumulator) {
        //审核耗时
        AuditMetric oldProcessCost = accumulator.getAudit();
        if (oldProcessCost == null) {
            oldProcessCost = copyFromPerformanceMxTask(value);
            oldProcessCost.setMetric(AUDIT_COST);
            oldProcessCost.setValue(Double.valueOf(value.getProcessCost()));
        } else {
            oldProcessCost.setValue(oldProcessCost.getValue() + value.getProcessCost());
        }
        accumulator.setAudit(oldProcessCost);
        //发布耗时
        AuditMetric oldWholeCost = accumulator.getPub();
        if (oldWholeCost == null) {
            oldWholeCost = copyFromPerformanceMxTask(value);
            oldWholeCost.setMetric(PUBLISH_COST);
            oldWholeCost.setValue(Double.valueOf(value.getWholeProcessCost()));
        } else {
            oldWholeCost.setValue(oldProcessCost.getValue() + value.getWholeProcessCost());
        }
        accumulator.setPub(oldWholeCost);

//        //处理量
//        AuditMetric processCount = accumulator.get(COUNT);
//        if (processCount == null) {
//            processCount = copyFromPerformanceMxTask(value);
//            processCount.setMetric(COUNT);
//            processCount.setValue(Double.valueOf(1));
//        } else {
//            processCount.setValue(processCount.getValue() + 1);
//        }
//        accumulator.put(COUNT, processCount);


        return accumulator;
    }

    @Override
    public TaskCostMetrics getResult(TaskCostMetrics accumulator) {
        return accumulator;
    }

    @Override
    public TaskCostMetrics merge(TaskCostMetrics a, TaskCostMetrics b) {
        return null;
    }

    private AuditMetric copyFromPerformanceMxTask(PerformanceMxTask resultTask) {
        AuditMetric auditMetric = new AuditMetric();
        auditMetric.setBu(resultTask.getBu());
        auditMetric.setGroup(resultTask.getAuditorGroupId().toString());
        auditMetric.setTeam(resultTask.getAuditorTeamId().toString());
        auditMetric.setAuditor(resultTask.getAuditor());
        auditMetric.setChannel(resultTask.getChannel());
        auditMetric.setPhase(resultTask.getCurrentPhase().toString());
        auditMetric.setResult(resultTask.getLatestResult());
        auditMetric.setLevel(resultTask.getLatestLevel().toString());
        auditMetric.setReason(resultTask.getLatestReason());
        auditMetric.setMetric("");
        auditMetric.setValue(0.0D);
        auditMetric.setSource(resultTask.getSource());
        String now = InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd_HH_mm_ss);
//        String taskUpdateTime =
        auditMetric.setCreatedAt(now);
        auditMetric.setStartTime(now); //业务时间
        auditMetric.setEndTime(now);
        return auditMetric;
    }

}
