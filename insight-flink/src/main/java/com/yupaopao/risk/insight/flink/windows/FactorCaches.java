package com.yupaopao.risk.insight.flink.windows;

import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.thread.InsightFlinkThreadFactory;
import com.yupaopao.risk.insight.flink.utils.DBUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Connection;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/****
 * zengxiangcai
 * 2022/12/12 18:00
 ***/


@Slf4j
public class FactorCaches {
    private static Map<String, Integer> FACTOR_WINDOW_TYPE_MAP;

    private static Map<String, String> FACTOR_FUNCTION_MAP;
    private static Connection dbConn = null;
    private static ScheduledExecutorService scheduler = null;

    static {
        startFactorCacheScheduler();
    }

    private static void startFactorCacheScheduler() {
        createConn();
        doFactorCache();
        startScheduler();
    }

    private static void startScheduler() {
        if (scheduler == null || scheduler.isShutdown()) {
            scheduler = Executors.newSingleThreadScheduledExecutor(new InsightFlinkThreadFactory("factor-cfg-cache"));
            scheduler.scheduleAtFixedRate(() -> doFactorCache(), 120, 120, TimeUnit.SECONDS);
        }
    }

    private static void doFactorCache() {
        List<Map<String, Object>> factorList = DBUtil.queryList(dbConn, "select id,window_type,_function from risk_factor " +
                " where business in (?)", Arrays.asList(1));
        if (CollectionUtils.isEmpty(factorList)) {
            log.info("cache factors empty");
            return;
        } else {
            log.info("get factors size is : {}", factorList.size());
        }
        Map<String, Integer> factorWindowMap =
                factorList.stream().collect(Collectors.toMap(elem -> elem.get("id").toString(),
                        elem -> Integer.valueOf(elem.get("window_type").toString())));
        Map<String, String> factorFunctionMap =
                factorList.stream().collect(Collectors.toMap(elem -> elem.get("id").toString(),
                        elem -> elem.get("_function").toString()));

        if (factorWindowMap != null && !factorWindowMap.isEmpty()) {
            FACTOR_WINDOW_TYPE_MAP = factorWindowMap;
        }
        if (factorFunctionMap != null && !factorFunctionMap.isEmpty()) {
            FACTOR_FUNCTION_MAP = factorFunctionMap;
        }
    }

    private static void createConn() {
        if (dbConn != null) {
            return;
        }
        synchronized (FactorCaches.class) {
            if (dbConn != null) {
                return;
            }
            dbConn = DBUtil.getConnection(DBProperties.getProperties(PropertyType.DB));
        }
    }

    public static Integer getWindowType(String factorId) {
        createConn();
        Integer windowType = FACTOR_WINDOW_TYPE_MAP.get(factorId);
        return windowType;
    }

    public static String getFunctionType(String factorId) {
        createConn();
        String functionType = FACTOR_FUNCTION_MAP.get(factorId);
        if (StringUtils.isEmpty(functionType)) {
            return "null";
        }
        return functionType;
    }


    public static void closeFactorCache() {
        try {
            if (dbConn != null) {
                dbConn.close();
                dbConn = null;
            }
            if (scheduler != null) {
                scheduler.shutdownNow();
                scheduler = null;
            }
        } catch (Exception e) {
            log.error("close factor cache error", e);
        }
    }


}
