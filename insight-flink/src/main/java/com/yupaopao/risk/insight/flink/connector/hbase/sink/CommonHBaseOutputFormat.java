package com.yupaopao.risk.insight.flink.connector.hbase.sink;

import com.alibaba.fastjson.JSON;
import com.alibaba.lindorm.client.core.utils.Bytes;
import com.github.wnameless.json.flattener.FlattenMode;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.support.JsonFlatterUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Put;

import java.io.IOException;
import java.io.Serializable;
import java.util.Map;

import static com.yupaopao.risk.insight.common.constants.HBaseConstants.COLUMN_FAMILY;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-03-01 10:50
 * 写入hbase 通用outputFormat:
 * 统一传入json串，通过flattenMap拍平字段，将map的key作为habse 的列
 * [可通过generateMap方法定制自己的string转map方法]
 * 提供抽象方法子类实现getRowKey 生成hbase的主键
 *
 ***/

@Slf4j
public abstract class CommonHBaseOutputFormat extends HBaseBaseOutputFormat<String> implements Serializable {

    public CommonHBaseOutputFormat(String tableName, HBaseProperties hBaseProperties) {
        super(tableName, hBaseProperties);
    }


    @Override
    public void writeRecord(String record) throws IOException {
        if (StringUtils.isEmpty(record)) {
            return;
        }
        try {
            //json扁平化处理
            Map<String, Object> flattenMap = generateMap(record);
            String rowKey = getRowKey(flattenMap);

            Map<String, Object> extraData = generateExtraColumn(flattenMap);
            if (extraData != null && !extraData.isEmpty()) {
                flattenMap.putAll(extraData);
            }
            Put put = new Put(Bytes.toBytes(rowKey));
            boolean columnExist = addColumns(flattenMap, put);
            if (!columnExist) {
                // put 没有列进行insert时会报错需要排除
                return;
            }
            this.getMutator().mutate(put);
            if (canFlush()) {
                flush();
            }
        } catch (Exception e) {
            log.error("write record error: ,record: " + record, e);
        }
    }

    public boolean addColumns(Map<String, Object> flattenMap, Put put) {
        byte[] columnFamily = Bytes.toBytes(COLUMN_FAMILY);
        int columnCount = 0;
        for (Map.Entry<String, Object> entry : flattenMap.entrySet()) {
            byte[] columnValue = HBaseUtil.jsonTypeToHBaseColumn(entry.getValue());
            if (columnValue == null) {
                continue;
            }
            columnCount++;
            put.addColumn(columnFamily, Bytes.toBytes(entry.getKey()), columnValue);
        }
        return columnCount != 0;
    }


    /***
     * 可重写该方法获取rowKey
     * @param flattenMap
     * @return
     */
    public abstract String getRowKey(Map<String, Object> flattenMap);

    /***
     * 可重写该方法将record转为map
     * @param record
     * @return
     */
    public Map<String, Object> generateMap(String record) {
        return JsonFlatterUtils.toMap(record, FlattenMode.KEEP_ARRAYS);
    }


    /***
     * 可重写该方法根据map数据处理产生一些额外的字段
     * @param flattenMap
     * @return
     */
    public Map<String, Object> generateExtraColumn(Map<String, Object> flattenMap) {
        return null;
    }

}
