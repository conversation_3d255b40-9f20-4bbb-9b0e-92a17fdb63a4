package com.yupaopao.risk.insight.flink.job.wangan.constant;

public class TopicConstant {

    // 登录
    public static final String ACCOUNT = "maserati_passport.t_account";//

    public static final String ACCOUNT_ASSOCIATE = "maserati_passport.t_account_associate";//
    public static final String T_USER_INFO = "maserati_user_center.t_user_info";//
    public static final String T_USER_AUTH = "maserati_user_center.t_user_auth";//
    public static final String T_USER_LOCATION = "maserati_bx_userdb.t_user_location";//

//    public static final String T_USER_ACTION_RECORD = "maserati_user_center.t_user_action_record";    // 废弃
    public static final String RELATION_DOMAIN = "maserati_relation_domain";//
    public static final String PAYMENT_THIRD_PAY = "maserati_ypp_platform_payment.payment_third_pay";//
    public static final String PAYMENT_RECHARGE_ORDER = "maserati_ypp_platform_payment.payment_recharge_order";//
    public static final String T_PAYMENT_TRADE = "maserati_ypp_platform_payment.t_payment_trade";//
//    public static final String PAYMENT_PAY_ORDER = "maserati_ypp_platform_payment.payment_pay_order";// 废弃
    public static final String PAYMENT_THIRD_PAY_ATTACH = "maserati_ypp_platform_payment.payment_third_pay_attach";//
    public static final String T_CHATROOM_BEHAVIOR_LOG = "maserati_bx_app_chatroom.t_chatroom_behavior_log";//

    public static final String CONTENT_MAIN = "maserati_content_manage.content_main";
    public static final String CONTENT_ANCHOR = "maserati_content_manage.content_anchor";
    public static final String CONTENT_POSITION = "maserati_content_manage.content_position";
    public static final String T_COMMENT = "maserati_content_interaction.t_comment";

    public static final String T_LOGIN_DEVICE = "maserati_passport.t_login_device";//
    public static final String LOGIN_RECODE = "maserati_passport.t_login_record";//
    public static final String GROUP_CHAT_BINLOG = "group_chat_binlog";



}
