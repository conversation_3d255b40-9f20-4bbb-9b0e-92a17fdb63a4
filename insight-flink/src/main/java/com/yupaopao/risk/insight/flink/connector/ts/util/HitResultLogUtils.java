package com.yupaopao.risk.insight.flink.connector.ts.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.yupaopao.risk.insight.common.constants.TsConstants;
import com.yupaopao.risk.insight.flink.connector.ts.businessmodel.*;
import com.yupaopao.risk.insight.flink.utils.TsPrimaryKeyTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2019-11-20 11:26
 *
 ***/

@Slf4j
public class HitResultLogUtils {

    private static final String DATE_FORMART = "yyyy-MM-dd HH:mm:ss";

    public static JSONObject translateToStoreMap(String valueJson, long msgTime) {

        //TODO action.data.tokenLabels
        JSONObject jsonObject = JSONObject.parseObject(valueJson);
        JSONObject riskAction = jsonObject.getJSONObject("riskAction");
        JSONObject data1 = riskAction.getJSONObject("data");
        data1.remove("deviceLabels");
        data1.remove("tokenLabels");

        HitResultLog hitResultLog = JSON.parseObject(JSONObject.toJSONString(jsonObject), HitResultLog.class);

        RiskAction request = hitResultLog.getRiskAction();

        String clientIp = request.getClientIp();
//        if (StringUtils.length(clientIp) > 16) {
//            log.warn("IP过长,自动截断:{}", clientIp);
//            clientIp = clientIp.substring(0, 15);
//        }
        String mobileNo = request.getMobile();
        if (StringUtils.length(mobileNo) > 15) {
            log.warn("Mobile过长,自动截断:{}", clientIp);
            mobileNo = mobileNo.substring(0, 14);
        }

        HitResult hitResult = new HitResult();
        //traceId
        hitResult.setTraceId(request.getTraceId());
        //clientId
        hitResult.setClientIp(clientIp);
        //deviceId
        hitResult.setDeviceId(request.getDeviceId());
        //eventCode
        hitResult.setEventCode(request.getEventCode());
        //mobile
        hitResult.setMobileNo(mobileNo);
        //level
        hitResult.setLevel((hitResultLog.getRiskResult().getLevel() == null ? RiskLevel.PASS : hitResultLog.getRiskResult().getLevel()).name());
        //userid
        hitResult.setUserId(request.getUserId());
        //cost
        hitResult.setCostTime(hitResultLog.getCostTime());
        //replay
        hitResult.setReply(hitResultLog.getRiskResult().getReply());
        //reason
        hitResult.setReason(hitResultLog.getRiskResult().getReason());

        HitData hitData = new HitData();
        //traceId
        hitData.setTraceId(request.getTraceId());
        //原始的actionData
        hitData.setActionData(JSON.toJSONString(request.getData(), SerializerFeature.DisableCircularReferenceDetect));
        //原始的riskResult
        hitData.setResultData(JSON.toJSONString(hitResultLog.getRiskResult()));


        JSONObject map = JSON.parseObject(JSON.toJSONString(hitResult));

        JSONObject data = JSONObject.parseObject(hitData.getActionData());
        map.put("data", data);
        map.put("result", JSONObject.parseObject(hitData.getResultData()));
        map.remove("id");
        map.remove("class");
        if (map.get("clientIp") == null || map.get("clientIp").toString().length() == 0) {
            map.put("clientIp", "0.0.0.0");
        }

        map.put("createdAt", DateFormatUtils.format(new Date(msgTime), DATE_FORMART));
        map.put("kafkaMsgTime", msgTime);
        map.put(TsConstants.RISK_HIT_LOG_DATE_PARTITION, TsPrimaryKeyTools.getRiskHitLogPk(msgTime, request.getTraceId()));

        return map;

    }

    public static void main(String[] args) {
        String json = "{\"costTime\":104,\"riskAction\":{\"appId\":10,\"async\":false,\"businessCode\":\"userService\",\"clientIp\":\"2408:841b:1d20:cffa:c2e6:4a4e:44a:8383\",\"data\":{\"userDeviceHit308Count7Days\":0.0,\"userData\":{\"birthday\":\"2003-10-03 00:00:00\",\"gender\":1,\"auth\":true,\"source\":0,\"planetLiveLevel\":1,\"encryptId\":\"egAgSdyWvBHOeAI7u7weF7aWVKOf85r4MSP3gkuH3uc=\",\"authWay\":\"FACE\",\"createdAt\":1633361442000,\"uid\":212771410411874089,\"vipLevel\":14,\"wechatUnionId\":\"o8YcIuOdksDqEtFZUhhBH3SGbJtE\",\"value\":14,\"nationCode\":\"86\",\"nickName\":\"修缘（年底回）\",\"yellowVip\":false,\"authStatus\":1,\"detectResultCode\":1,\"planetUserLevel\":8,\"chatroomHost\":false,\"blueVip\":false,\"userId\":\"1d8eac582d914b9e8e647a9b83fac1ac\",\"exposeUnderage\":false,\"liveRobot\":false,\"showNo\":142122410,\"qqUnionId\":\"C820B5D144A135B639A1CD591A71A126\",\"yuerRobot\":false,\"anchor\":false,\"god\":false,\"age\":18,\"nobilityLevel\":44},\"Os\":\"ANDROID\",\"Platform\":\"ANDROID\",\"deviceDetail\":{\"date\":1659371465618,\"ip\":\"2408:841b:1d20:cffa:c2e6:4a4e:44a:8383\",\"udid\":\"20210412152140773af5c8b903212a4ea6b5f5443efd9c01c80dec36490a09\",\"network\":\"MOBILE\"},\"countUserIdDeviceId1dLogin\":2.0,\"deviceIdWhiteCheck\":false,\"ClientIp\":\"2408:841b:1d20:cffa:c2e6:4a4e:44a:8383\",\"commDevicePortraitData\":{\"deviceRelateUserAggLevel\":1,\"deviceRelateCityAggLevel\":1},\"canRetFlag\":\"true\",\"riskRequestTime\":\"1659371465724\",\"userMobile\":{\"mobile\":\"***********\",\"detectResultCode\":1,\"userId\":\"212771410411874089\",\"nationCode\":\"86\"},\"commUserIdWhiteCheck\":false,\"signupPlatform\":\"wechat\",\"Business\":\"userService\",\"countUserIdDeviceId1hLogin\":1.0,\"unionId\":\"o8YcIuOdksDqEtFZUhhBH3SGbJtE\",\"Timeout\":\"985\",\"countByUidRealDid30ds\":76.0,\"profiles\":\"prod\",\"countUserIdDeviceId7dLogin\":3.0,\"invoker\":\"passport-biz-service\",\"modelSimulatorCheck\":{\"features\":{\"sys\":\"{\\\"serial\\\":\\\"unknown\\\",\\\"fingerprint\\\":\\\"OPPO/PCLM10/OP4A89:11/RKQ1.200928.002/1651146453306:user/release-keys\\\",\\\"model\\\":\\\"PCLM10\\\",\\\"brand\\\":\\\"OPPO\\\",\\\"board\\\":\\\"msmnile\\\",\\\"manufacturer\\\":\\\"OPPO\\\"}\",\"apps\":\"[]\"},\"detectResultCode\":1,\"deviceModel\":\"PCLM10\",\"deviceBrand\":\"OPPO\"},\"clientIpDetail\":{\"country\":\"中国\",\"riskLevel\":\"PASS\",\"province\":\"河北\",\"city\":\"唐山\",\"cityId\":0},\"commIpWhiteCheck\":false,\"AppId\":\"10\",\"idNoBlackCheck\":false,\"TraceId\":\"e88e5a82c6354669bcf95e4b23a6bde9\",\"countByUid7dLoginVirtualDevice\":0.0,\"countByUidDid30ds\":1758.0,\"tianXiangData\":{\"query\":false,\"profileExist\":-1,\"detectResultCode\":1,\"realId\":\"20210412152140773af5c8b903212a4ea6b5f5443efd9c01c80dec36490a09\"},\"DeviceId\":\"20210412152140773af5c8b903212a4ea6b5f5443efd9c01c80dec36490a09\",\"OsVersion\":\"30\",\"clientDetail\":{\"app\":\"BIXIN\",\"os\":\"ANDROID\",\"productId\":100,\"appName\":\"BIXIN\",\"equipment\":\"OPPO PCLM10\",\"platformId\":2,\"version\":\"8.11.3\",\"platform\":\"ANDROID\",\"market\":\"bx-oppo\",\"protocol\":\"mapi/1.1\",\"osVersion\":\"30\",\"appId\":10,\"platformName\":\"ANDROID\",\"bundle\":\"com.yitantech.gaigai\",\"subVersion\":\"37854\"},\"countRejectByUserId1hForLogin\":0.0,\"mobilePortraitData\":{\"detectChannel\":\"YONGAN\",\"riskLevel\":\"PASS\",\"pNamePrice\":\"\",\"cardType\":0,\"ctime\":\"\",\"detectResultCode\":1,\"location\":\"\",\"risk\":0,\"attribute\":0,\"user\":\"7d2eb93660870c58c85802f345f020a23477ae07\",\"uptime\":\"\"},\"countByDeviceId7dForUserLogin\":3.0,\"nationCode\":\"86\",\"commDeviceIdWhiteCheck\":false,\"product\":\"100\",\"preLoginCheck\":{\"detectChannel\":\"SHUMEI\",\"riskLevel\":\"PASS\",\"requestId\":\"ec1e6f80127017f223844187c6363a13\",\"detectResultCode\":1,\"detail\":{\"hits\":[],\"ip_province\":\"河北\",\"ip_country\":\"中国\",\"description\":\"正常\",\"model\":\"M1000\",\"descriptionV2\":\"正常\",\"token\":{\"score\":0,\"riskReason\":\"\",\"riskType\":\"\",\"groupId\":\"\",\"groupSize\":0,\"riskGrade\":\"\"}}},\"asynUserIdWhiteCheck\":false,\"Mobile\":\"***********\",\"Equipment\":\"OPPO PCLM10\",\"UserId\":\"212771410411874089\",\"frameworkTraceId\":\"a743db8ec75645f38810a302d15c11f3\",\"countByUid1hLogin\":0.0,\"userDeviceLoginCount5Minutes\":0.0,\"userIdWhiteCheck\":false,\"Event\":\"user-login\",\"userPortraitData\":{\"userRelateCityAggLevel\":1,\"userRelateDeviceAggLevel\":1},\"disCountUidByDid3dLogin\":3.0},\"deviceId\":\"20210412152140773af5c8b903212a4ea6b5f5443efd9c01c80dec36490a09\",\"eventCode\":\"user-login\",\"mobile\":\"***********\",\"timeout\":985,\"traceId\":\"e88e5a82c6354669bcf95e4b23a6bde9\",\"userId\":\"212771410411874089\"},\"riskResult\":{\"businessCode\":\"userService\",\"eventCode\":\"user-login\",\"level\":\"PASS\",\"ruleName\":\"PASS\",\"success\":true,\"traceId\":\"e88e5a82c6354669bcf95e4b23a6bde9\"}}";
        JSONObject res = translateToStoreMap(json,1655888845660L);
        System.err.println(res);
    }
}
