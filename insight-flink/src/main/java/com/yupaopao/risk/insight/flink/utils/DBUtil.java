package com.yupaopao.risk.insight.flink.utils;

import com.yupaopao.risk.insight.common.property.connection.DBProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/****
 *
 * @Author: zengxiangcai
 * @Date: 2020-06-29 10:57
 *
 ***/

@Slf4j
public class DBUtil {


    public static Connection getConnection(DBProperties dbProperties) {
        try {
            Class.forName(dbProperties.getDriver());
            return DriverManager.getConnection(dbProperties.getUrl(), dbProperties.getUsename(),
                    dbProperties.getPassword());
        } catch (Exception e) {
            log.info("connect to mysql error : ", e);
        }
        return null;
    }

    /***
     * 查询单行数据
     * @param sql
     * @param params
     * @return
     */
    public static Map<String, Object> queryObject(Connection conn, String sql, List<Object> params) {
        Map<String, Object> rowData = new HashMap<>();
        try (PreparedStatement pstmt = conn.prepareStatement(sql);) {
            if (params != null) {
                for (int i = 0; i < params.size(); i++) {
                    pstmt.setObject(i + 1, params.get(i));
                }
            }
            ResultSet rs = pstmt.executeQuery();

            while (rs.next()) {
                ResultSetMetaData rsmd = rs.getMetaData();
                int columnCount = rsmd.getColumnCount();
                for (int k = 1; k <= columnCount; k++) {
                    String columnName = rsmd.getColumnLabel(k);
                    Object columnValue = rs.getObject(columnName);
                    rowData.put(columnName, columnValue);
                }
                break; //单行数据直接退出
            }
        } catch (Exception e) {
            log.error("query data from db error, sql=" + sql, e);
        }
        return rowData;
    }

    /***
     * 查询单行数据
     * @param sql
     * @param params
     * @return
     */
    public static List<Map<String, Object>> queryList(Connection conn, String sql, List<Object> params) {
        List<Map<String, Object>> rowList = new ArrayList<>();
        try (PreparedStatement pstmt = conn.prepareStatement(sql);) {
            if (params != null) {
                for (int i = 0; i < params.size(); i++) {
                    pstmt.setObject(i + 1, params.get(i));
                }
            }
            ResultSet rs = pstmt.executeQuery();
            while (rs.next()) {
                ResultSetMetaData rsmd = rs.getMetaData();
                int columnCount = rsmd.getColumnCount();
                Map<String, Object> rowData = new HashMap<>();
                for (int k = 1; k <= columnCount; k++) {
                    String columnName = rsmd.getColumnLabel(k);
                    Object columnValue = rs.getObject(columnName);
                    rowData.put(columnName, columnValue);
                }
                rowList.add(rowData);
            }
        } catch (Exception e) {
            log.error("query data from db error, sql=" + sql, e);
        }
        return rowList;
    }

    public static int insertBatch(Connection conn, String table, List<String> columns, List<List<String>> rowList) {
        if (CollectionUtils.isEmpty(rowList) || CollectionUtils.isEmpty(columns) || StringUtils.isEmpty(table)) {
            return 0;
        }
        String batchSql = "insert into " + table + "(" + String.join(",", columns) + ") values ";
        List<String> valueList = rowList.stream().map(elem -> {
            return "('" + String.join("','", elem) + "')";
        }).collect(Collectors.toList());
        String values = String.join(",", valueList);

        String sql = batchSql + values;
        try (Statement stmt = conn.createStatement();) {
            return stmt.executeUpdate(sql);
        } catch (Exception e) {
            log.error("batch insert error: sql=" + sql, e);
        }
        return 0;
    }


    public static int executeUpdate(Connection conn, String sql, List<Object> params) {
        try (PreparedStatement pstmt = conn.prepareStatement(sql);) {
            if (params != null) {
                for (int i = 0; i < params.size(); i++) {
                    pstmt.setObject(i + 1, params.get(i));
                }
            }
            return pstmt.executeUpdate();
        } catch (Exception e) {
            log.error("executeUpdate error, sql=" + sql, e);
        }
        return 0;
    }


    public static void closeResource(AutoCloseable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (Exception e) {
                log.warn("close resource error: ", e);
            }
        }
    }

}
