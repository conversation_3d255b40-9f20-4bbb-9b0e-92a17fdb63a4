package com.yupaopao.risk.insight.flink.job.portrait.async;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.yupaopao.risk.insight.common.support.InsightDateUtils;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitBean;
import com.yupaopao.risk.insight.flink.bean.portrait.PortraitFirstTag;
import com.yupaopao.risk.insight.flink.connector.redis.sink.client.RedisClient;
import com.yupaopao.risk.insight.flink.job.portrait.process.PortraitProcess;
import com.yupaopao.risk.insight.flink.job.portrait.support.CommonTagSupport;
import com.yupaopao.risk.insight.flink.job.portrait.support.LocalAggTagSupport;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.java.tuple.Tuple2;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import redis.clients.jedis.JedisPool;

import java.util.Collections;
import java.util.Objects;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Deprecated
@Slf4j
public class PostShuMeiPortraitFunction extends RichAsyncFunction<String, Tuple2<Long, PortraitBean>> {

    private static final long serialVersionUID = 3606382770085272135L;
    private static final String NULL_STR = "null";
//    private static PortraitProcess portraitProcess = new PortraitProcess();
//    private transient ExecutorService executorService;

    private AtomicInteger count = new AtomicInteger(0);

    private JedisPool jedisPool;

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        jedisPool = RedisClient.getClient();
    }

    @Override
    public void close() throws Exception {
        super.close();
        RedisClient.closePool(jedisPool);
    }

    @Override
    public void asyncInvoke(String input, ResultFuture<Tuple2<Long, PortraitBean>> resultFuture) throws Exception {
        Transaction transaction = Cat.newTransaction("insight.portrait", "async.io");
        JSONObject jsonObject = JSONObject.parseObject(input);
        jsonObject.put("number", count.addAndGet(1));
        jsonObject.put("opration", this.hashCode());

        log.info("操作员:{},处理标记人员序号:{}", jsonObject.get("opration"),count.get());
        try {
            CompletableFuture.supplyAsync(()->{
                PortraitBean portraitBean = null;
                try {
                    log.info("操作员{}的异步人员序号:{}", jsonObject.get("opration"),jsonObject.get("number"));
                    portraitBean = PortraitProcess.process(jsonObject.toJSONString(), jedisPool);
                } catch (Exception e) {
                    log.warn("异步获取数美画像错误:", e);
                    return null;
                }
                if (!Objects.isNull(portraitBean.getUid())) {
                    Tuple2<Long, PortraitBean> value = new Tuple2<>(portraitBean.getUid(), portraitBean);
                    return value;
                }
                return null;
            }).thenAccept((Tuple2<Long, PortraitBean> tuple2) -> {
                if (!Objects.isNull(tuple2)) {
//                    log.info("操作员{}的返回人员序号:{}",this.hashCode(), tuple2.f1.getNum());
                    resultFuture.complete(Collections.singleton(tuple2));
                }
            });
      /*      CompletableFuture.supplyAsync(new Supplier<Tuple2<Long, PortraitBean>>() {
                @Override
                public Tuple2<Long, PortraitBean> get() {
                    PortraitBean portraitBean = null;
                    try {
                        log.info("操作员{}的异步人员序号:{}", jsonObject.get("opration"),jsonObject.get("number"));
                        portraitBean = PortraitProcess.process(jsonObject.toJSONString());
                    } catch (Exception e) {
                        log.warn("异步获取数美画像错误:", e);
                        return null;
                    }
                    if (!Objects.isNull(portraitBean.getUid())) {
                        Tuple2<Long, PortraitBean> value = new Tuple2<>(portraitBean.getUid(), portraitBean);
                        return value;
                    }
                    return null;
                }
            }).thenAccept((Tuple2<Long, PortraitBean> tuple2) -> {
                if (!Objects.isNull(tuple2)) {
                    log.info("操作员{}的返回人员序号:{}",this.hashCode(), tuple2.f1.getNum());
                    resultFuture.complete(Collections.singleton(tuple2));
                }
            });*/
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("请求数美超时:{}", input, e);
        } finally {
            transaction.complete();
        }
/*
        try {
            PortraitBean portraitBean = PortraitProcess.process(input);
            if (!Objects.isNull(portraitBean.getUid())){
                Tuple2<Long, PortraitBean> value = new Tuple2<>(portraitBean.getUid(), portraitBean);
                resultFuture.complete(Collections.singleton(value));
            }
        } catch (Exception e) {
            transaction.setStatus(e);
            log.warn("请求数美超时:{}",input, e);
        }finally {
            transaction.complete();
        }*/
    }

    @Override
    public void timeout(String input, ResultFuture<Tuple2<Long, PortraitBean>> resultFuture) throws Exception {
        log.error("请求数美超时:{}", input);
        JSONObject riskLog = JSON.parseObject(input);
        String userId = getUserId(riskLog);
        if (userId == null) {
            log.warn("过滤无效数据:{}", input);
            Cat.logMetricForCount("portrait.request.invalid.count");
            return;
        }
        PortraitBean portraitBean = new PortraitBean();
        portraitBean.setTimestamp(riskLog.getLong("timestamp"));
        TreeSet<PortraitFirstTag> portraitFirstTags = new TreeSet<>();
        LocalAggTagSupport.buildTag(portraitBean, portraitFirstTags, JSON.parseObject(input), InsightDateUtils.getCurrentDayStr(InsightDateUtils.DATE_FORMAT_yyyy_MM_dd));
        portraitBean.setRiskTags(portraitFirstTags);


        Cat.logMetricForCount("portrait.request.exception.count");
        portraitBean.setUid(Long.valueOf(userId));
        Tuple2<Long, PortraitBean> value = new Tuple2<>(portraitBean.getUid(), portraitBean);
        resultFuture.complete(Collections.singleton(value));
        log.info("超时仅聚合本地标签:", value);
    }

    private String getUserId(JSONObject riskLog) {
        JSONObject riskAction = riskLog.getJSONObject("riskAction");
        if (CommonTagSupport.isEmpty(riskAction)) {
            log.info("riskAction 无效数据:{}", riskLog);
            return null;
        }
        String userId = riskAction.getString("userId");
        if (StringUtils.isEmpty(userId) || NULL_STR.equals(userId) || !StringUtils.isNumeric(userId)) {
            JSONObject data = riskAction.getJSONObject("data");
            if (CommonTagSupport.isEmpty(data)) {
                return null;
            }
            userId = data.getString("uid");
            if (StringUtils.isEmpty(userId) || NULL_STR.equals(userId) || !StringUtils.isNumeric(userId)) {
                return null;
            }

        }
        return userId;
    }
}
