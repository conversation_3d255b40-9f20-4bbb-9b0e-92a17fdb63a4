package com.yupaopao.risk.insight.flink.utils.ip2region;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Map;

@Getter
@Setter
public class IPDetail {

    private int cityId;
    private String country;
    private String region;
    private String province;
    private String city;
    private String isp;


    public IPDetail(int cityId, String region) {
        this.cityId = cityId;
        if (StringUtils.isNotBlank(region)) {
            String[] items = region.split("\\|");
            this.country = items[0];
            this.region = items[1];
            this.province = items[2];
            this.city = items[3];
            this.isp = items[4];
        }
    }

    public Map<String, Object> toMap() {
        return (JSONObject) JSONObject.toJSON(this);
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SIMPLE_STYLE);
    }

}
