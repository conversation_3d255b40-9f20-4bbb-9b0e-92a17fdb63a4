/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yupaopao.risk.insight.flink.job.etl.common.connect.mysql.writer;

import com.yupaopao.risk.insight.flink.job.etl.common.connect.mysql.core.MySqlDatabaseMeta;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.rdb.core.util.DbUtil;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.rdb.writer.JdbcDataSinkFactory;
import com.yupaopao.risk.insight.flink.job.etl.common.connect.rdb.writer.JdbcOutputFormatBuilder;
import com.yupaopao.risk.insight.flink.job.etl.common.options.DataTransferConfig;

import java.util.Collections;

/**
 * MySQL writer plugin
 *
 * Company: www.dtstack.com
 * <AUTHOR>
 */
public class MysqlSinkFactory extends JdbcDataSinkFactory {

    public MysqlSinkFactory(DataTransferConfig config) throws Exception {
        super(config);
        setDatabaseInterface(new MySqlDatabaseMeta());
        dbUrl = DbUtil.formatJdbcUrl(dbUrl, Collections.singletonMap("zeroDateTimeBehavior", "convertToNull"));
    }

    @Override
    protected JdbcOutputFormatBuilder getBuilder() {
        return new JdbcOutputFormatBuilder(new MysqlOutputFormat());
    }
}
