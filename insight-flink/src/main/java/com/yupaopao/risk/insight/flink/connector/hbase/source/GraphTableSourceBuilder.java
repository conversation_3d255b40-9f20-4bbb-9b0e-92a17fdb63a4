//package com.yupaopao.risk.insight.flink.connector.hbase.source;
//
//import com.yupaopao.risk.insight.common.constants.HBaseConstants;
//import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
//import com.yupaopao.risk.insight.common.meta.JobParams;
//import com.yupaopao.risk.insight.common.meta.TsTableInfo;
//import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
//import org.apache.flink.configuration.Configuration;
//import org.apache.flink.connector.hbase2.HBase2DynamicTableFactory;
//import org.apache.flink.connector.hbase2.source.HBaseDynamicTableSource;
//import org.apache.flink.table.api.TableSchema;
//import org.apache.flink.table.catalog.CatalogTableImpl;
//import org.apache.flink.table.catalog.ObjectIdentifier;
//import org.apache.flink.table.connector.source.DynamicTableSource;
//import org.apache.flink.table.factories.FactoryUtil;
//
//import java.util.HashMap;
//import java.util.Map;
//
//import static com.yupaopao.risk.insight.common.property.enums.PropertyType.HBASE;
//import static org.apache.flink.table.api.DataTypes.TIME;
//
///****
// * zengxiangcai
// * 2021/2/23 7:50 下午
// ***/
//public class GraphTableSourceBuilder {
//
//    public static HBaseDynamicTableSource createHbaseTableSource(TsTableInfo tsTableInfo,
//                                                               HBaseProperties hBaseProperties){
//
//        TableSchema schema =tsTableInfo.getFlinkMetaInfo().toTableSchema();
//
//
//        DynamicTableSource source = createTableSource(schema, getAllOptions(tsTableInfo,hBaseProperties));
//        HBaseDynamicTableSource hbaseSource = (HBaseDynamicTableSource) source;
//        return hbaseSource;
//    }
//
//    private static DynamicTableSource createTableSource(
//            TableSchema schema, Map<String, String> options) {
//        return FactoryUtil.createTableSource(
//                null,
//                ObjectIdentifier.of("default", "default", "t1"),
//                new CatalogTableImpl(schema, options, "mock source"),
//                new Configuration(),
//                HBase2DynamicTableFactory.class.getClassLoader(),
//                false);
//    }
//
//
//    private static Map<String, String> getAllOptions(TsTableInfo tsTableInfo, HBaseProperties hBaseProperties) {
//        Map<String, String> options = new HashMap<>();
//        options.put("connector", "hbase-2.2");
//        options.put("table-name", HBaseConstants.NAMESPACE + ":" + tsTableInfo.getTableName());
//        options.put("zookeeper.quorum", hBaseProperties.getEndpoint());
//        // 设置用户名密码，默认root:root，可根据实际情况调整
////        options.put("client.username", hBaseProperties.getUsername());
////        options.put("client.password", hBaseProperties.getPassword());
//        return options;
//    }
//
//    public static void main(String[] args) {
//
//        TsTableInfo tsInTable = new TsTableInfo()
//                .withTableName("graph_edge")
//                .withBucketPerSplit(1)
//                .withColumnNameType("fromVertex#STRING,toVertex#STRING,label#STRING,updateTime#BIGINT")
////                .withTableQueryPeriod(period)
//                .withTableType("SYSTEM");
//        HBaseProperties hBaseProperties = HBaseProperties.getProperties(HBASE);
//        Object obj = createHbaseTableSource(tsInTable,hBaseProperties);
//        System.err.println(obj);
//    }
//}
