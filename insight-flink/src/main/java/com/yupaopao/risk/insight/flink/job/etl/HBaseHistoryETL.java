package com.yupaopao.risk.insight.flink.job.etl;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.yupaopao.risk.insight.common.enums.HBaseColumnTypeEnum;
import com.yupaopao.risk.insight.common.meta.FlinkMetaInfo;
import com.yupaopao.risk.insight.common.meta.JobParams;
import com.yupaopao.risk.insight.common.meta.TsTableInfo;
import com.yupaopao.risk.insight.common.property.connection.ClickHouseProperties;
import com.yupaopao.risk.insight.common.property.connection.EsProperties;
import com.yupaopao.risk.insight.common.property.connection.HBaseProperties;
import com.yupaopao.risk.insight.common.property.enums.PropertyType;
import com.yupaopao.risk.insight.common.support.HBaseUtil;
import com.yupaopao.risk.insight.common.support.TsFlinkTypeUtils;
import com.yupaopao.risk.insight.flink.connector.clickhouse.map.DataHBaseFlatMap;
import com.yupaopao.risk.insight.flink.connector.clickhouse.sink.ClickHouseStreamSink;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseCommonInputFormat;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseSplit;
import com.yupaopao.risk.insight.flink.connector.hbase.source.HBaseSplitBuilder;
import com.yupaopao.risk.insight.flink.connector.ts.util.EsUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.JobExecutionResult;
import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.hadoop.hbase.client.Result;

import java.io.IOException;
import java.net.URLDecoder;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

@Slf4j
public class HBaseHistoryETL {
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        ClickHouseProperties clickHouseProperties = ClickHouseProperties.getProperties(PropertyType.CLICK_HOUSE);
        HBaseProperties hBaseProperties = HBaseProperties.getProperties(PropertyType.HBASE);

        EsProperties esProperties = EsProperties.getProperties(PropertyType.ES);

        String targetTable = "coordinate";
        if (args != null && args.length > 0) {
            log.info("args: {}", args);
            String inputParams = args[0];
            if (StringUtils.isNotEmpty(inputParams)) {
                String param = URLDecoder.decode(inputParams, "UTF-8");
                if (!StringUtils.isEmpty(param)) {
                    targetTable = param;
                }
            }
        }
        Config config = ConfigService.getAppConfig();
        int customBatchSize = config.getIntProperty("history.clickhouse.batch.size", 0);

        if (customBatchSize != 0) {
            clickHouseProperties.setBatchSize(customBatchSize);
        }
        String beginDate = config.getProperty("history.clickhouse.start.date", "20200410");
        String endDate = config.getProperty("history.clickhouse.end.date", "20200411");
        Map<String, String> typeMap = EsUtil.fetchColumns(esProperties, "risk_hit_log_" + beginDate);
        if (typeMap.size() <= 1) {
            return;
        }
        FlinkMetaInfo flinkMetaInfo = TsFlinkTypeUtils.buildFlinkMetaInfoFromMap(typeMap);

        JobParams.TableQueryDatePeriod period = new JobParams.TableQueryDatePeriod(targetTable, beginDate, endDate);
        HBaseCommonInputFormat<String> inputFormat = getInputFormat(flinkMetaInfo, hBaseProperties, period);

        env.createInput(inputFormat)
            .filter((FilterFunction<String>) value -> {
                if (StringUtils.isEmpty(value)) {
                    return false;
                } else {
                    return true;
                }
            })
            .flatMap(new DataHBaseFlatMap())
            .addSink(new ClickHouseStreamSink(clickHouseProperties, targetTable))
            .name("History ETL (HBase ---> ClickHouse) " + targetTable)
            .setParallelism(1);

        JobExecutionResult result = env.execute("History ETL (HBase ---> ClickHouse) " + beginDate + "-" + endDate);
        long processCount = result.getAccumulatorResult("processCount");
        long sinkCount = result.getAccumulatorResult("sinkCount");
    }

    private static HBaseCommonInputFormat<String> getInputFormat(FlinkMetaInfo flinkMetaInfo, HBaseProperties hBaseProperties, JobParams.TableQueryDatePeriod period) {
        TsTableInfo tsInTable = new TsTableInfo()
            .withTableName("risk_hit_log")
            .withTableQueryPeriod(period)
            .withBucketPerSplit(1)
            .withTableId("47")
            .withTableType("SYSTEM")
            ;

        LinkedBlockingQueue<Result> cache = new LinkedBlockingQueue<>(1024 * 8);
        return new HBaseCommonInputFormat<String>(tsInTable, hBaseProperties, cache) {
            @Override public HBaseSplit[] createInputSplits(int minNumSplits) throws IOException {
                return new HBaseSplitBuilder(tsInTable, hBaseProperties).buildRiskHitLogSplits();
            }

            @Override public String nextRecord(String reuse) throws IOException {
                try {
                    Result rowResult = cache.poll(4, TimeUnit.MILLISECONDS);
                    if (rowResult != null) {
                        return HBaseUtil.parseResultToString(rowResult, flinkMetaInfo, HBaseColumnTypeEnum.FROM_JSON_FLATTER);
                    }
                } catch (Exception e) {
                    log.warn("read next row error ", e);
                }
                return "";
            }
        };
    }

}
