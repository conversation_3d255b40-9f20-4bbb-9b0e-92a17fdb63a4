刷单资金流行：
==
- 20201209 **************下聚集用户
```sql
select id from behavior_ip_clustering b where runDay = today() and ip = '**************' and clusterId=245

```
```
458人都是刚注册的用户：都是注册后下单,充值微信分布：
oEP2DxDxbfJyO2tyfdMifwnioDRE	1
oEP2DxIZr2TUgADSVtPeM6nTgECo	64
oEP2DxKY8AFh7wN5vyhh-hkRS9wo	11
oEP2DxLKSH8lmBIllq5JuUqwZKRw	192
oEP2DxOWkVdv6Z76l4Fjz-VnQrG4	191

刷单对象分布：
200840543167666430	73
192190982102806790	138
200431404376352642	2
192921202403911841	1
191711036432269877	140
191531256472127584	46
201090133448226793	77
1833900800200122	139
192450450413290194	110
1833814351300064	405
201921088060702993	42
200060220125766508	24
191300692441985112	148
190450096051046592	25
202201346041558906	2
201331082018845134	122
1833905974900019	281
201531017139674135	126
201630895139831226	24
201500923419650016	41
201631139409852703	2
1833811810700112	136

```

刷单用户关联关系
===
```
有consume和income两类，而且有before和after，鉴于关系都是一对一的
分析流量的时候只需要考虑CONSUME_BEFORE或INCOME_BEFORE

```
- SLOWLY_CONSUME_BEFORE各场景的businessCode下关系
  - ACTIVITY$ACTIVITY_VOICE_PAIR_REWARD userid为付款方id，无设备，ip
  - CHATROOM$GRAB_RED_ENVELOPE/LIVE$GRAB_RED_ENVELOPE 
  ```sql
    当记录的是发红包时,relationUID为空,此时和userid关联deviceId,clientip
    当记录抢红包时relationUID非空,表示抢红包的人,relationUID关联deviceId,clientip
    红包的比较特殊SLOWLY_INCOME_BEFORE 也是和这个类似，userid 和deviceId,clientIp关联
    一般情况下income是relation和deviceId关联
  ```
  - CHATROOM$ORDER_UNION_ONLINE userid和deviceId,clientip关联, 无relationUID
  - HEARTBEAT_MATCHING$HEARTBEAT_MATCHING_REWARD userid和deviceid,ip 关联
  - HOMEPAGE$BIXIN_SVIP userid和deviceid,ip 关联
  - LIVE$CRAZY_ADVENTURE userid和deviceid,ip 关联
  - LIVE$DRAW_GUESS userid和deviceid,ip 关联
  - LIVE$FACE_GINI userid和deviceid,ip 关联
  - LIVE$MUSIC_GAME userid和deviceid,ip 关联
  - MOMENT$MOMENT_RED_PACKET 有relationUID则和relationUID关联，无则userid和deviceid,ip 关联
  - ORDER$ORDER_ONLINE userid和deviceid,ip 关联
  - ORDER$ORDER_UNION_ONLINE userid和deviceid,ip 关联
  - ORDER$PRESENT_CARD_BUY userid和deviceid,ip 关联
  - PAY_$ELECTRONIC_SPORTS$200001$200001
  - PAY_$GAME$200030$200030/PAY_$GAME$200053$200053/PAY_$GAME$200054$200054/PAY_$GAME$200057$200057
    /PAY_$GAME$200065$200065/PAY_$GAME$200071$200071/PAY_$GAME$200072$200072 。。。。
    userid和deviceid,ip 关联
    
- QUICKLY_CONSUME_BEFORE
  - LIVE$LIVE_FANS_GROUP 【加入粉丝团】 都是系统账户，忽略
  - CHATROOM$CHATROOM_REWARD userid 和deviceid，ip关联
  - CHATROOM$NEW_PERSON_GIFT_PACKS 全部系统账号，无设备，无ip
  - MANUAL$MANUAL 无ip，无deviceId，转入系统账户
  - TREASURE_BOX$TREASURE_BOX_GIFT （直播宝箱打赏） userid和deviceid,ip 关联
  - CHATROOM$CHATROOM_BUY_SEAT userid和deviceid,ip 关联
  - MESSAGE$MYSTERY_GIFT userid和deviceid,ip 关联
  - DUBBING_SHOW$DUBBING_SHOW_REWARD （爱配音打赏） userid和deviceid,ip 关联
  - CHATROOM$ACTIVITY 和userid关联，userid可能是系统账号，此时和relationUID关联
  
- 资金流向图
```text
充值渠道
ALIPAY	414398
BALANCE	171589
GOOGLE_PLAY	4367
IAP	185987
PING_AN_BANK	151 -- 卡号 rcvAccountNo
QQ	11307
UNION_WEIXIN	391527 -- weChatOpenId
WEIXIN	320090
```
- 关联sql：
  - recharge(recharge_check用的openId，success用的thirdPayUser)
  ```odpsql
    insert into pay_edge(sourceid,targetid,type,weight)
    select payAccount,userid,'recharge' as type,count(1) as weight from (
    select
    （case when get_json_object(data,'$.weChatOpenId')!='' and isnotnull(get_json_object(data,'$.weChatOpenId'))
    and get_json_object(data,'$.weChatOpenId')!='undefined'
    then get_json_object(data,'$.weChatOpenId')
    when get_json_object(data,'$.rcvAccountNo')!='' and isnotnull(get_json_object(data,'$.rcvAccountNo'))
    then get_json_object(data,'$.rcvAccountNo')
    else
    concat(get_json_object(data,'$.payUid'),'_recharge')
    end） as payAccount, userid, 1 as count1
    from bigdata_realtime.ods_es_payes_pay_trade_risk_hit_log_day
    where dt>='********' and dt<='********'
    and  eventcode = 'RECHARGE_CHECK'
    ) tmp group by payAccount,userid
    ;
  ```
  - consume_before 
  ```odpsql
    insert into pay_edge(sourceid,targetid,type,weight)
           select userid,relationU,concat('transferTo','_',businessCode) as type,count(1) as weight from (
                  select
                  userid, get_json_object(data,'$.relationUID') relationU,get_json_object(data,'$.businessCode') as businessCode
                  from bigdata_realtime.ods_es_payes_pay_trade_risk_hit_log_day
                  where dt>='********' and dt<='********'
                  and  eventcode like  '%CONSUME_BEFORE'
                  and length(userid)>15 and length(get_json_object(data,'$.relationUID'))>15
    
           ) tmp group by userid,relationU,businessCode
    ;
  
  
  
  
  
  ```
  ```odpsql
     insert into pay_edge(sourceid, targetid, type, weight, amount)
       select payAccount, userid, 'recharge' as type, count(1) as weight
       ,      sum(cnyAmount)
       from (
         select              （case
         when get_json_object(data, '$.weChatOpenId') != '' and isnotnull(get_json_object(data, '$.weChatOpenId')) and
         get_json_object(data, '$.weChatOpenId') != 'undefined' then get_json_object(data, '$.weChatOpenId')
         when get_json_object(data, '$.rcvAccountNo') != '' and isnotnull(get_json_object(data, '$.rcvAccountNo')) then
         get_json_object(data, '$.rcvAccountNo')
         else concat(get_json_object(data, '$.payUid'), '_', get_json_object(data, '$.payChannel'))
         end） as payAccount, userid
         ,                   cast(get_json_object(data, '$.payAmount') as DOUBLE) as cnyAmount
         from bigdata_realtime.ods_es_payes_pay_trade_risk_hit_log_day
         where dt >= '********' and dt <= '********' and eventcode = 'RECHARGE_CHECK'
       ) tmp
       group by payAccount, userid;
     
     
     insert into pay_edge(sourceid, targetid, type, weight, amount)
       select userid, relationU, concat('transferTo', '_', businessCode) as type, count(1) as weight, sum(cnyAmount)
       from (
         select userid, get_json_object(data, '$.relationUID') relationU,
                get_json_object(data, '$.businessCode') as businessCode, (case
         when isnotnull(get_json_object(data, '$.relationCnyAmount')) and get_json_object(data, '$.relationCnyAmount') !=
         '' then cast(get_json_object(data, '$.relationCnyAmount') as DOUBLE)
         when isnotnull(get_json_object(data, '$.cnyAmount')) and get_json_object(data, '$.cnyAmount') != '' then cast(
         get_json_object(data, '$.cnyAmount') as DOUBLE)
         when isnotnull(get_json_object(data, '$.tradeAmount')) and get_json_object(data, '$.tradeAmount') != '' then cast(
         get_json_object(data, '$.tradeAmount') as DOUBLE)
         else 0.00
         end) cnyAmount
         from bigdata_realtime.ods_es_payes_pay_trade_risk_hit_log_day
         where dt >= '********' and dt <= '********' and eventcode like '%CONSUME_BEFORE' and length(userid) > 15
         and length(get_json_object(data, '$.relationUID')) > 15
     
       ) tmp
       group by userid, relationU, businessCode;
  ```
  
  
  
- 设备
   ```odpsql
       -- 红包   
           insert into pay_edge(sourceid,targetid,type,weight)
                  select sourceU,deviceid,'hasDevice' as type,count(1) as weight from (
                         select
                         （case when isnotnull(get_json_object(data,'$.relationUID')) and get_json_object(data,'$.relationUID')!=''
                         then get_json_object(data,'$.relationUID')
                         else userid
                         end） as sourceU, deviceid
                         from bigdata_realtime.ods_es_payes_pay_trade_risk_hit_log_day
                         where dt>='********' and dt<='********'
                         and  eventcode like  '%CONSUME_BEFORE'
                         and (
                             get_json_object(data,'$.businessCode') like '%RED_ENVELOPE'
                             or get_json_object(data,'$.businessCode')='MOMENT$MOMENT_RED_PACKET'
                         )
           
                  ) tmp where length(tmp.sourceU)>15 and deviceid!=''
                  group by payAccount,userid,businessCode
           ;
   ```
  
案例2
===
```sql
behavior_ip_clustering ********发现88个用户在抢红包场景聚集，经过查询发现是同一个openId(oT4Liwslk_mJJiKLeqyiSKjgY6SQ)充值的
经过分析这88个用户，发现都是抢完后包好打赏给两个人：
191120437181820630
201721214410095190

```



-- 充值

insert into pay_edge(sourceid, targetid, type, weight, amount, runDay)
select payAccount, userid, 'recharge', count(1), sum(cnyAmount), today()
from (
      select multiIf(JSONExtractString(data, 'weChatOpenId') != '' and
                     JSONExtractString(data, 'weChatOpenId') != 'undefined',
                     JSONExtractString(data, 'weChatOpenId')
                 , JSONExtractString(data, 'rcvAccountNo') != '', JSONExtractString(data, 'rcvAccountNo'),
                     concat(JSONExtractString(data, 'payUid'), '_', JSONExtractString(data, 'payChannel'))
                 )                                                 as payAccount,
             userid,
             toFloat32OrZero(JSONExtractString(data, 'payAmount')) as cnyAmount,
             today()
      from ods_pay_trade_risk_hit_log_day
      where createdat >= '2020-12-16 00:00:00'
        and eventcode = 'RECHARGE_CHECK')
group by payAccount, userid;

-- 提现

--
-- 3、抢红包\订单等慢支付

insert into pay_edge(sourceid, targetid, type, weight, amount, runDay)
select userid, relationU, concat('transferTo', '_', businessCode) as type, count(1) as weight, sum(cnyAmount) totalAmount,today()
from (
         select userid,
                JSONExtractString(data, 'relationUID')     relationU,
                JSONExtractString(data, 'businessCode') as businessCode,
                multiIf(JSONExtractString(data, 'relationCnyAmount') != '',
                        toFloat32OrZero(JSONExtractString(data, 'relationCnyAmount')),
                        JSONExtractString(data, 'cnyAmount') != '',
                        toFloat32OrZero(JSONExtractString(data, 'cnyAmount')),
                        JSONExtractString(data, 'tradeAmount') != '',
                        toFloat32OrZero(JSONExtractString(data, 'tradeAmount')),
                        0
                    )                                   as cnyAmount
         from ods_pay_trade_risk_hit_log_day
         where createdat>='2020-12-16 00:00:00'
           and createdat < '2020-12-17 00:00:00'
           and eventcode in ('SLOWLY_CONSUME_BEFORE', 'QUICKLY_CONSUME_BEFORE')
           and length(userid) > 15
           and length(relationU) > 15
           and userid != relationU
           and cnyAmount>0
         ) tmp
group by userid, relationU, businessCode;


insert into pay_edge(sourceid, targetid, type, weight, amount, runDay)
select userid, relationU, concat('transferTo', '_', businessCode) as type, count(1) as weight, sum(cnyAmount) totalAmount,today()
from (
         select userid,
                JSONExtractString(data, 'relationUID')     relationU,
                JSONExtractString(data, 'businessCode') as businessCode,
                multiIf(JSONExtractString(data, 'relationCnyAmount') != '',
                        toFloat32OrZero(JSONExtractString(data, 'relationCnyAmount')),
                        JSONExtractString(data, 'cnyAmount') != '',
                        toFloat32OrZero(JSONExtractString(data, 'cnyAmount')),
                        JSONExtractString(data, 'tradeAmount') != '',
                        toFloat32OrZero(JSONExtractString(data, 'tradeAmount')),
                        0
                    )                                   as cnyAmount
         from ods_pay_trade_risk_hit_log_day
         where createdat>='2020-12-16 00:00:00'
           and createdat < '2020-12-17 00:00:00'
           and eventcode in ('SLOWLY_CONSUME_AFTER')
           and JSONExtractString(data, 'businessCode')='ORDER$ORDER_ONLINE'
           and length(userid) > 15
           and length(relationU) > 15
           and userid != relationU
           and cnyAmount>0
         ) tmp
group by userid, relationU, businessCode;


-- 4、打赏等快支付

select median(amount),avg(amount) from pay_edge


