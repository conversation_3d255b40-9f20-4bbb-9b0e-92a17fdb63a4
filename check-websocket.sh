#!/bin/bash

echo "========================================"
echo "WebSocket连接诊断脚本"
echo "========================================"

# 检查端口占用
echo "1. 检查端口占用情况:"
echo "后端端口 8080:"
lsof -i :8080 || echo "端口8080未被占用"
echo ""
echo "前端端口 8081:"
lsof -i :8081 || echo "端口8081未被占用"
echo ""

# 检查后端服务状态
echo "2. 检查后端服务HTTP接口:"
echo "测试后端健康检查..."
curl -s -w "HTTP状态码: %{http_code}\n" http://localhost:8080/actuator/health || echo "后端服务未响应"
echo ""

# 检查WebSocket端点
echo "3. 检查WebSocket端点访问:"
echo "尝试HTTP升级到WebSocket (后端8080)..."
curl -i -N \
  -H "Connection: Upgrade" \
  -H "Upgrade: websocket" \
  -H "Sec-WebSocket-Version: 13" \
  -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" \
  http://localhost:8080/api/websocket/test_session_123 \
  --max-time 5 2>/dev/null || echo "WebSocket握手失败"
echo ""

# 检查前端代理
echo "4. 检查前端代理配置:"
echo "测试前端代理 HTTP 接口..."
curl -s -w "HTTP状态码: %{http_code}\n" http://localhost:8081/api/debug/session/start \
  -X POST -H "Content-Type: application/json" \
  -d '{"taskId": 123}' \
  --max-time 5 || echo "前端代理未响应"
echo ""

echo "5. 检查WebSocket代理..."
echo "尝试通过前端代理进行WebSocket连接..."
curl -i -N \
  -H "Connection: Upgrade" \
  -H "Upgrade: websocket" \
  -H "Sec-WebSocket-Version: 13" \
  -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" \
  http://localhost:8081/api/websocket/test_session_123 \
  --max-time 5 2>/dev/null || echo "前端WebSocket代理失败"
echo ""

# 检查Java进程
echo "6. 检查Java进程:"
ps aux | grep -E "(risk-insight|InsightApplication)" | grep -v grep || echo "未找到risk-insight Java进程"
echo ""

# 检查WebSocket相关配置
echo "7. 检查Spring Boot WebSocket配置:"
echo "查找WebSocket相关类文件..."
find /Users/<USER>/IdeaProjects/risk-insight -name "*WebSocket*" -type f | head -10
echo ""

echo "8. 检查网络连接:"
echo "测试本地环回接口..."
ping -c 2 127.0.0.1
echo ""

echo "========================================"
echo "诊断完成"
echo "========================================"
